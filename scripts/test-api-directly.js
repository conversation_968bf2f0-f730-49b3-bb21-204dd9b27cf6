#!/usr/bin/env node

/**
 * Test the /api/user/organizations endpoint directly
 */

console.log('🔍 Testing /api/user/organizations directly...\n');

// Note: This test requires a valid session cookie
// In a real browser, the cookie would be sent automatically
console.log('⚠️  This test requires authentication cookies from browser');
console.log('📋 To test properly:');
console.log('   1. Open browser dev tools');
console.log('   2. Go to Network tab');
console.log('   3. Refresh the page');
console.log('   4. Look for /api/user/organizations request');
console.log('   5. Check the response');

console.log('\n🎯 Expected issues:');
console.log('   • 401 Unauthorized - Authentication failing');
console.log('   • 500 Server Error - Database/RLS issues');
console.log('   • Empty data array - No organizations returned');
console.log('   • Wrong data format - Transformation issues');

console.log('\n🧪 Browser Console Commands:');
console.log('   // Test the API directly in browser console:');
console.log('   fetch("/api/user/organizations")');
console.log('     .then(r => r.json())');
console.log('     .then(console.log)');

console.log('\n✅ Run this script completed - check browser console!');