#!/usr/bin/env node

/**
 * Test Users Setup Script with Passwords
 * 
 * Creates comprehensive test users for four-tier architecture testing
 * with proper authentication and role assignments.
 */

const { createClient } = require('@supabase/supabase-js');

// Local Supabase configuration
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseKey);

class TestUserSetup {
  constructor() {
    this.testUsers = [
      {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: ['SUPER_ADMIN'],
        firstName: 'Platform',
        lastName: 'Administrator',
        orgType: 'transflow_super_admin'
      },
      {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: ['TENANT_ADMIN'],
        firstName: 'Sarah',
        lastName: 'Johnson',
        orgType: 'tnc_account'
      },
      {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: ['CLIENT'],
        firstName: 'Emma',
        lastName: 'Williams',
        orgType: 'tnc_customer'
      },
      {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: ['TENANT_ADMIN'],
        firstName: 'Sarah',
        lastName: 'Anderson',
        orgType: 'direct_client'
      }
    ];
  }

  async setupTestUsers() {
    console.log('🔐 SETTING UP TEST USERS WITH PASSWORDS');
    console.log('='.repeat(50));

    for (const user of this.testUsers) {
      await this.createUserWithPassword(user);
    }

    console.log('\n✅ TEST USER SETUP COMPLETE!');
    console.log('\n🧪 LOGIN CREDENTIALS:');
    console.log('='.repeat(30));
    this.testUsers.forEach(user => {
      console.log(`📧 ${user.email}`);
      console.log(`🔑 ${user.password}`);
      console.log(`👤 ${user.role.join(', ')}`);
      console.log('');
    });
  }

  async createUserWithPassword(userInfo) {
    try {
      console.log(`\n👤 Creating user: ${userInfo.email}`);

      // Check if user already exists in auth.users
      const { data: existingAuthUser } = await supabase.auth.admin.getUserByEmail(userInfo.email);
      
      let authUser;
      if (existingAuthUser.user) {
        console.log(`   ✅ Auth user already exists`);
        authUser = existingAuthUser.user;
      } else {
        // Create auth user
        const { data: newAuthUser, error: authError } = await supabase.auth.admin.createUser({
          email: userInfo.email,
          password: userInfo.password,
          email_confirm: true
        });

        if (authError) {
          console.error(`   ❌ Auth user creation failed: ${authError.message}`);
          return;
        }

        authUser = newAuthUser.user;
        console.log(`   ✅ Auth user created`);
      }

      // Find appropriate organization
      const { data: orgs } = await supabase
        .from('organizations')
        .select('id, name, account_type')
        .eq('account_type', userInfo.orgType)
        .limit(1);

      if (!orgs || orgs.length === 0) {
        console.error(`   ❌ No organization found for type: ${userInfo.orgType}`);
        return;
      }

      const organization = orgs[0];

      // Check if profile already exists
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', authUser.id)
        .single();

      if (existingProfile) {
        // Update existing profile
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            email: userInfo.email,
            roles: userInfo.role,
            first_name: userInfo.firstName,
            last_name: userInfo.lastName,
            organization_id: organization.id,
            status: 'active'
          })
          .eq('id', authUser.id);

        if (updateError) {
          console.error(`   ❌ Profile update failed: ${updateError.message}`);
        } else {
          console.log(`   ✅ Profile updated in ${organization.name}`);
        }
      } else {
        // Create new profile
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            id: authUser.id,
            email: userInfo.email,
            roles: userInfo.role,
            first_name: userInfo.firstName,
            last_name: userInfo.lastName,
            organization_id: organization.id,
            status: 'active'
          });

        if (profileError) {
          console.error(`   ❌ Profile creation failed: ${profileError.message}`);
        } else {
          console.log(`   ✅ Profile created in ${organization.name}`);
        }
      }

    } catch (error) {
      console.error(`   ❌ User setup failed: ${error.message}`);
    }
  }
}

// Execute setup
async function main() {
  const setup = new TestUserSetup();
  await setup.setupTestUsers();
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test user setup failed:', error);
    process.exit(1);
  });
}

module.exports = TestUserSetup;