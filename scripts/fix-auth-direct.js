#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixAuthDirect() {
  console.log('🔧 Applying direct authentication fixes...\n');

  try {
    // 1. Enable RLS on user_profiles
    console.log('1. Enabling RLS on user_profiles...');
    const { error: rlsError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;'
    });
    
    if (rlsError && !rlsError.message.includes('already enabled')) {
      console.log('⚠️  RLS enable warning:', rlsError.message);
    } else {
      console.log('✅ RLS enabled on user_profiles');
    }

    // 2. Create user profile policies
    console.log('2. Creating user profile policies...');
    const policies = [
      `DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;`,
      `CREATE POLICY "Users can view own profile" ON user_profiles
        FOR SELECT USING (auth.uid() = id);`,
      `DROP POLICY IF EXISTS "Super admins can view all profiles" ON user_profiles;`,
      `CREATE POLICY "Super admins can view all profiles" ON user_profiles
        FOR ALL USING (
          EXISTS (
            SELECT 1 FROM user_profiles up
            JOIN user_organizations uo ON uo.user_id = up.id
            WHERE up.id = auth.uid()
            AND uo.role = 'SUPER_ADMIN'
          )
        );`
    ];

    for (const policy of policies) {
      const { error } = await supabase.rpc('exec_sql', { sql: policy });
      if (error) {
        console.log('⚠️  Policy warning:', error.message);
      }
    }
    console.log('✅ User profile policies created');

    // 3. Create user organizations policies
    console.log('3. Creating user organizations policies...');
    const orgPolicies = [
      `DROP POLICY IF EXISTS "Users can view own organizations" ON user_organizations;`,
      `CREATE POLICY "Users can view own organizations" ON user_organizations
        FOR SELECT USING (user_id = auth.uid());`,
      `DROP POLICY IF EXISTS "Super admins can manage all user organizations" ON user_organizations;`,
      `CREATE POLICY "Super admins can manage all user organizations" ON user_organizations
        FOR ALL USING (
          EXISTS (
            SELECT 1 FROM user_profiles up
            JOIN user_organizations uo ON uo.user_id = up.id
            WHERE up.id = auth.uid()
            AND uo.role = 'SUPER_ADMIN'
          )
        );`
    ];

    for (const policy of orgPolicies) {
      const { error } = await supabase.rpc('exec_sql', { sql: policy });
      if (error) {
        console.log('⚠️  Org policy warning:', error.message);
      }
    }
    console.log('✅ User organization policies created');

    // 4. Test the fixes
    console.log('4. Testing fixes...');
    const { data: testData, error: testError } = await supabase
      .from('user_profiles')
      .select('id, email')
      .limit(1);

    if (testError) {
      console.log('❌ Test failed:', testError.message);
    } else {
      console.log('✅ Basic query test passed');
    }

    console.log('\n🎉 Authentication fixes completed!');
    console.log('\nNow try logging <NAME_EMAIL> or <EMAIL>');

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixAuthDirect();