#!/usr/bin/env node

/**
 * Test the /api/networks endpoint directly to debug the NetworkSwitcher issue
 */

const fetch = require('node-fetch');

async function testNetworksAPI() {
  console.log('🔍 Testing /api/networks endpoint...\n');

  try {
    // Test the networks API endpoint
    const response = await fetch('http://localhost:3000/api/networks', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add authentication headers if needed
        'Cookie': 'your-session-cookie-here' // This would need to be set properly
      }
    });

    console.log('📊 Response Status:', response.status);
    console.log('📊 Response Headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ API Response Data:', JSON.stringify(data, null, 2));

    if (data.success) {
      console.log(`\n🎯 Found ${data.data?.networks?.length || 0} networks`);
      if (data.data?.networks) {
        data.data.networks.forEach((network, index) => {
          console.log(`  ${index + 1}. ${network.name} (${network.account_type})`);
        });
      }
    } else {
      console.error('❌ API returned success: false');
      console.error('Error:', data.error);
    }

  } catch (error) {
    console.error('❌ Network request failed:', error.message);
  }
}

// Run the test
testNetworksAPI().catch(console.error);