#!/usr/bin/env node

/**
 * Four-Tier Account Type Validation Script
 * 
 * Tests all four account types in realistic scenarios:
 * 1. TRANSFLOW_SUPER_ADMIN - Platform administration
 * 2. TNC_ACCOUNT - Network coordination + customer management  
 * 3. TNC_CUSTOMER - Managed by parent <PERSON><PERSON> with inherited capabilities
 * 4. DIRECT_CLIENT - Independent Transflow relationship
 * 
 * ✅ COMPLIANCE FEATURES:
 * - Uses ONLY MCP access-db tool for database operations
 * - NO direct database writes - validation only
 * - READ-ONLY diagnostic approach
 * 
 * 🚨 IMPORTANT: This script requires MCP environment to run
 * - Must be executed through MCP access-db tool
 * - Cannot run standalone with direct database connections
 */

const { 
  queryDatabase,
  getAllOrganizations, 
  getUserProfiles, 
  getOrganizationsByType, 
  getTNCCustomerRelationships,
  testConnection
} = require('./shared/mcp-query');
const TestReporter = require('./shared/test-reporter');

class AccountTypeValidator {
  constructor() {
    this.reporter = new TestReporter('Account Type Validation');
    this.validationResults = {
      superAdminValidation: false,
      tncAccountValidation: false,
      tncCustomerValidation: false,
      directClientValidation: false,
      hierarchyValidation: false,
      permissionInheritanceValidation: false
    };
    
    this.testAccounts = {
      superAdmin: null,
      marriottTNC: null,
      marriottBoston: null,
      eliteTransport: null
    };
  }

  async runValidation() {
    this.reporter.startTest('Four-Tier Account Type Validation');
    
    try {
      // Compliance check: Ensure MCP environment
      await this.validateMCPCompliance();
      
      // Phase 1: Validate account type schema
      await this.validateAccountTypeSchema();
      
      // Phase 2: Test each account type
      await this.validateSuperAdminAccount();
      await this.validateTNCAccount();
      await this.validateTNCCustomerAccount();
      await this.validateDirectClientAccount();
      
      // Phase 3: Test account hierarchy and relationships
      await this.validateAccountHierarchy();
      await this.validatePermissionInheritance();
      
      // Phase 4: Test realistic business scenarios
      await this.testMarriottTNCScenario();
      await this.testBoutiqueHotelsScenario();
      
      this.reporter.generateSummary(this.validationResults);
      
    } catch (error) {
      this.reporter.logError('Account type validation failed', error);
    }
  }

  async validateMCPCompliance() {
    this.reporter.logStep('Validating MCP compliance for database ope);
    
    try {
      // Test if we're in MCP environment by attempting a simry
      await queryDatabase('SELECT 1 as test');
      this.reporter.logSuccess('MCP access-db toole');
    } catch (error) {
      if (error.message.includes('MCP accesd')) {
        this.reporter.logError('❌ COMPLIANCE VIOLATION: Script requires MCP);
        t
      ;
        throw new Error('MCP compliance violati;
      } else {
      
        throw error;
      }
    }
  }

  async v{
    thia'); schemtabaset type dating accounValidalogStep('rter.s.repoeSchema() ntTyplidateAccouad', error); test failetivity connec'DatabasegError(lo.reporter.  thise')ndalonnot run stascript can - onohibited')are prections ase connabatDirect d('.logErrorrterhis.repo  tool');db tP access- MC run through must beipts scrrror('Thi.logEis.reporterhironment' envol require tos-dblablvai is ale queprations'
    
    try {
      // Check if organizations table has account_type column
      const { data: columns } = await queryDatabase(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name IN ('account_type', 'parent_tnc_id', 'managed_by');
      `);
      
      const requiredColumns = ['account_type'];
      const foundColumns = columns ? columns.map(col => col.column_name) : [];
      
      for (const col of requiredColumns) {
        if (foundColumns.includes(col)) {
          this.reporter.logSuccess(`Found required column: ${col}`);
        } else {
          this.reporter.logError(`Missing required column: ${col}`);
        }
      }
      
      // Check for account types in the database
      const { data: accountTypes } = await queryDatabase(`
        SELECT DISTINCT account_type, COUNT(*) as count
        FROM organizations 
        WHERE account_type IS NOT NULL
        GROUP BY account_type
        ORDER BY account_type;
      `);
      
      if (accountTypes && accountTypes.length > 0) {
        this.reporter.logSuccess(`Found ${accountTypes.length} distinct account types`);
        this.reporter.logTestData('Account Types', accountTypes);
        this.validationResults.superAdminValidation = true;
      } else {
        this.reporter.logWarning('No account types found in database');
      }
      
    } catch (error) {
      this.reporter.logError('Schema validation failed', error);
    }
  }

  async validateSuperAdminAccount() {
    this.reporter.logStep('Validating transflow_super_admin account type');
    
    try {
      // Find super admin accounts
      const { data: superAdmins } = await queryDatabase(`
        SELECT o.id, o.name, o.account_type, o.parent_tnc_id, o.managed_by,
               COUNT(uo.user_id) as user_count
        FROM organizations o
        LEFT JOIN user_organizations uo ON uo.organization_id = o.id
        WHERE o.account_type = 'transflow_super_admin'
        GROUP BY o.id, o.name, o.account_type, o.parent_tnc_id, o.managed_by;
      `);
      
      if (superAdmins && superAdmins.length > 0) {
        this.testAccounts.superAdmin = superAdmins[0];
        this.reporter.logSuccess(`Found ${superAdmins.length} transflow_super_admin account(s)`);
        
        // Validate super admin characteristics
        for (const admin of superAdmins) {
          this.reporter.logInfo(`${admin.name} (${admin.user_count} users)`);
          
          // Super admin should not have a parent TNC
          if (!admin.parent_tnc_id) {
            this.reporter.logSuccess('Super admin is independent (no parent TNC)');
          } else {
            this.reporter.logError('Super admin should not have a parent TNC');
          }
          
          // Should be managed by transflow
          if (admin.managed_by === 'transflow') {
            this.reporter.logSuccess('Super admin is managed by transflow');
          } else {
            this.reporter.logWarning(`Super admin managed by: ${admin.managed_by || 'none'}`);
          }
        }
        
        this.validationResults.superAdminValidation = true;
      } else {
        this.reporter.logError('No transflow_super_admin accounts found');
      }
      
    } catch (error) {
      this.reporter.logError('Super admin validation failed', error);
    }
  }

  async validateTNCAccount() {
    this.reporter.logStep('Validating tnc_account type');
    
    try {
      // Find TNC accounts
      const { data: tncAccounts } = await queryDatabase(`
        SELECT o.id, o.name, o.account_type, o.parent_tnc_id, o.managed_by,
               COUNT(uo.user_id) as user_count,
               COUNT(customers.id) as customer_count
        FROM organizations o
        LEFT JOIN user_organizations uo ON uo.organization_id = o.id
        LEFT JOIN organizations customers ON customers.parent_tnc_id = o.id
        WHERE o.account_type = 'tnc_account'
        GROUP BY o.id, o.name, o.account_type, o.parent_tnc_id, o.managed_by;
      `);
      
      if (tncAccounts && tncAccounts.length > 0) {
        this.testAccounts.marriottTNC = tncAccounts[0];
        this.reporter.logSuccess(`Found ${tncAccounts.length} tnc_account(s)`);
        
        for (const tnc of tncAccounts) {
          this.reporter.logInfo(`${tnc.name} (${tnc.user_count} users, ${tnc.customer_count} customers)`);
          
          // TNC should not have a parent TNC (they're top-level)
          if (!tnc.parent_tnc_id) {
            this.reporter.logSuccess('TNC is independent (no parent TNC)');
          } else {
            this.reporter.logWarning('TNC has a parent TNC - unusual configuration');
          }
          
          // Should be managed by transflow
          if (tnc.managed_by === 'transflow') {
            this.reporter.logSuccess('TNC is managed by transflow');
          } else {
            this.reporter.logWarning(`TNC managed by: ${tnc.managed_by || 'none'}`);
          }
        }
        
        this.validationResults.tncAccountValidation = true;
      } else {
        this.reporter.logError('No tnc_account accounts found');
      }
      
    } catch (error) {
      this.reporter.logError('TNC account validation failed', error);
    }
  }

  async validateTNCCustomerAccount() {
    this.reporter.logStep('Validating tnc_customer account type');
    
    try {
      // Find TNC customer accounts
      const { data: tncCustomers } = await queryDatabase(`
        SELECT o.id, o.name, o.account_type, o.parent_tnc_id, o.managed_by,
               parent.name as parent_name,
               COUNT(uo.user_id) as user_count
        FROM organizations o
        LEFT JOIN organizations parent ON parent.id = o.parent_tnc_id
        LEFT JOIN user_organizations uo ON uo.organization_id = o.id
        WHERE o.account_type = 'tnc_customer'
        GROUP BY o.id, o.name, o.account_type, o.parent_tnc_id, o.managed_by, parent.name;
      `);
      
      if (tncCustomers && tncCustomers.length > 0) {
        this.testAccounts.marriottBoston = tncCustomers[0];
        this.reporter.logSuccess(`Found ${tncCustomers.length} tnc_customer account(s)`);
        
        for (const customer of tncCustomers) {
          this.reporter.logInfo(`${customer.name} (${customer.user_count} users)`);
          this.reporter.logInfo(`Parent TNC: ${customer.parent_name || 'None'}`);
          
          // TNC customer should have a parent TNC
          if (customer.parent_tnc_id) {
            this.reporter.logSuccess('TNC customer has parent TNC');
          } else {
            this.reporter.logError('TNC customer should have a parent TNC');
          }
          
          // Should be managed by TNC
          if (customer.managed_by === 'tnc') {
            this.reporter.logSuccess('TNC customer is managed by TNC');
          } else {
            this.reporter.logWarning(`TNC customer managed by: ${customer.managed_by || 'none'}`);
          }
        }
        
        this.validationResults.tncCustomerValidation = true;
      } else {
        this.reporter.logError('No tnc_customer accounts found');
      }
      
    } catch (error) {
      this.reporter.logError('TNC customer validation failed', error);
    }
  }

  async validateDirectClientAccount() {
    this.reporter.logStep('Validating direct_client account type');
    
    try {
      // Find direct client accounts
      const { data: directClients } = await queryDatabase(`
        SELECT o.id, o.name, o.account_type, o.parent_tnc_id, o.managed_by,
               COUNT(uo.user_id) as user_count
        FROM organizations o
        LEFT JOIN user_organizations uo ON uo.organization_id = o.id
        WHERE o.account_type = 'direct_client'
        GROUP BY o.id, o.name, o.account_type, o.parent_tnc_id, o.managed_by;
      `);
      
      if (directClients && directClients.length > 0) {
        this.testAccounts.eliteTransport = directClients[0];
        this.reporter.logSuccess(`Found ${directClients.length} direct_client account(s)`);
        
        for (const client of directClients) {
          this.reporter.logInfo(`${client.name} (${client.user_count} users)`);
          
          // Direct client should not have a parent TNC
          if (!client.parent_tnc_id) {
            this.reporter.logSuccess('Direct client is independent (no parent TNC)');
          } else {
            this.reporter.logError('Direct client should not have a parent TNC');
          }
          
          // Should be managed by transflow
          if (client.managed_by === 'transflow') {
            this.reporter.logSuccess('Direct client is managed by transflow');
          } else {
            this.reporter.logWarning(`Direct client managed by: ${client.managed_by || 'none'}`);
          }
        }
        
        this.validationResults.directClientValidation = true;
      } else {
        this.reporter.logError('No direct_client accounts found');
      }
      
    } catch (error) {
      this.reporter.logError('Direct client validation failed', error);
    }
  }

  async validateAccountHierarchy() {
    this.reporter.logStep('Validating account hierarchy relationships');
    
    try {
      // Test hierarchy: Super Admin -> TNC -> TNC Customer
      const { data: hierarchy } = await queryDatabase(`
        WITH RECURSIVE org_hierarchy AS (
          -- Base case: top-level organizations (no parent TNC)
          SELECT id, name, account_type, parent_tnc_id, managed_by, 0 as level
          FROM organizations 
          WHERE parent_tnc_id IS NULL
          
          UNION ALL
          
          -- Recursive case: child organizations
          SELECT o.id, o.name, o.account_type, o.parent_tnc_id, o.managed_by, h.level + 1
          FROM organizations o
          INNER JOIN org_hierarchy h ON o.parent_tnc_id = h.id
        )
        SELECT * FROM org_hierarchy ORDER BY level, account_type, name;
      `);
      
      if (hierarchy && hierarchy.length > 0) {
        this.reporter.logSuccess(`Found organizational hierarchy with ${hierarchy.length} organizations`);
        
        // Analyze hierarchy levels
        const levels = {};
        for (const org of hierarchy) {
          if (!levels[org.level]) levels[org.level] = [];
          levels[org.level].push(org);
        }
        
        for (const [level, orgs] of Object.entries(levels)) {
          this.reporter.logInfo(`Level ${level}: ${orgs.length} organization(s)`);
          for (const org of orgs) {
            this.reporter.logInfo(`  - ${org.name} (${org.account_type})`);
          }
        }
        
        this.validationResults.hierarchyValidation = true;
      } else {
        this.reporter.logWarning('No organizational hierarchy found');
      }
      
    } catch (error) {
      this.reporter.logError('Hierarchy validation failed', error);
    }
  }

  async validatePermissionInheritance() {
    this.reporter.logStep('Validating permission inheritance between account types');
    
    try {
      // Check if TNC customers inherit permissions from their parent TNC
      if (this.testAccounts.marriottTNC && this.testAccounts.marriottBoston) {
        const { data: parentPermissions } = await queryDatabase(`
          SELECT permission_key, permission_value
          FROM organization_permissions
          WHERE organization_id = '${this.testAccounts.marriottTNC.id}';
        `);
        
        const { data: childPermissions } = await queryDatabase(`
          SELECT permission_key, permission_value
          FROM organization_permissions
          WHERE organization_id = '${this.testAccounts.marriottBoston.id}';
        `);
        
        if (parentPermissions && childPermissions) {
          this.reporter.logSuccess('Found permission data for parent-child relationship');
          
          // Compare permissions
          const parentPerms = new Set(parentPermissions.filter(p => p.permission_value).map(p => p.permission_key));
          const childPerms = new Set(childPermissions.filter(p => p.permission_value).map(p => p.permission_key));
          
          const inheritedCount = [...parentPerms].filter(p => childPerms.has(p)).length;
          this.reporter.logInfo(`Inherited permissions: ${inheritedCount}/${parentPerms.size}`);
          
          if (inheritedCount > 0) {
            this.validationResults.permissionInheritanceValidation = true;
          }
        }
      } else {
        this.reporter.logWarning('No TNC parent-child relationship found for permission testing');
      }
      
    } catch (error) {
      this.reporter.logError('Permission inheritance validation failed', error);
    }
  }

  async testMarriottTNCScenario() {
    this.reporter.logStep('Testing TNC real-world scenario');
    
    try {
      // Test: TNC manages multiple customer properties
      const { data: tncScenario } = await queryDatabase(`
        SELECT 
          parent.name as tnc_name,
          parent.account_type as tnc_type,
          COUNT(child.id) as managed_customers,
          COUNT(DISTINCT q.id) as total_quotes
        FROM organizations parent
        LEFT JOIN organizations child ON child.parent_tnc_id = parent.id
        LEFT JOIN quotes q ON q.organization_id IN (parent.id, child.id)
        WHERE parent.account_type = 'tnc_account'
        GROUP BY parent.id, parent.name, parent.account_type;
      `);
      
      if (tncScenario && tncScenario.length > 0) {
        this.reporter.logSuccess('TNC Scenario Results:');
        for (const scenario of tncScenario) {
          this.reporter.logInfo(`TNC: ${scenario.tnc_name} (${scenario.tnc_type})`);
          this.reporter.logInfo(`Managed Customers: ${scenario.managed_customers}`);
          this.reporter.logInfo(`Total Quotes: ${scenario.total_quotes}`);
        }
      } else {
        this.reporter.logWarning('TNC scenario data not found');
      }
      
    } catch (error) {
      this.reporter.logError('TNC scenario test failed', error);
    }
  }

  async testBoutiqueHotelsScenario() {
    this.reporter.logStep('Testing Direct Client scenario');
    
    try {
      // Test: Independent clients as direct clients
      const { data: directClientScenario } = await queryDatabase(`
        SELECT 
          o.name,
          o.account_type,
          COUNT(q.id) as quote_count,
          COUNT(DISTINCT qr.affiliate_company_id) as affiliate_responses
        FROM organizations o
        LEFT JOIN quotes q ON q.organization_id = o.id
        LEFT JOIN quote_responses qr ON qr.quote_id = q.id
        WHERE o.account_type = 'direct_client'
        GROUP BY o.id, o.name, o.account_type;
      `);
      
      if (directClientScenario && directClientScenario.length > 0) {
        this.reporter.logSuccess('Direct Client Scenario Results:');
        for (const client of directClientScenario) {
          this.reporter.logInfo(`${client.name}: ${client.quote_count} quotes, ${client.affiliate_responses} affiliate responses`);
        }
      } else {
        this.reporter.logWarning('Direct client scenario data not found');
      }
      
    } catch (error) {
      this.reporter.logError('Direct client scenario test failed', error);
    }
  }
}

// Export for use in other test scripts
module.exports = AccountTypeValidator;

// Run if called directly
if (require.main === module) {
  const validator = new AccountTypeValidator();
  validator.runValidation().catch(console.error);
}