#!/usr/bin/env node

/**
 * Simplified Account Type Validation Test
 * 
 * Tests the four-tier architecture with direct database queries:
 * 1. TRANSFLOW_SUPER_ADMIN - Platform administration
 * 2. TNC_ACCOUNT - Transportation Network Companies  
 * 3. TNC_CUSTOMER - Customers managed by TNCs
 * 4. DIRECT_CLIENT - Independent clients
 */

const { 
  getOrganizations, 
  getProfiles, 
  getOrganizationsByType, 
  getTNCCustomerRelationships 
} = require('./shared/simple-db-query');
const TestReporter = require('./shared/test-reporter');

class SimplifiedAccountValidator {
  constructor() {
    this.reporter = new TestReporter('Simplified Account Type Validation');
    this.results = {
      superAdmin: [],
      tncAccounts: [],
      tncCustomers: [],
      directClients: [],
      relationships: []
    };
  }

  async runValidation() {
    this.reporter.startTest('Four-Tier Account Type Validation');
    
    try {
      // Test 1: Validate all organization types exist
      await this.validateAllAccountTypes();
      
      // Test 2: Validate TNC customer relationships
      await this.validateTNCRelationships();
      
      // Test 3: Validate user roles and permissions
      await this.validateUserRoles();
      
      // Test 4: Generate summary report
      await this.generateSummaryReport();
      
    } catch (error) {
      this.reporter.recordError('Account validation failed', error);
    }
    
    return this.reporter.getResults();
  }

  async validateAllAccountTypes() {
    this.reporter.logStep('Validating all four account types exist');
    
    try {
      // Get all organizations
      const allOrgs = await getOrganizations();
      this.reporter.logSuccess(`Found ${allOrgs.length} total organizations`);
      
      // Test Super Admin accounts
      const superAdmins = await getOrganizationsByType('transflow_super_admin');
      this.results.superAdmin = superAdmins;
      this.reporter.logSuccess(`✅ Super Admin: ${superAdmins.length} accounts`);
      superAdmins.forEach(org => {
        this.reporter.logInfo(`   - ${org.name} (${org.subscription_plan})`);
      });
      
      // Test TNC Account organizations
      const tncAccounts = await getOrganizationsByType('tnc_account');
      this.results.tncAccounts = tncAccounts;
      this.reporter.logSuccess(`✅ TNC Accounts: ${tncAccounts.length} accounts`);
      tncAccounts.forEach(org => {
        this.reporter.logInfo(`   - ${org.name} (${org.business_type})`);
      });
      
      // Test TNC Customer organizations
      const tncCustomers = await getOrganizationsByType('tnc_customer');
      this.results.tncCustomers = tncCustomers;
      this.reporter.logSuccess(`✅ TNC Customers: ${tncCustomers.length} accounts`);
      tncCustomers.forEach(org => {
        this.reporter.logInfo(`   - ${org.name} (Parent: ${org.parent_tnc_id ? 'Yes' : 'No'})`);
      });
      
      // Test Direct Client organizations
      const directClients = await getOrganizationsByType('direct_client');
      this.results.directClients = directClients;
      this.reporter.logSuccess(`✅ Direct Clients: ${directClients.length} accounts`);
      directClients.forEach(org => {
        this.reporter.logInfo(`   - ${org.name} (${org.subscription_plan})`);
      });
      
      // Validate we have all four types
      const accountTypes = [
        { name: 'Super Admin', count: superAdmins.length, required: true },
        { name: 'TNC Account', count: tncAccounts.length, required: true },
        { name: 'TNC Customer', count: tncCustomers.length, required: false },
        { name: 'Direct Client', count: directClients.length, required: true }
      ];
      
      let allTypesValid = true;
      accountTypes.forEach(type => {
        if (type.required && type.count === 0) {
          this.reporter.recordError(`Missing required account type: ${type.name}`, 'Four-tier architecture incomplete');
          allTypesValid = false;
        }
      });
      
      if (allTypesValid) {
        this.reporter.logSuccess('🎉 All four account types validated successfully!');
      }
      
    } catch (error) {
      this.reporter.recordError('Account type validation failed', error);
    }
  }

  async validateTNCRelationships() {
    this.reporter.logStep('Validating TNC customer relationships');
    
    try {
      const relationships = await getTNCCustomerRelationships();
      this.results.relationships = relationships;
      
      if (relationships.length > 0) {
        this.reporter.logSuccess(`✅ Found ${relationships.length} TNC customer relationships`);
        
        relationships.forEach(rel => {
          if (rel.parent_tnc) {
            this.reporter.logSuccess(`   - ${rel.name} → ${rel.parent_tnc.name}`);
          } else {
            this.reporter.recordError(`TNC customer without parent: ${rel.name}`, 'Orphaned TNC customer');
          }
        });
        
        // Validate inheritance patterns
        const validRelationships = relationships.filter(rel => rel.parent_tnc);
        const inheritanceRate = (validRelationships.length / relationships.length) * 100;
        
        if (inheritanceRate >= 90) {
          this.reporter.logSuccess(`🎯 Excellent inheritance pattern: ${inheritanceRate.toFixed(1)}% valid relationships`);
        } else {
          this.reporter.recordError('Poor inheritance pattern', `Only ${inheritanceRate.toFixed(1)}% of TNC customers have valid parent relationships`);
        }
        
      } else {
        this.reporter.logWarning('No TNC customer relationships found');
      }
      
    } catch (error) {
      this.reporter.recordError('TNC relationship validation failed', error);
    }
  }

  async validateUserRoles() {
    this.reporter.logStep('Validating user roles and permissions');
    
    try {
      const allProfiles = await getProfiles();
      this.reporter.logSuccess(`Found ${allProfiles.length} user profiles`);
      
      // Group users by role
      const roleGroups = {};
      allProfiles.forEach(profile => {
        if (profile.roles && profile.roles.length > 0) {
          profile.roles.forEach(role => {
            if (!roleGroups[role]) roleGroups[role] = [];
            roleGroups[role].push(profile);
          });
        }
      });
      
      // Validate role distribution
      Object.entries(roleGroups).forEach(([role, users]) => {
        this.reporter.logSuccess(`✅ ${role}: ${users.length} users`);
        
        // Show sample users for each role
        users.slice(0, 3).forEach(user => {
          const orgName = user.organizations?.name || 'Unknown Org';
          this.reporter.logInfo(`   - ${user.email} (${orgName})`);
        });
      });
      
      // Validate role-organization alignment
      const misalignedUsers = allProfiles.filter(profile => {
        if (!profile.organizations) return true;
        
        const orgType = profile.organizations.account_type;
        const userRoles = profile.roles || [];
        
        // Check if user roles match organization type
        if (orgType === 'transflow_super_admin' && !userRoles.includes('SUPER_ADMIN')) {
          return true;
        }
        if (orgType === 'tnc_account' && !userRoles.some(r => ['TENANT_ADMIN', 'TENANT_MANAGER'].includes(r))) {
          return true;
        }
        
        return false;
      });
      
      if (misalignedUsers.length === 0) {
        this.reporter.logSuccess('🎯 All user roles properly aligned with organization types');
      } else {
        this.reporter.recordError('Role misalignment detected', `${misalignedUsers.length} users have misaligned roles`);
      }
      
    } catch (error) {
      this.reporter.recordError('User role validation failed', error);
    }
  }

  async generateSummaryReport() {
    this.reporter.logStep('Generating four-tier architecture summary');
    
    const summary = {
      accountTypes: {
        superAdmin: this.results.superAdmin.length,
        tncAccounts: this.results.tncAccounts.length,
        tncCustomers: this.results.tncCustomers.length,
        directClients: this.results.directClients.length
      },
      relationships: {
        tncCustomerRelationships: this.results.relationships.length,
        validInheritance: this.results.relationships.filter(r => r.parent_tnc).length
      },
      architectureHealth: this.calculateArchitectureHealth()
    };
    
    this.reporter.addCustomData('fourTierSummary', summary);
    
    // Architecture health assessment
    if (summary.architectureHealth >= 90) {
      this.reporter.logSuccess(`🏆 Excellent four-tier architecture health: ${summary.architectureHealth}%`);
    } else if (summary.architectureHealth >= 70) {
      this.reporter.logSuccess(`✅ Good four-tier architecture health: ${summary.architectureHealth}%`);
    } else {
      this.reporter.recordError('Poor architecture health', `Only ${summary.architectureHealth}% health score`);
    }
    
    // Business readiness assessment
    const businessReadiness = this.assessBusinessReadiness(summary);
    this.reporter.logSuccess(`📊 Business Readiness: ${businessReadiness}`);
  }

  calculateArchitectureHealth() {
    let score = 0;
    let maxScore = 0;
    
    // Account type coverage (40 points)
    maxScore += 40;
    if (this.results.superAdmin.length > 0) score += 10;
    if (this.results.tncAccounts.length > 0) score += 10;
    if (this.results.tncCustomers.length > 0) score += 10;
    if (this.results.directClients.length > 0) score += 10;
    
    // Relationship integrity (30 points)
    maxScore += 30;
    if (this.results.relationships.length > 0) {
      const validRelationships = this.results.relationships.filter(r => r.parent_tnc).length;
      const relationshipScore = (validRelationships / this.results.relationships.length) * 30;
      score += relationshipScore;
    }
    
    // Data volume (30 points)
    maxScore += 30;
    const totalOrgs = this.results.superAdmin.length + this.results.tncAccounts.length + 
                     this.results.tncCustomers.length + this.results.directClients.length;
    if (totalOrgs >= 10) score += 30;
    else if (totalOrgs >= 5) score += 20;
    else if (totalOrgs >= 2) score += 10;
    
    return Math.round((score / maxScore) * 100);
  }

  assessBusinessReadiness(summary) {
    if (summary.accountTypes.superAdmin >= 1 && 
        summary.accountTypes.tncAccounts >= 1 && 
        summary.accountTypes.directClients >= 1) {
      return 'READY FOR PRODUCTION';
    } else if (summary.accountTypes.superAdmin >= 1) {
      return 'DEVELOPMENT READY';
    } else {
      return 'SETUP INCOMPLETE';
    }
  }
}

// Export for use in test runner
module.exports = SimplifiedAccountValidator;

// Allow direct execution
if (require.main === module) {
  const validator = new SimplifiedAccountValidator();
  validator.runValidation()
    .then(results => {
      console.log('\n🏗️ SIMPLIFIED ACCOUNT TYPE VALIDATION RESULTS');
      console.log('='.repeat(60));
      console.log(JSON.stringify(results, null, 2));
      process.exit(results.success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Validation failed:', error);
      process.exit(1);
    });
}