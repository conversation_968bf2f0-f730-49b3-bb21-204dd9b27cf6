#!/usr/bin/env node

/**
 * MCP Database Query Utility for Four-Tier Tests
 * 
 * This utility provides a standardized way to query the database
 * using the MCP access-db tool, ensuring compliance with database
 * operation standards.
 * 
 * ✅ COMPLIANT: Uses mcp_access_db_query tool only for database reads
 * ❌ PROHIBITED: Direct database connections are not allowed
 */

require('dotenv').config();

/**
 * Execute a database query using the MCP access-db tool
 * This is the ONLY approved method for database queries in scripts
 */
async function queryDatabase(sql) {
    // This function will be called by the MCP environment
    // For now, we'll throw an error to indicate MCP tool is required
    throw new Error('MCP access-db tool required - this script must be run in MCP environment');
}

/**
 * Get organizations by account type
 */
async function getOrganizationsByType(accountType) {
    const sql = `
    SELECT 
      o.id,
      o.name,
      o.slug,
      o.account_type,
      o.organization_type,
      o.status,
      o.parent_tnc_id,
      o.managed_by,
      COUNT(uo.user_id) as user_count
    FROM organizations o
    LEFT JOIN user_organizations uo ON uo.organization_id = o.id
    WHERE o.account_type = '${accountType}'
    GROUP BY o.id, o.name, o.slug, o.account_type, o.organization_type, o.status, o.parent_tnc_id, o.managed_by
    ORDER BY o.name;
  `;

    return await queryDatabase(sql);
}

/**
 * Get all organizations with their relationships
 */
async function getAllOrganizations() {
    const sql = `
    SELECT 
      o.id,
      o.name,
      o.slug,
      o.account_type,
      o.organization_type,
      o.status,
      o.parent_tnc_id,
      o.managed_by,
      parent.name as parent_name,
      COUNT(uo.user_id) as user_count
    FROM organizations o
    LEFT JOIN organizations parent ON parent.id = o.parent_tnc_id
    LEFT JOIN user_organizations uo ON uo.organization_id = o.id
    GROUP BY o.id, o.name, o.slug, o.account_type, o.organization_type, o.status, o.parent_tnc_id, o.managed_by, parent.name
    ORDER BY o.account_type, o.name;
  `;

    return await queryDatabase(sql);
}

/**
 * Get user profiles with their organization associations
 */
async function getUserProfiles() {
    const sql = `
    SELECT 
      p.id,
      p.email,
      p.full_name,
      p.roles,
      uo.role as org_role,
      o.name as organization_name,
      o.account_type
    FROM profiles p
    LEFT JOIN user_organizations uo ON uo.user_id = p.id
    LEFT JOIN organizations o ON o.id = uo.organization_id
    ORDER BY p.email;
  `;

    return await queryDatabase(sql);
}

/**
 * Get TNC customer relationships
 */
async function getTNCCustomerRelationships() {
    const sql = `
    SELECT 
      customer.id as customer_id,
      customer.name as customer_name,
      customer.account_type as customer_type,
      tnc.id as tnc_id,
      tnc.name as tnc_name,
      tnc.account_type as tnc_type
    FROM organizations customer
    JOIN organizations tnc ON tnc.id = customer.parent_tnc_id
    WHERE customer.account_type = 'tnc_customer'
    AND tnc.account_type = 'tnc_account'
    ORDER BY tnc.name, customer.name;
  `;

    return await queryDatabase(sql);
}

/**
 * Get quotes with organization context
 */
async function getQuotesWithContext() {
    const sql = `
    SELECT 
      q.id,
      q.pickup_location,
      q.dropoff_location,
      q.status,
      q.created_at,
      o.name as organization_name,
      o.account_type,
      COUNT(qr.id) as response_count
    FROM quotes q
    JOIN organizations o ON o.id = q.organization_id
    LEFT JOIN quote_responses qr ON qr.quote_id = q.id
    GROUP BY q.id, q.pickup_location, q.dropoff_location, q.status, q.created_at, o.name, o.account_type
    ORDER BY q.created_at DESC
    LIMIT 50;
  `;

    return await queryDatabase(sql);
}

/**
 * Test database connectivity
 */
async function testConnection() {
    const sql = `SELECT 'Database connection successful' as message, NOW() as timestamp;`;
    return await queryDatabase(sql);
}

module.exports = {
    queryDatabase,
    getOrganizationsByType,
    getAllOrganizations,
    getUserProfiles,
    getTNCCustomerRelationships,
    getQuotesWithContext,
    testConnection
};