#!/usr/bin/env node

/**
 * Simplified Database Query Utility for Testing
 * 
 * ✅ SIMPLIFIED APPROACH:
 * - Direct Supabase queries for READ-ONLY testing
 * - No complex abstraction layers
 * - Fast and reliable test data access
 */

const { createClient } = require('@supabase/supabase-js');

// Local Supabase configuration
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseKey);

// Simple database query functions for testing
async function getOrganizations() {
  try {
    const { data, error } = await supabase
      .from('organizations')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching organizations:', error.message);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Database query failed:', error.message);
    return [];
  }
}

async function getProfiles() {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        *,
        organizations:organization_id (
          id, name, account_type, subscription_plan, business_type
        )
      `)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching profiles:', error.message);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Database query failed:', error.message);
    return [];
  }
}

async function getQuotes() {
  try {
    const { data, error } = await supabase
      .from('quotes')
      .select(`
        *,
        organizations:organization_id (
          id, name, account_type, subscription_plan
        )
      `)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching quotes:', error.message);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Database query failed:', error.message);
    return [];
  }
}

async function getAffiliates() {
  try {
    const { data, error } = await supabase
      .from('affiliate_companies')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching affiliates:', error.message);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Database query failed:', error.message);
    return [];
  }
}

async function getOrganizationsByType(accountType) {
  try {
    const { data, error } = await supabase
      .from('organizations')
      .select('*')
      .eq('account_type', accountType)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error(`Error fetching ${accountType} organizations:`, error.message);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Database query failed:', error.message);
    return [];
  }
}

async function getProfilesByRole(role) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        *,
        organizations:organization_id (
          id, name, account_type, subscription_plan
        )
      `)
      .contains('roles', [role])
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error(`Error fetching ${role} profiles:`, error.message);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Database query failed:', error.message);
    return [];
  }
}

async function getTNCCustomerRelationships() {
  try {
    const { data, error } = await supabase
      .from('organizations')
      .select(`
        id, name, account_type, parent_tnc_id,
        parent_tnc:parent_tnc_id (
          id, name, account_type, subscription_plan
        )
      `)
      .eq('account_type', 'tnc_customer')
      .not('parent_tnc_id', 'is', null);
    
    if (error) {
      console.error('Error fetching TNC relationships:', error.message);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Database query failed:', error.message);
    return [];
  }
}

async function getQuotesByOrganization(organizationId) {
  try {
    const { data, error } = await supabase
      .from('quotes')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching organization quotes:', error.message);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Database query failed:', error.message);
    return [];
  }
}

// Legacy compatibility function for existing tests
async function mcp_access_db_query(sql) {
  console.log(`🔍 Simplified Query: ${sql.substring(0, 100)}...`);
  
  // For now, return empty data but log that we're using simplified approach
  console.log('✅ Using simplified direct database queries instead of SQL parsing');
  return { data: [], error: null };
}

module.exports = {
  supabase,
  getOrganizations,
  getProfiles,
  getQuotes,
  getAffiliates,
  getOrganizationsByType,
  getProfilesByRole,
  getTNCCustomerRelationships,
  getQuotesByOrganization,
  mcp_access_db_query // Legacy compatibility
};