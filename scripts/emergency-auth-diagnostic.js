#!/usr/bin/env node

/**
 * EMERGENCY AUTHENTICATION DIAGNOSTIC SCRIPT
 * 
 * This script performs rapid diagnosis of authentication system failure
 * Run immediately to identify root cause of P0 outage
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

console.log('🚨 EMERGENCY AUTHENTICATION DIAGNOSTIC STARTING...\n');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('📋 STEP 1: ENVIRONMENT VARIABLES CHECK');
console.log('=====================================');
console.log(`NEXT_PUBLIC_SUPABASE_URL: ${supabaseUrl ? '✅ SET' : '❌ MISSING'}`);
console.log(`NEXT_PUBLIC_SUPABASE_ANON_KEY: ${supabaseAnonKey ? '✅ SET' : '❌ MISSING'}`);
console.log(`SUPABASE_SERVICE_ROLE_KEY: ${supabaseServiceKey ? '✅ SET' : '❌ MISSING'}`);

if (!supabaseUrl || !supabaseAnonKey) {
  console.log('\n❌ CRITICAL: Missing Supabase environment variables');
  console.log('🔧 FIX: Check .env file and ensure variables are set');
  process.exit(1);
}

// Create Supabase clients
const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = supabaseServiceKey ? createClient(supabaseUrl, supabaseServiceKey) : null;

async function runDiagnostic() {
  try {
    console.log('\n📋 STEP 2: SUPABASE CONNECTION TEST');
    console.log('===================================');
    
    // Test basic connection
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    if (error) {
      console.log(`❌ Supabase connection failed: ${error.message}`);
      console.log('🔧 FIX: Check Supabase URL and API key');
      return;
    }
    console.log('✅ Supabase connection successful');

    console.log('\n📋 STEP 3: AUTHENTICATION TABLES CHECK');
    console.log('======================================');
    
    // Check auth.users table
    if (supabaseAdmin) {
      const { data: users, error: usersError } = await supabaseAdmin.auth.admin.listUsers();
      if (usersError) {
        console.log(`❌ Cannot access auth.users: ${usersError.message}`);
      } else {
        console.log(`✅ Auth users table accessible (${users.users.length} users)`);
      }
    }

    // Check profiles table
    const { data: profiles, error: profilesError } = await supabase.from('profiles').select('*').limit(5);
    if (profilesError) {
      console.log(`❌ Cannot access profiles table: ${profilesError.message}`);
      console.log('🔧 FIX: Check RLS policies on profiles table');
    } else {
      console.log(`✅ Profiles table accessible (${profiles.length} profiles)`);
    }

    // Check organizations table
    const { data: orgs, error: orgsError } = await supabase.from('organizations').select('*').limit(5);
    if (orgsError) {
      console.log(`❌ Cannot access organizations table: ${orgsError.message}`);
      console.log('🔧 FIX: Check RLS policies on organizations table');
    } else {
      console.log(`✅ Organizations table accessible (${orgs.length} organizations)`);
    }

    // Check user_organizations table
    const { data: userOrgs, error: userOrgsError } = await supabase.from('user_organizations').select('*').limit(5);
    if (userOrgsError) {
      console.log(`❌ Cannot access user_organizations table: ${userOrgsError.message}`);
      console.log('🔧 FIX: Check RLS policies on user_organizations table');
    } else {
      console.log(`✅ User_organizations table accessible (${userOrgs.length} relationships)`);
    }

    console.log('\n📋 STEP 4: RLS POLICIES CHECK');
    console.log('=============================');
    
    // Check RLS status
    const { data: rlsStatus, error: rlsError } = await supabase.rpc('check_rls_status');
    if (rlsError) {
      console.log(`⚠️  Cannot check RLS status: ${rlsError.message}`);
    } else {
      console.log('✅ RLS status check completed');
    }

    console.log('\n📋 STEP 5: CRITICAL FUNCTIONS CHECK');
    console.log('===================================');
    
    // Check is_super_admin_safe function
    const { data: superAdminCheck, error: superAdminError } = await supabase.rpc('is_super_admin_safe');
    if (superAdminError) {
      console.log(`❌ is_super_admin_safe function failed: ${superAdminError.message}`);
      console.log('🔧 FIX: Recreate is_super_admin_safe function');
    } else {
      console.log('✅ is_super_admin_safe function working');
    }

    console.log('\n📋 STEP 6: AUTHENTICATION FLOW TEST');
    console.log('===================================');
    
    // Test sign in with a test user (if exists)
    const testEmail = '<EMAIL>';
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: 'password123'
    });
    
    if (signInError) {
      console.log(`❌ Authentication test failed: ${signInError.message}`);
      if (signInError.message.includes('Invalid login credentials')) {
        console.log('ℹ️  This is expected if test user doesn\'t exist');
      } else {
        console.log('🔧 FIX: Check authentication configuration');
      }
    } else {
      console.log('✅ Authentication flow working');
    }

    console.log('\n📋 DIAGNOSTIC SUMMARY');
    console.log('=====================');
    console.log('Environment: ' + (supabaseUrl && supabaseAnonKey ? '✅ OK' : '❌ FAILED'));
    console.log('Connection: ' + (data !== undefined ? '✅ OK' : '❌ FAILED'));
    console.log('Tables: ' + (profiles && orgs && userOrgs ? '✅ OK' : '❌ SOME FAILED'));
    console.log('Functions: ' + (superAdminCheck !== undefined ? '✅ OK' : '❌ FAILED'));

    console.log('\n🚨 NEXT STEPS:');
    console.log('1. If tables failed: Disable RLS temporarily');
    console.log('2. If functions failed: Recreate missing functions');
    console.log('3. If connection failed: Check environment variables');
    console.log('4. Test authentication endpoints directly');

  } catch (error) {
    console.log(`\n❌ CRITICAL ERROR: ${error.message}`);
    console.log('🔧 IMMEDIATE ACTION: Check Supabase service status and environment configuration');
  }
}

// Run the diagnostic
runDiagnostic().then(() => {
  console.log('\n🚨 EMERGENCY DIAGNOSTIC COMPLETE');
  console.log('Refer to EMERGENCY_AUTHENTICATION_RECOVERY_PLAN.md for next steps');
}).catch(error => {
  console.log(`\n💥 DIAGNOSTIC FAILED: ${error.message}`);
  console.log('ESCALATE IMMEDIATELY TO SENIOR DEVELOPER');
});