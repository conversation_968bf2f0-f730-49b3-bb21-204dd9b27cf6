#!/bin/bash

# Architecture Validation Suite
# Comprehensive testing of Networks vs Organizations architecture + Four-Tier system

echo "🚀 ARCHITECTURE VALIDATION SUITE"
echo "=================================="
echo ""
echo "This suite validates:"
echo "✅ Networks vs Organizations architecture analysis"
echo "✅ Four-tier account system"
echo "✅ Network switcher visibility logic"
echo "✅ Service tier and geographic scenarios"
echo "✅ Complete system integration"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "${BLUE}🔄 Running: $test_name${NC}"
    echo "----------------------------------------"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if eval "$test_command"; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    echo ""
    echo "=========================================="
    echo ""
}

# Start test suite
echo "🏁 Starting Architecture Validation Suite..."
echo ""

# Test 1: Networks vs Organizations Architecture
run_test "Networks vs Organizations Architecture" "node scripts/test-networks-vs-organizations-architecture.js"

# Test 2: Four-Tier Account Types Validation
run_test "Four-Tier Account Types" "node scripts/four-tier-tests/1-account-type-validation.js"

# Test 3: Portal Access Validation
run_test "Portal Access Validation" "node scripts/four-tier-tests/2-portal-access-validation.js"

# Test 4: Network Inheritance Validation
run_test "Network Inheritance" "node scripts/four-tier-tests/4-network-inheritance-validation.js"

# Test 5: Real-World Scenarios (Marriott example)
run_test "Real-World Scenarios" "node scripts/four-tier-tests/7-real-world-scenarios.js"

# Calculate success rate
SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))

# Print comprehensive summary
echo ""
echo "📊 ARCHITECTURE VALIDATION SUITE SUMMARY"
echo "=========================================="
echo ""
echo "📈 OVERALL RESULTS:"
echo "   Total Tests: $TOTAL_TESTS"
echo "   Passed: $PASSED_TESTS"
echo "   Failed: $FAILED_TESTS"
echo "   Success Rate: $SUCCESS_RATE%"
echo ""

if [ $SUCCESS_RATE -ge 80 ]; then
    echo -e "${GREEN}🎉 ARCHITECTURE VALIDATION: SUCCESS!${NC}"
    echo "✅ Networks vs Organizations architecture is validated"
    echo "✅ Four-tier account system is working correctly"
    echo "✅ Network switcher logic is sound"
    echo "✅ System is ready for implementation"
elif [ $SUCCESS_RATE -ge 60 ]; then
    echo -e "${YELLOW}⚠️  ARCHITECTURE VALIDATION: MOSTLY WORKING${NC}"
    echo "🔧 Some components need attention"
    echo "📋 Review failed tests and implement fixes"
else
    echo -e "${RED}❌ ARCHITECTURE VALIDATION: NEEDS MAJOR WORK${NC}"
    echo "🚨 Significant architectural issues found"
    echo "🛠️  Major fixes required before implementation"
fi

echo ""
echo "🎯 QUICK TEST COMMANDS:"
echo "   Individual Tests:"
echo "   • node scripts/test-networks-vs-organizations-architecture.js"
echo "   • node scripts/four-tier-tests/run-all-tests.js"
echo "   • node scripts/four-tier-tests/4-network-inheritance-validation.js"
echo ""
echo "   Full Suite:"
echo "   • bash scripts/run-architecture-validation-suite.sh"
echo ""
echo "📋 Architecture Validation Suite Complete!"
echo "=========================================="

# Exit with appropriate code
if [ $SUCCESS_RATE -ge 80 ]; then
    exit 0
else
    exit 1
fi