#!/usr/bin/env node

/**
 * EMERGENCY AUTHENTICATION TEST
 * Tests authentication system after emergency recovery
 */

const fetch = require('node-fetch');

console.log('🚨 EMERGENCY AUTHENTICATION TEST STARTING...\n');

const BASE_URL = 'http://localhost:3000';

async function testEndpoint(endpoint, description) {
  try {
    console.log(`Testing ${description}...`);
    const response = await fetch(`${BASE_URL}${endpoint}`);
    const data = await response.text();
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${data.substring(0, 200)}${data.length > 200 ? '...' : ''}`);
    
    if (response.status === 200) {
      console.log('✅ SUCCESS\n');
      return true;
    } else {
      console.log('❌ FAILED\n');
      return false;
    }
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}\n`);
    return false;
  }
}

async function runTests() {
  console.log('📋 TESTING CRITICAL ENDPOINTS');
  console.log('=============================\n');
  
  const tests = [
    ['/api/debug/auth-check', 'Auth Check API'],
    ['/api/user/organizations', 'User Organizations API'],
    ['/api/super-admin/organizations-simple', 'Super Admin Organizations API'],
    ['/api/networks-simple', 'Networks Simple API']
  ];
  
  let passedTests = 0;
  
  for (const [endpoint, description] of tests) {
    const passed = await testEndpoint(endpoint, description);
    if (passed) passedTests++;
  }
  
  console.log('📊 TEST RESULTS');
  console.log('===============');
  console.log(`Passed: ${passedTests}/${tests.length}`);
  
  if (passedTests === tests.length) {
    console.log('✅ ALL TESTS PASSED - Authentication system recovered!');
    console.log('\n🔧 NEXT STEPS:');
    console.log('1. Test user login in browser');
    console.log('2. Verify network switcher works');
    console.log('3. Re-enable RLS with proper policies');
  } else {
    console.log('❌ SOME TESTS FAILED - Further investigation needed');
    console.log('\n🚨 ESCALATION REQUIRED');
  }
}

runTests().catch(error => {
  console.log(`💥 TEST SUITE FAILED: ${error.message}`);
  console.log('ESCALATE IMMEDIATELY');
});