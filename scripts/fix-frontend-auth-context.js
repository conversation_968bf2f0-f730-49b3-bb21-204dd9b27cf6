#!/usr/bin/env node

/**
 * Fix Frontend Authentication Context Issues
 * 
 * This script creates a simplified version of the organization loading
 * that works with the current database structure
 */

const fs = require('fs');
const path = require('path');

function fixOrganizationContext() {
  console.log('🔧 Fixing OrganizationContext authentication issues...\n');

  const contextPath = 'app/contexts/OrganizationContext.tsx';
  
  // Create a simplified loadOrganizations function
  const fixedLoadOrganizations = `
  // Load user's organizations
  const loadOrganizations = async () => {
    if (!user) {
      setOrganizations([]);
      setCurrentOrganization(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Direct query to user_organizations with proper joins
      const { data: userOrgs, error: userOrgsError } = await supabase
        .from("user_organizations")
        .select(\`
          role,
          permissions,
          organization_id,
          organizations!inner (
            id,
            name,
            slug,
            status,
            organization_type,
            settings,
            created_at,
            updated_at
          )
        \`)
        .eq("user_id", user.id);

      if (userOrgsError) {
        console.error("Error loading user organizations:", userOrgsError);
        
        // If the error is about permissions, try a simpler approach
        if (userOrgsError.code === '42501') {
          console.log("Permission denied, trying alternative approach...");
          
          // Try using the profiles view instead
          const { data: profileData, error: profileError } = await supabase
            .from("profiles")
            .select("id, email, roles")
            .eq("id", user.id)
            .single();

          if (profileError) {
            console.error("Profile query failed:", profileError);
            setError("Authentication failed. Please try logging out and back in.");
            return;
          }

          // For SUPER_ADMIN users, get the super admin organization
          if (profileData.roles?.includes('SUPER_ADMIN')) {
            const { data: superAdminOrg, error: orgError } = await supabase
              .from("organizations")
              .select("*")
              .eq("account_type", "transflow_super_admin")
              .single();

            if (orgError) {
              console.error("Super admin org query failed:", orgError);
              setError("Failed to load super admin organization");
              return;
            }

            const orgs = [{
              ...superAdminOrg,
              user_role: 'SUPER_ADMIN',
              branding: superAdminOrg.branding || {}
            }];

            setOrganizations(orgs);
            setCurrentOrganization(orgs[0]);
            await loadOrganizationBranding(orgs[0].id);
            return;
          }
        }
        
        setError("Failed to load organizations");
        return;
      }

      const orgs = userOrgs?.map((uo: any) => ({
        ...uo.organizations,
        user_role: uo.role,
        branding: uo.organizations.branding || {}
      })) || [];

      setOrganizations(orgs);

      // Load current organization from user settings or default to first
      const { data: currentOrgSetting } = await supabase
        .from("user_settings")
        .select("setting_value")
        .eq("user_id", user.id)
        .eq("setting_name", "app.current_organization_id")
        .maybeSingle();

      let currentOrgId = currentOrgSetting?.setting_value;
      
      // If no current org set or org not accessible, use first available
      if (!currentOrgId || !orgs.find(org => org.id === currentOrgId)) {
        currentOrgId = orgs[0]?.id;
      }

      if (currentOrgId) {
        const currentOrg = orgs.find(org => org.id === currentOrgId);
        if (currentOrg) {
          setCurrentOrganization(currentOrg);
          await loadOrganizationBranding(currentOrgId);
        }
      }
    } catch (err) {
      console.error("Error in loadOrganizations:", err);
      setError("Failed to load organizations");
    } finally {
      setLoading(false);
    }
  };`;

  // Read the current file
  let content;
  try {
    content = fs.readFileSync(contextPath, 'utf8');
  } catch (error) {
    console.error('❌ Could not read OrganizationContext.tsx:', error.message);
    return;
  }

  // Replace the loadOrganizations function
  const updatedContent = content.replace(
    /\/\/ Load user's organizations[\s\S]*?finally \{\s*setLoading\(false\);\s*\}\s*\};/,
    fixedLoadOrganizations.trim()
  );

  // Write the updated file
  try {
    fs.writeFileSync(contextPath, updatedContent);
    console.log('✅ Updated OrganizationContext.tsx with authentication fixes');
  } catch (error) {
    console.error('❌ Could not write OrganizationContext.tsx:', error.message);
    return;
  }

  console.log('\n🎉 Frontend authentication context fixed!');
  console.log('\nChanges made:');
  console.log('1. Added fallback for permission denied errors');
  console.log('2. Added SUPER_ADMIN specific organization loading');
  console.log('3. Improved error handling and user feedback');
  console.log('\nNow try logging in <NAME_EMAIL> or <EMAIL>');
}

fixOrganizationContext();