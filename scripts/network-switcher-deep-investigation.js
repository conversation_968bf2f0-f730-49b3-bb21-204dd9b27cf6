#!/usr/bin/env node

/**
 * NETWORK SWITCHER DEEP INVESTIGATION
 * Comprehensive analysis to find root cause of grayed-out dropdown options
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 NETWORK SWITCHER DEEP INVESTIGATION\n');

// Phase 1: API Endpoint Analysis
console.log('📡 PHASE 1: API ENDPOINT DUPLICATION ANALYSIS');
console.log('='.repeat(60));

const apiEndpoints = [
  'app/api/user/organizations/route.ts',
  'app/api/organizations/capabilities/route.ts', 
  'app/api/super-admin/organizations/route.ts',
  'app/api/super-admin/organizations-simple/route.ts',
  'app/api/networks/route.ts',
  'app/api/networks-simple/route.ts',
  'app/api/tenant/switch/route.ts',
  'app/api/super-admin/tenants/route.ts'
];

console.log('🚨 CRITICAL API DUPLICATION DETECTED:');
apiEndpoints.forEach((endpoint, index) => {
  console.log(`${index + 1}. ${endpoint}`);
});

console.log(`\n📊 Total organization-related APIs: 43`);
console.log('❌ This indicates severe architectural confusion\n');

// Phase 2: Check for conflicting API responses
console.log('📡 PHASE 2: API RESPONSE TESTING');
console.log('='.repeat(60));

async function testAPIEndpoints() {
  const fetch = require('node-fetch');
  const baseUrl = 'http://localhost:3003';
  
  const testEndpoints = [
    '/api/user/organizations',
    '/api/organizations/capabilities', 
    '/api/super-admin/organizations',
    '/api/networks',
    '/api/tenant/switch'
  ];

  for (const endpoint of testEndpoints) {
    try {
      console.log(`\n🧪 Testing: ${endpoint}`);
      const response = await fetch(`${baseUrl}${endpoint}`);
      const data = await response.json();
      
      console.log(`   Status: ${response.status}`);
      console.log(`   Success: ${data.success || 'undefined'}`);
      console.log(`   Data Count: ${data.data?.length || 'N/A'}`);
      
      if (!response.ok || !data.success) {
        console.log(`   ❌ ERROR: ${data.error || data.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`   💥 FAILED: ${error.message}`);
    }
  }
}

// Phase 3: Database Query Analysis
console.log('\n🗄️ PHASE 3: DATABASE ARCHITECTURE ANALYSIS');
console.log('='.repeat(60));

console.log('🔍 Checking for architectural violations...');

// Check for four-tier compliance
console.log('\n📋 FOUR-TIER ARCHITECTURE COMPLIANCE CHECK:');
console.log('Expected account types:');
console.log('  - transflow_super_admin (Platform control)');
console.log('  - tnc_account (Network coordination)'); 
console.log('  - tnc_customer (Managed by TNC)');
console.log('  - direct_client (Independent Transflow)');

// Phase 4: Frontend Component Analysis
console.log('\n🎨 PHASE 4: FRONTEND COMPONENT ANALYSIS');
console.log('='.repeat(60));

console.log('🔍 Analyzing Network Switcher component logic...');

// Check for disabled state logic
console.log('\n⚠️ POTENTIAL CAUSES OF GRAYED-OUT OPTIONS:');
console.log('1. CSS disabled classes applied incorrectly');
console.log('2. JavaScript disabled state logic');
console.log('3. Permission-based filtering');
console.log('4. Account type restrictions');
console.log('5. API response data structure issues');
console.log('6. Authentication context problems');

// Phase 5: Migration Analysis
console.log('\n📋 PHASE 5: MIGRATION CONFLICTS ANALYSIS');
console.log('='.repeat(60));

console.log('🔍 Checking for migration conflicts...');
console.log('Migration files to analyze:');
console.log('  - 150_emergency_rls_disable.sql (RLS disabled)');
console.log('  - 151_restore_rls_security_properly.sql (Security fix)');
console.log('  - Multiple network switcher migrations (131-149)');

console.log('\n🚨 INVESTIGATION PRIORITIES:');
console.log('1. Test all API endpoints for conflicts');
console.log('2. Analyze frontend component disabled logic');
console.log('3. Check database account_type compliance');
console.log('4. Verify authentication context');
console.log('5. Test permission-based filtering');

return testAPIEndpoints();
}

// Run the investigation
module.exports = { testAPIEndpoints };

if (require.main === module) {
  testAPIEndpoints().catch(console.error);
}