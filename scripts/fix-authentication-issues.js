#!/usr/bin/env node

/**
 * Fix Authentication Issues
 * 
 * This script addresses the authentication problems:
 * 1. <EMAIL> login failure
 * 2. <EMAIL> missing organization associations
 * 3. RLS policy issues
 * 4. User registration flow problems
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixAuthenticationIssues() {
  console.log('🔧 Starting authentication fixes...\n');

  try {
    // 1. Fix <EMAIL> - add to super admin organization
    console.log('1. Fixing <EMAIL> organization association...');
    
    // Get superior6 user ID
    const { data: superior6Profile } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('email', '<EMAIL>')
      .single();

    if (!superior6Profile) {
      console.log('❌ <EMAIL> profile not found');
      return;
    }

    // Get super admin organization
    const { data: superAdminOrg } = await supabase
      .from('organizations')
      .select('id, name')
      .eq('account_type', 'transflow_super_admin')
      .single();

    if (!superAdminOrg) {
      console.log('❌ Super admin organization not found');
      return;
    }

    // Add superior6 to super admin organization
    const { error: insertError } = await supabase
      .from('user_organizations')
      .insert({
        user_id: superior6Profile.id,
        organization_id: superAdminOrg.id,
        role: 'SUPER_ADMIN',
        permissions: ['super_admin', 'manage_all_organizations'],
        created_at: new Date().toISOString()
      });

    if (insertError) {
      console.log('❌ Error adding superior6 to organization:', insertError.message);
    } else {
      console.log('✅ Added <EMAIL> to', superAdminOrg.name);
    }

    // 2. Check <NAME_EMAIL>
    console.log('\n2. Checking <EMAIL>...');
    
    const { data: superior5Profile } = await supabase
      .from('user_profiles')
      .select('id, email')
      .eq('email', '<EMAIL>')
      .single();

    if (superior5Profile) {
      // Check if superior5 has organization association
      const { data: superior5Orgs } = await supabase
        .from('user_organizations')
        .select('organization_id, role')
        .eq('user_id', superior5Profile.id);

      if (!superior5Orgs || superior5Orgs.length === 0) {
        // Add superior5 to super admin organization
        const { error: insertError5 } = await supabase
          .from('user_organizations')
          .insert({
            user_id: superior5Profile.id,
            organization_id: superAdminOrg.id,
            role: 'SUPER_ADMIN',
            permissions: ['super_admin', 'manage_all_organizations'],
            created_at: new Date().toISOString()
          });

        if (insertError5) {
          console.log('❌ Error adding superior5 to organization:', insertError5.message);
        } else {
          console.log('✅ Added <EMAIL> to', superAdminOrg.name);
        }
      } else {
        console.log('✅ <EMAIL> already has organization associations');
      }
    }

    // 3. Fix RLS policies for user_profiles if needed
    console.log('\n3. Checking RLS policies...');
    
    // Enable RLS on user_profiles if not enabled
    const { error: rlsError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on user_profiles
        ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
        
        -- Create policy for users to see their own profile
        DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
        CREATE POLICY "Users can view own profile" ON user_profiles
          FOR SELECT USING (auth.uid() = id);
        
        -- Create policy for SUPER_ADMIN to see all profiles
        DROP POLICY IF EXISTS "Super admins can view all profiles" ON user_profiles;
        CREATE POLICY "Super admins can view all profiles" ON user_profiles
          FOR ALL USING (
            EXISTS (
              SELECT 1 FROM user_profiles up
              JOIN user_organizations uo ON uo.user_id = up.id
              WHERE up.id = auth.uid()
              AND uo.role = 'SUPER_ADMIN'
            )
          );
      `
    });

    if (rlsError) {
      console.log('⚠️  RLS policy update had issues:', rlsError.message);
    } else {
      console.log('✅ RLS policies updated');
    }

    // 4. Create a simple API endpoint test
    console.log('\n4. Testing API access...');
    
    // Test if we can query user_organizations
    const { data: testQuery, error: testError } = await supabase
      .from('user_organizations')
      .select('user_id, organization_id, role')
      .limit(1);

    if (testError) {
      console.log('❌ API test failed:', testError.message);
    } else {
      console.log('✅ API access working');
    }

    console.log('\n🎉 Authentication fixes completed!');
    console.log('\nNext steps:');
    console.log('1. Try logging <NAME_EMAIL> or <EMAIL>');
    console.log('2. Both should now have access to the super admin organization');
    console.log('3. If login still fails, check Supabase auth logs');

  } catch (error) {
    console.error('❌ Error during authentication fixes:', error);
  }
}

// Execute the fixes
fixAuthenticationIssues();