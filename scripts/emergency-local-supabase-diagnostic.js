#!/usr/bin/env node

/**
 * EMERGENCY LOCAL SUPABASE DIAGNOSTIC
 * Specifically designed for local Supabase development
 */

const { createClient } = require('@supabase/supabase-js');
const fetch = require('node-fetch');

console.log('🚨 EMERGENCY LOCAL SUPABASE DIAGNOSTIC STARTING...\n');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('📋 STEP 1: LOCAL SUPABASE ENVIRONMENT CHECK');
console.log('============================================');
console.log(`NEXT_PUBLIC_SUPABASE_URL: ${supabaseUrl}`);
console.log(`NEXT_PUBLIC_SUPABASE_ANON_KEY: ${supabaseAnonKey ? '✅ SET' : '❌ MISSING'}`);
console.log(`SUPABASE_SERVICE_ROLE_KEY: ${supabaseServiceKey ? '✅ SET' : '❌ MISSING'}`);

async function testLocalSupabase() {
  try {
    console.log('\n📋 STEP 2: LOCAL SUPABASE SERVICE CHECK');
    console.log('=======================================');
    
    // Test if local Supabase is running
    console.log('Testing local Supabase health endpoint...');
    const healthResponse = await fetch(`${supabaseUrl}/health`);
    if (healthResponse.ok) {
      console.log('✅ Local Supabase service is running');
    } else {
      console.log('❌ Local Supabase health check failed');
      console.log('🔧 FIX: Run "supabase start" to start local Supabase');
      return;
    }

    console.log('\n📋 STEP 3: DATABASE CONNECTION TEST');
    console.log('===================================');
    
    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Test basic connection with a simple query
    console.log('Testing database connection...');
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    
    if (error) {
      console.log(`❌ Database connection failed: ${error.message}`);
      
      if (error.message.includes('relation "profiles" does not exist')) {
        console.log('🔧 FIX: Run "supabase db push" to apply migrations');
      } else if (error.message.includes('RLS')) {
        console.log('🔧 FIX: RLS policies may be blocking access - apply emergency RLS disable');
      } else {
        console.log('🔧 FIX: Check if local Supabase database is properly initialized');
      }
    } else {
      console.log('✅ Database connection successful');
    }

    console.log('\n📋 STEP 4: AUTHENTICATION TABLES CHECK');
    console.log('======================================');
    
    // Check if we can access auth.users (requires service role)
    if (supabaseServiceKey) {
      const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);
      
      try {
        const { data: users, error: usersError } = await supabaseAdmin.auth.admin.listUsers();
        if (usersError) {
          console.log(`❌ Cannot access auth.users: ${usersError.message}`);
        } else {
          console.log(`✅ Auth users accessible (${users.users.length} users)`);
        }
      } catch (authError) {
        console.log(`❌ Auth admin error: ${authError.message}`);
      }
    }

    // Test each critical table
    const tables = ['profiles', 'organizations', 'user_organizations', 'user_settings'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('*').limit(1);
        if (error) {
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          console.log(`✅ ${table}: accessible`);
        }
      } catch (tableError) {
        console.log(`❌ ${table}: ${tableError.message}`);
      }
    }

    console.log('\n📋 STEP 5: NEXT.JS APPLICATION TEST');
    console.log('===================================');
    
    // Test if Next.js is running
    try {
      const nextResponse = await fetch('http://localhost:3000/api/debug/auth-check');
      if (nextResponse.ok) {
        const data = await nextResponse.text();
        console.log('✅ Next.js application is running');
        console.log(`Response: ${data.substring(0, 100)}...`);
      } else {
        console.log('❌ Next.js application not responding');
        console.log('🔧 FIX: Start Next.js with "npm run dev"');
      }
    } catch (nextError) {
      console.log('❌ Next.js application not running');
      console.log('🔧 FIX: Start Next.js with "npm run dev"');
    }

    console.log('\n📊 DIAGNOSTIC SUMMARY');
    console.log('=====================');
    console.log('Local Supabase: ' + (healthResponse.ok ? '✅ RUNNING' : '❌ NOT RUNNING'));
    console.log('Database: ' + (data !== undefined ? '✅ CONNECTED' : '❌ CONNECTION FAILED'));
    console.log('Next.js: Testing required');

  } catch (error) {
    console.log(`\n💥 CRITICAL ERROR: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔧 IMMEDIATE FIX: Local Supabase is not running');
      console.log('   Run: supabase start');
    } else if (error.message.includes('fetch failed')) {
      console.log('🔧 IMMEDIATE FIX: Network connection issue');
      console.log('   1. Check if Supabase is running: supabase status');
      console.log('   2. Restart Supabase: supabase stop && supabase start');
    }
  }
}

// Run the diagnostic
testLocalSupabase().then(() => {
  console.log('\n🚨 LOCAL SUPABASE DIAGNOSTIC COMPLETE');
  console.log('=====================================');
  console.log('Next steps:');
  console.log('1. If Supabase not running: supabase start');
  console.log('2. If database issues: supabase db push');
  console.log('3. If RLS issues: Apply emergency RLS disable migration');
  console.log('4. If Next.js issues: npm run dev');
}).catch(error => {
  console.log(`\n💥 DIAGNOSTIC FAILED: ${error.message}`);
  console.log('🚨 IMMEDIATE ACTION REQUIRED:');
  console.log('1. Check if Supabase is running: supabase status');
  console.log('2. Start Supabase if needed: supabase start');
  console.log('3. Check if port 54321 is available');
});