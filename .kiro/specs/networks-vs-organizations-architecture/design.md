# Networks vs Organizations Architecture Design

## Overview

This design document outlines the implementation approach for maintaining the validated Networks vs Organizations architectural distinction across all platform components. The architecture has been validated with 80% success rate and represents a critical foundation for the four-tier account system.

## Architecture

### Core Architectural Principles

#### 1. Clear Entity Separation
- **Organizations**: Business entities that own/provide networks and manage customers
- **Networks**: Service offerings that provide transportation capabilities
- **Relationship**: Organizations → own → Networks → serve → Customers

#### 2. Network Switcher Visibility Logic
Based on validated testing, network switcher visibility follows these rules:
- **SUPER_ADMIN**: Always visible (platform administration)
- **TNC_ADMIN**: Visible only with multiple networks (rare)
- **TNC_CUSTOMER**: Hidden (network inherited)
- **DIRECT_CLIENT**: Hidden (locked to shared)

#### 3. Service Tier Strategy
- **MVP**: Single network per TNC with basic rate differentiation
- **Post-MVP**: Service tier implementation when market validated
- **Focus**: Complete four-tier architecture before advanced features

## Components and Interfaces

### Database Schema Design

#### Organizations Table
```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  account_type VARCHAR(50) NOT NULL, -- 'tnc_account', 'tnc_customer', 'direct_client'
  parent_tnc_id UUID REFERENCES organizations(id),
  organization_type VARCHAR(50), -- 'shared', 'segregated', 'isolated'
  managed_by VARCHAR(50) -- 'transflow', 'tnc'
);
```

#### Networks Table
```sql
CREATE TABLE networks (
  id UUID PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  owner_organization_id UUID REFERENCES organizations(id) NOT NULL,
  network_type VARCHAR(50), -- 'shared', 'isolated', 'regional'
  region VARCHAR(50), -- For geographic networks (post-MVP)
  currency VARCHAR(3) -- For regional networks (post-MVP)
);
```

#### Network Access Control
```sql
CREATE TABLE organization_network_access (
  organization_id UUID REFERENCES organizations(id),
  network_id UUID REFERENCES networks(id),
  access_type VARCHAR(50), -- 'owner', 'inherited', 'granted'
  granted_by UUID REFERENCES organizations(id),
  PRIMARY KEY (organization_id, network_id)
);
```

### UI Component Architecture

#### Network Switcher Component
```typescript
interface NetworkSwitcherProps {
  user: User;
  currentNetwork: Network;
  availableNetworks: Network[];
  onNetworkChange: (networkId: string) => void;
}

export function NetworkSwitcher({ user, currentNetwork, availableNetworks, onNetworkChange }: NetworkSwitcherProps) {
  const shouldShow = shouldShowNetworkSwitcher(user, availableNetworks);
  
  if (!shouldShow) {
    return null; // Hidden for most users
  }
  
  return (
    <Select value={currentNetwork.id} onValueChange={onNetworkChange}>
      <SelectTrigger className="w-[200px]">
        <SelectValue>{currentNetwork.name}</SelectValue>
      </SelectTrigger>
      <SelectContent>
        {availableNetworks.map(network => (
          <SelectItem key={network.id} value={network.id}>
            {network.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
```

#### Organization Selector Component (Separate from Network Switcher)
```typescript
interface OrganizationSelectorProps {
  user: User;
  currentOrganization: Organization;
  availableOrganizations: Organization[];
  onOrganizationChange: (orgId: string) => void;
}

export function OrganizationSelector({ user, currentOrganization, availableOrganizations, onOrganizationChange }: OrganizationSelectorProps) {
  // This is for OPS menu organization selection, NOT network switching
  return (
    <Select value={currentOrganization.id} onValueChange={onOrganizationChange}>
      <SelectTrigger>
        <SelectValue>{currentOrganization.name}</SelectValue>
      </SelectTrigger>
      <SelectContent>
        {availableOrganizations.map(org => (
          <SelectItem key={org.id} value={org.id}>
            {org.name} ({org.account_type})
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
```

### API Design Patterns

#### Network Management API
```typescript
// GET /api/networks - List available networks for user
export async function GET(request: NextRequest) {
  const { user, organization } = await validateUserAccess(request);
  
  let availableNetworks: Network[];
  
  if (user.role === 'SUPER_ADMIN') {
    // Super admin sees all networks
    availableNetworks = await getAllNetworks();
  } else if (user.account_type === 'tnc_account') {
    // TNC admin sees their own networks
    availableNetworks = await getNetworksByOwner(organization.id);
  } else if (user.account_type === 'tnc_customer') {
    // TNC customer inherits parent's networks
    availableNetworks = await getInheritedNetworks(organization.parent_tnc_id);
  } else {
    // Direct client locked to shared network
    availableNetworks = await getSharedNetworks();
  }
  
  return NextResponse.json({ networks: availableNetworks });
}
```

#### Organization Management API
```typescript
// GET /api/organizations - List organizations for OPS menu
export async function GET(request: NextRequest) {
  const { user } = await validateUserAccess(request);
  
  let availableOrganizations: Organization[];
  
  if (user.role === 'SUPER_ADMIN') {
    // Super admin sees all organizations
    availableOrganizations = await getAllOrganizations();
  } else if (user.account_type === 'tnc_account') {
    // TNC admin sees their customers
    availableOrganizations = await getTNCCustomers(user.organization_id);
  } else {
    // Others see only their own organization
    availableOrganizations = [await getOrganization(user.organization_id)];
  }
  
  return NextResponse.json({ organizations: availableOrganizations });
}
```

## Data Models

### Network Inheritance Model

#### TNC Customer Network Inheritance
```typescript
interface NetworkInheritance {
  customer_organization_id: string;
  parent_tnc_id: string;
  inherited_networks: Network[];
  inherited_branding: BrandingConfig;
  inherited_features: FeatureFlags;
}

async function getInheritedNetworkAccess(customerOrgId: string): Promise<NetworkInheritance> {
  const customer = await getOrganization(customerOrgId);
  if (customer.account_type !== 'tnc_customer') {
    throw new Error('Organization is not a TNC customer');
  }
  
  const parentTNC = await getOrganization(customer.parent_tnc_id);
  const inheritedNetworks = await getNetworksByOwner(parentTNC.id);
  
  return {
    customer_organization_id: customerOrgId,
    parent_tnc_id: parentTNC.id,
    inherited_networks: inheritedNetworks,
    inherited_branding: parentTNC.branding_config,
    inherited_features: parentTNC.feature_flags
  };
}
```

### Service Tier Model (Post-MVP)

#### Future Service Tier Implementation
```typescript
interface ServiceTier {
  id: string;
  name: string; // 'luxury', 'business', 'express'
  network_id: string;
  minimum_vehicle_class: string;
  minimum_affiliate_rating: number;
  rate_multiplier: number;
  features: string[];
}

interface ServiceTierAssignment {
  organization_id: string;
  service_tier_id: string;
  assigned_by: string; // Parent TNC organization ID
  effective_date: Date;
}
```

## Error Handling

### Network Access Validation
```typescript
class NetworkAccessError extends Error {
  constructor(
    message: string,
    public userId: string,
    public networkId: string,
    public reason: 'insufficient_permissions' | 'network_not_found' | 'inheritance_violation'
  ) {
    super(message);
    this.name = 'NetworkAccessError';
  }
}

async function validateNetworkAccess(userId: string, networkId: string): Promise<void> {
  const user = await getUser(userId);
  const network = await getNetwork(networkId);
  
  if (!network) {
    throw new NetworkAccessError('Network not found', userId, networkId, 'network_not_found');
  }
  
  if (user.role === 'SUPER_ADMIN') {
    return; // Super admin has access to all networks
  }
  
  if (user.account_type === 'tnc_customer') {
    const inheritedNetworks = await getInheritedNetworks(user.organization.parent_tnc_id);
    if (!inheritedNetworks.some(n => n.id === networkId)) {
      throw new NetworkAccessError('Network access not inherited from parent TNC', userId, networkId, 'inheritance_violation');
    }
    return;
  }
  
  if (user.account_type === 'direct_client') {
    const sharedNetworks = await getSharedNetworks();
    if (!sharedNetworks.some(n => n.id === networkId)) {
      throw new NetworkAccessError('Direct client can only access shared networks', userId, networkId, 'insufficient_permissions');
    }
    return;
  }
  
  // Additional validation logic for TNC accounts
}
```

## Testing Strategy

### Validation Test Cases

#### Network Switcher Visibility Tests
```typescript
describe('Network Switcher Visibility', () => {
  test('SUPER_ADMIN sees switcher with multiple networks', async () => {
    const user = createSuperAdminUser();
    const networks = [createSharedNetwork(), createTNCNetwork()];
    
    expect(shouldShowNetworkSwitcher(user, networks)).toBe(true);
  });
  
  test('TNC_CUSTOMER does not see switcher', async () => {
    const user = createTNCCustomerUser();
    const networks = [createInheritedNetwork()];
    
    expect(shouldShowNetworkSwitcher(user, networks)).toBe(false);
  });
  
  test('DIRECT_CLIENT does not see switcher', async () => {
    const user = createDirectClientUser();
    const networks = [createSharedNetwork()];
    
    expect(shouldShowNetworkSwitcher(user, networks)).toBe(false);
  });
});
```

#### Network Inheritance Tests
```typescript
describe('Network Inheritance', () => {
  test('TNC customer inherits parent network access', async () => {
    const parentTNC = createTNCAccount();
    const customer = createTNCCustomer(parentTNC.id);
    const parentNetwork = createNetwork(parentTNC.id);
    
    const inheritedAccess = await getInheritedNetworkAccess(customer.id);
    
    expect(inheritedAccess.inherited_networks).toContain(parentNetwork);
    expect(inheritedAccess.parent_tnc_id).toBe(parentTNC.id);
  });
});
```

## Implementation Approach

### Phase 1: Core Architecture Validation
- Implement network switcher visibility logic
- Create organization vs network distinction in UI
- Validate four-tier account architecture
- Test network inheritance patterns

### Phase 2: API Implementation
- Build network management APIs
- Implement organization management APIs
- Create network access validation
- Add error handling and logging

### Phase 3: UI Component Integration
- Integrate network switcher component
- Separate organization selector from network switcher
- Implement portal branding inheritance
- Add responsive design for mobile

### Phase 4: Testing and Validation
- Run comprehensive test suite
- Validate against architectural patterns
- Test edge cases and error scenarios
- Performance testing for large datasets

## Technology Integration Points

### Database Integration
- PostgreSQL with proper foreign key constraints
- RLS policies for multi-tenant isolation
- Indexes for performance optimization
- Migration scripts for schema evolution

### Frontend Integration
- React components with TypeScript
- Zustand for state management
- React Query for API caching
- Responsive design with Tailwind CSS

### API Integration
- Next.js API routes with validation
- tRPC for type-safe APIs
- Authentication middleware
- Error handling and logging

This design ensures that the validated Networks vs Organizations architecture is properly implemented across all platform components while maintaining flexibility for future enhancements.