# Networks vs Organizations Architecture Requirements

## Introduction

This specification ensures that all future development correctly implements and maintains the validated Networks vs Organizations architectural distinction. This is a critical architectural pattern that has been validated with 80% success rate and must be preserved in all code generation and development activities.

## Critical Architectural Context

### Key Architectural Distinction
- **NETWORKS**: Service offerings (e.g., "Metro Ride Transportation Network", "TransFlow Shared Network")
- **ORGANIZATIONS**: Business entities (e.g., "Metro Ride Network", "Marriott International")
- **RELATIONSHIP**: Organizations provide/own Networks; Customers use Networks

### Four-Tier Account Architecture
- **TRANSFLOW_SUPER_ADMIN**: Ultimate platform control + network operations capability
- **TNC_ACCOUNT**: Network coordination + customer portal management (mini-SaaS provider)
- **TNC_CUSTOMER**: Managed by parent TNC with inherited permissions and network access
- **DIRECT_CLIENT**: Independent Transflow relationship using shared affiliate network

### Network Switcher Visibility Logic (VALIDATED)
- **SUPER_ADMIN**: Always visible (platform administration needs)
- **TNC_ADMIN**: Visible only if multiple networks (rare edge cases)
- **TNC_CUSTOMER**: Hidden (network inherited from parent TNC)
- **DIRECT_CLIENT**: Hidden (locked to shared network)

## Requirements

### Requirement 1

**User Story:** As a platform architect, I want all code to correctly distinguish between Networks and Organizations, so that the architectural integrity is maintained across all implementations.

#### Acceptance Criteria

1. WHEN creating organization-related code THEN the system SHALL treat organizations as business entities that own/provide networks
2. WHEN creating network-related code THEN the system SHALL treat networks as service offerings provided by organizations
3. WHEN implementing data models THEN the system SHALL maintain clear separation between organization tables and network tables
4. WHEN creating UI components THEN the system SHALL clearly distinguish between organization selectors and network switchers
5. WHEN implementing business logic THEN the system SHALL respect that customers use networks but belong to organizations

### Requirement 2

**User Story:** As a developer, I want network switcher visibility to follow validated logic, so that users only see network switching when appropriate.

#### Acceptance Criteria

1. WHEN user is SUPER_ADMIN THEN the system SHALL always show network switcher if networks are available
2. WHEN user is TNC_ADMIN with single network THEN the system SHALL hide network switcher
3. WHEN user is TNC_ADMIN with multiple networks THEN the system SHALL show network switcher
4. WHEN user is TNC_CUSTOMER THEN the system SHALL hide network switcher (inherited network)
5. WHEN user is DIRECT_CLIENT THEN the system SHALL hide network switcher (locked to shared network)

### Requirement 3

**User Story:** As a TNC customer, I want my network access and branding to be inherited from my parent TNC, so that I have a consistent branded experience.

#### Acceptance Criteria

1. WHEN TNC customer accesses portal THEN the system SHALL inherit network access from parent TNC
2. WHEN TNC customer uses features THEN the system SHALL apply parent TNC's branding and customization
3. WHEN TNC customer creates quotes THEN the system SHALL use parent TNC's affiliate network
4. WHEN TNC customer sees pricing THEN the system SHALL use parent TNC's rate cards and service tiers
5. WHEN TNC customer accesses features THEN the system SHALL respect parent TNC's feature flags and permissions

### Requirement 4

**User Story:** As a system administrator, I want service tier implementation to be deferred to post-MVP, so that MVP completion is prioritized over advanced features.

#### Acceptance Criteria

1. WHEN implementing network features THEN the system SHALL focus on single network per TNC (90% use case)
2. WHEN creating rate card logic THEN the system SHALL use basic rate differentiation without service tier complexity
3. WHEN building TNC features THEN the system SHALL defer service tier UI and advanced rate card logic
4. WHEN prioritizing development THEN the system SHALL complete core four-tier architecture before service tier enhancements
5. WHEN planning features THEN the system SHALL implement service tiers only when customers request them

### Requirement 5

**User Story:** As a quality assurance engineer, I want all network/organization code to be validated against architectural patterns, so that implementation matches the validated design.

#### Acceptance Criteria

1. WHEN modifying network-related files THEN the system SHALL validate against network switcher visibility logic
2. WHEN modifying organization files THEN the system SHALL validate against four-tier account architecture
3. WHEN creating database migrations THEN the system SHALL maintain network ownership relationships
4. WHEN implementing UI components THEN the system SHALL validate against portal access control matrix
5. WHEN writing business logic THEN the system SHALL validate against network inheritance patterns

## Key Implementation Patterns

### Network Switcher Implementation
```typescript
const shouldShowNetworkSwitcher = (user: User, availableNetworks: Network[]) => {
  // Always show for TransFlow Super Admin
  if (user.role === 'SUPER_ADMIN') {
    return availableNetworks.length > 0;
  }
  
  // Show for TNC_ADMIN only if they have multiple networks (rare)
  if (user.account_type === 'tnc_account' && user.role === 'TNC_ADMIN') {
    return availableNetworks.length > 1;
  }
  
  // Hide for everyone else (99% of cases)
  return false;
};
```

### Service Tier Architecture (Post-MVP)
```sql
-- Future service tier support (NOT MVP)
ALTER TABLE rate_cards ADD COLUMN service_tier VARCHAR(50);
ALTER TABLE rate_cards ADD COLUMN minimum_vehicle_class VARCHAR(50);
ALTER TABLE rate_cards ADD COLUMN minimum_affiliate_rating DECIMAL(3,2);

-- Regional network support (edge cases)
ALTER TABLE networks ADD COLUMN region VARCHAR(50);
ALTER TABLE networks ADD COLUMN currency VARCHAR(3);
```

### TNC Customer Hierarchy Validation
```sql
-- Validate TNC customer belongs to correct parent
SELECT customer.name, customer.account_type, parent.name as parent_name
FROM organizations customer
JOIN organizations parent ON parent.id = customer.parent_tnc_id
WHERE customer.account_type = 'tnc_customer';
```