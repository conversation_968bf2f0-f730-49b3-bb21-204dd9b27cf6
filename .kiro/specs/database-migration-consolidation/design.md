# Database Migration Consolidation Design

## Overview

This design addresses the critical need to consolidate 150+ database migration files into a manageable, reliable structure while preserving all functionality and maintaining data integrity.

## 🚨 CRITICAL: Tenant vs Organization Architecture Issue

**MAJOR PROBLEM IDENTIFIED:**
The existing `/supabase/migrations` folder contains a **dual-tenant architecture** that has caused numerous conflicts and migration failures:

### ❌ Problematic Legacy Architecture (TO BE ELIMINATED):
- `saas_organizations.tenants` table (conflicting with organizations)
- Mixed `tenant_id` and `organization_id` references
- `saas_organizations` schema with tenant_users, tenant_subscriptions
- Circular dependencies between tenant and organization concepts
- Multiple disabled migrations due to these conflicts

### ✅ Clean Target Architecture (WHAT WE'RE BUILDING):
- **ONLY** `public.organizations` table as the tenant entity
- **ONLY** `organization_id` foreign keys throughout the system
- **NO** `tenant_id` columns anywhere in the database
- **NO** `saas_organizations.tenants` table
- **NO** `saas_organizations` schema
- Organizations ARE the tenants - no separate concept needed

### Terminology Standards:
- ✅ **"Tenant"** in API endpoints (`/api/tenant/switch`) - conceptual/routing only
- ✅ **"Tenant"** in documentation and comments - architectural concept
- ✅ **"Tenant"** in function names (`tenant-context`, `tenant-isolation`) - conceptual
- ✅ **"Organization"** in database tables and columns - actual implementation
- ❌ **"Tenant"** in table names, column names, or schema names

## Architecture

### Migration Consolidation Strategy

The consolidation follows a 6-phase approach (not 5 as originally proposed) to properly separate concerns:

#### Phase 1: Foundation (001_foundation_auth_profiles.sql)
- Core authentication setup
- User profiles with all required columns
- Basic role system
- Essential triggers and functions

#### Phase 2: Core Business Entities (002_core_business_schema.sql)
- Quotes table with all evolutions
- Events and trips tables
- Basic business relationships
- Core constraints and indexes

#### Phase 3: Multi-Company System (003_affiliate_multi_company.sql)
- Affiliate companies structure
- Multi-company support
- Rate cards and pricing
- Vehicle and driver management

#### Phase 4: Organization-Based Multi-Tenancy (004_organization_multi_tenant.sql)
- **CRITICAL**: Organization-based tenant isolation ONLY
- **NO** separate tenant tables or tenant_id columns
- Multi-tenant RLS foundation using organization_id
- Organization context functions (NOT tenant functions)
- **ELIMINATE** all saas_organizations.tenants references

#### Phase 5: Advanced Features (005_advanced_features.sql)
- Communication systems
- Document management
- Advanced workflow features
- Reporting and analytics

#### Phase 6: Security and Optimization (006_security_optimization.sql)
- Final RLS policies
- Security optimizations
- Performance indexes
- Audit systems

### Critical Gaps in Original Plan

The original plan missed several critical areas:

1. **Communication Systems**: Email templates, messaging, notifications
2. **Document Management**: File uploads, storage buckets
3. **Advanced Workflow**: Quote affiliate offers, submission tracking
4. **Analytics and Reporting**: Admin functions, dashboard data
5. **Storage and Buckets**: Supabase storage configuration
6. **Real-time Features**: Socket.io integration, live updates

## Components and Interfaces

### Migration File Structure
```
migrations-work/consolidated/
├── 001_foundation_auth_profiles.sql
├── 002_core_business_schema.sql
├── 003_affiliate_multi_company.sql
├── 004_organization_multi_tenant.sql  # NOT tenant_saas - organizations only
├── 005_advanced_features.sql
└── 006_security_optimization.sql

migrations-work/
├── consolidated/           # Working area for consolidation
├── test-data/             # Separated test data
├── archive/
│   ├── disabled/          # All .disabled files
│   ├── emergency/         # Emergency fix files
│   └── duplicates/        # Duplicate migrations
└── analysis/              # Analysis and mapping files
```

### Data Models

#### Core Tables Consolidation Map
```sql
-- Foundation Phase
- auth.users (enhanced)
- public.profiles (consolidated)
- public.organizations (core structure)
- public.user_organizations (relationships)

-- Business Phase  
- public.quotes (all evolutions)
- public.events (consolidated)
- public.trips (consolidated)
- public.quote_timeline (consolidated)

-- Multi-Company Phase
- public.affiliate_companies (all features)
- public.vehicles (consolidated)
- public.drivers (consolidated)
- public.rate_cards (all pricing models)

-- Organization Multi-Tenant Phase
- **NO** public.tenants table (organizations ARE the tenants)
- RLS policies using organization_id ONLY
- Organization context functions (NOT tenant functions)

-- Advanced Features Phase
- public.communications (consolidated)
- public.email_templates (consolidated)
- public.document_uploads (consolidated)
- storage.buckets (configured)

-- Security Phase
- All RLS policies (optimized)
- Audit logs (consolidated)
- Security functions (optimized)
```

## Error Handling

### Migration Conflict Resolution
1. **Duplicate Table Creation**: Use `CREATE TABLE IF NOT EXISTS`
2. **Duplicate Column Addition**: Check existence before adding
3. **Policy Conflicts**: Drop existing before creating new
4. **Index Conflicts**: Use `CREATE INDEX IF NOT EXISTS`
5. **Function Conflicts**: Use `CREATE OR REPLACE FUNCTION`

### Rollback Strategy
Each consolidated migration includes:
- Rollback script with reverse operations
- Data preservation checks
- Dependency validation
- Recovery procedures

## Testing Strategy

### Validation Approach
1. **Fresh Database Testing**: Apply consolidated migrations to empty database
2. **Data Migration Testing**: Migrate existing data through consolidation
3. **Application Testing**: Full regression test suite
4. **Performance Testing**: Query performance validation
5. **Security Testing**: RLS policy validation

### Test Data Management
- Separate test data into dedicated files
- Create minimal seed data for development
- Maintain comprehensive test scenarios
- Preserve sample data for different user roles

## Critical Considerations Not in Original Plan

### 1. Storage and File Management
The original plan missed Supabase storage bucket configurations and file upload systems that are critical for document management.

### 2. Real-time Features
Socket.io integration and real-time update mechanisms need to be preserved during consolidation.

### 3. API Compatibility
Ensure all tRPC endpoints continue to work with consolidated schema.

### 4. Environment-Specific Configurations
Handle differences between development, staging, and production environments.

### 5. Data Migration Scripts
Create scripts to migrate existing production data through the consolidation process.

### 6. Monitoring and Alerting
Implement monitoring for the consolidation process and post-deployment validation.

## Implementation Phases

### Phase 1: Analysis and Mapping (Week 1)
- Complete migration analysis
- Create dependency mapping
- Identify all table relationships
- Document current state thoroughly

### Phase 2: Consolidation Development (Week 2-3)
- Create consolidated migration files
- Develop rollback scripts
- Create data migration utilities
- Implement validation checks

### Phase 3: Testing and Validation (Week 4)
- Fresh database testing
- Data migration testing
- Application regression testing
- Performance validation

### Phase 4: Deployment and Monitoring (Week 5)
- Production deployment
- Post-deployment validation
- Performance monitoring
- Issue resolution

## Risk Mitigation

### High-Risk Areas
1. **RLS Policy Changes**: Extensive testing required
2. **Foreign Key Relationships**: Careful dependency management
3. **Data Loss Prevention**: Multiple backup strategies
4. **Application Downtime**: Staged deployment approach
5. **Performance Degradation**: Continuous monitoring

### Mitigation Strategies
- Comprehensive backup procedures
- Staged rollout with rollback points
- Extensive testing at each phase
- Performance monitoring throughout
- 24/7 support during deployment