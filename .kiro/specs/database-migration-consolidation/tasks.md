# Database Migration Consolidation Implementation Plan

## Task Overview

This implementation plan consolidates 150+ database migration files into 6 core migrations while preserving all functionality and maintaining data integrity.

## Implementation Tasks

- [x] 1. Migration Analysis and Mapping
  - Analyze all 150+ migration files and categorize by purpose
  - Create dependency mapping between migrations
  - Identify duplicate functionality and conflicts
  - Document current schema state and relationships
  - Map each migration to target consolidated file
  - _Requirements: 1.1, 1.2, 1.3, 8.1, 8.3_

- [x] 1.1 Create Migration Inventory
  - Scan all migration files and create comprehensive inventory
  - Categorize by: auth, business logic, multi-tenant, RLS, test data, emergency fixes
  - Identify disabled migrations and their reasons for disabling
  - Document migration timestamps and dependencies
  - _Requirements: 1.1, 1.2_

- [x] 1.2 Analyze Schema Dependencies
  - Map foreign key relationships across all tables
  - Identify table creation order requirements
  - Document RLS policy dependencies
  - Map function and trigger dependencies
  - Create dependency graph for consolidation order
  - _Requirements: 4.2, 8.3_

- [x] 1.3 Identify Consolidation Conflicts
  - Find duplicate table creations and column additions
  - Identify conflicting RLS policies
  - Document emergency fixes that override previous migrations
  - Map tenant-organization consolidation conflicts
  - _Requirements: 1.3, 1.5, 2.3_

- [x] 2. Create Foundation Migration (001_foundation_auth_profiles.sql)
  - Implement core authentication setup with auth.users enhancements
  - Create consolidated profiles table with all required columns
  - Implement basic role system with all 7 user roles
  - Create essential user management triggers and functions
  - Implement basic RLS policies for user data
  - _Requirements: 1.1, 3.3, 4.5_

- [x] 2.1 Implement Core Authentication
  - Set up auth.users table with required extensions
  - Create handle_new_user() function with all features
  - Implement user creation triggers
  - Set up basic authentication policies
  - _Requirements: 3.3, 4.5_

- [x] 2.2 Create Consolidated Profiles Table
  - Design profiles table with all columns from various migrations
  - Include organization_id, roles array, metadata fields
  - Implement profile creation and update triggers
  - Create profile RLS policies without recursion issues
  - _Requirements: 2.1, 3.3, 4.1_

- [x] 2.3 Implement Role Management System
  - Define all 7 user roles (SUPER_ADMIN, ADMIN, etc.)
  - Create role validation constraints
  - Implement role-based access functions
  - Create role assignment and management utilities
  - _Requirements: 3.3, 4.1_

- [x] 3. Create Core Business Schema (002_core_business_schema.sql)
  - Implement consolidated quotes table with all evolutions
  - Create events and trips tables with full feature set
  - Implement quote timeline and communication systems
  - Create core business relationships and constraints
  - Add essential indexes for performance
  - _Requirements: 3.1, 3.4, 4.2, 7.1_

- [x] 3.1 Implement Consolidated Quotes Table
  - Create quotes table with all columns from various migrations
  - Include service-specific fields, coordinates, stops
  - Implement quote status management and timeline
  - Create quote validation constraints and triggers
  - _Requirements: 3.1, 3.4_

- [x] 3.2 Create Events and Trips Schema
  - Implement events table with all required features
  - Create trips table with comprehensive tracking
  - Implement event-trip relationships
  - Add passenger management capabilities
  - _Requirements: 3.1, 4.2_

- [x] 3.3 Implement Quote Workflow System
  - Create quote timeline tracking
  - Implement quote status change triggers
  - Create quote communication system
  - Implement quote assignment and matching logic
  - _Requirements: 3.1, 3.5_

- [x] 4. Create Multi-Company System (003_affiliate_multi_company.sql)
  - Implement consolidated affiliate companies structure
  - Create vehicle and driver management systems
  - Implement comprehensive rate cards and pricing
  - Create multi-company relationship management
  - Implement company-specific RLS policies
  - _Requirements: 3.2, 3.4, 4.1, 7.2_

- [x] 4.1 Implement Affiliate Companies Schema
  - Create affiliate_companies table with all features
  - Include address, insurance, onboarding fields
  - Implement company status and application workflow
  - Create company document management
  - _Requirements: 3.2, 4.1_

- [x] 4.2 Create Vehicle and Driver Management
  - Implement vehicles table with insurance tracking
  - Create drivers table with certification management
  - Implement vehicle-driver relationships
  - Create vehicle availability and scheduling
  - _Requirements: 3.2, 4.2_

- [x] 4.3 Implement Rate Cards and Pricing
  - Create comprehensive rate cards system
  - Implement multiple pricing models
  - Create rate card validation and management
  - Implement pricing calculation functions
  - _Requirements: 3.4, 7.2_

- [x] 5. Create Organization-Based Multi-Tenancy (004_organization_multi_tenant.sql)
  - **CRITICAL**: Implement organization-based tenant isolation ONLY
  - **ELIMINATE**: All saas_organizations.tenants table references
  - **ELIMINATE**: All tenant_id columns and use organization_id ONLY
  - Create organization-based RLS foundation (NO tenant RLS)
  - Create organization context functions (NOT tenant functions)
  - Implement default organization setup
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 5.1 Implement Organization-Based Tenancy (ELIMINATE Tenant Confusion)
  - **CRITICAL**: Use organizations table as the ONLY tenant entity
  - **ELIMINATE**: Any saas_organizations.tenants table creation
  - **ELIMINATE**: Any tenant_id columns - use organization_id ONLY
  - Implement user_organizations relationship table
  - Create organization-based data isolation (NOT tenant isolation)
  - Implement organization switching mechanisms (NOT tenant switching)
  - _Requirements: 2.1, 2.2_

- [x] 5.2 Create Organization Context System
  - **CRITICAL**: Implement organization context functions (NOT tenant functions)
  - Create organization switching utilities (NOT tenant switching)
  - Implement organization-aware RLS policies (NOT tenant-aware)
  - Create organization data isolation validation
  - **ELIMINATE**: Any references to tenant context or tenant switching
  - _Requirements: 2.2, 2.3_

- [x] 5.3 Implement Default Organization Setup
  - Create default organization for new users
  - Implement automatic user-organization assignment
  - Create organization management functions
  - Implement organization-level configurations
  - _Requirements: 2.5, 4.5_

- [x] 6. Create Advanced Features (005_advanced_features.sql)
  - Implement communication and messaging systems
  - Create document management and storage
  - Implement advanced workflow features
  - Create reporting and analytics functions
  - Implement notification and email systems
  - _Requirements: 3.5, 5.1, 5.2, 5.3_

- [x] 6.1 Implement Communication Systems
  - Create communications table and messaging
  - Implement email templates system
  - Create notification management
  - Implement real-time communication features
  - _Requirements: 3.5, 5.2_

- [x] 6.2 Create Document Management
  - Implement document_uploads table
  - Configure Supabase storage buckets
  - Create file upload and management utilities
  - Implement document security and access control
  - _Requirements: 5.1, 4.1_

- [x] 6.3 Implement Advanced Workflow
  - Create quote affiliate offers system
  - Implement submission order tracking
  - Create counter-offer support
  - Implement affiliate matching logic
  - _Requirements: 3.1, 3.2_

- [x] 7. Create Security and Optimization (006_security_optimization.sql)
  - Implement final optimized RLS policies
  - Create comprehensive audit logging
  - Implement security functions and validation
  - Create performance indexes and optimizations
  - Implement monitoring and diagnostic functions
  - _Requirements: 4.1, 4.3, 4.4, 7.1, 7.2_

- [x] 7.1 Implement Optimized RLS Policies
  - Create non-recursive RLS policies for all tables
  - Implement efficient tenant isolation policies
  - Create role-based access policies
  - Optimize policy performance and eliminate conflicts
  - _Requirements: 4.1, 2.2_

- [x] 7.2 Create Audit and Security Systems
  - Implement comprehensive audit_logs table
  - Create security validation functions
  - Implement access logging and monitoring
  - Create security diagnostic utilities
  - _Requirements: 4.4, 8.4_

- [x] 7.3 Implement Performance Optimizations
  - Create optimal indexing strategy
  - Remove redundant indexes and constraints
  - Implement query performance optimizations
  - Create database maintenance utilities
  - _Requirements: 7.1, 7.2, 7.4_

- [ ] 8. Create Test Data and Development Support
  - Separate all test data into dedicated files
  - Create minimal seed data for development
  - Implement development utilities and debugging
  - Create sample data for all user roles and scenarios
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 8.1 Separate Test Data
  - Extract all test data insertions from migrations
  - Create organized test data files by category
  - Implement test data loading utilities
  - Create test data cleanup procedures
  - _Requirements: 5.1, 5.2_

- [ ] 8.2 Create Development Seed Data
  - Create minimal seed data for development
  - Implement sample users for each role
  - Create sample companies and quotes
  - Implement development configuration utilities
  - _Requirements: 5.3, 5.4_

- [ ] 9. Create Rollback and Recovery Systems
  - Create rollback scripts for each consolidated migration
  - Implement data backup and recovery procedures
  - Create migration validation and testing utilities
  - Document emergency recovery procedures
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 9.1 Implement Rollback Scripts
  - Create reverse migration for each consolidated file
  - Implement data preservation during rollbacks
  - Create rollback validation procedures
  - Test rollback procedures thoroughly
  - _Requirements: 6.1, 6.4_

- [ ] 9.2 Create Backup and Recovery
  - Implement automated backup procedures
  - Create data migration utilities
  - Implement recovery validation
  - Document recovery procedures
  - _Requirements: 6.2, 6.3_

- [ ] 10. Testing and Validation
  - Test consolidated migrations on fresh database
  - Validate data migration from existing schema
  - Perform comprehensive application testing
  - Validate performance and security
  - _Requirements: All requirements validation_

- [ ] 10.1 Fresh Database Testing
  - Apply consolidated migrations to empty database
  - Validate all table structures and relationships
  - Test all RLS policies and security
  - Validate all functions and triggers
  - _Requirements: 1.1, 4.1, 4.2_

- [ ] 10.2 Data Migration Testing
  - Test migration from current production schema
  - Validate data integrity during migration
  - Test application functionality post-migration
  - Validate performance after consolidation
  - _Requirements: 4.3, 7.4, 7.5_

- [ ] 10.3 Comprehensive Application Testing
  - Run full regression test suite
  - Test all user roles and permissions
  - Validate all business workflows
  - Test multi-tenant functionality
  - _Requirements: 3.1, 3.2, 3.3, 2.1_

- [ ] 11. Documentation and Deployment
  - Create comprehensive documentation for consolidated migrations
  - Document deployment procedures and rollback plans
  - Create troubleshooting guides and maintenance procedures
  - Implement monitoring and alerting for post-deployment
  - _Requirements: 8.1, 8.2, 8.4, 8.5_

- [ ] 11.1 Create Migration Documentation
  - Document each consolidated migration thoroughly
  - Explain architectural decisions and trade-offs
  - Create dependency documentation
  - Document troubleshooting procedures
  - _Requirements: 8.1, 8.2, 8.4_

- [ ] 11.2 Create Deployment Procedures
  - Document step-by-step deployment process
  - Create pre-deployment validation checklist
  - Document post-deployment validation procedures
  - Create monitoring and alerting setup
  - _Requirements: 8.5, 6.4_