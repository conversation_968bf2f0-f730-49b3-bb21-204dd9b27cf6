# Database Migration Consolidation Requirements

## Introduction

The WWMS DIY platform currently has 150+ database migration files with significant duplication, inconsistencies, and architectural conflicts. This has created a critical maintenance burden and deployment instability that must be resolved through systematic consolidation.

## Requirements

### Requirement 1: Migration File Consolidation

**User Story:** As a developer, I want a clean, consolidated migration structure so that database deployments are reliable and maintainable.

#### Acceptance Criteria

1. WH<PERSON> consolidating migrations THEN the total number of migration files SHALL be reduced from 150+ to 5 core architectural migrations
2. W<PERSON><PERSON> creating consolidated migrations THEN each migration SHALL represent a distinct architectural phase
3. W<PERSON>EN consolidating THEN all duplicate functionality SHALL be eliminated
4. W<PERSON>EN consolidating THEN all disabled migrations SHALL be properly archived
5. W<PERSON>EN consolidating THEN all emergency fixes SHALL be integrated into appropriate core migrations

### Requirement 2: Organization-Based Multi-Tenancy (CRITICAL: Eliminate Tenant Confusion)

**User Story:** As a platform architect, I want a clean, single-table multi-tenant architecture using only organizations so that tenant isolation is secure without architectural conflicts.

#### Acceptance Criteria

1. W<PERSON><PERSON> consolidating multi-tenant migrations THEN the system SHALL use ONLY `public.organizations` as the tenant entity
2. WH<PERSON> consolidating THEN there SHALL be NO `saas_organizations.tenants` table or `saas_organizations` schema
3. WHEN consolidating THEN all foreign keys SHALL use `organization_id` ONLY (NO `tenant_id` columns anywhere)
4. WHEN consolidating RLS policies THEN they SHALL reference `organization_id` ONLY
5. WHEN consolidating THEN all legacy tenant-organization dual architecture SHALL be eliminated
6. WHEN consolidating THEN organizations SHALL serve as tenants with no separate tenant concept

### Requirement 3: Business Logic Preservation

**User Story:** As a business stakeholder, I want all core business functionality to be preserved during consolidation so that the platform continues to operate correctly.

#### Acceptance Criteria

1. WHEN consolidating quote-related migrations THEN all quote workflow functionality SHALL be preserved
2. WHEN consolidating affiliate migrations THEN multi-company affiliate support SHALL be maintained
3. WHEN consolidating user management THEN all 7 user roles SHALL be properly supported
4. WHEN consolidating rate cards THEN pricing functionality SHALL be preserved
5. WHEN consolidating communication systems THEN messaging and notification features SHALL be maintained

### Requirement 4: Data Integrity and Security

**User Story:** As a security administrator, I want all RLS policies and data constraints to be properly consolidated so that data security is maintained.

#### Acceptance Criteria

1. WHEN consolidating RLS policies THEN all security constraints SHALL be preserved
2. WHEN consolidating THEN foreign key relationships SHALL be maintained
3. WHEN consolidating THEN data validation constraints SHALL be preserved
4. WHEN consolidating THEN audit logging functionality SHALL be maintained
5. WHEN consolidating THEN user authentication triggers SHALL be properly implemented

### Requirement 5: Development and Testing Support

**User Story:** As a developer, I want test data and development utilities to be properly organized so that development workflows are not disrupted.

#### Acceptance Criteria

1. WHEN organizing migrations THEN test data SHALL be separated into dedicated files
2. WHEN organizing migrations THEN development seed data SHALL be clearly identified
3. WHEN organizing migrations THEN debugging functions SHALL be preserved where needed
4. WHEN organizing migrations THEN development-specific configurations SHALL be maintained
5. WHEN organizing migrations THEN sample data for testing SHALL be available

### Requirement 6: Rollback and Recovery

**User Story:** As a DevOps engineer, I want proper rollback capabilities so that failed deployments can be safely reverted.

#### Acceptance Criteria

1. WHEN creating consolidated migrations THEN rollback scripts SHALL be provided for each migration
2. WHEN archiving old migrations THEN they SHALL be preserved for emergency reference
3. WHEN consolidating THEN backup procedures SHALL be documented
4. WHEN consolidating THEN recovery procedures SHALL be tested
5. WHEN consolidating THEN migration dependencies SHALL be clearly documented

### Requirement 7: Performance and Optimization

**User Story:** As a system administrator, I want optimized database schema and indexes so that application performance is maintained or improved.

#### Acceptance Criteria

1. WHEN consolidating THEN redundant indexes SHALL be eliminated
2. WHEN consolidating THEN optimal indexing strategy SHALL be implemented
3. WHEN consolidating THEN database constraints SHALL be optimized
4. WHEN consolidating THEN query performance SHALL be maintained or improved
5. WHEN consolidating THEN database size SHALL be optimized where possible

### Requirement 8: Documentation and Maintenance

**User Story:** As a future developer, I want clear documentation of the consolidated migration structure so that I can understand and maintain the system.

#### Acceptance Criteria

1. WHEN consolidating THEN each migration SHALL be thoroughly documented
2. WHEN consolidating THEN architectural decisions SHALL be explained
3. WHEN consolidating THEN migration dependencies SHALL be documented
4. WHEN consolidating THEN troubleshooting guides SHALL be provided
5. WHEN consolidating THEN future migration guidelines SHALL be established