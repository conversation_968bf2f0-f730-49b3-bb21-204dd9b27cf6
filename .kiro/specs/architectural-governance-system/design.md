# Design Document

## Overview

The Architectural Governance System provides contextual reminders and validation checklists to ensure developers leverage existing SUPER_ADMIN control capabilities when modifying code. The system follows the same pattern as the existing multi-tenant isolation validation, using agent hooks and steering files to display relevant reminders when specific file types are modified.

## Architecture

### Component Structure

```
.kiro/
├── hooks/
│   ├── super-admin-controls-reminder.kiro.hook
│   ├── permission-architecture-validator.kiro.hook
│   └── subscription-architecture-validator.kiro.hook
├── steering/
│   └── super-admin-controls-reference.md
```

### Hook Trigger Patterns

The system uses file pattern matching to trigger appropriate reminders:

- **Permission Files**: `app/api/**/*`, `app/lib/auth/**/*`, `components/**/permissions/**/*`
- **Organization Files**: `app/api/organizations/**/*`, `app/contexts/OrganizationContext.tsx`
- **Subscription Files**: `app/api/subscriptions/**/*`, `app/lib/billing/**/*`
- **Database Migrations**: `supabase/migrations/**/*.sql`

## Components and Interfaces

### Hook Configuration Structure

```typescript
interface GovernanceHook {
  enabled: boolean;
  name: string;
  description: string;
  version: string;
  when: {
    type: "fileEdited";
    patterns: string[];
  };
  then: {
    type: "askAgent";
    prompt: string;
  };
}
```

### Reminder Display Format

Following the multi-tenant isolation pattern, reminders will use:

```markdown
🎛️ **SUPER_ADMIN CONTROLS VALIDATION** 🎛️

A file has been modified that may affect [permissions/subscriptions/organizations]. Please validate:

## 1. **Available Controls**
[Contextual list of relevant SUPER_ADMIN capabilities]

## 2. **Required Patterns**
[Code examples showing correct usage]

## 3. **Validation Checklist**
- [ ] Uses existing capabilities instead of creating new ones
- [ ] Considers organization types (shared/segregated/isolated)
- [ ] Leverages permission templates and granular permissions
- [ ] Respects subscription plan architecture

Please analyze the file for SUPER_ADMIN control compliance.
```

### Steering File Structure

The steering file will contain the complete SUPER_ADMIN controls reference:

```markdown
# SUPER_ADMIN Controls Reference

## Organization Foundation Settings
- Organization Types: shared, segregated, isolated
- Subscription Plans: free_trial, professional, enterprise  
- Permission Templates: basic_client, premium_client, tnc_enterprise

## White-Label & Branding Controls
- has_white_labeling, has_custom_domain, has_custom_branding

## Feature Flags (JSONB Toggle)
- apiAccess, whiteLabeling, realTimeTracking, advancedAnalytics, prioritySupport

## Granular Permission Keys (20 Available)
[Complete list with usage examples]
```

## Data Models

### Hook Trigger Mapping

```typescript
const GOVERNANCE_TRIGGERS = {
  permissions: {
    patterns: [
      "app/api/**/*.ts",
      "app/lib/auth/**/*.ts", 
      "components/**/permissions/**/*.tsx",
      "app/middleware.ts"
    ],
    reminder: "PERMISSION_ARCHITECTURE_REMINDER"
  },
  organizations: {
    patterns: [
      "app/api/organizations/**/*.ts",
      "app/contexts/OrganizationContext.tsx",
      "app/lib/organization/**/*.ts"
    ],
    reminder: "ORGANIZATION_CONTROLS_REMINDER"
  },
  subscriptions: {
    patterns: [
      "app/api/subscriptions/**/*.ts",
      "app/lib/billing/**/*.ts",
      "components/**/subscription/**/*.tsx"
    ],
    reminder: "SUBSCRIPTION_ARCHITECTURE_REMINDER"
  },
  database: {
    patterns: [
      "supabase/migrations/**/*.sql"
    ],
    reminder: "DATABASE_ARCHITECTURE_REMINDER"
  }
};
```

### Reminder Content Templates

```typescript
const REMINDER_TEMPLATES = {
  PERMISSION_ARCHITECTURE: {
    title: "🔐 PERMISSION ARCHITECTURE VALIDATION",
    sections: [
      "Available Granular Permissions (20 keys)",
      "Permission Template System",
      "Role-Based Access Patterns",
      "Validation Checklist"
    ]
  },
  ORGANIZATION_CONTROLS: {
    title: "🏢 ORGANIZATION CONTROLS VALIDATION", 
    sections: [
      "Organization Types (shared/segregated/isolated)",
      "White-Label Capabilities",
      "Feature Flag System",
      "Validation Checklist"
    ]
  },
  SUBSCRIPTION_ARCHITECTURE: {
    title: "💳 SUBSCRIPTION ARCHITECTURE VALIDATION",
    sections: [
      "Subscription Plans & Permission Templates",
      "Feature Access Patterns",
      "Billing Integration Points",
      "Validation Checklist"
    ]
  }
};
```

## Error Handling

### Hook Execution Safety

- Hooks will fail gracefully if steering files are missing
- Default reminders will be shown if template loading fails
- File pattern matching will be case-insensitive
- Hook execution will not block file saving

### Validation Feedback

- Clear, actionable validation points
- Code examples showing correct patterns
- Links to existing implementations
- Specific guidance on using SUPER_ADMIN controls

## Testing Strategy

### Hook Trigger Testing

```typescript
describe('Governance Hooks', () => {
  it('should trigger permission reminder for auth files', () => {
    const filePath = 'app/lib/auth/permissions.ts';
    expect(shouldTriggerHook(filePath, 'permissions')).toBe(true);
  });
  
  it('should trigger organization reminder for org context', () => {
    const filePath = 'app/contexts/OrganizationContext.tsx';
    expect(shouldTriggerHook(filePath, 'organizations')).toBe(true);
  });
});
```

### Reminder Content Testing

- Validate all reminder templates load correctly
- Test steering file parsing and display
- Verify code examples are syntactically correct
- Ensure checklists are comprehensive

### Integration Testing

- Test hook execution in development environment
- Validate steering file inclusion in AI context
- Test reminder display in various file modification scenarios
- Verify performance impact is minimal

## Implementation Notes

### File Pattern Optimization

- Use specific patterns to avoid false triggers
- Balance coverage with precision
- Consider file size limits for hook execution
- Implement caching for steering file content

### User Experience Considerations

- Reminders should be helpful, not intrusive
- Provide option to dismiss reminders temporarily
- Include "Learn More" links to detailed documentation
- Allow customization of reminder frequency

### Maintenance Strategy

- Keep steering files synchronized with actual SUPER_ADMIN capabilities
- Update hook patterns as codebase structure evolves
- Regular review of reminder effectiveness
- Community feedback integration for improvements