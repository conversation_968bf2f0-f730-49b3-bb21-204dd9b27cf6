# Design Document

## Overview

This document outlines the comprehensive analysis and documentation approach for the WWMS DIY multi-tenant global ground transportation platform. The platform is a sophisticated SaaS solution that handles complete booking-to-completion workflows with different stakeholder portals, granular permissions, and multi-tenant isolation.

Based on the codebase analysis, the platform is approximately 81% complete with a robust foundation including:
- Multi-tenant architecture with organization-based isolation
- Role-based access control with 7 distinct user roles
- Complete quote workflow management
- Affiliate company management and onboarding
- Real-time notifications and tracking capabilities
- Comprehensive authentication and authorization system

## Architecture

### Current System Architecture

The platform follows a modern Next.js 14 App Router architecture with the following key components:

#### Frontend Architecture
- **Framework**: Next.js 14 with App Router and TypeScript
- **UI Components**: Radix UI with Tailwind CSS and shadcn/ui
- **State Management**: React Query + Zustand + Context API
- **Authentication**: Custom JWT-based auth with Supabase Auth
- **Real-time Updates**: Socket.io for live notifications

#### Backend Architecture
- **Database**: PostgreSQL with Supabase
- **API Layer**: Next.js API routes + tRPC for type-safe APIs
- **Authentication**: Supabase Auth with custom middleware
- **Security**: Row Level Security (RLS) policies for multi-tenant isolation
- **File Storage**: Supabase Storage for avatars and documents

#### Multi-Tenant Architecture
The platform implements a sophisticated multi-tenant system with:
- **Organization-based isolation**: Each tenant has an `organization_id`
- **Network tenant support**: Super admin can manage multiple client organizations
- **Granular permissions**: 4 pre-configured templates with per-user customization
- **Role-based access control**: 7 distinct user roles with hierarchical permissions

### Portal Structure

The platform is organized into distinct portals based on user roles:

1. **Super Admin Portal** (`/super-admin`)
   - Platform-wide management and oversight
   - Multi-tenant administration
   - System configuration and monitoring

2. **Event Manager Portal** (`/event-manager`) 
   - Maps to CLIENT and CLIENT_COORDINATOR roles
   - Event transportation planning and management
   - Quote creation and management
   - CLIENT role has full organizational access
   - CLIENT_COORDINATOR role has filtered access to assigned events only

3. **Affiliate Portal** (`/affiliate`)
   - Maps to AFFILIATE and AFFILIATE_DISPATCH roles
   - Company profile and fleet management
   - Rate card configuration
   - Quote response and trip management

4. **Customer Portal** (`/customer`)
   - Maps to PASSENGER role (legacy, may be deprecated)
   - Booking requests and trip tracking
   - Note: Passenger management is now integrated into event-manager portal

## Components and Interfaces

### Core Data Models

Based on the database schema analysis, the platform includes these key entities:

#### User Management
- **profiles**: User profiles with role assignments
- **organizations**: Tenant isolation and management
- **user_organizations**: Many-to-many relationship for user-tenant associations

#### Transportation Management
- **quotes**: Transportation quote requests with full lifecycle tracking
- **quote_offers**: Affiliate responses to quote requests
- **quote_communications**: Real-time messaging between stakeholders
- **trips**: Confirmed transportation bookings
- **intermediate_stops**: Multi-stop journey management

#### Affiliate Management
- **affiliate_companies**: Affiliate business profiles and capabilities
- **affiliate_user_companies**: User-company associations with roles
- **rate_cards**: Dynamic pricing structures
- **drivers**: Driver profiles and verification status
- **vehicles**: Fleet management and availability

#### Event Management
- **events**: Event planning and coordination
- **customers**: Customer relationship management
- **passengers**: Individual passenger management

### API Architecture

The platform uses a hybrid API approach:

#### tRPC Integration
- Type-safe API calls with automatic TypeScript inference
- Centralized error handling and validation
- Real-time subscriptions for live updates

#### REST API Endpoints
Organized by domain with role-based access control:
- `/api/super-admin/*` - Platform administration
- `/api/affiliate/*` - Affiliate operations
- `/api/event-manager/*` - Client operations (CLIENT and CLIENT_COORDINATOR roles)
- `/api/quotes/*` - Quote management
- `/api/auth/*` - Authentication flows

### Authentication & Authorization

#### Multi-layered Security
1. **Middleware-level**: Route protection and role validation
2. **API-level**: Request authentication and authorization
3. **Database-level**: RLS policies for data isolation
4. **Component-level**: UI element visibility control

#### Role Hierarchy
```
SUPER_ADMIN (Platform oversight)
├── CLIENT (Event managers)
├── AFFILIATE (Transportation providers)
├── CLIENT_COORDINATOR (Client assistants)
├── PASSENGER (End customers)
├── AFFILIATE_DISPATCH (Affiliate operations)
└── DRIVER (Vehicle operators)
```

## Data Models

### Multi-Tenant Data Isolation

The platform implements comprehensive data isolation through:

#### Organization-based Partitioning
- All core entities include `organization_id` for tenant separation
- RLS policies enforce data access boundaries
- Cross-tenant data sharing only through explicit super admin actions

#### User-Organization Relationships
- Users can belong to multiple organizations with different roles
- Role assignments are organization-specific
- Session context maintains current organization scope

### Quote Workflow Data Model

The quote system supports two primary workflows:

#### Rate Request Flow
1. Client creates quote with service requirements
2. System identifies qualified affiliates based on:
   - Geographic coverage areas
   - Service availability
   - Rate card configurations
3. Affiliates submit competitive proposals
4. Client selects preferred offer

#### Fixed Offer Flow
1. Client sets fixed price for service
2. System broadcasts to qualified affiliates
3. Affiliates accept/reject or counter-offer
4. Client confirms selected affiliate

## Error Handling

### Comprehensive Error Management

#### Client-side Error Handling
- React Error Boundaries for component-level failures
- Toast notifications for user-facing errors
- Automatic retry mechanisms for transient failures
- Graceful degradation for offline scenarios

#### Server-side Error Handling
- Centralized error logging with context preservation
- Structured error responses with appropriate HTTP status codes
- Database transaction rollback on failures
- Rate limiting and abuse prevention

#### Authentication Error Recovery
- Automatic token refresh mechanisms
- Session restoration after network interruptions
- Graceful handling of expired sessions
- Multi-device session management

## Testing Strategy

### Current Testing Infrastructure

#### End-to-End Testing
- Playwright test suite for critical user journeys
- Multi-browser compatibility testing
- Mobile responsiveness validation

#### Integration Testing
- API endpoint testing with realistic data scenarios
- Database migration testing
- Authentication flow validation

#### Unit Testing
- Component testing with React Testing Library
- Utility function validation
- Business logic verification

### Recommended Testing Enhancements

#### Performance Testing
- Load testing for multi-tenant scenarios
- Database query optimization validation
- Real-time feature stress testing

#### Security Testing
- Penetration testing for multi-tenant isolation
- Authentication bypass attempt detection
- Data access boundary validation

#### Accessibility Testing
- WCAG compliance validation
- Screen reader compatibility
- Keyboard navigation testing

## Implementation Approach

### Phase 1: Architecture Documentation
- Complete technical architecture mapping
- Database schema documentation with relationships
- API endpoint inventory and documentation
- Security model documentation

### Phase 2: Gap Analysis
- Feature completeness assessment
- Performance bottleneck identification
- Security vulnerability assessment
- Code quality and technical debt analysis

### Phase 3: Optimization Planning
- Scalability improvement recommendations
- Performance optimization strategies
- Security enhancement proposals
- Code quality improvement roadmap

### Phase 4: Feature Completion
- Missing feature identification and prioritization
- Implementation specifications for remaining features
- Testing strategy for new components
- Deployment and rollout planning

## Technology Integration Points

### External Service Integrations

#### Mapping Services
- Integration points for route optimization
- Geographic coverage validation
- Real-time traffic data incorporation

#### Communication Services
- Email notification system (Resend integration)
- SMS messaging capabilities
- Real-time chat functionality

#### Payment Processing
- Secure payment method storage
- Transaction processing workflows
- Billing and invoicing automation

### Real-time Features

#### WebSocket Implementation
- Live quote updates and notifications
- Real-time trip tracking
- Instant messaging between stakeholders

#### Notification System
- Multi-channel notification delivery
- User preference management
- Notification history and acknowledgment tracking

This design provides the foundation for comprehensive platform analysis, gap identification, and optimization planning while maintaining the existing architectural strengths and addressing scalability requirements for global deployment.