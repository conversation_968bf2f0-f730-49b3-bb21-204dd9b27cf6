{"enabled": true, "name": "Scripts Database Operations Monitor", "description": "Monitors scripts directory for database operations compliance violations", "version": "1.0.0", "when": {"type": "fileEdited", "patterns": ["scripts/**/*.js", "scripts/**/*.ts", "scripts/**/*.mjs"]}, "then": {"type": "askAgent", "prompt": "🚨 **SCRIPTS DATABASE OPERATIONS COMPLIANCE CHECK** 🚨\n\nA script file has been modified. Please validate for database operations compliance:\n\n## ❌ PROHIBITED PATTERNS\n\n### Direct Database Operations (NEVER ALLOWED)\n```javascript\n// ❌ PROHIBITED:\nconst { createClient } = require('@supabase/supabase-js');\nconst supabase = createClient(...);\nsupabase.from('table').insert(...);\nsupabase.from('table').update(...);\nsupabase.from('table').delete(...);\nsupabase.from('table').upsert(...);\n```\n\n### Direct SQL Commands (NEVER ALLOWED)\n```bash\n# ❌ PROHIBITED:\npsql $DATABASE_URL -c \"INSERT INTO...\"\nexecuteBash('psql ...')\n```\n\n## ✅ COMPLIANT PATTERNS\n\n### For Database Reads\n```javascript\n// ✅ CORRECT:\nimport { mcp_access_db_query } from '@/tools';\nconst result = await mcp_access_db_query('SELECT * FROM table WHERE id = $1', [id]);\n```\n\n### For Database Changes\n```sql\n-- ✅ CORRECT: Create migration file\n-- supabase/migrations/XXX_description.sql\nBEGIN;\nINSERT INTO table (column) VALUES ('value');\nCOMMIT;\n```\n\n## 🔍 VALIDATION CHECKLIST\n\n- [ ] No `createClient` from '@supabase/supabase-js'\n- [ ] No `.from()`, `.insert()`, `.update()`, `.delete()`, `.upsert()` calls\n- [ ] No direct SQL execution commands\n- [ ] Uses `mcp_access_db_query` for reads only\n- [ ] Database changes documented in migration files\n- [ ] Script purpose is diagnostic/testing only\n\n## 🚨 COMPLIANCE REQUIREMENT\n\n**ALL database operations must follow the database operations standards:**\n- **Reads**: Use `mcp_access_db_query` tool only\n- **Writes**: Use migration files only\n- **No exceptions**: Scripts cannot make direct database changes\n\nPlease review this script and flag any compliance violations immediately."}}