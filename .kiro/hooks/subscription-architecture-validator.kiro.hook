{"enabled": true, "name": "Subscription Architecture Validator", "description": "Validates subscription plan architecture compliance when subscription or billing-related files are modified", "version": "1.0.0", "when": {"type": "fileEdited", "patterns": ["app/api/subscriptions/**/*.ts", "app/api/billing/**/*.ts", "app/lib/billing/**/*.ts", "app/lib/subscriptions/**/*.ts", "components/**/subscription/**/*.tsx", "components/**/billing/**/*.tsx", "app/contexts/SubscriptionContext.tsx", "app/hooks/useSubscription.ts", "app/hooks/useBilling.ts", "app/types/subscription.ts", "app/types/billing.ts", "supabase/migrations/**/*subscription*.sql", "supabase/migrations/**/*billing*.sql", "supabase/migrations/**/*plan*.sql"]}, "then": {"type": "askAgent", "prompt": "const GovernanceReminderGenerator = require('../lib/governance-reminder-generator.cjs');\nconst filePaths = context.changedFiles || ['subscription-related file'];\nconst reminder = GovernanceReminderGenerator.generateHookReminder('subscription-architecture-validator', filePaths);\nconsole.log(reminder);\n\n// Generate subscription architecture reminder using template system\nPlease analyze the modified file for subscription architecture compliance."}}