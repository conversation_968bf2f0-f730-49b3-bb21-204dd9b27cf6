{"enabled": true, "name": "Tenant ID Elimination Validator", "description": "Enforces the complete elimination of tenant_id usage - ensures only organization_id is used throughout the platform", "version": "1.0.0", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "app/lib/**/*.ts", "app/components/**/*.tsx", "app/(portals)/**/*.tsx", "lib/**/*.ts", "components/**/*.tsx", "supabase/migrations/**/*.sql", "app/types/**/*.ts", "app/hooks/**/*.ts", "app/utils/**/*.ts", "app/contexts/**/*.tsx", "scripts/**/*.js", "tests/**/*.ts", "**/*.prisma"]}, "then": {"type": "askAgent", "prompt": "🚫 **TENANT_ID ELIMINATION VALIDATION** 🚫\n\nA file has been modified. Please validate against the **COMPLETED tenant_id Elimination Plan** - NO tenant_id usage is allowed anywhere in the platform.\n\n## 🎯 **ELIMINATION STATUS: COMPLETE** ✅\n\n**Mission Accomplished**: tenant_id has been systematically eliminated from the entire platform. Only `organization_id` should be used.\n\n## 🔍 **CRITICAL VALIDATION CHECKLIST**\n\n### **1. NO tenant_id Usage (ZERO TOLERANCE)**\n- [ ] **NO** `tenant_id` column references in database queries\n- [ ] **NO** `tenant_id` parameters in API endpoints\n- [ ] **NO** `tenant_id` properties in TypeScript interfaces\n- [ ] **NO** `tenant_id` variables in JavaScript/TypeScript code\n- [ ] **NO** `tenant_id` fields in forms or UI components\n\n### **2. ONLY organization_id Usage (MANDATORY)**\n- [ ] All database queries use `organization_id` for multi-tenant isolation\n- [ ] All API endpoints use `organization_id` parameters\n- [ ] All TypeScript interfaces use `organization_id` properties\n- [ ] All RLS policies reference `organization_id` only\n- [ ] All foreign key constraints reference `organizations(id)`\n\n### **3. Database Schema Compliance**\n- [ ] **NO** `tenant_id` columns in any table\n- [ ] All foreign keys reference `organizations(id)` using `organization_id`\n- [ ] RLS policies use `organization_id` for isolation\n- [ ] Database functions use `organization_id` parameters\n\n### **4. API Endpoint Compliance**\n- [ ] Route parameters use `organizationId` or `orgId` (NOT `tenantId`)\n- [ ] Request/response schemas use `organization_id` fields\n- [ ] Query parameters use `organization_id` filtering\n- [ ] Authentication context uses `organization_id`\n\n### **5. UI Component Compliance**\n- [ ] Form fields use `organization_id` or `organizationId`\n- [ ] Component props use `organization_id` naming\n- [ ] State management uses `organization_id` keys\n- [ ] Context providers use `organization_id` values\n\n## ✅ **CORRECT PATTERNS (USE THESE)**\n\n```typescript\n// ✅ CORRECT: Database query with organization_id\nconst quotes = await mcp_access_db_query(\n  'SELECT * FROM quotes WHERE organization_id = $1',\n  [organizationId]\n);\n\n// ✅ CORRECT: API endpoint with organization_id\nexport async function GET(request: NextRequest, { params }: { params: { organizationId: string } }) {\n  const { user, organization } = await validateUserAccess(request);\n  \n  const data = await db.quotes.findMany({\n    where: { organization_id: params.organizationId }\n  });\n  \n  return NextResponse.json({ data });\n}\n\n// ✅ CORRECT: TypeScript interface with organization_id\ninterface Quote {\n  id: string;\n  organization_id: string;\n  pickup_location: string;\n  dropoff_location: string;\n}\n\n// ✅ CORRECT: React component with organization_id\ninterface QuoteListProps {\n  organizationId: string;\n  onQuoteSelect: (quoteId: string) => void;\n}\n\n// ✅ CORRECT: RLS policy with organization_id\nCREATE POLICY \"quotes_organization_isolation\" ON quotes\n  FOR ALL USING (\n    organization_id IN (\n      SELECT organization_id FROM user_profiles \n      WHERE user_id = auth.uid()\n    )\n  );\n```\n\n## 🚨 **VIOLATIONS TO PREVENT (NEVER USE THESE)**\n\n```typescript\n// ❌ WRONG: Using tenant_id in database query\nconst quotes = await db.query('SELECT * FROM quotes WHERE tenant_id = $1', [tenantId]);\n\n// ❌ WRONG: API endpoint with tenant_id parameter\nexport async function GET(request: NextRequest, { params }: { params: { tenantId: string } }) {\n\n// ❌ WRONG: TypeScript interface with tenant_id\ninterface Quote {\n  id: string;\n  tenant_id: string; // ❌ FORBIDDEN\n}\n\n// ❌ WRONG: Component props with tenant_id\ninterface Props {\n  tenantId: string; // ❌ FORBIDDEN\n}\n\n// ❌ WRONG: Database column with tenant_id\nALTER TABLE quotes ADD COLUMN tenant_id UUID; -- ❌ FORBIDDEN\n\n// ❌ WRONG: RLS policy with tenant_id\nCREATE POLICY \"policy_name\" ON table_name\n  FOR ALL USING (tenant_id = current_tenant_id()); -- ❌ FORBIDDEN\n```\n\n## 📊 **ELIMINATION PLAN STATUS**\n\n### **✅ COMPLETED PHASES**\n- **Database Schema**: 0 tenant_id columns in public schema ✅\n- **Core APIs**: All major endpoints use organization_id ✅\n- **Permission System**: Completely overhauled ✅\n- **UI Components**: All updated to organization_id ✅\n- **RLS Policies**: All use organization_id ✅\n\n### **🎯 CURRENT ENFORCEMENT**\n- **Zero Tolerance**: NO new tenant_id usage allowed\n- **Organization Only**: All new code must use organization_id\n- **Legacy Cleanup**: 202 remaining references in non-critical files (docs, tests, legacy)\n\n## 🔧 **MIGRATION GUIDANCE**\n\nIf you encounter legacy tenant_id usage, here's how to migrate:\n\n```typescript\n// BEFORE (tenant_id usage)\nconst user = await db.user.findFirst({\n  where: { tenant_id: tenantId }\n});\n\n// AFTER (organization_id usage)\nconst user = await db.user.findFirst({\n  where: { organization_id: organizationId }\n});\n\n// BEFORE (API route with tenant_id)\napp/api/tenants/[tenantId]/users/route.ts\n\n// AFTER (API route with organization_id)\napp/api/organizations/[organizationId]/users/route.ts\n```\n\n## 📋 **VALIDATION REQUIREMENTS**\n\n- [ ] File contains NO tenant_id references\n- [ ] All multi-tenant isolation uses organization_id\n- [ ] Database operations use organization_id filtering\n- [ ] API endpoints use organizationId parameters\n- [ ] TypeScript interfaces use organization_id properties\n- [ ] UI components use organization_id props\n- [ ] RLS policies reference organization_id\n- [ ] Foreign keys reference organizations(id)\n\n## 🎉 **SUCCESS METRICS**\n\n**The tenant_id elimination was a complete success:**\n- ✅ **Simplified Architecture**: Single identifier system\n- ✅ **Eliminated Confusion**: No more tenant_id vs organization_id\n- ✅ **Reduced Complexity**: Cleaner code throughout\n- ✅ **Better Performance**: Fewer joins and lookups\n- ✅ **Easier Maintenance**: Clear, consistent patterns\n\nPlease analyze the modified file to ensure it follows the tenant_id elimination standards and uses ONLY organization_id for multi-tenant operations."}}