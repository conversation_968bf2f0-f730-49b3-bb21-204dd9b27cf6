{"enabled": true, "name": "Permission Architecture Validator", "description": "Validates permission architecture compliance with enhanced SUPER_ADMIN control patterns and four-tier account architecture", "version": "2.0.0", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "app/lib/auth/**/*.ts", "components/**/permissions/**/*.tsx", "app/middleware.ts", "app/lib/permissions/**/*.ts", "app/contexts/**/*Context.tsx", "app/hooks/use*Permission*.ts", "app/utils/permissions/**/*.ts", "supabase/migrations/**/*.sql", "app/lib/database/**/*.ts", "app/api/**/route.ts"]}, "then": {"type": "askAgent", "prompt": "🔐 **PERMISSION ARCHITECTURE VALIDATION** 🔐\n\nA permission-related file has been modified. Please validate against **ENHANCED Permission Architecture** with Networks vs Organizations integration:\n\n## 🎯 **SUPER_ADMIN CONTROL ARCHITECTURE**\n\n### **1. Database Function SUPER_ADMIN Override Requirements**\n- [ ] All database functions include `is_super_admin_safe()` usage\n- [ ] SUPER_ADMIN bypass logic for all permission checks\n- [ ] Audit logging for SUPER_ADMIN override actions\n- [ ] Proper error handling with SUPER_ADMIN context\n\n### **2. Permission Template Integration**\n- [ ] Feature flags validation with SUPER_ADMIN override\n- [ ] Granular permissions respect template hierarchy\n- [ ] Permission inheritance for TNC customers\n- [ ] Subscription plan validation for enterprise features\n\n### **3. Four-Tier Account Architecture Compliance**\n- [ ] **TRANSFLOW_SUPER_ADMIN**: Ultimate platform control + network operations\n- [ ] **TNC_ACCOUNT**: Network coordination + customer portal management\n- [ ] **TNC_CUSTOMER**: Managed by parent TNC with inherited permissions\n- [ ] **DIRECT_CLIENT**: Independent TransFlow relationship using shared network\n\n### **4. Network Access Permission Validation (NEW)**\n- [ ] Network access permissions respect inheritance hierarchy\n- [ ] TNC customers inherit network permissions from parent TNC\n- [ ] Direct clients restricted to shared network permissions\n- [ ] Network switcher permissions follow validated visibility logic\n\n### **5. TNC Customer Portal Provisioning Patterns**\n- [ ] Hierarchy validation for TNC customer creation\n- [ ] Permission inheritance from parent TNC\n- [ ] Audit logging for TNC customer provisioning\n- [ ] Branding and feature inheritance validation\n\n## ✅ **CORRECT PERMISSION PATTERNS**\n\n```typescript\n// ✅ CORRECT: Network access permission validation\nconst validateNetworkAccess = async (userId: string, networkId: string) => {\n  const user = await getUser(userId);\n  \n  // SUPER_ADMIN override\n  if (user.role === 'SUPER_ADMIN') {\n    return true; // Full access\n  }\n  \n  const organization = await getOrganization(user.organization_id);\n  \n  if (organization.account_type === 'tnc_customer') {\n    // TNC customer: check inherited network access\n    const parentTNC = await getOrganization(organization.parent_tnc_id);\n    const inheritedNetworks = await getNetworksByOwner(parentTNC.id);\n    return inheritedNetworks.some(n => n.id === networkId);\n  }\n  \n  if (organization.account_type === 'direct_client') {\n    // Direct client: only shared networks\n    const sharedNetworks = await getSharedNetworks();\n    return sharedNetworks.some(n => n.id === networkId);\n  }\n  \n  // TNC account: own networks\n  const ownNetworks = await getNetworksByOwner(organization.id);\n  return ownNetworks.some(n => n.id === networkId);\n};\n\n// ✅ CORRECT: TNC customer permission inheritance\nconst getTNCCustomerPermissions = async (customerOrgId: string) => {\n  const customer = await getOrganization(customerOrgId);\n  if (customer.account_type !== 'tnc_customer') {\n    throw new Error('Organization is not a TNC customer');\n  }\n  \n  const parentTNC = await getOrganization(customer.parent_tnc_id);\n  \n  return {\n    inherited_permissions: parentTNC.permission_template,\n    inherited_features: parentTNC.feature_flags,\n    inherited_subscription: parentTNC.subscription_plan,\n    network_access: await getNetworksByOwner(parentTNC.id)\n  };\n};\n\n// ✅ CORRECT: Database function with SUPER_ADMIN override\nCREATE OR REPLACE FUNCTION validate_network_access(\n  p_user_id UUID,\n  p_network_id UUID\n) RETURNS BOOLEAN\nLANGUAGE plpgsql\nSECURITY DEFINER\nAS $$\nDECLARE\n  is_super_admin BOOLEAN;\n  user_org_id UUID;\n  org_account_type VARCHAR(50);\n  parent_tnc_id UUID;\nBEGIN\n  -- Check if user is SUPER_ADMIN\n  is_super_admin := is_super_admin_safe();\n  \n  IF is_super_admin THEN\n    -- SUPER_ADMIN override - log the action\n    INSERT INTO audit_logs (action, user_id, details, created_at) \n    VALUES ('super_admin_network_access', p_user_id, \n            jsonb_build_object('network_id', p_network_id, 'reason', 'super_admin_override'),\n            NOW());\n    RETURN TRUE;\n  END IF;\n  \n  -- Get user organization\n  SELECT organization_id INTO user_org_id\n  FROM user_profiles WHERE user_id = p_user_id;\n  \n  -- Get organization account type\n  SELECT account_type, parent_tnc_id INTO org_account_type, parent_tnc_id\n  FROM organizations WHERE id = user_org_id;\n  \n  -- Validate network access based on account type\n  IF org_account_type = 'tnc_customer' THEN\n    -- Check inherited network access\n    RETURN EXISTS (\n      SELECT 1 FROM networks \n      WHERE id = p_network_id \n      AND owner_organization_id = parent_tnc_id\n    );\n  ELSIF org_account_type = 'direct_client' THEN\n    -- Check shared network access\n    RETURN EXISTS (\n      SELECT 1 FROM networks \n      WHERE id = p_network_id \n      AND network_type = 'shared'\n    );\n  ELSE\n    -- TNC account: check own networks\n    RETURN EXISTS (\n      SELECT 1 FROM networks \n      WHERE id = p_network_id \n      AND owner_organization_id = user_org_id\n    );\n  END IF;\nEND;\n$$;\n```\n\n## 🚨 **PERMISSION VIOLATIONS TO AVOID**\n\n❌ **WRONG**: Permission checks without SUPER_ADMIN override\n❌ **WRONG**: TNC customers having permissions parent TNC doesn't have\n❌ **WRONG**: Network access without inheritance validation\n❌ **WRONG**: Missing audit logging for SUPER_ADMIN actions\n❌ **WRONG**: Direct clients accessing TNC-specific networks\n\n## 📋 **VALIDATION CHECKLIST**\n\n- [ ] SUPER_ADMIN override in all permission functions\n- [ ] Permission template integration with inheritance\n- [ ] Network access permission validation\n- [ ] TNC customer permission inheritance\n- [ ] Four-tier account architecture compliance\n- [ ] Audit logging for administrative actions\n- [ ] Feature flag validation with inheritance\n- [ ] Subscription plan permission enforcement\n\nPlease analyze the modified file for complete permission architecture compliance."}}