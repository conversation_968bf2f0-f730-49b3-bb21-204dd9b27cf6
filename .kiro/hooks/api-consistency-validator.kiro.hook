{"enabled": true, "name": "API Consistency Validator", "description": "Maintains code quality by ensuring API consistency, tRPC patterns, and response formats", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "lib/trpc/**/*.ts", "app/server/**/*.ts"]}, "then": {"type": "askAgent", "prompt": "🔧 **CRITICAL API CONSISTENCY VALIDATION** 🔧\n\nAn API file has been saved. I need to perform comprehensive API consistency analysis:\n\n## 1. **tRPC Procedure Standards**\nCheck for proper implementation of:\n- [ ] Consistent response patterns (`{ success, data, message }`)\n- [ ] Proper error handling with TRPCError\n- [ ] Zod schema input validation\n- [ ] Authentication context usage\n- [ ] Organization-scoped queries\n\n## 2. **Response Format Consistency**\nVerify standardized responses:\n```typescript\n// ✅ CORRECT:\n// return { success: true, data: result, message: \"Success\" };\n// throw new TRPCError({ code: 'BAD_REQUEST', message: 'Error' });\n\n// ❌ WRONG:\n// return result; // Missing wrapper\n// throw new Error('message'); // Not TRPCError\n```\n\n## 3. **Input Validation Standards**\nCheck for:\n- [ ] Zod schemas for all inputs\n- [ ] UUID validation for IDs\n- [ ] Required field validation\n- [ ] Business rule validation\n- [ ] Sanitization of user inputs\n\n## 4. **Authentication Context Validation**\nVerify proper use of:\n- [ ] `protectedProcedure` for authenticated routes\n- [ ] `validateUserAccess()` context\n- [ ] Role-based access control\n- [ ] Organization context validation\n\n## 5. **Database Query Standards**\nCheck for:\n- [ ] Organization-scoped queries\n- [ ] Proper includes/selects (avoid N+1)\n- [ ] Pagination implementation\n- [ ] Query optimization\n- [ ] Transaction usage where needed\n\n## 6. **Error Handling Standards**\nValidate:\n- [ ] Standardized error codes\n- [ ] Proper error messages\n- [ ] Error logging\n- [ ] Client-safe error responses\n- [ ] Validation error handling\n\n## 7. **REST API Compatibility (Legacy)**\nFor REST endpoints, check:\n- [ ] RESTful naming conventions\n- [ ] Consistent response structure\n- [ ] Proper HTTP status codes\n- [ ] Organization-scoped routes\n\n## 8. **Performance Standards**\nVerify:\n- [ ] Response time considerations\n- [ ] Caching implementation\n- [ ] Query optimization\n- [ ] Pagination for large datasets\n- [ ] Background job usage for heavy operations\n\n## 9. **Critical Violations to Flag**\n```typescript\n// ❌ API CONSISTENCY VIOLATIONS:\n// - Inconsistent response formats\n// - Missing input validation\n// - No authentication checks\n// - Direct database access without organization filtering\n// - Poor error handling\n// - Missing TypeScript types\n```\n\n## 10. **Required Patterns**\n```typescript\n// ✅ CORRECT API PATTERNS:\n// - export const procedure = protectedProcedure.input(schema).mutation()\n// - const { user, organization } = ctx;\n// - return { success: true, data: result };\n// - throw new TRPCError({ code: 'FORBIDDEN', message: 'Access denied' });\n```\n\n## 11. **Testing Requirements**\nCheck for consideration of:\n- [ ] Input validation tests\n- [ ] Authentication tests\n- [ ] Business logic tests\n- [ ] Error handling tests\n- [ ] Multi-tenant isolation tests\n\n## 12. **Action Required**\nIf violations found:\n- List specific API consistency issues\n- Provide corrected implementation examples\n- Reference API consistency standards\n- Suggest improvements for maintainability\n\nIf compliant:\n- Confirm API follows established patterns\n- Highlight good practices implemented\n\nPlease analyze the saved API file thoroughly for consistency compliance."}}