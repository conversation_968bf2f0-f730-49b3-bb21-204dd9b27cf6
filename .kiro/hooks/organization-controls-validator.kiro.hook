{"enabled": true, "name": "Organization Controls Validator", "description": "Validates organization controls compliance when organization-related files are modified", "version": "1.0.0", "when": {"type": "fileEdited", "patterns": ["app/api/organizations/**/*.ts", "app/contexts/OrganizationContext.tsx", "app/lib/organization/**/*.ts", "components/**/organization/**/*.tsx", "app/types/organization.ts", "app/hooks/useOrganization.ts", "app/utils/organization/**/*.ts", "supabase/migrations/**/*organization*.sql", "supabase/migrations/**/*tenant*.sql", "supabase/migrations/**/*white*label*.sql"]}, "then": {"type": "askAgent", "prompt": "🏢 **ORGANIZATION CONTROLS VALIDATION** 🏢\n\nAn organization-related file has been modified. Please validate against **ENHANCED Organization Controls** with Networks vs Organizations architecture:\n\n## 🎯 **ORGA<PERSON>ZATION VS NETWORK DISTINCTION**\n\n### **ORGANIZATIONS** (Business Entities)\n- ✅ **Metro Ride Network** - TNC Account organization that provides transportation network\n- ✅ **Marriott International** - TNC Account organization that provides premium network\n- ✅ **Metro Boston Downtown Office** - TNC Customer that uses Metro Ride's network\n\n### **NETWORKS** (Service Offerings)\n- ✅ **TransFlow Shared Network** - Platform-wide network for direct clients\n- ✅ **Metro Ride Transportation Network** - Service provided by Metro Ride Network org\n- ✅ **Marriott Premium Transportation Network** - Service provided by Marriott International org\n\n## 🔍 **ORGANIZATION CONTROLS VALIDATION**\n\n### **1. Organization Type Architecture**\n- [ ] **shared**: Basic SaaS model, shared affiliate network, common pricing\n- [ ] **segregated**: Custom branding, isolated customer bases, separate networks\n- [ ] **isolated**: Complete data isolation, enterprise-level separation\n\n### **2. Four-Tier Account Architecture**\n- [ ] **TRANSFLOW_SUPER_ADMIN**: Ultimate platform control + network operations\n- [ ] **TNC_ACCOUNT**: Network coordination + customer portal management\n- [ ] **TNC_CUSTOMER**: Managed by parent TNC with inherited permissions\n- [ ] **DIRECT_CLIENT**: Independent TransFlow relationship using shared network\n\n### **3. Network Ownership Validation (NEW)**\n- [ ] Organizations OWN/PROVIDE networks (not the other way around)\n- [ ] TNC accounts can own multiple networks (rare, geographic cases)\n- [ ] TNC customers inherit network access from parent TNC\n- [ ] Direct clients locked to shared network only\n\n### **4. TNC Customer Hierarchy Validation**\n- [ ] `parent_tnc_id` properly references TNC account\n- [ ] `account_type = 'tnc_customer'` for all TNC customers\n- [ ] `managed_by = 'tnc'` (not 'transflow') for TNC customers\n- [ ] Inheritance patterns for branding, features, permissions\n\n### **5. White-Label Feature Integration**\n- [ ] `has_white_labeling`: Custom branding and UI customization\n- [ ] `has_custom_domain`: Custom domain support\n- [ ] `has_custom_branding`: Logo, colors, theme customization\n- [ ] White-label features work with any organization type\n\n## ✅ **CORRECT ORGANIZATION PATTERNS**\n\n```typescript\n// ✅ CORRECT: Organization type-specific behavior\nconst getOrganizationNetworkAccess = async (organizationId: string) => {\n  const organization = await getOrganization(organizationId);\n  \n  switch (organization.account_type) {\n    case 'tnc_account':\n      // TNC account owns networks\n      return await getNetworksByOwner(organizationId);\n      \n    case 'tnc_customer':\n      // TNC customer inherits from parent\n      return await getInheritedNetworks(organization.parent_tnc_id);\n      \n    case 'direct_client':\n      // Direct client uses shared network\n      return await getSharedNetworks();\n      \n    default:\n      throw new Error('Invalid account type');\n  }\n};\n\n// ✅ CORRECT: TNC customer creation with validation\nconst createTNCCustomer = async (parentTNCId: string, customerData: CreateCustomerInput) => {\n  const parentTNC = await getOrganization(parentTNCId);\n  \n  if (parentTNC.account_type !== 'tnc_account') {\n    throw new Error('Parent must be a TNC account');\n  }\n  \n  const customer = await createOrganization({\n    ...customerData,\n    account_type: 'tnc_customer',\n    parent_tnc_id: parentTNCId,\n    managed_by: 'tnc',\n    // Inherit from parent TNC\n    branding_config: parentTNC.branding_config,\n    feature_flags: parentTNC.feature_flags,\n    permission_template: parentTNC.permission_template\n  });\n  \n  return customer;\n};\n\n// ✅ CORRECT: Organization-network relationship validation\nconst validateOrganizationNetworkRelationship = async (orgId: string, networkId: string) => {\n  const organization = await getOrganization(orgId);\n  const network = await getNetwork(networkId);\n  \n  if (organization.account_type === 'tnc_customer') {\n    // TNC customer must use inherited networks\n    const parentNetworks = await getNetworksByOwner(organization.parent_tnc_id);\n    if (!parentNetworks.some(n => n.id === networkId)) {\n      throw new Error('TNC customer cannot access non-inherited network');\n    }\n  } else if (organization.account_type === 'direct_client') {\n    // Direct client must use shared networks\n    if (network.network_type !== 'shared') {\n      throw new Error('Direct client can only access shared networks');\n    }\n  } else if (organization.account_type === 'tnc_account') {\n    // TNC account can access own networks\n    if (network.owner_organization_id !== orgId) {\n      throw new Error('TNC account can only access owned networks');\n    }\n  }\n};\n```\n\n## 🚨 **ORGANIZATION VIOLATIONS TO AVOID**\n\n❌ **WRONG**: Treating Metro Ride Network as a network (it's an organization)\n❌ **WRONG**: TNC customers owning their own networks\n❌ **WRONG**: Direct clients accessing TNC-specific networks\n❌ **WRONG**: Missing parent_tnc_id validation for TNC customers\n❌ **WRONG**: Confusing organization management with network management\n\n## 📊 **DATABASE VALIDATION PATTERNS**\n\n```sql\n-- ✅ CORRECT: Organization hierarchy validation\nSELECT \n  o.name as organization_name,\n  o.account_type,\n  o.parent_tnc_id,\n  parent.name as parent_name,\n  COUNT(n.id) as owned_networks\nFROM organizations o\nLEFT JOIN organizations parent ON parent.id = o.parent_tnc_id\nLEFT JOIN networks n ON n.owner_organization_id = o.id\nGROUP BY o.id, o.name, o.account_type, o.parent_tnc_id, parent.name;\n\n-- ✅ CORRECT: Network ownership constraints\nALTER TABLE networks \nADD CONSTRAINT networks_owner_must_be_tnc_account \nCHECK (\n  owner_organization_id IN (\n    SELECT id FROM organizations \n    WHERE account_type IN ('tnc_account', 'super_admin')\n  )\n);\n\n-- ✅ CORRECT: TNC customer hierarchy constraints\nALTER TABLE organizations\nADD CONSTRAINT tnc_customer_must_have_parent\nCHECK (\n  (account_type = 'tnc_customer' AND parent_tnc_id IS NOT NULL)\n  OR \n  (account_type != 'tnc_customer')\n);\n```\n\n## 📋 **VALIDATION CHECKLIST**\n\n- [ ] Organization vs network distinction maintained\n- [ ] Four-tier account architecture compliance\n- [ ] Network ownership relationships validated\n- [ ] TNC customer hierarchy properly enforced\n- [ ] White-label features properly integrated\n- [ ] Organization type behavior correctly implemented\n- [ ] Database constraints enforce relationships\n- [ ] Inheritance patterns properly implemented\n\nPlease analyze the modified file for complete organization controls compliance."}}