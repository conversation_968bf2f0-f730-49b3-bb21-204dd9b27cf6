{"enabled": true, "name": "Transportation Logic Validator", "description": "Validates transportation business logic, quote workflows, and industry-specific patterns", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "lib/**/*.ts", "app/components/**/*.tsx"]}, "then": {"type": "askAgent", "prompt": "🚛 **TRANSPORTATION LOGIC VALIDATION** 🚛\n\nA transportation-related file has been modified. Please validate business logic:\n\n## 1. **Quote Business Rules**\n- [ ] Pickup time must be in the future\n- [ ] Pickup and dropoff locations are different\n- [ ] Passenger count is between 1 and 50\n- [ ] Organization subscription limits are respected\n- [ ] Service area boundaries are validated\n\n## 2. **Quote Lifecycle States**\n```typescript\n// ✅ CORRECT: Valid state transitions\nconst QUOTE_STATES = {\n  DRAFT: 'draft',\n  PENDING: 'pending', \n  RESPONDED: 'responded',\n  ACCEPTED: 'accepted',\n  REJECTED: 'rejected',\n  EXPIRED: 'expired',\n  CANCELLED: 'cancelled'\n} as const;\n\n// Valid transitions:\n// draft → pending → responded → accepted/rejected\n```\n\n## 3. **Rate Calculation Logic**\n- [ ] Base rate calculation\n- [ ] Distance-based pricing\n- [ ] Time-based surcharges (night, weekend)\n- [ ] Passenger count adjustments\n- [ ] Minimum fare enforcement\n\n## 4. **Vehicle Assignment Rules**\n- [ ] Vehicle capacity >= passenger count\n- [ ] Vehicle availability at pickup time\n- [ ] Geographic coverage validation\n- [ ] Special requirements compatibility\n\n## 5. **Affiliate Response Validation**\n- [ ] Response submitted before quote expiration\n- [ ] Quoted rate within acceptable range\n- [ ] Vehicle assignment is valid\n- [ ] Affiliate is authorized for organization network\n\n## 6. **Multi-Tenant Business Logic**\n- [ ] Organization type affects data sharing\n- [ ] Subscription-based feature gates\n- [ ] Network participation rules\n- [ ] Branding and customization logic\n\n## 7. **Time Zone Handling**\n- [ ] Timezone-aware scheduling\n- [ ] Business hours validation\n- [ ] Lead time requirements (minimum 2 hours)\n- [ ] Cross-timezone coordination\n\n## 8. **Critical Validations**\n```typescript\n// ✅ CORRECT: Comprehensive validation\nconst validateQuoteRules = (input: CreateQuoteInput) => {\n  if (input.pickup_datetime <= new Date()) {\n    throw new Error('Pickup time must be in the future');\n  }\n  \n  if (input.pickup_location === input.dropoff_location) {\n    throw new Error('Pickup and dropoff locations cannot be the same');\n  }\n  \n  if (!organization.canCreateQuotes()) {\n    throw new Error('Organization has reached quote limit');\n  }\n};\n```\n\nPlease analyze the file for transportation business logic compliance."}}