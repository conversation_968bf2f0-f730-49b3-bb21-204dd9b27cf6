{"enabled": true, "name": "Database Standards Validator", "description": "Validates database operations against established standards and patterns", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "lib/**/*.ts", "supabase/migrations/**/*.sql"]}, "then": {"type": "askAgent", "prompt": "📊 **DATABASE STANDARDS VALIDATION** 📊\n\nA database file has been modified. Please validate against standards:\n\n## 1. **Mandatory Standards**\n- [ ] All queries use `mcp_access_db_query` tool\n- [ ] Schema changes go through migration files only\n- [ ] Organization-scoped queries include `organization_id`\n- [ ] RLS policies are properly implemented\n\n## 2. **Migration Standards**\n- [ ] Proper transaction usage (BEGIN/COMMIT)\n- [ ] Rollback instructions in comments\n- [ ] Sequential numbering (XXX_descriptive_name.sql)\n- [ ] Proper error handling\n\n## 3. **Query Patterns**\n```typescript\n// ✅ CORRECT:\nconst result = await mcp_access_db_query(\n  'SELECT * FROM quotes WHERE organization_id = $1',\n  [organizationId]\n);\n\n// ❌ WRONG:\nconst result = await db.query('SELECT * FROM quotes');\n```\n\n## 4. **Security Validation**\n- [ ] No SQL injection vulnerabilities\n- [ ] Parameterized queries used\n- [ ] Proper input sanitization\n- [ ] Access control validation\n\nPlease check the file for database standards compliance."}}