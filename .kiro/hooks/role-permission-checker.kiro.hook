{"enabled": true, "name": "Role Permission Checker", "description": "Validates role-based permission implementations including TNC customer management (Task 22 compliant)", "version": "1.0.0", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "app/lib/auth/**/*.ts", "app/middleware.ts", "app/lib/permissions/**/*.ts", "app/contexts/**/*Context.tsx", "app/hooks/use*Role*.ts", "app/hooks/use*Permission*.ts", "app/utils/role-validation/**/*.ts", "app/utils/permissions/**/*.ts", "components/**/permissions/**/*.tsx", "components/**/auth/**/*.tsx"]}, "then": {"type": "askAgent", "prompt": "const GovernanceReminderGenerator = require('../lib/governance-reminder-generator.cjs');\nconst filePaths = context.changedFiles || ['role-permission-related file'];\nconst reminder = GovernanceReminderGenerator.generateHookReminder('role-permission-checker', filePaths);\nconsole.log(reminder);\n\n// Generate role permission checker reminder using template system\nPlease analyze the modified file for role-based permission compliance including:\n- Permission enforcement in API routes\n- Role validation patterns\n- TNC customer management permissions\n- Portal access control (TNCs access customer tenancy but not platform tenancy)\n- Customer portal provisioning permissions\n- Network access inheritance for TNC customers"}}