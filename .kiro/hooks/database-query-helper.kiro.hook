{"enabled": true, "name": "Database Query Helper", "description": "Provides guidance on proper database query patterns and optimization", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "lib/**/*.ts", "supabase/migrations/**/*.sql"]}, "then": {"type": "askAgent", "prompt": "🗄️ **DATABASE QUERY ASSISTANCE** 🗄️\n\nA database-related file has been modified. Please provide guidance:\n\n## 1. **Query Optimization**\n- [ ] Proper indexing strategy\n- [ ] Efficient JOIN operations\n- [ ] Pagination implementation\n- [ ] Query performance analysis\n\n## 2. **Required Patterns**\n```sql\n-- ✅ CORRECT: Organization-scoped query\nSELECT * FROM quotes WHERE organization_id = $1;\n\n-- ❌ WRONG: Missing organization filter\nSELECT * FROM quotes WHERE user_id = $1;\n```\n\n## 3. **Best Practices**\n- [ ] Use `mcp_access_db_query` for read operations\n- [ ] Include proper error handling\n- [ ] Implement connection pooling\n- [ ] Use prepared statements\n\n## 4. **Migration Guidelines**\n- [ ] Include rollback instructions\n- [ ] Use transactions (BEGIN/COMMIT)\n- [ ] Add proper indexes\n- [ ] Update RLS policies\n\nPlease analyze the database operations and suggest optimizations."}}