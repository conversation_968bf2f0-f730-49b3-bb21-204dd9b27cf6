{"enabled": true, "name": "Integration Test Runner", "description": "Suggests and validates integration tests for modified components", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "app/components/**/*.tsx", "lib/**/*.ts"]}, "then": {"type": "askAgent", "prompt": "🧪 **INTEGRATION TEST VALIDATION** 🧪\n\nA file has been modified. Please suggest integration tests:\n\n## 1. **API Route Testing**\n- [ ] Authentication/authorization tests\n- [ ] Input validation tests\n- [ ] Business logic tests\n- [ ] Error handling tests\n- [ ] Multi-tenant isolation tests\n\n## 2. **Component Testing**\n- [ ] User interaction tests\n- [ ] State management tests\n- [ ] Props validation tests\n- [ ] Accessibility tests\n- [ ] Responsive design tests\n\n## 3. **Test Patterns**\n```typescript\n// ✅ API Route Test Example\ndescribe('Quote API', () => {\n  it('should enforce organization isolation', async () => {\n    const org1Quote = await createTestQuote({ organization_id: 'org1' });\n    const org2User = await createTestUser({ organization_id: 'org2' });\n    \n    const response = await request(app)\n      .get(`/api/quotes/${org1Quote.id}`)\n      .set('Authorization', `Bearer ${org2User.token}`);\n    \n    expect(response.status).toBe(403);\n  });\n});\n\n// ✅ Component Test Example\ndescribe('QuoteForm', () => {\n  it('should validate required fields', async () => {\n    render(<QuoteForm />);\n    \n    fireEvent.click(screen.getByText('Submit'));\n    \n    expect(screen.getByText('Pickup location is required')).toBeInTheDocument();\n  });\n});\n```\n\n## 4. **Test Coverage Areas**\n- [ ] Happy path scenarios\n- [ ] Edge cases and error conditions\n- [ ] Permission boundary tests\n- [ ] Data validation tests\n- [ ] Performance regression tests\n\n## 5. **Integration Points**\n- [ ] Database operations\n- [ ] External API calls\n- [ ] Authentication flows\n- [ ] Real-time updates\n- [ ] File uploads/downloads\n\nPlease suggest specific integration tests for the modified file."}}