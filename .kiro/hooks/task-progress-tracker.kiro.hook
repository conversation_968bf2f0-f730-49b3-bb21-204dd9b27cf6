{"enabled": true, "name": "Task Progress Tracker", "description": "Tracks progress on platform analysis tasks and provides implementation guidance", "version": "1", "when": {"type": "userTriggered", "patterns": [".kiro/specs/**/*.md", "docs/**/*.md", "app/**/*.ts", "app/**/*.tsx"]}, "then": {"type": "askAgent", "prompt": "📋 **TASK PROGRESS TRACKING** 📋\n\nA task-related file has been modified. Please provide progress guidance:\n\n## 1. **Current Task Context**\n- [ ] Identify which platform analysis task is being worked on\n- [ ] Check task dependencies and prerequisites\n- [ ] Validate task completion criteria\n- [ ] Reference related documentation\n\n## 2. **Implementation Standards**\n- [ ] Follow task execution standards\n- [ ] Use database operations standards (mcp_access_db_query)\n- [ ] Maintain multi-tenant isolation\n- [ ] Follow architectural compliance patterns\n\n## 3. **Progress Validation**\n- [ ] Task requirements are being met\n- [ ] Implementation follows established patterns\n- [ ] Documentation is being updated\n- [ ] Testing considerations are addressed\n\n## 4. **Task Completion Checklist**\n```markdown\n## Task Status: [IN_PROGRESS/COMPLETED]\n\n### Requirements Met:\n- [ ] Requirement 1\n- [ ] Requirement 2\n- [ ] Requirement 3\n\n### Implementation Notes:\n- Implementation approach\n- Key decisions made\n- Challenges encountered\n\n### Next Steps:\n- [ ] Next action item\n- [ ] Dependencies to resolve\n- [ ] Testing required\n```\n\n## 5. **Quality Assurance**\n- [ ] Code follows platform standards\n- [ ] Documentation is comprehensive\n- [ ] Testing strategy is defined\n- [ ] Performance impact considered\n\n## 6. **Reference Materials**\n- [ ] Check `.kiro/specs/platform-analysis/` for requirements\n- [ ] Reference `docs/reference/` for context\n- [ ] Follow steering guidelines in `.kiro/steering/`\n- [ ] Use established patterns from existing code\n\nPlease analyze the current task progress and provide specific guidance for completion."}}