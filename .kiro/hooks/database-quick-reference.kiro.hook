{"enabled": true, "name": "Database Quick Reference", "description": "Provides quick reference for database operations and common patterns", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "lib/**/*.ts", "supabase/migrations/**/*.sql"]}, "then": {"type": "askAgent", "prompt": "📚 **DAT<PERSON><PERSON><PERSON> QUICK REFERENCE** 📚\n\nHere's a quick reference for database operations:\n\n## 1. **Query Operations**\n```typescript\n// Read operations - Use MCP tool\nconst quotes = await mcp_access_db_query(\n  'SELECT * FROM quotes WHERE organization_id = $1',\n  [orgId]\n);\n\n// Write operations - Use migration files\n// Create: supabase/migrations/XXX_add_feature.sql\n```\n\n## 2. **Common Patterns**\n```sql\n-- Organization-scoped query\nSELECT * FROM table_name WHERE organization_id = $1;\n\n-- With pagination\nSELECT * FROM table_name \nWHERE organization_id = $1 \nORDER BY created_at DESC \nLIMIT $2 OFFSET $3;\n\n-- With joins\nSELECT q.*, o.name as org_name \nFROM quotes q \nJOIN organizations o ON q.organization_id = o.id \nWHERE q.organization_id = $1;\n```\n\n## 3. **Migration Template**\n```sql\n-- Migration: XXX_descriptive_name.sql\n-- Purpose: Brief description\n-- Date: YYYY-MM-DD\n\nBEGIN;\n\n-- Your changes here\n\nCOMMIT;\n\n-- Rollback instructions:\n-- [rollback SQL]\n```\n\n## 4. **RLS Policy Template**\n```sql\nCREATE POLICY \"table_organization_isolation\" ON table_name\n  FOR ALL USING (\n    organization_id IN (\n      SELECT organization_id FROM user_profiles \n      WHERE user_id = auth.uid()\n    )\n  );\n```\n\nReference the database operations standards for more details."}}