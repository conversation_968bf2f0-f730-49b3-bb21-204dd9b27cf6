{"enabled": true, "name": "Migration Dependency Checker", "description": "Checks migration dependencies and ensures proper sequencing", "version": "1", "when": {"type": "fileEdited", "patterns": ["supabase/migrations/**/*.sql"]}, "then": {"type": "askAgent", "prompt": "🔗 **MIGRATION DEPENDENCY CHECK** 🔗\n\nA migration file has been modified. Please check dependencies:\n\n## 1. **Dependency Analysis**\n- [ ] Check if migration depends on previous migrations\n- [ ] Verify table/column references exist\n- [ ] Validate foreign key dependencies\n- [ ] Check for circular dependencies\n\n## 2. **Sequencing Validation**\n- [ ] Migration number is sequential\n- [ ] No gaps in migration sequence\n- [ ] Dependencies are satisfied by earlier migrations\n- [ ] No conflicts with existing migrations\n\n## 3. **Reference Validation**\n```sql\n-- ✅ CORRECT: Reference existing table\nALTER TABLE quotes ADD COLUMN organization_id UUID \nREFERENCES organizations(id);\n\n-- ❌ WRONG: Reference non-existent table\nALTER TABLE quotes ADD COLUMN missing_ref_id UUID \nREFERENCES non_existent_table(id);\n```\n\n## 4. **Common Dependencies**\n- [ ] `organizations` table exists before referencing\n- [ ] `user_profiles` table exists for RLS policies\n- [ ] Required functions are created first\n- [ ] Indexes are created after tables\n\n## 5. **Safety Checks**\n- [ ] Migration can be rolled back safely\n- [ ] No destructive operations without backups\n- [ ] Data migration preserves integrity\n- [ ] Performance impact is acceptable\n\nPlease analyze the migration dependencies and suggest any required changes."}}