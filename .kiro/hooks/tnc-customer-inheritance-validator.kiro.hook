{"enabled": true, "name": "TNC Customer Inheritance Validator", "description": "Validates TNC customer inheritance patterns - ensures customers inherit network access, branding, and features from parent TNC", "version": "1.0.0", "when": {"type": "fileEdited", "patterns": ["app/api/**/tnc-customer/**/*.ts", "app/api/**/customer/**/*.ts", "app/components/**/customer/**/*.tsx", "app/components/**/tnc/**/*.tsx", "app/lib/tnc/**/*.ts", "app/lib/customer/**/*.ts", "app/lib/inheritance/**/*.ts", "app/lib/branding/**/*.ts", "app/hooks/useTNC*.ts", "app/hooks/useCustomer*.ts", "app/hooks/useInheritance*.ts", "app/utils/tnc-branding-inheritance.ts", "lib/utils/tnc-branding-inheritance.ts", "app/contexts/TNCContext.tsx", "app/contexts/CustomerContext.tsx", "supabase/migrations/**/*tnc*customer*.sql", "supabase/migrations/**/*inheritance*.sql", "supabase/migrations/**/*branding*.sql", "scripts/**/*tnc*customer*.js", "scripts/**/*inheritance*.js"]}, "then": {"type": "askAgent", "prompt": "🏢 **TNC CUSTOMER INHERITANCE VALIDATION** 🏢\n\nA TNC customer or inheritance-related file has been modified. Please validate against the **VALIDATED TNC Customer Inheritance Architecture**:\n\n## 🎯 **TNC CUSTOMER INHERITANCE PRINCIPLES**\n\n### **Complete Inheritance Model**\n```\n🏢 Marriott International (TNC_ACCOUNT)\n├── Role: TNC_ADMIN\n├── Portal: /super-admin (stripped version)\n├── Manages: Marriott Premium Transportation Network\n└── Creates TNC Customers:\n    ├── 🏨 Ritz-Carlton Boston (TNC_CUSTOMER)\n    │   ├── Role: CLIENT/CLIENT_COORDINATOR  \n    │   ├── Portal: /event-manager (Marriott-branded)\n    │   ├── Service Tier: \"luxury\" (inherited)\n    │   └── Network: Marriott Premium Transportation Network\n    ├── 🏨 Marriott Downtown Boston (TNC_CUSTOMER)\n    │   ├── Role: CLIENT/CLIENT_COORDINATOR\n    │   ├── Portal: /event-manager (Marriott-branded)\n    │   ├── Service Tier: \"business\" (inherited)\n    │   └── Network: Marriott Premium Transportation Network\n    └── 🏨 Courtyard Boston Airport (TNC_CUSTOMER)\n        ├── Role: CLIENT/CLIENT_COORDINATOR\n        ├── Portal: /event-manager (Marriott-branded)\n        ├── Service Tier: \"express\" (inherited)\n        └── Network: Marriott Premium Transportation Network\n```\n\n## 🔍 **INHERITANCE VALIDATION CHECKLIST**\n\n### **1. Network Access Inheritance (CRITICAL)**\n- [ ] TNC customers MUST inherit network access from parent TNC\n- [ ] TNC customers CANNOT choose their own networks\n- [ ] Network access is determined by `parent_tnc_id` relationship\n- [ ] Direct clients use shared network, TNC customers use parent's network\n\n### **2. Branding Inheritance (CRITICAL)**\n- [ ] TNC customers inherit branding from parent TNC (NOT TransFlow)\n- [ ] Portal shows parent TNC logo, colors, and theme\n- [ ] Email templates use parent TNC branding\n- [ ] All customer-facing materials show parent TNC identity\n\n### **3. Feature Flag Inheritance**\n- [ ] TNC customers inherit feature flags from parent TNC\n- [ ] Cannot enable features not available to parent TNC\n- [ ] Feature access controlled by parent TNC configuration\n- [ ] Subscription limits inherited from parent TNC\n\n### **4. Permission Inheritance**\n- [ ] TNC customers inherit permission template from parent TNC\n- [ ] Cannot exceed parent TNC's permission level\n- [ ] Role assignments respect parent TNC constraints\n- [ ] Portal access determined by parent TNC configuration\n\n### **5. Service Tier Inheritance (POST-MVP)**\n- [ ] Service tier assigned by parent TNC (luxury/business/express)\n- [ ] Rate cards determined by parent TNC configuration\n- [ ] Vehicle class access based on inherited service tier\n- [ ] Affiliate network filtered by service tier requirements\n\n## 🚨 **INHERITANCE VIOLATIONS TO AVOID**\n\n❌ **WRONG**: TNC customer seeing TransFlow branding\n❌ **WRONG**: TNC customer choosing their own network\n❌ **WRONG**: TNC customer having features parent TNC doesn't have\n❌ **WRONG**: TNC customer bypassing parent TNC permissions\n❌ **WRONG**: Direct database access without inheritance validation\n\n## ✅ **CORRECT INHERITANCE PATTERNS**\n\n```typescript\n// ✅ CORRECT: Network inheritance validation\nconst getCustomerNetworkAccess = async (customerOrgId: string) => {\n  const customer = await getOrganization(customerOrgId);\n  if (customer.account_type !== 'tnc_customer') {\n    throw new Error('Organization is not a TNC customer');\n  }\n  \n  const parentTNC = await getOrganization(customer.parent_tnc_id);\n  return await getNetworksByOwner(parentTNC.id);\n};\n\n// ✅ CORRECT: Branding inheritance\nconst getCustomerBranding = async (customerOrgId: string) => {\n  const customer = await getOrganization(customerOrgId);\n  if (customer.account_type === 'tnc_customer') {\n    const parentTNC = await getOrganization(customer.parent_tnc_id);\n    return parentTNC.branding_config; // Inherit parent branding\n  }\n  return getDefaultBranding(); // TransFlow branding for direct clients\n};\n\n// ✅ CORRECT: Feature flag inheritance\nconst getCustomerFeatures = async (customerOrgId: string) => {\n  const customer = await getOrganization(customerOrgId);\n  if (customer.account_type === 'tnc_customer') {\n    const parentTNC = await getOrganization(customer.parent_tnc_id);\n    return parentTNC.feature_flags; // Inherit parent features\n  }\n  return customer.feature_flags; // Own features for direct clients\n};\n```\n\n## 📊 **DATABASE INHERITANCE PATTERNS**\n\n```sql\n-- ✅ CORRECT: TNC customer hierarchy validation\nSELECT \n  customer.name as customer_name,\n  customer.account_type,\n  parent.name as parent_tnc_name,\n  parent.feature_flags as inherited_features,\n  parent.branding_config as inherited_branding\nFROM organizations customer\nJOIN organizations parent ON parent.id = customer.parent_tnc_id\nWHERE customer.account_type = 'tnc_customer';\n\n-- ✅ CORRECT: Network inheritance query\nSELECT n.* \nFROM networks n\nJOIN organizations parent_tnc ON parent_tnc.id = n.owner_organization_id\nJOIN organizations customer ON customer.parent_tnc_id = parent_tnc.id\nWHERE customer.id = $1 AND customer.account_type = 'tnc_customer';\n```\n\n## 🎯 **VALIDATION REQUIREMENTS**\n\n### **Account Type Validation**\n- [ ] `account_type = 'tnc_customer'` for all TNC customers\n- [ ] `parent_tnc_id` must reference valid TNC account\n- [ ] `managed_by = 'tnc'` (not 'transflow')\n- [ ] Proper foreign key constraints enforced\n\n### **Portal Access Validation**\n- [ ] TNC customers use `/event-manager` portal\n- [ ] Portal shows parent TNC branding and theme\n- [ ] No access to super admin or platform tenancy features\n- [ ] Cannot create other TNC accounts or customers\n\n### **Business Logic Validation**\n- [ ] All quotes use parent TNC's affiliate network\n- [ ] Rate cards inherited from parent TNC configuration\n- [ ] Email notifications use parent TNC templates\n- [ ] Reporting data scoped to parent TNC context\n\n## 📋 **IMPLEMENTATION CHECKLIST**\n\n- [ ] Database queries include inheritance validation\n- [ ] API endpoints validate TNC customer relationships\n- [ ] UI components show inherited branding\n- [ ] Business logic respects inheritance hierarchy\n- [ ] Error handling includes inheritance violations\n- [ ] Testing covers inheritance edge cases\n\nPlease analyze the modified file for TNC customer inheritance compliance."}}