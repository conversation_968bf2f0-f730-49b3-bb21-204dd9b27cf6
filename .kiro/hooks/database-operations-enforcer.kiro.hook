{"enabled": true, "name": "Database Operations Enforcer", "description": "Enforces database operation standards and multi-tenant isolation", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "lib/**/*.ts", "supabase/migrations/**/*.sql"]}, "then": {"type": "askAgent", "prompt": "🛡️ **DATABASE OPERATIONS VALIDATION** 🛡️\n\nA database-related file has been modified. Please validate:\n\n## 1. **Multi-Tenant Isolation**\n- [ ] All queries include `organization_id` filtering\n- [ ] No cross-organization data access\n- [ ] RLS policies are properly implemented\n- [ ] SUPER_ADMIN bypass is explicit when needed\n\n## 2. **Database Query Standards**\n- [ ] Use `mcp_access_db_query` tool for read operations\n- [ ] Schema changes go through migration files only\n- [ ] No direct SQL execution outside migrations\n- [ ] Proper error handling for database operations\n\n## 3. **Required Patterns**\n```typescript\n// ✅ CORRECT:\n// const data = await mcp_access_db_query('SELECT * FROM quotes WHERE organization_id = $1');\n// \n// ❌ WRONG:\n// const data = await db.query('SELECT * FROM quotes');\n```\n\n## 4. **Migration Standards**\nFor SQL files:\n- [ ] Proper transaction usage (BEGIN/COMMIT)\n- [ ] Rollback instructions in comments\n- [ ] RLS policies for new tables\n- [ ] Organization-scoped indexes\n\nPlease check the file for compliance with database operation standards."}}