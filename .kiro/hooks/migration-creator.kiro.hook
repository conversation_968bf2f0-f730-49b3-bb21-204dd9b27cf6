{"enabled": true, "name": "Migration Creator", "description": "Assists with creating proper database migration files", "version": "1", "when": {"type": "fileEdited", "patterns": ["supabase/migrations/**/*.sql"]}, "then": {"type": "askAgent", "prompt": "🔄 **MIGRATION CREATION ASSISTANT** 🔄\n\nA migration file has been modified. Please validate:\n\n## 1. **Migration Structure**\n- [ ] Proper file naming: `XXX_descriptive_name.sql`\n- [ ] Sequential numbering\n- [ ] Descriptive purpose comment\n- [ ] Transaction wrapper (BEGIN/COMMIT)\n\n## 2. **Required Elements**\n```sql\n-- Migration: XXX_descriptive_name.sql\n-- Purpose: Brief description of changes\n-- Date: YYYY-MM-DD\n\nBEGIN;\n\n-- Your SQL changes here\n-- Include comments explaining each section\n\nCOMMIT;\n\n-- Rollback instructions (commented):\n-- To rollback this migration:\n-- [specific rollback SQL statements]\n```\n\n## 3. **Best Practices**\n- [ ] Include rollback instructions\n- [ ] Add proper indexes for new tables\n- [ ] Create RLS policies for multi-tenant tables\n- [ ] Test migration on development first\n\n## 4. **Multi-Tenant Requirements**\n- [ ] New tables include `organization_id` column\n- [ ] RLS policies enforce organization isolation\n- [ ] Indexes include `organization_id`\n- [ ] Foreign key constraints are proper\n\n## 5. **Validation Checklist**\n- [ ] Migration runs without errors\n- [ ] Rollback instructions are accurate\n- [ ] No data loss potential\n- [ ] Performance impact considered\n\nPlease review the migration for completeness and safety."}}