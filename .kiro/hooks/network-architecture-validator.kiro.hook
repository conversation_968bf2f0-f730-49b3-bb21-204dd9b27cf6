{"enabled": true, "name": "Network Architecture Validator", "description": "Validates Networks vs Organizations architecture compliance - ensures proper distinction between business entities (organizations) and service offerings (networks)", "version": "1.0.0", "when": {"type": "fileEdited", "patterns": ["app/api/networks/**/*.ts", "app/api/organizations/**/*.ts", "app/components/**/network/**/*.tsx", "app/components/**/tenant/**/*.tsx", "app/components/**/NetworkSwitcher*.tsx", "app/components/**/TenantSwitcher*.tsx", "app/lib/network/**/*.ts", "app/lib/tenant/**/*.ts", "app/hooks/useNetwork*.ts", "app/hooks/useTenant*.ts", "app/utils/network/**/*.ts", "app/contexts/NetworkContext.tsx", "app/contexts/TenantContext.tsx", "supabase/migrations/**/*network*.sql", "supabase/migrations/**/*tenant*.sql", "scripts/**/*network*.js", "scripts/**/*tenant*.js"]}, "then": {"type": "askAgent", "prompt": "🌐 **NETWORK ARCHITECTURE VALIDATION** 🌐\n\nA network or organization-related file has been modified. Please validate against the **VALIDATED Networks vs Organizations Architecture** (80% success rate):\n\n## 🎯 **CRITICAL ARCHITECTURAL DISTINCTION**\n\n### **NETWORKS** (Service Offerings)\n- ✅ **TransFlow Shared Network** - Platform-wide network\n- ✅ **Metro Ride Transportation Network** - Service provided by Metro Ride Network org\n- ✅ **Marriott Premium Transportation Network** - Service provided by Marriott International org\n\n### **ORGANIZATIONS** (Business Entities)\n- ✅ **Metro Ride Network** - TNC Account organization that provides transportation network\n- ✅ **Marriott International** - TNC Account organization that provides premium network\n- ✅ **Metro Boston Downtown Office** - TNC Customer that uses Metro Ride's network\n\n## 🔍 **VALIDATION CHECKLIST**\n\n### **1. Network Switcher Visibility Logic (VALIDATED)**\n- [ ] **SUPER_ADMIN**: Always shows switcher (platform administration)\n- [ ] **TNC_ADMIN**: Shows switcher ONLY if multiple networks (rare edge case)\n- [ ] **TNC_CUSTOMER**: Switcher HIDDEN (network inherited from parent TNC)\n- [ ] **DIRECT_CLIENT**: Switcher HIDDEN (locked to shared network)\n\n### **2. Organization vs Network Distinction**\n- [ ] Organizations are business entities that OWN/PROVIDE networks\n- [ ] Networks are service offerings PROVIDED BY organizations\n- [ ] Customers USE networks but BELONG TO organizations\n- [ ] UI clearly separates network switcher (top-right) from organization selector (left sidebar)\n\n### **3. TNC Customer Inheritance**\n- [ ] TNC customers inherit network access from parent TNC\n- [ ] TNC customers inherit branding from parent TNC (not TransFlow)\n- [ ] TNC customers cannot choose networks (inherited and fixed)\n- [ ] Portal shows parent TNC branding, not TransFlow branding\n\n### **4. Service Tier Deferral (POST-MVP)**\n- [ ] Service tier implementation is DEFERRED to post-MVP\n- [ ] Focus on single network per TNC (90% use case)\n- [ ] No service tier UI or advanced rate card logic\n- [ ] Basic rate differentiation only (existing rate_cards table)\n\n### **5. Four-Tier Account Architecture**\n- [ ] TRANSFLOW_SUPER_ADMIN: Ultimate platform control + network operations\n- [ ] TNC_ACCOUNT: Network coordination + customer portal management\n- [ ] TNC_CUSTOMER: Managed by parent TNC with inherited permissions\n- [ ] DIRECT_CLIENT: Independent TransFlow relationship using shared network\n\n## 🚨 **COMMON VIOLATIONS TO AVOID**\n\n❌ **WRONG**: Treating Metro Ride Network as a network (it's an organization)\n❌ **WRONG**: Showing network switcher to TNC customers or direct clients\n❌ **WRONG**: Implementing service tier features before MVP completion\n❌ **WRONG**: Confusing organization selector with network switcher\n❌ **WRONG**: TNC customers seeing TransFlow branding instead of parent TNC branding\n\n## ✅ **CORRECT PATTERNS**\n\n```typescript\n// ✅ CORRECT: Network switcher visibility logic\nconst shouldShowNetworkSwitcher = (user: User, availableNetworks: Network[]) => {\n  if (user.role === 'SUPER_ADMIN') {\n    return availableNetworks.length > 0;\n  }\n  if (user.account_type === 'tnc_account' && user.role === 'TNC_ADMIN') {\n    return availableNetworks.length > 1; // Only if multiple networks\n  }\n  return false; // Hidden for TNC customers and direct clients\n};\n\n// ✅ CORRECT: TNC customer network inheritance\nconst getNetworkAccess = async (user: User) => {\n  if (user.account_type === 'tnc_customer') {\n    return await getInheritedNetworks(user.organization.parent_tnc_id);\n  }\n  if (user.account_type === 'direct_client') {\n    return await getSharedNetworks();\n  }\n  return await getUserNetworks(user.id);\n};\n```\n\n## 📊 **VALIDATION STATUS**\n- **Architecture Validation**: 80% success rate\n- **Implementation Ready**: Network switcher logic validated\n- **Service Tier Status**: Deferred to post-MVP (market validation required)\n- **Priority**: Complete four-tier architecture before advanced features\n\nPlease analyze the modified file for compliance with this validated architecture."}}