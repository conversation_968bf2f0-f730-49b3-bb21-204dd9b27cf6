{"enabled": true, "name": "Multi-Tenant Validator", "description": "Validates multi-tenant data isolation patterns including four-tier architecture (Task 22 compliant)", "version": "1.0.0", "when": {"type": "fileEdited", "patterns": ["app/api/**/*.ts", "app/lib/db/**/*.ts", "app/lib/auth/**/*.ts", "supabase/migrations/**/*.sql", "app/contexts/OrganizationContext.tsx", "app/contexts/TenantContext.tsx", "app/hooks/useOrganization*.ts", "app/hooks/useTenant*.ts", "app/utils/tenant/**/*.ts", "app/utils/organization/**/*.ts", "components/**/tenant/**/*.tsx", "components/**/organization/**/*.tsx"]}, "then": {"type": "askAgent", "prompt": "🏢 **MULTI-TENANT ISOLATION VALIDATION** 🏢\n\nA multi-tenant related file has been modified. Please validate against **ENHANCED Multi-Tenant Architecture** with Networks vs Organizations integration:\n\n## 🔍 **CORE ISOLATION REQUIREMENTS**\n\n### **1. Organization-Based Data Isolation (MANDATORY)**\n- [ ] All database queries include `organization_id` filtering\n- [ ] RLS policies enforce organization boundaries\n- [ ] Cross-tenant data access only through SUPER_ADMIN\n- [ ] User data scoped to their organization context\n\n### **2. Four-Tier Account Architecture Validation**\n- [ ] **TRANSFLOW_SUPER_ADMIN**: Ultimate platform control + network operations\n- [ ] **TNC_ACCOUNT**: Network coordination + customer portal management\n- [ ] **TNC_CUSTOMER**: Managed by parent TNC with inherited permissions\n- [ ] **DIRECT_CLIENT**: Independent TransFlow relationship using shared network\n\n### **3. Network Inheritance Validation (NEW)**\n- [ ] TNC customers inherit network access from parent TNC (`parent_tnc_id`)\n- [ ] Direct clients locked to shared network only\n- [ ] Network ownership relationships properly maintained\n- [ ] Network access control respects inheritance hierarchy\n\n### **4. TNC Customer Hierarchy Validation**\n- [ ] `parent_tnc_id` relationships properly enforced\n- [ ] `account_type` validation for four-tier architecture\n- [ ] `managed_by` field usage (TNC vs TransFlow management)\n- [ ] Inheritance patterns for branding, features, and permissions\n\n## ✅ **CORRECT PATTERNS**\n\n```typescript\n// ✅ CORRECT: Organization-scoped query with network inheritance\nconst getCustomerQuotes = async (userId: string) => {\n  const user = await getUser(userId);\n  const organization = await getOrganization(user.organization_id);\n  \n  // Validate organization access\n  if (user.role !== 'SUPER_ADMIN' && user.organization_id !== organization.id) {\n    throw new Error('Insufficient permissions');\n  }\n  \n  // Get network access (inherited for TNC customers)\n  let networkIds: string[];\n  if (organization.account_type === 'tnc_customer') {\n    const parentTNC = await getOrganization(organization.parent_tnc_id);\n    const networks = await getNetworksByOwner(parentTNC.id);\n    networkIds = networks.map(n => n.id);\n  } else {\n    const networks = await getUserNetworks(user.id);\n    networkIds = networks.map(n => n.id);\n  }\n  \n  return await db.quotes.findMany({\n    where: {\n      organization_id: organization.id, // Organization isolation\n      network_id: { in: networkIds }    // Network inheritance\n    }\n  });\n};\n\n// ✅ CORRECT: RLS policy with network inheritance\nCREATE POLICY \"quotes_multi_tenant_isolation\" ON quotes\n  FOR ALL USING (\n    -- Organization isolation\n    organization_id IN (\n      SELECT organization_id FROM user_profiles \n      WHERE user_id = auth.uid()\n    )\n    OR \n    -- SUPER_ADMIN override\n    EXISTS (\n      SELECT 1 FROM user_profiles \n      WHERE user_id = auth.uid() \n      AND role = 'SUPER_ADMIN'\n    )\n  );\n```\n\n## 🚨 **ISOLATION VIOLATIONS TO AVOID**\n\n❌ **WRONG**: Global queries without organization filtering\n❌ **WRONG**: TNC customers accessing networks they don't inherit\n❌ **WRONG**: Missing parent_tnc_id validation for TNC customers\n❌ **WRONG**: Cross-tenant data leakage through network access\n❌ **WRONG**: Bypassing inheritance hierarchy in business logic\n\n## 📊 **VALIDATION CHECKLIST**\n\n- [ ] Organization_id filtering in all queries\n- [ ] RLS policy compliance with SUPER_ADMIN override\n- [ ] Parent_tnc_id relationship validation\n- [ ] Account_type validation for four-tier architecture\n- [ ] Managed_by field usage (TNC vs TransFlow management)\n- [ ] Network inheritance for TNC customers\n- [ ] Cross-tenant access prevention\n- [ ] Proper error handling for isolation violations\n\nPlease analyze the modified file for complete multi-tenant isolation compliance."}}