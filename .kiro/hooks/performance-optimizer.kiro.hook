{"enabled": true, "name": "Performance Optimizer", "description": "Analyzes code for performance issues and suggests optimizations", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/**/*.ts", "app/**/*.tsx", "lib/**/*.ts", "components/**/*.tsx"]}, "then": {"type": "askAgent", "prompt": "⚡ **PERFORMANCE OPTIMIZATION ANALYSIS** ⚡\n\nA file has been modified. Please analyze for performance optimizations:\n\n## 1. **React Performance**\n- [ ] Proper use of React.memo for expensive components\n- [ ] useCallback for event handlers\n- [ ] useMemo for expensive calculations\n- [ ] Avoid unnecessary re-renders\n\n## 2. **Database Performance**\n- [ ] Efficient query patterns\n- [ ] Proper indexing strategy\n- [ ] Pagination implementation\n- [ ] N+1 query prevention\n\n## 3. **Bundle Optimization**\n- [ ] Dynamic imports for code splitting\n- [ ] Tree shaking opportunities\n- [ ] Bundle size impact analysis\n- [ ] Lazy loading implementation\n\n## 4. **Optimization Patterns**\n```typescript\n// ✅ CORRECT: Memoized expensive calculation\nconst expensiveValue = useMemo(() => {\n  return heavyCalculation(data);\n}, [data]);\n\n// ✅ CORRECT: Memoized callback\nconst handleClick = useCallback(() => {\n  onAction(id);\n}, [onAction, id]);\n\n// ✅ CORRECT: Dynamic import\nconst HeavyComponent = lazy(() => import('./HeavyComponent'));\n```\n\n## 5. **Performance Metrics**\n- [ ] Component render time\n- [ ] Bundle size impact\n- [ ] Memory usage\n- [ ] Network requests optimization\n\n## 6. **Caching Strategies**\n- [ ] Redis caching for expensive operations\n- [ ] Browser caching headers\n- [ ] Query result caching\n- [ ] Static asset optimization\n\nPlease analyze the file and suggest specific performance improvements."}}