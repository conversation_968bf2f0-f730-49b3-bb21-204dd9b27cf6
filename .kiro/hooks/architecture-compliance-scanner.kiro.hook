{"enabled": true, "name": "Architecture Compliance Scanner", "description": "Scans code for compliance with Next.js 14, TypeScript, and architectural standards", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/**/*.ts", "app/**/*.tsx", "lib/**/*.ts", "components/**/*.tsx"]}, "then": {"type": "askAgent", "prompt": "🏗️ **ARCHITECTURE COMPLIANCE SCAN** 🏗️\n\nA file has been modified. Please validate architectural compliance:\n\n## 1. **Next.js 14 App Router Standards**\n- [ ] Proper file structure in `app/` directory\n- [ ] Correct route handler patterns\n- [ ] Server/Client component separation\n- [ ] Proper metadata and layout usage\n\n## 2. **TypeScript Standards**\n- [ ] Strict type definitions\n- [ ] No `any` types without justification\n- [ ] Proper interface definitions\n- [ ] Generic type usage where appropriate\n\n## 3. **Component Architecture**\n- [ ] Single responsibility principle\n- [ ] Proper prop typing\n- [ ] Error boundary implementation\n- [ ] Accessibility compliance\n\n## 4. **Performance Standards**\n- [ ] Lazy loading implementation\n- [ ] Proper memoization (React.memo, useMemo, useCallback)\n- [ ] Bundle size considerations\n- [ ] Image optimization\n\n## 5. **Security Standards**\n- [ ] Input sanitization\n- [ ] XSS prevention\n- [ ] CSRF protection\n- [ ] Authentication validation\n\nPlease analyze the file for architectural compliance and provide specific recommendations."}}