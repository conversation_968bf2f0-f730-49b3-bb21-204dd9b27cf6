{"enabled": true, "name": "Pre-Deployment Validator", "description": "Comprehensive validation before deployment including security, performance, and compliance checks", "version": "1", "when": {"type": "fileEdited", "patterns": ["app/**/*.ts", "app/**/*.tsx", "lib/**/*.ts", "supabase/migrations/**/*.sql"]}, "then": {"type": "askAgent", "prompt": "🚀 **PRE-DEPLOYMENT VALIDATION** 🚀\n\nA critical file has been modified. Please perform comprehensive deployment readiness check:\n\n## 1. **Security Validation**\n- [ ] No hardcoded secrets or API keys\n- [ ] Proper input sanitization\n- [ ] Authentication/authorization implemented\n- [ ] XSS and CSRF protection\n- [ ] SQL injection prevention\n\n## 2. **Performance Validation**\n- [ ] Database queries are optimized\n- [ ] Proper caching implementation\n- [ ] Bundle size impact acceptable\n- [ ] No memory leaks\n- [ ] Response times under 2 seconds\n\n## 3. **Multi-Tenant Compliance**\n- [ ] Organization-scoped data access\n- [ ] RLS policies properly implemented\n- [ ] No cross-tenant data leakage\n- [ ] Proper isolation testing\n\n## 4. **Code Quality**\n- [ ] TypeScript compilation passes\n- [ ] No console.log statements in production\n- [ ] Proper error handling\n- [ ] Code follows architectural standards\n- [ ] Documentation is updated\n\n## 5. **Testing Requirements**\n- [ ] Unit tests pass\n- [ ] Integration tests pass\n- [ ] E2E tests pass\n- [ ] Performance tests pass\n- [ ] Security tests pass\n\n## 6. **Database Migration Safety**\n- [ ] Migration has rollback plan\n- [ ] No destructive operations without backup\n- [ ] Migration tested on staging\n- [ ] Performance impact assessed\n\n## 7. **Environment Configuration**\n- [ ] Environment variables properly set\n- [ ] Feature flags configured\n- [ ] Monitoring and logging enabled\n- [ ] Error tracking configured\n\n## 8. **Deployment Checklist**\n- [ ] Build process completes successfully\n- [ ] All dependencies are up to date\n- [ ] No breaking changes introduced\n- [ ] Backward compatibility maintained\n- [ ] Documentation updated\n\n## 9. **Critical Blockers**\n❌ **DO NOT DEPLOY IF:**\n- Security vulnerabilities detected\n- Multi-tenant isolation broken\n- Performance degradation > 20%\n- Critical tests failing\n- Database migration risks data loss\n\nPlease provide a comprehensive deployment readiness assessment."}}