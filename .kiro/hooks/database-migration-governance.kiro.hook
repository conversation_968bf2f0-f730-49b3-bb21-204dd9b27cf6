{"enabled": true, "name": "Database Migration Governance Validator", "description": "Validates database migrations for SUPER_ADMIN control architecture compliance and four-tier account architecture", "version": "1.0.0", "when": {"type": "fileEdited", "patterns": ["supabase/migrations/**/*.sql", "database/migrations/**/*.sql", "migrations/**/*.sql"]}, "then": {"type": "askAgent", "prompt": "const GovernanceReminderGenerator = require('../lib/governance-reminder-generator.cjs');\nconst filePaths = context.changedFiles || ['database migration'];\nconst reminder = GovernanceReminderGenerator.generateHookReminder('database-migration-governance', filePaths);\nconsole.log(reminder);\n\n// Database Migration Governance Validation Checklist:\n// 1. SUPER_ADMIN Override Logic - All functions must include is_super_admin_safe() bypass\n// 2. Permission Template Integration - Validate permission templates and feature flags\n// 3. Subscription Plan Validation - Check enterprise subscription requirements\n// 4. Four-Tier Account Architecture - Validate account types and network inheritance\n// 5. Multi-Tenant Data Isolation - Ensure RLS policies with SUPER_ADMIN bypass\n// 6. Audit Trail Implementation - Log SUPER_ADMIN actions for compliance\n// 7. TNC Customer Portal Compliance - Validate TNC hierarchy and portal provisioning\n// 8. Granular Permission Integration - Check specific permission keys\n\nPlease analyze the database migration for complete SUPER_ADMIN control architecture compliance."}}