# Multi-Tenant Isolation Standards

## Critical Data Isolation Requirements

### Organization-Based Isolation (TENANT_ID ELIMINATED)
- **MANDATORY**: Every database query MUST include `organization_id` filtering
- **NO EXCEPTIONS**: All user data must be scoped to their organization
- **SUPER_ADMIN OVERRIDE**: Only SUPER_ADMIN can access cross-organization data
- **ZERO TOLERANCE**: NO `tenant_id` usage anywhere - ONLY `organization_id`

### Required Patterns

#### Database Queries
```sql
-- ✅ CORRECT: Always filter by organization_id
SELECT * FROM quotes WHERE organization_id = $1;

-- ❌ WRONG: Missing organization_id filter
SELECT * FROM quotes WHERE user_id = $1;

-- ❌ FORBIDDEN: Using tenant_id (ELIMINATED)
SELECT * FROM quotes WHERE tenant_id = $1;
```

#### API Route Protection
```typescript
// ✅ CORRECT: Validate organization access
const { user, organization } = await validateUserAccess(req);
const quotes = await db.quotes.findMany({
  where: { organization_id: organization.id }
});

// ❌ WRONG: No organization validation
const quotes = await db.quotes.findMany({
  where: { user_id: user.id }
});
```

### Organization Types & Isolation Levels

#### Shared Organizations (`shared`)
- Share affiliate network across organizations
- Isolated customer data and events
- Common rate cards and pricing

#### Segregated Organizations (`segregated`) 
- Isolated affiliate networks
- Completely separate customer bases
- Custom branding and pricing

#### Isolated Organizations (`isolated`)
- Complete data isolation
- Dedicated infrastructure
- Enterprise-level separation

### RLS Policy Requirements
- Every table with organization_id MUST have RLS policies
- Policies must enforce organization-based access
- SUPER_ADMIN bypass policies must be explicit

### Validation Checklist
- [ ] All queries include organization_id filtering
- [ ] API routes validate organization access
- [ ] RLS policies enforce isolation
- [ ] Cross-organization access is explicitly authorized
- [ ] Organization type affects data sharing rules

### Prohibited Patterns
- ❌ Global queries without organization filtering
- ❌ User-based access without organization validation
- ❌ Cross-organization data leakage
- ❌ Missing RLS policies on multi-tenant tables
- ❌ Hardcoded organization references
- ❌ **ANY tenant_id usage (COMPLETELY ELIMINATED)**
- ❌ **tenant_id columns in database tables**
- ❌ **tenant_id parameters in API endpoints**
- ❌ **tenant_id properties in TypeScript interfaces**

### Testing Requirements
- Multi-tenant isolation tests for all features
- Cross-organization access prevention tests
- Organization type behavior validation
- RLS policy effectiveness verification