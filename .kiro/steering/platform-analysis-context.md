---
inclusion: always
---

# Platform Analysis Project Context

## Project Overview
This is a comprehensive analysis of the WWMS DIY multi-tenant global ground transportation platform. The platform is approximately 81% complete with 19% remaining to reach MVP status.

## Key Project Information
- **Platform Type**: Multi-tenant SaaS transportation management system
- **Tech Stack**: Next.js 14, TypeScript, Supabase, PostgreSQL, tRPC
- **Architecture**: Multi-tenant with organization-based isolation
- **Current Status**: 81% complete, targeting MVP completion

## Reference Documentation
All analysis documents are located in `docs/reference/` directory and should be referenced when working on tasks.

## Task Execution Guidelines
1. Always reference the requirements.md, design.md, and tasks.md files in `.kiro/specs/platform-analysis/`
2. Focus on one task at a time from the implementation plan
3. Use the reference documentation in `docs/reference/` for context
4. Maintain consistency with existing architectural patterns
5. Ensure all work respects multi-tenant isolation requirements

## Key Architectural Principles
- Multi-tenant data isolation using organization_id
- Role-based access control with 6 distinct user roles: SUPER_ADMIN, CLIENT, PASSENGER, AFFILIATE, DISPATCHER, DRIVER
- Three-tier client levels architecture with permission templates controlled by SUPER_ADMIN
- Type-safe APIs using tRPC
- Real-time features using Socket.io
- Comprehensive security with RLS policies

## Critical User Role Context
- CLIENT role uses /event-manager/ portal and creates/manages PASSENGER users
- DISPATCHER is a sub-role of AFFILIATE, both use /affiliate/ portal
- Same CLIENT role has different capabilities based on organization configuration
- SUPER_ADMIN controls all client levels through permission templates and granular settings

## Priority Areas for Completion
1. Technical architecture documentation
2. Gap analysis and assessment
3. Optimization recommendations
4. Feature completion specifications
5. Code quality and standards implementation
6. Implementation roadmap creation