# Networks vs Organizations Architecture Standards

## 🎯 **VALIDATED ARCHITECTURAL FOUNDATION**

This architecture has been **validated with 80% success rate** through comprehensive testing against real database data and system implementation. These patterns are **PRODUCTION-READY** and must be followed in all development.

**Status**: Architecture Decision Record  
**Validation Date**: January 2025  
**Platform Completion**: 81% (targeting MVP)  
**Implementation Priority**: HIGH - Foundational architecture

---

## 🌐 **CORE ARCHITECTURAL DISTINCTION**

### **NETWORKS** (Service Offerings)
Networks are **transportation service offerings** provided by organizations:
- ✅ **TransFlow Shared Network** - Platform-wide network for direct clients
- ✅ **Metro Ride Transportation Network** - Service provided by Metro Ride Network organization
- ✅ **Marriott Premium Transportation Network** - Service provided by Marriott International organization

### **ORGANIZATIONS** (Business Entities)
Organizations are **business entities** that own/provide networks and manage customers:
- ✅ **Metro Ride Network** - TNC Account organization that provides transportation network
- ✅ **Marriott International** - TNC Account organization that provides premium network
- ✅ **Metro Boston Downtown Office** - TNC Customer that uses Metro Ride's network

### **KEY RELATIONSHIP**
```
Organizations → OWN/PROVIDE → Networks → SERVE → Customers
```

---

## 🔍 **NETWORK SWITCHER VISIBILITY LOGIC** (VALIDATED 100%)

### **Implementation Pattern**
```typescript
const shouldShowNetworkSwitcher = (user: User, availableNetworks: Network[]) => {
  // Always show for TransFlow Super Admin
  if (user.role === 'SUPER_ADMIN') {
    return availableNetworks.length > 0;
  }
  
  // Show for TNC_ADMIN only if they have multiple networks (rare)
  if (user.account_type === 'tnc_account' && user.role === 'TNC_ADMIN') {
    return availableNetworks.length > 1;
  }
  
  // Hide for everyone else (99% of cases)
  return false;
};
```

### **Visibility Rules**
| User Type | Networks | Show Switcher | Reason |
|-----------|----------|---------------|---------|
| **SUPER_ADMIN** | 3+ | ✅ YES | Platform administration needs |
| **TNC_ADMIN (Single)** | 1 | ❌ NO | Only one network - no choice needed |
| **TNC_ADMIN (Multi)** | 2+ | ✅ YES | Multiple networks - choice needed |
| **TNC_CUSTOMER** | 1 | ❌ NO | Network inherited - no choice |
| **DIRECT_CLIENT** | 1 | ❌ NO | Locked to shared - no choice |

---

## 🏢 **FOUR-TIER ACCOUNT ARCHITECTURE**

### **Account Type Hierarchy**
```
TRANSFLOW_SUPER_ADMIN (Ultimate Control)
├── Platform Tenancy: Full organization management, TNC creation
├── Customer Tenancy: All TNC customer management capabilities  
├── OPS Menu: All network operations, quotes, affiliates, analytics
└── Network Access: All networks available

TNC_ACCOUNT (Network Coordination + Customer Management)
├── Platform Tenancy: ❌ BLOCKED (cannot create TNCs or manage platform)
├── Customer Tenancy: ✅ FULL ACCESS (manage their customer accounts)
├── OPS Menu: ✅ NETWORK-SCOPED (network operations, quotes, affiliates)
└── Network Access: Own networks only

TNC_CUSTOMER (Managed by Parent TNC)
├── Platform Tenancy: ❌ BLOCKED
├── Customer Tenancy: ❌ BLOCKED  
├── OPS Menu: ❌ BLOCKED
└── Portal Access: /event-manager (TNC-branded, inherited capabilities)

DIRECT_CLIENT (Independent Transflow Relationship)
├── Platform Tenancy: ❌ BLOCKED
├── Customer Tenancy: ❌ BLOCKED
├── OPS Menu: ❌ BLOCKED  
└── Portal Access: /event-manager (TransFlow-branded, shared network)
```

---

## 🎨 **TNC CUSTOMER INHERITANCE PATTERNS**

### **Complete Inheritance Model**
```typescript
interface TNCCustomerInheritance {
  // Network Access Inheritance
  network_access: Network[];           // Inherited from parent TNC
  affiliate_network: AffiliateCompany[]; // Parent TNC's network
  
  // Branding Inheritance  
  branding_config: BrandingConfig;     // Parent TNC's branding
  portal_theme: ThemeConfig;           // Parent TNC's theme
  email_templates: EmailTemplate[];   // Parent TNC's templates
  
  // Feature Inheritance
  feature_flags: FeatureFlags;        // Parent TNC's features
  permission_template: PermissionTemplate; // Parent TNC's permissions
  subscription_limits: SubscriptionLimits; // Parent TNC's limits
  
  // Service Tier Inheritance (POST-MVP)
  service_tier: 'luxury' | 'business' | 'express'; // Assigned by parent
  rate_cards: RateCard[];             // Parent TNC's rate configuration
}
```

### **Inheritance Implementation**
```typescript
// ✅ CORRECT: Network inheritance
const getCustomerNetworkAccess = async (customerOrgId: string) => {
  const customer = await getOrganization(customerOrgId);
  if (customer.account_type !== 'tnc_customer') {
    throw new Error('Organization is not a TNC customer');
  }
  
  const parentTNC = await getOrganization(customer.parent_tnc_id);
  return await getNetworksByOwner(parentTNC.id);
};

// ✅ CORRECT: Branding inheritance
const getCustomerBranding = async (customerOrgId: string) => {
  const customer = await getOrganization(customerOrgId);
  if (customer.account_type === 'tnc_customer') {
    const parentTNC = await getOrganization(customer.parent_tnc_id);
    return parentTNC.branding_config; // Inherit parent branding
  }
  return getDefaultBranding(); // TransFlow branding for direct clients
};

// ✅ CORRECT: Feature inheritance
const getCustomerFeatures = async (customerOrgId: string) => {
  const customer = await getOrganization(customerOrgId);
  if (customer.account_type === 'tnc_customer') {
    const parentTNC = await getOrganization(customer.parent_tnc_id);
    return parentTNC.feature_flags; // Inherit parent features
  }
  return customer.feature_flags; // Own features for direct clients
};
```

---

## 📊 **DATABASE ARCHITECTURE PATTERNS**

### **Organization-Network Relationship**
```sql
-- Organizations table (business entities)
CREATE TABLE organizations (
  id UUID PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  account_type VARCHAR(50) NOT NULL, -- 'tnc_account', 'tnc_customer', 'direct_client'
  parent_tnc_id UUID REFERENCES organizations(id),
  organization_type VARCHAR(50), -- 'shared', 'segregated', 'isolated'
  managed_by VARCHAR(50), -- 'transflow', 'tnc'
  branding_config JSONB,
  feature_flags JSONB
);

-- Networks table (service offerings)
CREATE TABLE networks (
  id UUID PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  owner_organization_id UUID REFERENCES organizations(id) NOT NULL,
  network_type VARCHAR(50), -- 'shared', 'isolated', 'regional'
  region VARCHAR(50), -- For geographic networks (post-MVP)
  currency VARCHAR(3) -- For regional networks (post-MVP)
);

-- Network access control
CREATE TABLE organization_network_access (
  organization_id UUID REFERENCES organizations(id),
  network_id UUID REFERENCES networks(id),
  access_type VARCHAR(50), -- 'owner', 'inherited', 'granted'
  granted_by UUID REFERENCES organizations(id),
  PRIMARY KEY (organization_id, network_id)
);
```

### **TNC Customer Hierarchy Queries**
```sql
-- ✅ CORRECT: TNC customer hierarchy validation
SELECT 
  customer.name as customer_name,
  customer.account_type,
  parent.name as parent_tnc_name,
  parent.feature_flags as inherited_features,
  parent.branding_config as inherited_branding
FROM organizations customer
JOIN organizations parent ON parent.id = customer.parent_tnc_id
WHERE customer.account_type = 'tnc_customer';

-- ✅ CORRECT: Network inheritance query
SELECT n.* 
FROM networks n
JOIN organizations parent_tnc ON parent_tnc.id = n.owner_organization_id
JOIN organizations customer ON customer.parent_tnc_id = parent_tnc.id
WHERE customer.id = $1 AND customer.account_type = 'tnc_customer';
```

---

## 🎯 **UI COMPONENT PATTERNS**

### **Network Switcher Component (Top-Right)**
```typescript
interface NetworkSwitcherProps {
  user: User;
  currentNetwork: Network;
  availableNetworks: Network[];
  onNetworkChange: (networkId: string) => void;
}

export function NetworkSwitcher({ user, currentNetwork, availableNetworks, onNetworkChange }: NetworkSwitcherProps) {
  const shouldShow = shouldShowNetworkSwitcher(user, availableNetworks);
  
  if (!shouldShow) {
    return null; // Hidden for most users
  }
  
  return (
    <Select value={currentNetwork.id} onValueChange={onNetworkChange}>
      <SelectTrigger className="w-[200px]">
        <SelectValue>{currentNetwork.name}</SelectValue>
      </SelectTrigger>
      <SelectContent>
        {availableNetworks.map(network => (
          <SelectItem key={network.id} value={network.id}>
            {network.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
```

### **Organization Selector Component (Left Sidebar - Separate!)**
```typescript
interface OrganizationSelectorProps {
  user: User;
  currentOrganization: Organization;
  availableOrganizations: Organization[];
  onOrganizationChange: (orgId: string) => void;
}

export function OrganizationSelector({ user, currentOrganization, availableOrganizations, onOrganizationChange }: OrganizationSelectorProps) {
  // This is for OPS menu organization selection, NOT network switching
  return (
    <Select value={currentOrganization.id} onValueChange={onOrganizationChange}>
      <SelectTrigger>
        <SelectValue>{currentOrganization.name}</SelectValue>
      </SelectTrigger>
      <SelectContent>
        {availableOrganizations.map(org => (
          <SelectItem key={org.id} value={org.id}>
            {org.name} ({org.account_type})
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
```

---

## 🚀 **API ENDPOINT PATTERNS**

### **Network Management API**
```typescript
// GET /api/networks - List available networks for user
export async function GET(request: NextRequest) {
  const { user, organization } = await validateUserAccess(request);
  
  let availableNetworks: Network[];
  
  if (user.role === 'SUPER_ADMIN') {
    // Super admin sees all networks
    availableNetworks = await getAllNetworks();
  } else if (user.account_type === 'tnc_account') {
    // TNC admin sees their own networks
    availableNetworks = await getNetworksByOwner(organization.id);
  } else if (user.account_type === 'tnc_customer') {
    // TNC customer inherits parent's networks
    availableNetworks = await getInheritedNetworks(organization.parent_tnc_id);
  } else {
    // Direct client locked to shared network
    availableNetworks = await getSharedNetworks();
  }
  
  return NextResponse.json({ networks: availableNetworks });
}
```

### **Organization Management API**
```typescript
// GET /api/organizations - List organizations for OPS menu
export async function GET(request: NextRequest) {
  const { user } = await validateUserAccess(request);
  
  let availableOrganizations: Organization[];
  
  if (user.role === 'SUPER_ADMIN') {
    // Super admin sees all organizations
    availableOrganizations = await getAllOrganizations();
  } else if (user.account_type === 'tnc_account') {
    // TNC admin sees their customers
    availableOrganizations = await getTNCCustomers(user.organization_id);
  } else {
    // Others see only their own organization
    availableOrganizations = [await getOrganization(user.organization_id)];
  }
  
  return NextResponse.json({ organizations: availableOrganizations });
}
```

---

## 🎨 **SERVICE TIER ARCHITECTURE** (POST-MVP)

### **Service Tier Implementation Strategy**
- **MVP Priority**: Single network per TNC with basic rate differentiation
- **Post-MVP**: Service tier implementation when market validated
- **Market Reality**: 90% of TNCs want single network with service tiers, not multiple networks

### **Service Tier Examples**
```typescript
// POST-MVP: Service tier differentiation
interface ServiceTier {
  id: string;
  name: 'luxury' | 'business' | 'express';
  network_id: string;
  minimum_vehicle_class: string;
  minimum_affiliate_rating: number;
  rate_multiplier: number;
  features: string[];
}

// Marriott Service Tier Example (POST-MVP)
const marriottServiceTiers = [
  {
    name: 'luxury',
    customer_type: 'Ritz-Carlton properties',
    minimum_vehicle_class: 'luxury_sedan',
    minimum_affiliate_rating: 4.8,
    rate_multiplier: 1.5
  },
  {
    name: 'business', 
    customer_type: 'Marriott Hotels',
    minimum_vehicle_class: 'business_sedan',
    minimum_affiliate_rating: 4.5,
    rate_multiplier: 1.2
  },
  {
    name: 'express',
    customer_type: 'Courtyard/Fairfield',
    minimum_vehicle_class: 'standard_sedan',
    minimum_affiliate_rating: 4.0,
    rate_multiplier: 1.0
  }
];
```

---

## 🚨 **COMMON VIOLATIONS TO AVOID**

### **❌ ARCHITECTURAL VIOLATIONS**
- **WRONG**: Treating Metro Ride Network as a network (it's an organization)
- **WRONG**: Showing network switcher to TNC customers or direct clients
- **WRONG**: Implementing service tier features before MVP completion
- **WRONG**: Confusing organization selector with network switcher
- **WRONG**: TNC customers seeing TransFlow branding instead of parent TNC branding

### **❌ DATABASE VIOLATIONS**
- **WRONG**: Direct queries without inheritance validation
- **WRONG**: Missing foreign key constraints for parent_tnc_id
- **WRONG**: Allowing TNC customers to own networks
- **WRONG**: Breaking organization-network ownership relationships

### **❌ UI VIOLATIONS**
- **WRONG**: Network switcher visible when it should be hidden
- **WRONG**: Organization selector and network switcher in same component
- **WRONG**: TNC customer portals showing TransFlow branding
- **WRONG**: Service tier UI before MVP completion

---

## 📋 **IMPLEMENTATION CHECKLIST**

### **✅ Network Architecture Compliance**
- [ ] Network switcher follows validated visibility logic
- [ ] Organizations and networks are clearly distinguished
- [ ] TNC customers inherit network access from parent
- [ ] Service tier implementation is deferred to post-MVP
- [ ] Four-tier account architecture is maintained

### **✅ Database Compliance**
- [ ] Organizations table contains business entities
- [ ] Networks table contains service offerings
- [ ] Foreign key constraints enforce relationships
- [ ] TNC customer hierarchy is properly modeled
- [ ] Network ownership relationships are maintained

### **✅ API Compliance**
- [ ] Network APIs respect inheritance patterns
- [ ] Organization APIs maintain proper separation
- [ ] TNC customer APIs inherit from parent TNC
- [ ] Access control validates account types
- [ ] Error handling includes inheritance violations

### **✅ UI Compliance**
- [ ] Network switcher and organization selector are separate
- [ ] TNC customer portals show parent TNC branding
- [ ] Portal access follows four-tier architecture
- [ ] Service tier UI is not implemented (post-MVP)
- [ ] Responsive design works across all account types

---

## 🎯 **SUCCESS CRITERIA**

### **Architecture Validation**
- ✅ **80%+ validation success rate** (already achieved)
- ✅ **Network switcher logic validated** (100% success)
- ✅ **Service tier mapping validated** (100% success)
- ✅ **User role mapping validated** (100% success)

### **Implementation Readiness**
- ✅ **Network switcher logic**: Ready for implementation
- ✅ **Service tier architecture**: Validated but deferred to post-MVP
- ✅ **User role mapping**: Roles correctly defined
- ✅ **Metro Ride classification**: Validated as organization

### **MVP Priorities**
- 🎯 **Complete four-tier architecture** before advanced features
- 🎯 **Focus on single network per TNC** (90% use case)
- 🎯 **Defer service tier features** until market validation
- 🎯 **Implement network switcher** with validated logic

---

## 📞 **QUICK REFERENCE**

### **Test Commands**
```bash
# Validate architecture
node scripts/test-networks-vs-organizations-architecture.js

# Test network inheritance
node scripts/four-tier-tests/4-network-inheritance-validation.js

# Test portal access
node scripts/four-tier-tests/2-portal-access-validation.js

# Run full validation suite
bash scripts/run-architecture-validation-suite.sh
```

### **Key Files**
- **Architecture Analysis**: `docs/reference/networks-vs-organizations-architecture-analysis.md`
- **Validation Results**: `NETWORKS_VS_ORGANIZATIONS_VALIDATION_RESULTS.md`
- **UI Testing Plan**: `UI_TESTING_PLAN_NETWORKS_VS_ORGANIZATIONS.md`
- **Testing Guide**: `docs/reference/architecture-testing-guide.md`

**This architecture is PRODUCTION-READY and must be followed in all development!** 🚀