# Task Execution Standards

## Task Management Protocol
When executing tasks from the platform analysis spec:

### Before Starting Any Task
1. Read the current task description and requirements
2. Review related reference documentation in `docs/reference/`
3. Check dependencies and prerequisites
4. Update task status to "in progress"

### During Task Execution
1. Focus on ONE task at a time - do not jump to other tasks
2. Create comprehensive, well-structured documentation
3. Use proper markdown formatting with clear headings
4. Include code examples where relevant
5. Reference specific requirements from requirements.md
6. **ALWAYS follow database operation standards** - use access-db MCP tool for queries, migration files for changes

### After Task Completion
1. Update task status to "completed"
2. Provide summary of what was accomplished
3. Stop and wait for user review before proceeding
4. Do not automatically start the next task

## Documentation Standards
- Use clear, professional language
- Include technical diagrams where helpful (<PERSON><PERSON> preferred)
- Provide actionable recommendations
- Include implementation examples
- Maintain consistency with existing documentation style

## Quality Assurance
- Ensure all documentation is complete and accurate
- Verify all code examples are syntactically correct
- Check that all requirements are addressed
- Maintain multi-tenant architecture principles
- Follow TypeScript and Next.js best practices
- **Enforce database operation standards** in all code and scripts

## Database Operations in Tasks
- **Query Requirements**: All database queries in task execution must use `mcp_access_db_query`
- **Schema Changes**: Any database schema modifications must be done through migration files
- **Data Analysis**: Use access-db MCP tool to analyze current database state
- **Verification**: Use access-db MCP tool to verify task completion against database state