---
inclusion: always
---

# SUPER_ADMIN Controls Reference

## 🎛️ Complete SUPER_ADMIN Control Capabilities

This reference provides developers with the complete set of SUPER_ADMIN control capabilities to ensure existing architecture is leveraged instead of creating duplicate systems.

## 🏗️ Organization Foundation Controls

### Organization Types (Data Isolation Levels)
```typescript
type OrganizationType = 'shared' | 'segregated' | 'isolated';
```

- **shared**: Basic SaaS model, shared affiliate network, common pricing
- **segregated**: Custom branding, isolated customer bases, separate networks  
- **isolated**: Complete data isolation, enterprise-level separation, dedicated infrastructure

### Subscription Plans
```typescript
type SubscriptionPlan = 'free_trial' | 'starter' | 'professional' | 'enterprise';
```

| Plan | Monthly Price | Key Features |
|------|---------------|--------------|
| **Free Trial** | $0 | Basic quotes, email support |
| **Starter** | $49 | Basic analytics, enhanced support |
| **Professional** | $149 | API access, priority support, advanced analytics |
| **Enterprise** | $499 | White labeling, full feature access, dedicated support |

### Permission Templates (Applied by SUPER_ADMIN)
```typescript
type PermissionTemplate = 'basic_client' | 'premium_client' | 'tnc_enterprise';
```

- **basic_client**: Core features, no financial access, limited analytics
- **premium_client**: Enhanced features, financial visibility, advanced analytics
- **tnc_enterprise**: Full platform access, network management, cross-organization visibility

## 🎨 White-Label & Branding Controls

### White-Label Feature Flags (Boolean Controls)
```typescript
interface WhiteLabelControls {
  has_white_labeling: boolean;    // Custom branding and UI customization
  has_custom_domain: boolean;     // Custom domain support
  has_custom_branding: boolean;   // Logo, colors, and theme customization
}
```

### Branding Configuration (JSONB)
```typescript
interface BrandingConfig {
  logo_url?: string;
  primary_color?: string;
  secondary_color?: string;
  font_family?: string;
  custom_css?: string;
  favicon_url?: string;
}
```

## 🚀 Feature Flags System (JSONB Toggle)

### Available Feature Flags
```typescript
interface FeatureFlags {
  // Core Platform Features
  apiAccess?: boolean;           // REST/GraphQL API access
  whiteLabeling?: boolean;       // Custom branding capabilities
  realTimeTracking?: boolean;    // Live GPS tracking
  advancedAnalytics?: boolean;   // Enhanced reporting and insights
  prioritySupport?: boolean;     // Dedicated support channel
  customIntegrations?: boolean;  // Third-party integrations
  bulkOperations?: boolean;      // Batch processing capabilities
  advancedReporting?: boolean;   // Custom report generation
  
  // NEW: TNC Account Capabilities
  tncCustomerManagement?: boolean;     // Create and manage TNC customer accounts
  customPortalProvisioning?: boolean;  // Provision branded customer portals
  isolatedAffiliateNetwork?: boolean;  // Access to isolated affiliate networks
  serviceBasedPricing?: boolean;       // Service-based revenue model (not subscription)
  
  // NEW: Booking Workflow Controls
  blindQuoteRequests?: boolean;        // Enable blind/managed quote workflow
  managedServiceMode?: boolean;        // Enable full managed service experience
  affiliateSelectionHidden?: boolean; // Hide affiliate selection from customers
  customBookingWorkflow?: boolean;     // Enable custom booking workflow configuration
  embeddedBookingForm?: boolean;       // Enable embedded form integration
}
```

### Feature Flag Usage Examples
```typescript
// ✅ CORRECT: Check feature flags before enabling functionality
const organization = await getOrganization(orgId);
if (organization.feature_flags?.apiAccess) {
  // Enable API endpoints
  return renderAPISettings();
}

// ✅ CORRECT: Conditional UI rendering based on flags
{organization.feature_flags?.advancedAnalytics && (
  <AdvancedAnalyticsPanel />
)}
```

## 🏢 TNC Account Management Controls

### TNC Customer Management
```typescript
interface TNCCustomerControls {
  // Customer Account Creation
  createTNCCustomer: (tncId: string, customerData: CustomerData) => TNCCustomer;
  
  // Portal Provisioning
  provisionCustomerPortal: (customerId: string, branding: BrandingConfig) => Portal;
  
  // Network Access Control
  grantNetworkAccess: (customerId: string, networkId: string) => void;
  
  // Service Pricing Control
  setServicePricing: (customerId: string, pricingModel: ServicePricing) => void;
}
```

### TNC-Specific Permission Keys
```typescript
const TNC_PERMISSIONS = [
  'tnc.manage_customers',           // Create and manage TNC customer accounts
  'tnc.provision_portals',          // Provision branded customer portals
  'tnc.control_network_access',     // Control customer network access
  'tnc.set_customer_pricing',       // Set pricing for customer accounts
  'tnc.manage_affiliate_network',   // Manage isolated affiliate network
  'tnc.blind_quote_management',     // Manage blind quote workflows
  'tnc.service_margin_control',     // Control service margins and pricing
  'tnc.customer_branding_control'   // Control customer portal branding
] as const;
```

### Booking Workflow Controls
```typescript
interface BookingWorkflowControls {
  // Workflow Mode Configuration
  setBookingMode: (orgId: string, mode: 'marketplace' | 'managed_service') => void;
  
  // Affiliate Visibility Control
  setAffiliateVisibility: (orgId: string, visible: boolean) => void;
  
  // Quote Display Configuration
  setQuoteDisplayMode: (orgId: string, mode: 'detailed' | 'summary' | 'blind') => void;
  
  // Service Experience Control
  enableManagedService: (orgId: string, enabled: boolean) => void;
}
```

## 🔐 Granular Permission Keys (28 Available)

### Core Permission Keys
```typescript
const GRANULAR_PERMISSIONS = [
  // Quote Management
  'quotes.create',              // Create new quotes
  'quotes.edit',                // Edit existing quotes
  'quotes.delete',              // Delete quotes
  'can_create_quotes',          // Legacy quote creation permission
  
  // Event Management
  'events.create',              // Create new events
  'events.manage',              // Full event management
  
  // User Management
  'users.manage',               // Manage organization users
  'can_manage_users',           // Legacy user management permission
  'manage_users',               // Alternative user management permission
  
  // Analytics & Reporting
  'analytics.view',             // View analytics dashboards
  'analytics.export',           // Export analytics data
  'view_analytics',             // Legacy analytics permission
  
  // Billing & Financial
  'billing.view',               // View billing information
  'manage_billing',             // Full billing management
  
  // Affiliate Management
  'affiliates.manage',          // Manage affiliate relationships
  
  // API Access
  'api.access',                 // REST/GraphQL API access
  
  // System Administration
  'manage_settings',            // Organization settings management
  'manage_all_organizations',   // Cross-organization access (SUPER_ADMIN)
  'super_admin',                // Full platform administration
  
  // White Label
  'white_label.branding'        // Custom branding management
] as const;
```

### Permission Usage Examples
```typescript
// ✅ CORRECT: Check granular permissions
const hasPermission = await checkUserPermission(userId, 'quotes.edit');
if (!hasPermission) {
  throw new Error('Insufficient permissions to edit quotes');
}

// ✅ CORRECT: Role-based permission checking
const userPermissions = await getUserPermissions(userId, organizationId);
const canManageUsers = userPermissions.includes('users.manage');
```

## 🏢 Organization Settings System (JSONB)

### Standard Settings Structure
```typescript
interface OrganizationSettings {
  // Feature Configuration
  features?: string[];                    // Enabled feature list
  upsell_threshold?: number;             // Quote threshold for upselling
  allowGlobalNetwork?: boolean;          // Access to global affiliate network
  
  // Business Configuration
  defaultCurrency?: string;              // Default currency (USD, EUR, etc.)
  autoQuoteEnabled?: boolean;            // Automatic quote generation
  premiumService?: boolean;              // Premium service tier
  exclusiveNetwork?: boolean;            // Exclusive affiliate network access
  
  // Operational Settings
  maxQuotesPerMonth?: number;           // Monthly quote limits
  maxUsersPerOrg?: number;              // User limits per organization
  apiRateLimit?: number;                // API request rate limits
}
```

### Settings Usage Examples
```typescript
// ✅ CORRECT: Access organization settings
const settings = organization.settings as OrganizationSettings;
if (settings.autoQuoteEnabled) {
  await generateAutomaticQuote(quoteRequest);
}

// ✅ CORRECT: Enforce limits based on settings
const monthlyQuotes = await getMonthlyQuoteCount(organizationId);
if (monthlyQuotes >= (settings.maxQuotesPerMonth || 100)) {
  throw new Error('Monthly quote limit exceeded');
}
```

## 🔄 SUPER_ADMIN Control Patterns

### Organization Creation Pattern
```typescript
// ✅ CORRECT: SUPER_ADMIN creates organization with full control
const createOrganization = async (superAdminId: string, orgData: CreateOrgInput) => {
  // Validate SUPER_ADMIN role
  await validateSuperAdminRole(superAdminId);
  
  const organization = await db.organization.create({
    data: {
      ...orgData,
      organization_type: orgData.organization_type || 'shared',
      subscription_plan: orgData.subscription_plan || 'free_trial',
      permission_template: orgData.permission_template || 'basic_client',
      feature_flags: orgData.feature_flags || {},
      settings: orgData.settings || {},
      has_white_labeling: orgData.has_white_labeling || false,
      has_custom_domain: orgData.has_custom_domain || false,
      has_custom_branding: orgData.has_custom_branding || false
    }
  });
  
  // Apply permission template
  await applyPermissionTemplate(organization.id, orgData.permission_template);
  
  return organization;
};
```

### Permission Template Application
```typescript
// ✅ CORRECT: Apply permission templates based on client level
const applyPermissionTemplate = async (orgId: string, template: PermissionTemplate) => {
  const templatePermissions = {
    basic_client: [
      'quotes.create',
      'events.create',
      'analytics.view'
    ],
    premium_client: [
      'quotes.create',
      'quotes.edit',
      'events.create',
      'events.manage',
      'analytics.view',
      'analytics.export',
      'billing.view',
      'users.manage'
    ],
    tnc_enterprise: [
      'quotes.create',
      'quotes.edit',
      'quotes.delete',
      'events.create',
      'events.manage',
      'analytics.view',
      'analytics.export',
      'billing.view',
      'manage_billing',
      'users.manage',
      'affiliates.manage',
      'api.access',
      'white_label.branding'
    ]
  };
  
  const permissions = templatePermissions[template] || templatePermissions.basic_client;
  
  // Clear existing permissions
  await db.organizationPermissions.deleteMany({
    where: { organization_id: orgId }
  });
  
  // Apply new permissions
  await db.organizationPermissions.createMany({
    data: permissions.map(key => ({
      organization_id: orgId,
      permission_key: key,
      permission_value: true
    }))
  });
};
```

### User Permission Override Pattern
```typescript
// ✅ CORRECT: SUPER_ADMIN can override individual user permissions
const overrideUserPermissions = async (
  superAdminId: string,
  userId: string,
  organizationId: string,
  permissions: string[]
) => {
  await validateSuperAdminRole(superAdminId);
  
  await db.userOrganizations.update({
    where: {
      user_id_organization_id: {
        user_id: userId,
        organization_id: organizationId
      }
    },
    data: {
      permissions: permissions
    }
  });
};
```

## 📊 Client Experience Variation Matrix

### Experience Calculation Formula
```typescript
// ✅ CORRECT: Calculate user experience based on all factors
const calculateUserExperience = (
  userRole: UserRole,
  organizationType: OrganizationType,
  subscriptionPlan: SubscriptionPlan,
  permissionTemplate: PermissionTemplate,
  featureFlags: FeatureFlags,
  granularPermissions: string[]
) => {
  // Base capabilities from role
  const baseCapabilities = getRoleCapabilities(userRole);
  
  // Enhanced by organization type
  const orgCapabilities = getOrganizationTypeCapabilities(organizationType);
  
  // Limited/enhanced by subscription
  const subscriptionCapabilities = getSubscriptionCapabilities(subscriptionPlan);
  
  // Shaped by permission template
  const templateCapabilities = getTemplateCapabilities(permissionTemplate);
  
  // Modified by feature flags
  const flagCapabilities = getFeatureFlagCapabilities(featureFlags);
  
  // Fine-tuned by granular permissions
  const granularCapabilities = getGranularCapabilities(granularPermissions);
  
  return mergeCapabilities([
    baseCapabilities,
    orgCapabilities,
    subscriptionCapabilities,
    templateCapabilities,
    flagCapabilities,
    granularCapabilities
  ]);
};
```

## 🚨 Comprehensive Validation Checklists

### 🔐 Permission Architecture Validation Checklist

#### Core Permission System Compliance
- [ ] **Uses existing granular permission keys** instead of creating new ones
  - ✅ CORRECT: `permissions.includes('quotes.edit')`
  - ❌ WRONG: Creating new permission like `'quotes.modify'`
- [ ] **Respects permission template hierarchy** (basic → premium → enterprise)
  - ✅ CORRECT: Check template level before granting advanced permissions
  - ❌ WRONG: Hardcoding permissions without template consideration
- [ ] **Checks organization-level permissions** before user-level permissions
  - ✅ CORRECT: `organization.permissions.includes(permission) && user.permissions.includes(permission)`
  - ❌ WRONG: Only checking user permissions
- [ ] **Implements SUPER_ADMIN override capabilities**
  - ✅ CORRECT: `user.role === 'SUPER_ADMIN' || hasPermission(user, permission)`
  - ❌ WRONG: No SUPER_ADMIN bypass logic
- [ ] **Maintains organization-based data isolation**
  - ✅ CORRECT: All queries include `organization_id` filtering
  - ❌ WRONG: Global queries without organization scope

#### Permission Template Integration
- [ ] **Validates against permission template capabilities**
  - basic_client: Core features only (quotes.create, events.create, analytics.view)
  - premium_client: Enhanced features (quotes.edit, billing.view, users.manage)
  - tnc_enterprise: Full access (all permissions including cross-org visibility)
- [ ] **Handles permission template upgrades/downgrades**
  - ✅ CORRECT: Graceful feature enablement/disablement
  - ❌ WRONG: Breaking functionality on template changes
- [ ] **Provides fallback for missing permissions**
  - ✅ CORRECT: Default to most restrictive permission level
  - ❌ WRONG: Failing silently or granting excessive access

#### API Route Permission Validation
- [ ] **Every API route validates user permissions**
  ```typescript
  // ✅ CORRECT Pattern
  const { user, organization, permissions } = await validateUserAccess(request);
  if (!permissions.includes('required.permission')) {
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
  }
  ```
- [ ] **Permission checks happen before business logic**
- [ ] **Error messages are consistent and informative**
- [ ] **Audit logging for permission failures**

### 🏢 Organization Controls Validation Checklist

#### Organization Type Architecture Compliance
- [ ] **Considers organization type in data access patterns**
  - **shared**: Shared affiliate network, common pricing
  - **segregated**: Isolated networks, custom branding
  - **isolated**: Complete data isolation, enterprise features
- [ ] **Respects data isolation levels**
  - ✅ CORRECT: `shared` orgs can access shared affiliate network
  - ✅ CORRECT: `segregated` orgs have isolated customer bases
  - ✅ CORRECT: `isolated` orgs have complete data separation
  - ❌ WRONG: Mixing data across isolation levels
- [ ] **Implements organization type-specific business logic**
  ```typescript
  // ✅ CORRECT: Type-specific behavior
  switch (organization.organization_type) {
    case 'shared':
      return getSharedAffiliateNetwork();
    case 'segregated':
      return getOrganizationAffiliateNetwork(orgId);
    case 'isolated':
      return getIsolatedAffiliateNetwork(orgId);
  }
  ```

#### White-Label Feature Implementation
- [ ] **Uses white-label feature flags correctly**
  - `has_white_labeling`: Custom branding and UI customization
  - `has_custom_domain`: Custom domain support
  - `has_custom_branding`: Logo, colors, theme customization
- [ ] **White-label features work with any organization type**
  - ✅ CORRECT: shared + has_white_labeling = custom branding on shared network
  - ❌ WRONG: Restricting white-label to specific organization types
- [ ] **Graceful degradation when white-label disabled**
  - ✅ CORRECT: Falls back to default branding
  - ❌ WRONG: Breaking UI when white-label flags are false

#### Organization Settings Integration
- [ ] **Uses organization settings for operational parameters**
  ```typescript
  // ✅ CORRECT: Settings-based limits
  const monthlyLimit = organization.settings?.maxQuotesPerMonth || 100;
  if (currentQuotes >= monthlyLimit) {
    throw new Error('Monthly quote limit exceeded');
  }
  ```
- [ ] **Validates settings against subscription plan**
- [ ] **Provides sensible defaults for missing settings**
- [ ] **Handles settings updates gracefully**

### 💳 Subscription Architecture Validation Checklist

#### Subscription Plan Integration
- [ ] **Maps subscription plans to permission templates correctly**
  - free_trial → basic_client (typically)
  - professional → premium_client (typically)
  - enterprise → tnc_enterprise (typically)
- [ ] **Enforces subscription-based limits**
  - free_trial: 5 quotes/month, basic features
  - professional: 100 quotes/month, API access, analytics
  - enterprise: Unlimited quotes, all features, white-label
- [ ] **Validates feature access against subscription tier**
  ```typescript
  // ✅ CORRECT: Subscription-based feature gating
  if (organization.subscription_plan === 'free_trial' && requestedFeature === 'api_access') {
    throw new Error('API access requires Professional or Enterprise subscription');
  }
  ```

#### Feature Flag System Integration
- [ ] **Uses feature flags for conditional functionality**
  - apiAccess, whiteLabeling, realTimeTracking, advancedAnalytics
  - prioritySupport, customIntegrations, bulkOperations, advancedReporting
- [ ] **Feature flags override subscription defaults when enabled**
  - ✅ CORRECT: free_trial + apiAccess flag = API access enabled
  - ❌ WRONG: Ignoring feature flags in favor of subscription defaults
- [ ] **Handles feature flag changes dynamically**
  - ✅ CORRECT: Real-time feature enablement/disablement
  - ❌ WRONG: Requiring app restart for feature flag changes

#### Billing Integration Consistency
- [ ] **Subscription changes update permission templates**
- [ ] **Billing limits are enforced in real-time**
- [ ] **Graceful handling of subscription downgrades**
  - ✅ CORRECT: Disable features, preserve data
  - ❌ WRONG: Breaking functionality or losing data
- [ ] **Upgrade paths are clearly defined and functional**

### 🗄️ Database Migration Validation Checklist

#### Schema Architecture Preservation
- [ ] **Preserves existing permission template structure**
  - Don't modify core permission template tables
  - Extend through additional tables if needed
  - Maintain backward compatibility
- [ ] **Maintains organization type architecture**
  - Keep organization_type enum values: shared, segregated, isolated
  - Don't add conflicting organization classification systems
  - Preserve organization type-specific behavior
- [ ] **Respects granular permission key naming conventions**
  - Use dot notation: 'resource.action' (e.g., 'quotes.edit')
  - Maintain consistency with existing 20 permission keys
  - Don't create duplicate or conflicting permission names

#### Multi-Tenant Data Isolation
- [ ] **Includes proper RLS policies for multi-tenant isolation**
  ```sql
  -- ✅ CORRECT: Organization-based RLS policy
  CREATE POLICY "table_organization_isolation" ON table_name
    FOR ALL USING (
      organization_id IN (
        SELECT organization_id FROM user_profiles 
        WHERE user_id = auth.uid()
      )
      OR 
      EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE user_id = auth.uid() 
        AND role = 'SUPER_ADMIN'
      )
    );
  ```
- [ ] **Every table with organization_id has RLS policies**
- [ ] **SUPER_ADMIN bypass is explicitly included in policies**
- [ ] **Cross-organization access is properly restricted**

#### SUPER_ADMIN Control Impact Assessment
- [ ] **Documents impact on SUPER_ADMIN control capabilities**
  - How does this migration affect organization creation?
  - Does it change permission template application?
  - Are there new controls SUPER_ADMIN should have?
- [ ] **Maintains SUPER_ADMIN override capabilities**
  - SUPER_ADMIN can still access all organizations
  - SUPER_ADMIN can still modify all permission templates
  - SUPER_ADMIN can still override user permissions
- [ ] **Preserves existing control patterns**
  - Don't break existing SUPER_ADMIN workflows
  - Maintain consistency with established patterns
  - Extend rather than replace existing controls

### 🔧 API Development Validation Checklist

#### Route Handler Architecture
- [ ] **Every API route validates organization access**
  ```typescript
  // ✅ CORRECT: Complete validation pattern
  export async function GET(request: NextRequest) {
    const { user, organization, permissions } = await validateUserAccess(request);
    
    // Check subscription limits
    if (organization.subscription_plan === 'free_trial') {
      await enforceTrialLimits(organization);
    }
    
    // Check granular permissions
    if (!permissions.includes('required.permission')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    // Check feature flags
    if (requiresAdvancedFeature && !organization.feature_flags?.advancedAnalytics) {
      return NextResponse.json({ error: 'Feature not enabled' }, { status: 403 });
    }
    
    // Organization-scoped business logic
    const data = await db.resource.findMany({
      where: { organization_id: organization.id }
    });
    
    return NextResponse.json({ success: true, data });
  }
  ```
- [ ] **Organization-scoped database queries**
- [ ] **Consistent error response format**
- [ ] **Proper HTTP status codes**

#### Component Architecture
- [ ] **Components respect all SUPER_ADMIN control layers**
  - Organization type, subscription plan, permission template
  - Feature flags, granular permissions, settings
- [ ] **Conditional rendering based on capabilities**
  ```typescript
  // ✅ CORRECT: Multi-layer capability checking
  const canAccessFeature = 
    organization.subscription_plan !== 'free_trial' &&
    organization.feature_flags?.advancedAnalytics &&
    userPermissions.includes('analytics.view') &&
    organization.permission_template !== 'basic_client';
  ```
- [ ] **Graceful degradation for missing capabilities**
- [ ] **Clear user feedback for restricted features**

### 📋 Quick Validation Reference

#### Before Implementing Any Feature
1. **Check existing SUPER_ADMIN controls** - Can this be achieved with current capabilities?
2. **Identify required permission level** - Which granular permissions are needed?
3. **Determine subscription requirements** - What subscription tier should this require?
4. **Consider organization type impact** - How does this behave across shared/segregated/isolated?
5. **Plan feature flag integration** - Should this be toggleable via feature flags?

#### Red Flags to Avoid
- ❌ Creating new permission systems instead of using existing granular permissions
- ❌ Hardcoding subscription or organization type assumptions
- ❌ Bypassing SUPER_ADMIN control architecture
- ❌ Breaking multi-tenant data isolation
- ❌ Ignoring permission template hierarchy
- ❌ Creating features that can't be controlled by SUPER_ADMIN

#### Green Lights for Implementation
- ✅ Uses existing granular permission keys
- ✅ Respects organization type architecture
- ✅ Integrates with subscription plan system
- ✅ Supports feature flag control
- ✅ Maintains SUPER_ADMIN override capabilities
- ✅ Preserves multi-tenant data isolation
- ✅ Follows established architectural patterns

## 🔧 Implementation Examples

### API Route with Full SUPER_ADMIN Architecture
```typescript
// ✅ CORRECT: Complete SUPER_ADMIN-aware API route
export async function GET(request: NextRequest) {
  try {
    const { user, organization, permissions } = await validateUserAccess(request);
    
    // Check subscription limits
    if (!organization.subscription_plan || organization.subscription_plan === 'free_trial') {
      const monthlyQuotes = await getMonthlyQuoteCount(organization.id);
      const limit = organization.settings?.maxQuotesPerMonth || 5;
      if (monthlyQuotes >= limit) {
        return NextResponse.json(
          { error: 'Monthly quote limit exceeded. Upgrade subscription.' },
          { status: 403 }
        );
      }
    }
    
    // Check granular permissions
    if (!permissions.includes('quotes.create')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create quotes' },
        { status: 403 }
      );
    }
    
    // Check feature flags
    if (requestRequiresAdvancedFeatures && !organization.feature_flags?.advancedAnalytics) {
      return NextResponse.json(
        { error: 'Advanced features not enabled for this organization' },
        { status: 403 }
      );
    }
    
    // Proceed with organization-scoped operation
    const quotes = await db.quotes.findMany({
      where: { organization_id: organization.id }
    });
    
    return NextResponse.json({ success: true, data: quotes });
    
  } catch (error) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
```

### Component with SUPER_ADMIN Architecture Awareness
```typescript
// ✅ CORRECT: Component that respects all SUPER_ADMIN controls
interface FeatureComponentProps {
  organization: Organization;
  userPermissions: string[];
  userRole: UserRole;
}

export function FeatureComponent({ 
  organization, 
  userPermissions, 
  userRole 
}: FeatureComponentProps) {
  // Check permission template capabilities
  const hasAdvancedFeatures = organization.permission_template !== 'basic_client';
  
  // Check granular permissions
  const canManageUsers = userPermissions.includes('users.manage');
  
  // Check feature flags
  const hasAnalytics = organization.feature_flags?.advancedAnalytics;
  
  // Check subscription capabilities
  const hasApiAccess = organization.subscription_plan !== 'free_trial';
  
  // Check organization type capabilities
  const isIsolated = organization.organization_type === 'isolated';
  
  return (
    <div className="feature-component">
      {hasAdvancedFeatures && (
        <AdvancedFeaturesPanel />
      )}
      
      {canManageUsers && (
        <UserManagementPanel />
      )}
      
      {hasAnalytics && (
        <AnalyticsPanel />
      )}
      
      {hasApiAccess && (
        <APIAccessPanel />
      )}
      
      {isIsolated && (
        <EnterpriseControlsPanel />
      )}
      
      {organization.has_white_labeling && (
        <BrandingControlsPanel />
      )}
    </div>
  );
}
```

## 📚 Quick Reference Summary

### Key SUPER_ADMIN Control Points
1. **Organization Types**: shared, segregated, isolated
2. **Subscription Plans**: free_trial, starter, professional, enterprise
3. **Permission Templates**: basic_client, premium_client, tnc_enterprise
4. **Feature Flags**: 8+ toggleable features (apiAccess, whiteLabeling, etc.)
5. **Granular Permissions**: 20 specific permission keys
6. **White-Label Controls**: 3 boolean flags for branding capabilities
7. **Settings System**: Flexible JSONB configuration for operational parameters

### Remember: SUPER_ADMIN Controls Everything
- Organization creation and configuration
- Permission template assignment and modification
- Feature flag enablement/disablement
- Subscription plan management
- User permission overrides
- Cross-organization access and visibility
- White-label feature activation
- Settings and limits configuration

**Always leverage existing SUPER_ADMIN capabilities instead of creating new permission systems!**