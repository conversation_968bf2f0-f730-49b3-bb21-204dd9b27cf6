# Role-Based Access Standards

## User Role Validation Requirements

### Platform Roles (Always Valid)
```typescript
const PLATFORM_ROLES = [
    'SUPER_ADMIN',      // Platform administration
    'CLIENT',           // Organizations that book transportation
    'PASSENGER',        // Individual passengers (created by <PERSON><PERSON>IENT)
    'AFFILIATE',        // Transportation service providers
    'DISPATCHER',       // Affiliate sub-role for operations
    'DRIVER'            // Vehicle operators
] as const;
```

### Enhanced Platform Roles
```typescript
const ENHANCED_ROLES = [
    'CLIENT_COORDINATOR',   // Enhanced CLIENT with additional permissions
    'TNC_ADMIN',           // Transportation Network Company Admin
    'TENANT_ADMIN',        // Tenant-level administrator
    'TENANT_MANAGER'       // Tenant-level manager
] as const;
```

### Role Validation Patterns

#### API Route Protection
```typescript
// ✅ CORRECT: Validate role and organization
import { validateUserRole } from '@/lib/utils/role-validation';

export async function GET(req: Request) {
  const { user, hasRole } = await validateUserRole(req, ['CLIENT', 'CLIENT_COORDINATOR']);
  
  if (!hasRole) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }
  
  // Proceed with organization-scoped logic
}

// ❌ WRONG: No role validation
export async function GET(req: Request) {
  // Direct access without role checking
}
```

#### Multi-Tier Authorization
```typescript
// ✅ CORRECT: Platform + Organization + Permissions
const { user, organization, permissions } = await validateUserAccess(req);

// Check platform role
if (!['CLIENT', 'CLIENT_COORDINATOR'].includes(user.role)) {
  throw new Error('Invalid platform role');
}

// Check organization permissions
if (!permissions.canManageEvents) {
  throw new Error('Insufficient organization permissions');
}
```

### Role-Specific Access Patterns

#### SUPER_ADMIN Access
- Can access all organizations and data
- Bypasses organization_id filtering when explicitly needed
- Controls permission templates and client levels

#### CLIENT Organization Roles
- CLIENT: Core booking and passenger management
- CLIENT_COORDINATOR: Enhanced permissions + rate negotiation
- PASSENGER: Limited consumer access

#### Affiliate Company Roles
- AFFILIATE: Company ownership and management
- DISPATCHER: Operations and quote management
- DRIVER: Vehicle operations and trip execution

### Portal Access Validation
```typescript
// ✅ CORRECT: Portal-specific role validation
const PORTAL_ROLE_MAP = {
  '/super-admin': ['SUPER_ADMIN', 'TNC_ADMIN'],
  '/event-manager': ['CLIENT', 'CLIENT_COORDINATOR', 'PASSENGER'],
  '/affiliate': ['AFFILIATE', 'DISPATCHER'],
  '/driver': ['DRIVER']
} as const;
```

### Permission Template System
- **basic_client**: Core features, no financial access
- **premium_client**: Enhanced features, financial visibility
- **tnc_enterprise**: Full platform access, network management

### Validation Requirements
- [ ] Every API route validates user role
- [ ] Organization-level permissions are checked
- [ ] Portal access matches user role
- [ ] Permission templates are respected
- [ ] Multi-tier authorization is implemented

### Prohibited Patterns
- ❌ Role checking in frontend only
- ❌ Hardcoded role assumptions
- ❌ Missing organization permission validation
- ❌ Bypassing role validation for "convenience"
- ❌ Using deprecated roles (CUSTOMER, EVENT_MANAGER, etc.)

### Testing Requirements
- Role-based access tests for all endpoints
- Permission template behavior validation
- Cross-role access prevention tests
- Portal access restriction verification

## Four-Tier Account Architecture

### Account Type Definitions

#### TRANSFLOW_SUPER_ADMIN (Ultimate Platform Control)
**Capabilities**:
- Full platform administration and control
- Create and manage TNC accounts
- Configure all booking workflow modes
- Override all permission and access controls
- Access all networks and customer data

**Portal Access**:
- ✅ **Platform Tenancy**: Full organization management, TNC creation, platform settings
- ✅ **Customer Tenancy**: All TNC customer management capabilities
- ✅ **OPS Menu**: All network operations, quotes, affiliates, analytics
- ✅ **All Portals**: Access to all portal types with full permissions

#### TNC_ACCOUNT (Network Coordination + Customer Management)
**Account Characteristics**:
- `account_type`: Set to 'tnc_account'
- `parent_tnc_id`: NULL (top-level TNC entity)
- `managed_by`: Set to 'transflow'
- `subscription_plan`: 'enterprise' (service-based pricing model)

**Core Capabilities**:
- Create and manage TNC customer accounts under their hierarchy
- Provision branded customer portals with TNC branding
- Control isolated affiliate network access
- Set service-based pricing (not software subscriptions)
- Manage blind quote workflows for customers
- Control customer feature access and branding

**Portal Access**:
- ✅ **Customer Tenancy**: Full access to manage their customer accounts
- ✅ **OPS Menu**: Network operations, quotes, affiliates, analytics (network-scoped)
- ❌ **Platform Tenancy**: Cannot access organization management, TNC creation, platform settings

#### TNC_CUSTOMER (Managed by Parent TNC)
**Account Characteristics**:
- `account_type`: Set to 'tnc_customer'
- `parent_tnc_id`: Must reference valid TNC account
- `managed_by`: Set to 'tnc' (not 'transflow')

**Inherited Capabilities**:
- Network access inherited from parent TNC (isolated network)
- Feature access controlled by parent TNC
- Pricing controlled by parent TNC (service-based)
- Branding inherited from parent TNC (TNC-branded portals)
- Booking experience determined by parent TNC (marketplace vs blind quotes)

**Portal Access**:
- ✅ **Event Manager Portal**: TNC-branded interface with inherited capabilities
- ❌ **Super Admin Portal**: No access
- ❌ **Customer Creation**: Cannot create other accounts
- ❌ **Network Management**: Cannot manage affiliate networks

#### DIRECT_CLIENT (Independent Transflow Relationship)
**Account Characteristics**:
- `account_type`: Set to 'direct_client'
- `parent_tnc_id`: NULL (no parent TNC)
- `managed_by`: Set to 'transflow'

**Network Access**:
- ✅ **Shared Network**: Must use Transflow shared affiliate network
- ❌ **TNC Networks**: Cannot access TNC-specific affiliate networks
- ❌ **Isolated Networks**: Cannot create own affiliate networks

**Booking Experience**:
- ✅ **Marketplace Model**: Transparent affiliate selection and pricing
- ❌ **Managed Service**: Cannot use blind quote workflows
- ✅ **Direct Selection**: Can select affiliates and see all pricing details

### Portal Access Control Matrix

| Role | Platform Tenancy | Customer Tenancy | OPS Menu | Event Manager |
|------|------------------|------------------|----------|---------------|
| `SUPER_ADMIN` | ✅ Full | ✅ Full | ✅ Full | ✅ Full |
| `TNC_ADMIN` | ❌ Blocked | ✅ Own customers | ✅ Network scope | ✅ If needed |
| `TNC_CUSTOMER` | ❌ Blocked | ❌ Blocked | ❌ Blocked | ✅ TNC-branded |
| `DIRECT_CLIENT` | ❌ Blocked | ❌ Blocked | ❌ Blocked | ✅ Transflow-branded |
