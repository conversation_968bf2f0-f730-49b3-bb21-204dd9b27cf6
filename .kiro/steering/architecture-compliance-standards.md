# Architecture Compliance Standards

## Next.js 14 App Router Standards

### File Structure Requirements
```typescript
// ✅ CORRECT: App router structure
app/
├── (portals)/
│   ├── super-admin/
│   ├── event-manager/
│   ├── affiliate/
│   └── driver/
├── api/
│   ├── organizations/
│   ├── quotes/
│   └── trpc/
├── components/
│   ├── ui/
│   ├── features/
│   └── layout/
└── lib/
    ├── auth/
    ├── db/
    └── utils/
```

### Route Handler Standards
```typescript
// ✅ CORRECT: Proper route handler structure
import { NextRequest, NextResponse } from 'next/server';
import { validateUserRole } from '@/lib/utils/role-validation';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, organization } = await validateUserRole(request, ['CLIENT']);
    
    // Organization-scoped query
    const data = await db.quotes.findMany({
      where: { 
        organization_id: organization.id,
        id: params.id 
      }
    });
    
    return NextResponse.json({ success: true, data });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}
```

### Component Architecture Standards
```typescript
// ✅ CORRECT: Component structure
interface ComponentProps {
  organizationId: string;
  userRole: UserRole;
  className?: string;
}

export function FeatureComponent({ 
  organizationId, 
  userRole, 
  className 
}: ComponentProps) {
  // Use organization-scoped hooks
  const { data, isLoading } = useOrganizationData(organizationId);
  
  // Role-based rendering
  if (!hasPermission(userRole, 'view_feature')) {
    return <UnauthorizedMessage />;
  }
  
  return (
    <div className={cn("feature-component", className)}>
      {/* Component content */}
    </div>
  );
}
```

## TypeScript Standards

### Type Safety Requirements
```typescript
// ✅ CORRECT: Strict type definitions
interface User {
  id: string;
  email: string;
  role: UserRole;
  organization_id: string;
  created_at: Date;
  updated_at: Date;
}

interface Organization {
  id: string;
  name: string;
  slug: string;
  organization_type: 'shared' | 'segregated' | 'isolated';
  subscription_plan: 'free_trial' | 'professional' | 'enterprise';
  permission_template: 'basic_client' | 'premium_client' | 'tnc_enterprise';
}

// ✅ CORRECT: Generic type for API responses
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  meta?: {
    pagination?: PaginationMeta;
    timestamp: string;
  };
}
```

### Zod Schema Standards
```typescript
// ✅ CORRECT: Comprehensive validation schemas
import { z } from 'zod';

export const createQuoteSchema = z.object({
  organization_id: z.string().uuid('Invalid organization ID'),
  pickup_location: z.string().min(1, 'Pickup location required'),
  dropoff_location: z.string().min(1, 'Dropoff location required'),
  pickup_datetime: z.date().refine(
    date => date > new Date(),
    'Pickup time must be in the future'
  ),
  passenger_count: z.number().min(1).max(50),
  special_requirements: z.string().optional(),
  contact_info: z.object({
    name: z.string().min(1),
    phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/),
    email: z.string().email()
  })
});

export type CreateQuoteInput = z.infer<typeof createQuoteSchema>;
```

## Database Architecture Standards

### Prisma Schema Patterns
```prisma
// ✅ CORRECT: Multi-tenant table structure
model Quote {
  id              String   @id @default(cuid())
  organization_id String   // Required for multi-tenancy
  user_id         String
  pickup_location String
  dropoff_location String
  pickup_datetime DateTime
  passenger_count Int
  status          QuoteStatus @default(DRAFT)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  // Relationships with proper constraints
  organization Organization @relation(fields: [organization_id], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [user_id], references: [id], onDelete: Cascade)
  responses    QuoteResponse[]
  
  // Multi-tenant indexes
  @@index([organization_id, created_at])
  @@index([organization_id, status])
  @@index([user_id, organization_id])
}
```

### RLS Policy Standards
```sql
-- ✅ CORRECT: Organization-based RLS policies
CREATE POLICY "quotes_organization_isolation" ON quotes
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id FROM user_profiles 
      WHERE user_id = auth.uid()
    )
    OR 
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.uid() 
      AND role = 'SUPER_ADMIN'
    )
  );
```

## State Management Standards

### Zustand Store Patterns
```typescript
// ✅ CORRECT: Organization-scoped state management
interface OrganizationStore {
  currentOrganization: Organization | null;
  userPermissions: Permission[];
  setOrganization: (org: Organization) => void;
  hasPermission: (permission: string) => boolean;
  reset: () => void;
}

export const useOrganizationStore = create<OrganizationStore>((set, get) => ({
  currentOrganization: null,
  userPermissions: [],
  
  setOrganization: (org) => set({ 
    currentOrganization: org,
    userPermissions: calculatePermissions(org)
  }),
  
  hasPermission: (permission) => {
    const { userPermissions } = get();
    return userPermissions.some(p => p.name === permission);
  },
  
  reset: () => set({ 
    currentOrganization: null, 
    userPermissions: [] 
  })
}));
```

## Security Architecture Standards

### Authentication Patterns
```typescript
// ✅ CORRECT: Comprehensive auth validation
export async function validateUserAccess(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    throw new Error('Authentication required');
  }
  
  const user = await db.user.findUnique({
    where: { id: session.user.id },
    include: { 
      organization: true,
      permissions: true 
    }
  });
  
  if (!user) {
    throw new Error('User not found');
  }
  
  return {
    user,
    organization: user.organization,
    permissions: user.permissions,
    hasRole: (roles: UserRole[]) => roles.includes(user.role),
    hasPermission: (permission: string) => 
      user.permissions.some(p => p.name === permission)
  };
}
```

### Middleware Standards
```typescript
// ✅ CORRECT: Multi-tenant middleware
export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  // Portal-based routing
  if (pathname.startsWith('/super-admin')) {
    return await validateSuperAdminAccess(request);
  }
  
  if (pathname.startsWith('/event-manager')) {
    return await validateClientAccess(request);
  }
  
  if (pathname.startsWith('/affiliate')) {
    return await validateAffiliateAccess(request);
  }
  
  return NextResponse.next();
}
```

## Performance Standards

### Query Optimization Requirements
```typescript
// ✅ CORRECT: Optimized database queries
const getOrganizationQuotes = async (organizationId: string) => {
  return await db.quote.findMany({
    where: { organization_id: organizationId },
    select: {
      id: true,
      pickup_location: true,
      dropoff_location: true,
      pickup_datetime: true,
      status: true,
      // Only select needed fields
    },
    include: {
      responses: {
        select: {
          id: true,
          quoted_rate: true,
          affiliate_company: {
            select: { name: true, rating: true }
          }
        }
      }
    },
    orderBy: { created_at: 'desc' },
    take: 50 // Limit results
  });
};
```

### Caching Strategies
```typescript
// ✅ CORRECT: Redis caching implementation
import { redis } from '@/lib/redis';

export async function getCachedOrganizationData(orgId: string) {
  const cacheKey = `org:${orgId}:data`;
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Fetch from database
  const data = await fetchOrganizationData(orgId);
  
  // Cache for 5 minutes
  await redis.setex(cacheKey, 300, JSON.stringify(data));
  
  return data;
}
```

## Code Quality Standards

### Error Handling Patterns
```typescript
// ✅ CORRECT: Comprehensive error handling
export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code: string = 'INTERNAL_ERROR'
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export const handleApiError = (error: unknown) => {
  if (error instanceof AppError) {
    return NextResponse.json(
      { success: false, error: error.message, code: error.code },
      { status: error.statusCode }
    );
  }
  
  console.error('Unexpected error:', error);
  return NextResponse.json(
    { success: false, error: 'Internal server error' },
    { status: 500 }
  );
};
```

### Testing Standards
```typescript
// ✅ CORRECT: Comprehensive test coverage
describe('Quote API', () => {
  it('should enforce organization isolation', async () => {
    const org1Quote = await createTestQuote({ organization_id: 'org1' });
    const org2User = await createTestUser({ organization_id: 'org2' });
    
    const response = await request(app)
      .get(`/api/quotes/${org1Quote.id}`)
      .set('Authorization', `Bearer ${org2User.token}`);
    
    expect(response.status).toBe(403);
  });
  
  it('should validate business rules', async () => {
    const invalidQuote = {
      pickup_datetime: new Date(Date.now() - 1000), // Past date
      passenger_count: 0
    };
    
    const response = await request(app)
      .post('/api/quotes')
      .send(invalidQuote);
    
    expect(response.status).toBe(400);
    expect(response.body.error).toContain('future');
  });
});
```

## Compliance Checklist

### Architecture Requirements
- [ ] Follows Next.js 14 App Router patterns
- [ ] Implements proper TypeScript typing
- [ ] Uses Zod for input validation
- [ ] Enforces multi-tenant isolation
- [ ] Implements role-based access control
- [ ] Follows security best practices
- [ ] Optimizes database queries
- [ ] Implements proper error handling
- [ ] Includes comprehensive testing
- [ ] Uses consistent coding patterns

## Four-Tier Account Architecture Standards

### Account Types
The platform operates on a four-tier account architecture that must be respected in all implementations:

- **`transflow_super_admin`**: Ultimate platform control + network operations capability
- **`tnc_account`**: Network coordination + customer portal management (mini-SaaS provider)
- **`tnc_customer`**: Managed by parent TNC with inherited permissions and network access
- **`direct_client`**: Independent Transflow relationship using shared affiliate network

### TNC Customer Management Standards
- TNCs MUST be able to create customer accounts under their hierarchy
- Customer portal provisioning MUST be TNC-controlled with branded interfaces
- Customer branding MUST inherit from parent TNC, not Transflow
- Customer network access MUST inherit from parent TNC (isolated or shared)
- Customer pricing and features MUST be controlled by parent TNC

### Portal Access Control Standards
#### Super Admin Portal Access
- **`SUPER_ADMIN`**: Full access (platform tenancy + ops menu)
- **`TNC_ADMIN`**: Limited access (customer tenancy + ops menu, NO platform tenancy)
- **Platform tenancy**: Organization management, TNC creation, platform-wide settings
- **Customer tenancy**: TNC's customer account management, portal provisioning

#### Event Manager Portal Access
- All account types use same `/event-manager` portal with adaptive UI
- UI complexity determined by account type and feature flags
- Branding determined by account type (TNC-branded vs Transflow-branded)
- Feature access controlled by parent TNC for TNC customers

### Network Inheritance Standards
- **TNC customers**: MUST inherit affiliate network access from parent TNC
- **Direct clients**: MUST use Transflow shared affiliate network only
- **TNCs**: Can choose isolated affiliate network OR shared network access
- **Network isolation**: TNC customers cannot access networks outside their parent's scope
