---
inclusion: always
---

# Database Function Compliance Standards

## SUPER_ADMIN Override Requirements (MANDATORY)

### 1. SUPER_ADMIN Bypass Logic Pattern
```sql
-- ✅ REQUIRED: Every database function must include this pattern
DECLARE
    is_super_admin BOOLEAN;
BEGIN
    -- Check if user is SUPER_ADMIN
    is_super_admin := is_super_admin_safe();
    
    -- Validation with SUPER_ADMIN override
    IF NOT is_super_admin AND NOT meets_normal_requirements() THEN
        RAISE EXCEPTION 'Insufficient permissions or requirements not met';
    END IF;
    
    -- SUPER_ADMIN override logging
    IF is_super_admin AND override_action THEN
        INSERT INTO audit_logs (action, user_id, details, created_at) 
        VALUES ('super_admin_override', auth.uid(), 
                jsonb_build_object('function', 'function_name', 'reason', 'platform_administration'),
                NOW());
    END IF;
END;
```

### 2. Permission Template Validation Pattern
```sql
-- ✅ REQUIRED: Validate permission templates with SUPER_ADMIN override
IF NOT is_super_admin THEN
    -- Check organization permission template
    IF NOT EXISTS (
        SELECT 1 FROM organizations
        WHERE id = target_org_id
        AND permission_template IN ('premium_client', 'tnc_enterprise')
    ) THEN
        RAISE EXCEPTION 'Organization permission template insufficient for this operation';
    END IF;
    
    -- Check granular permissions
    IF NOT EXISTS (
        SELECT 1 FROM organization_permissions
        WHERE organization_id = target_org_id
        AND permission_key = 'required.permission'
        AND permission_value = true
    ) THEN
        RAISE EXCEPTION 'Organization lacks required granular permission';
    END IF;
END IF;
```

### 3. Feature Flag Validation Pattern
```sql
-- ✅ REQUIRED: Validate feature flags with SUPER_ADMIN override
IF NOT is_super_admin THEN
    -- Check if feature is enabled
    IF NOT EXISTS (
        SELECT 1 FROM organizations
        WHERE id = target_org_id
        AND feature_flags->>'requiredFeature' = 'true'
    ) THEN
        RAISE EXCEPTION 'Required feature not enabled for this organization';
    END IF;
END IF;
```

### 4. Subscription Plan Validation Pattern
```sql
-- ✅ REQUIRED: Validate subscription requirements with SUPER_ADMIN override
IF NOT is_super_admin THEN
    -- Check subscription plan for enterprise features
    IF NOT EXISTS (
        SELECT 1 FROM organizations
        WHERE id = target_org_id
        AND subscription_plan IN ('enterprise', 'tnc_enterprise')
    ) THEN
        RAISE EXCEPTION 'Enterprise subscription required for this operation';
    END IF;
END IF;
```

### 5. Four-Tier Account Architecture Validation
```sql
-- ✅ REQUIRED: Validate account types and hierarchy
IF NOT is_super_admin THEN
    -- Validate account type
    IF NOT EXISTS (
        SELECT 1 FROM organizations
        WHERE id = target_org_id
        AND account_type = 'expected_account_type'
    ) THEN
        RAISE EXCEPTION 'Invalid account type for this operation';
    END IF;
    
    -- Validate TNC hierarchy for TNC customers
    IF account_type = 'tnc_customer' AND NOT EXISTS (
        SELECT 1 FROM organizations
        WHERE id = target_org_id
        AND parent_tnc_id = expected_parent_tnc_id
    ) THEN
        RAISE EXCEPTION 'Invalid TNC customer hierarchy';
    END IF;
END IF;
```

## Audit Trail Requirements

### 1. SUPER_ADMIN Action Logging
```sql
-- ✅ REQUIRED: Log all SUPER_ADMIN override actions
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    action VARCHAR(100) NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    organization_id UUID REFERENCES organizations(id),
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ✅ REQUIRED: Logging pattern in functions
IF is_super_admin AND performing_override THEN
    INSERT INTO audit_logs (action, user_id, organization_id, details)
    VALUES (
        'super_admin_function_override',
        auth.uid(),
        target_org_id,
        jsonb_build_object(
            'function_name', 'current_function_name',
            'override_reason', 'platform_administration',
            'parameters', jsonb_build_object('param1', value1, 'param2', value2)
        )
    );
END IF;
```

### 2. Compliance Tracking
```sql
-- ✅ REQUIRED: Track compliance-related actions
INSERT INTO audit_logs (action, user_id, details)
VALUES (
    'compliance_validation',
    auth.uid(),
    jsonb_build_object(
        'validation_type', 'permission_template',
        'result', 'passed',
        'checked_permissions', ARRAY['permission1', 'permission2']
    )
);
```

## TNC Customer Portal Compliance

### 1. TNC Hierarchy Validation
```sql
-- ✅ REQUIRED: Validate TNC customer relationships
IF NOT is_super_admin THEN
    -- Validate TNC customer belongs to correct parent
    IF NOT EXISTS (
        SELECT 1 FROM organizations customer
        JOIN organizations parent ON parent.id = customer.parent_tnc_id
        WHERE customer.id = p_customer_org_id
        AND customer.account_type = 'tnc_customer'
        AND parent.id = p_parent_tnc_id
        AND parent.account_type = 'tnc_admin'
    ) THEN
        RAISE EXCEPTION 'Invalid TNC customer-parent relationship';
    END IF;
END IF;
```

### 2. Portal Provisioning Validation
```sql
-- ✅ REQUIRED: Validate portal provisioning permissions
IF NOT is_super_admin THEN
    -- Check if TNC can provision customer portals
    IF NOT EXISTS (
        SELECT 1 FROM organization_permissions
        WHERE organization_id = p_parent_tnc_id
        AND permission_key = 'tnc.manage_customers'
        AND permission_value = true
    ) THEN
        RAISE EXCEPTION 'TNC lacks customer management permissions';
    END IF;
    
    -- Check portal provisioning feature flag
    IF NOT EXISTS (
        SELECT 1 FROM organizations
        WHERE id = p_parent_tnc_id
        AND feature_flags->>'customPortalProvisioning' = 'true'
    ) THEN
        RAISE EXCEPTION 'Custom portal provisioning not enabled';
    END IF;
END IF;
```

## Error Handling Standards

### 1. Consistent Error Messages
```sql
-- ✅ REQUIRED: Use consistent error message format
RAISE EXCEPTION 'PERMISSION_DENIED: %', detailed_message;
RAISE EXCEPTION 'SUBSCRIPTION_REQUIRED: %', subscription_requirement;
RAISE EXCEPTION 'FEATURE_DISABLED: %', feature_name;
RAISE EXCEPTION 'HIERARCHY_INVALID: %', hierarchy_issue;
```

### 2. Error Context Information
```sql
-- ✅ REQUIRED: Provide context in error messages
RAISE EXCEPTION 'Permission denied: User % lacks permission % for organization %', 
    auth.uid(), required_permission, target_org_id;
```

## Function Security Standards

### 1. Security Definer Usage
```sql
-- ✅ REQUIRED: Use SECURITY DEFINER for elevated permissions
CREATE OR REPLACE FUNCTION function_name(...)
RETURNS return_type
LANGUAGE plpgsql
SECURITY DEFINER  -- Required for functions that need elevated permissions
AS $function_body$
```

### 2. Input Validation
```sql
-- ✅ REQUIRED: Validate all input parameters
IF p_parameter IS NULL OR p_parameter = '' THEN
    RAISE EXCEPTION 'Parameter cannot be null or empty';
END IF;

-- Validate UUID parameters
IF NOT (p_uuid_param ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$') THEN
    RAISE EXCEPTION 'Invalid UUID format';
END IF;
```

## Compliance Checklist

### Database Function Requirements
- [ ] Includes SUPER_ADMIN bypass logic using `is_super_admin_safe()`
- [ ] Validates permission templates with SUPER_ADMIN override
- [ ] Checks feature flags with SUPER_ADMIN override
- [ ] Validates subscription plans for enterprise features
- [ ] Implements four-tier account architecture validation
- [ ] Includes audit logging for SUPER_ADMIN actions
- [ ] Uses consistent error message format
- [ ] Validates all input parameters
- [ ] Uses SECURITY DEFINER when appropriate
- [ ] Implements proper TNC hierarchy validation

### Prohibited Patterns
- ❌ Functions without SUPER_ADMIN override capability
- ❌ Hardcoded permission checks without template validation
- ❌ Missing feature flag validation
- ❌ No audit logging for administrative actions
- ❌ Inconsistent error handling
- ❌ Missing input validation
- ❌ Breaking four-tier account architecture

### Testing Requirements
- [ ] Test SUPER_ADMIN override functionality
- [ ] Test permission template validation
- [ ] Test feature flag enforcement
- [ ] Test subscription plan requirements
- [ ] Test audit logging functionality
- [ ] Test error handling and messages
- [ ] Test four-tier account architecture compliance