# API Consistency Standards

## tRPC Procedure Standards

### Standard Response Patterns
```typescript
// ✅ CORRECT: Consistent success response
return {
  success: true,
  data: result,
  message: "Operation completed successfully"
};

// ✅ CORRECT: Consistent error response
throw new TRPCError({
  code: 'BAD_REQUEST',
  message: 'Invalid input parameters',
  cause: validationError
});
```

### Input Validation Requirements
```typescript
// ✅ CORRECT: Zod schema validation
const createQuoteSchema = z.object({
  organization_id: z.string().uuid(),
  pickup_location: z.string().min(1),
  dropoff_location: z.string().min(1),
  pickup_datetime: z.date(),
  passenger_count: z.number().min(1).max(50)
});

export const createQuote = publicProcedure
  .input(createQuoteSchema)
  .mutation(async ({ input, ctx }) => {
    // Implementation
  });
```

### Error Handling Standards
```typescript
// ✅ CORRECT: Standardized error codes
const API_ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN', 
  NOT_FOUND: 'NOT_FOUND',
  BAD_REQUEST: 'BAD_REQUEST',
  INTERNAL_ERROR: 'INTERNAL_SERVER_ERROR',
  VALIDATION_ERROR: 'BAD_REQUEST',
  ORGANIZATION_ACCESS_DENIED: 'FORBIDDEN',
  ROLE_INSUFFICIENT: 'FORBIDDEN'
} as const;
```

### Authentication Context Pattern
```typescript
// ✅ CORRECT: Consistent auth context
export const protectedProcedure = publicProcedure.use(async ({ ctx, next }) => {
  const { user, organization, permissions } = await validateUserAccess(ctx.req);
  
  return next({
    ctx: {
      ...ctx,
      user,
      organization,
      permissions
    }
  });
});
```

## REST API Standards (Legacy Support)

### Endpoint Naming Conventions
```typescript
// ✅ CORRECT: RESTful naming
GET    /api/organizations/{id}/quotes
POST   /api/organizations/{id}/quotes
PUT    /api/organizations/{id}/quotes/{quoteId}
DELETE /api/organizations/{id}/quotes/{quoteId}

// ❌ WRONG: Inconsistent naming
GET /api/getQuotes
POST /api/quote-create
```

### Response Format Standards
```typescript
// ✅ CORRECT: Consistent response structure
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: PaginationMeta;
    timestamp: string;
  };
}
```

## Database Query Standards

### Query Optimization Requirements
```typescript
// ✅ CORRECT: Optimized with proper includes
const quotes = await db.quote.findMany({
  where: { organization_id: orgId },
  include: {
    organization: {
      select: { name: true, slug: true }
    },
    responses: {
      include: {
        affiliate_company: {
          select: { name: true, rating: true }
        }
      }
    }
  },
  orderBy: { created_at: 'desc' },
  take: 20
});

// ❌ WRONG: N+1 query potential
const quotes = await db.quote.findMany({
  where: { organization_id: orgId }
});
// Separate queries for each quote's responses
```

### Pagination Standards
```typescript
// ✅ CORRECT: Cursor-based pagination for large datasets
const paginationInput = z.object({
  cursor: z.string().optional(),
  limit: z.number().min(1).max(100).default(20)
});

// ✅ CORRECT: Offset pagination for small datasets
const offsetPaginationInput = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20)
});
```

## Validation Requirements

### Input Sanitization
- All user inputs must be validated with Zod schemas
- SQL injection prevention through parameterized queries
- XSS prevention through proper escaping
- File upload validation and sanitization

### Business Logic Validation
```typescript
// ✅ CORRECT: Business rule validation
const validateQuoteBusinessRules = (input: CreateQuoteInput) => {
  // Pickup time must be in future
  if (input.pickup_datetime <= new Date()) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Pickup time must be in the future'
    });
  }
  
  // Validate organization subscription limits
  if (!organization.canCreateQuotes()) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Organization has reached quote limit'
    });
  }
};
```

## Performance Standards

### Response Time Requirements
- API responses must complete within 2 seconds
- Database queries should be under 500ms
- Complex operations should use background jobs
- Implement proper caching strategies

### Caching Patterns
```typescript
// ✅ CORRECT: Redis caching for expensive operations
const getCachedOrganizationStats = async (orgId: string) => {
  const cacheKey = `org:${orgId}:stats`;
  const cached = await redis.get(cacheKey);
  
  if (cached) {
    return JSON.parse(cached);
  }
  
  const stats = await calculateOrganizationStats(orgId);
  await redis.setex(cacheKey, 300, JSON.stringify(stats)); // 5min cache
  
  return stats;
};
```

## Testing Requirements

### API Testing Standards
- Unit tests for all tRPC procedures
- Integration tests for complex workflows
- Authentication and authorization tests
- Performance and load testing
- Multi-tenant isolation testing

### Required Test Coverage
- [ ] Input validation tests
- [ ] Authentication/authorization tests
- [ ] Business logic tests
- [ ] Error handling tests
- [ ] Performance benchmarks
- [ ] Multi-tenant isolation tests