# Transportation Business Logic Standards

## Core Business Entity Validation

### Quote Business Rules
```typescript
// ✅ CORRECT: Quote validation
const validateQuoteRules = (input: CreateQuoteInput) => {
  // Temporal validation
  if (input.pickup_datetime <= new Date()) {
    throw new Error('Pickup time must be in the future');
  }
  
  if (input.pickup_datetime > addDays(new Date(), 365)) {
    throw new Error('Pickup time cannot be more than 1 year in advance');
  }
  
  // Location validation
  if (input.pickup_location === input.dropoff_location) {
    throw new Error('Pickup and dropoff locations cannot be the same');
  }
  
  // Passenger validation
  if (input.passenger_count < 1 || input.passenger_count > 50) {
    throw new Error('Passenger count must be between 1 and 50');
  }
  
  // Organization subscription limits
  if (!organization.canCreateQuotes()) {
    throw new Error('Organization has reached quote limit for current subscription');
  }
};
```

### Event Business Rules
```typescript
// ✅ CORRECT: Event validation
const validateEventRules = (input: CreateEventInput) => {
  // Event must have at least one quote
  if (!input.quote_ids || input.quote_ids.length === 0) {
    throw new Error('Event must include at least one quote');
  }
  
  // All quotes must belong to same organization
  const orgIds = input.quotes.map(q => q.organization_id);
  if (new Set(orgIds).size > 1) {
    throw new Error('All quotes in event must belong to same organization');
  }
  
  // Event date validation
  if (input.event_date <= new Date()) {
    throw new Error('Event date must be in the future');
  }
  
  // Capacity validation
  const totalPassengers = input.quotes.reduce((sum, q) => sum + q.passenger_count, 0);
  if (totalPassengers > 500) {
    throw new Error('Event exceeds maximum passenger capacity');
  }
};
```

### Vehicle Assignment Rules
```typescript
// ✅ CORRECT: Vehicle assignment validation
const validateVehicleAssignment = (vehicle: Vehicle, quote: Quote) => {
  // Capacity check
  if (vehicle.passenger_capacity < quote.passenger_count) {
    throw new Error('Vehicle capacity insufficient for passenger count');
  }
  
  // Vehicle availability
  if (!isVehicleAvailable(vehicle.id, quote.pickup_datetime)) {
    throw new Error('Vehicle not available at requested time');
  }
  
  // Geographic coverage
  if (!isLocationInServiceArea(vehicle.service_area, quote.pickup_location)) {
    throw new Error('Vehicle does not service pickup location');
  }
  
  // Vehicle type compatibility
  if (quote.special_requirements && !vehicle.meets_requirements(quote.special_requirements)) {
    throw new Error('Vehicle does not meet special requirements');
  }
};
```

## Workflow State Management

### Quote Lifecycle States
```typescript
const QUOTE_STATES = {
  DRAFT: 'draft',
  PENDING: 'pending',
  RESPONDED: 'responded',
  ACCEPTED: 'accepted',
  REJECTED: 'rejected',
  EXPIRED: 'expired',
  CANCELLED: 'cancelled'
} as const;

// ✅ CORRECT: State transition validation
const validateQuoteStateTransition = (currentState: string, newState: string) => {
  const validTransitions = {
    draft: ['pending', 'cancelled'],
    pending: ['responded', 'expired', 'cancelled'],
    responded: ['accepted', 'rejected', 'expired'],
    accepted: ['cancelled'],
    rejected: [],
    expired: [],
    cancelled: []
  };
  
  if (!validTransitions[currentState]?.includes(newState)) {
    throw new Error(`Invalid state transition from ${currentState} to ${newState}`);
  }
};
```

### Trip Lifecycle States
```typescript
const TRIP_STATES = {
  SCHEDULED: 'scheduled',
  CONFIRMED: 'confirmed',
  EN_ROUTE_PICKUP: 'en_route_pickup',
  ARRIVED_PICKUP: 'arrived_pickup',
  PASSENGER_ONBOARD: 'passenger_onboard',
  EN_ROUTE_DROPOFF: 'en_route_dropoff',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  NO_SHOW: 'no_show'
} as const;
```

## Pricing and Rate Card Logic

### Rate Calculation Rules
```typescript
// ✅ CORRECT: Rate calculation with business rules
const calculateQuoteRate = (quote: Quote, rateCard: RateCard) => {
  let baseRate = rateCard.base_rate;
  
  // Distance-based pricing
  const distance = calculateDistance(quote.pickup_location, quote.dropoff_location);
  const distanceRate = distance * rateCard.per_mile_rate;
  
  // Time-based adjustments
  const hour = quote.pickup_datetime.getHours();
  if (hour >= 22 || hour <= 6) {
    baseRate *= 1.5; // Night surcharge
  }
  
  // Weekend surcharge
  const dayOfWeek = quote.pickup_datetime.getDay();
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    baseRate *= 1.2;
  }
  
  // Passenger count adjustment
  if (quote.passenger_count > rateCard.base_passenger_count) {
    const extraPassengers = quote.passenger_count - rateCard.base_passenger_count;
    baseRate += extraPassengers * rateCard.per_passenger_rate;
  }
  
  // Minimum fare enforcement
  const totalRate = Math.max(baseRate + distanceRate, rateCard.minimum_fare);
  
  return {
    base_rate: baseRate,
    distance_rate: distanceRate,
    total_rate: totalRate,
    surcharges: calculateSurcharges(quote, rateCard)
  };
};
```

### Affiliate Response Validation
```typescript
// ✅ CORRECT: Affiliate response business rules
const validateAffiliateResponse = (response: AffiliateResponse, quote: Quote) => {
  // Response timing
  if (response.created_at > quote.expires_at) {
    throw new Error('Response submitted after quote expiration');
  }
  
  // Rate reasonableness check
  if (response.quoted_rate < quote.minimum_acceptable_rate) {
    throw new Error('Quoted rate below minimum acceptable rate');
  }
  
  if (response.quoted_rate > quote.maximum_acceptable_rate * 2) {
    throw new Error('Quoted rate unreasonably high');
  }
  
  // Vehicle assignment validation
  if (response.assigned_vehicle_id) {
    await validateVehicleAssignment(response.assigned_vehicle, quote);
  }
  
  // Affiliate network participation
  if (!isAffiliateInNetwork(response.affiliate_company_id, quote.organization_id)) {
    throw new Error('Affiliate not authorized for this organization network');
  }
};
```

## Geographic and Temporal Logic

### Service Area Validation
```typescript
// ✅ CORRECT: Geographic service validation
const validateServiceArea = (location: string, serviceAreas: ServiceArea[]) => {
  const coordinates = geocodeLocation(location);
  
  const isInServiceArea = serviceAreas.some(area => 
    isPointInPolygon(coordinates, area.boundary_polygon)
  );
  
  if (!isInServiceArea) {
    throw new Error('Location outside of service area');
  }
  
  return true;
};
```

### Time Zone Handling
```typescript
// ✅ CORRECT: Timezone-aware scheduling
const validateSchedulingTime = (datetime: Date, timezone: string) => {
  const localTime = convertToTimezone(datetime, timezone);
  const hour = localTime.getHours();
  
  // Business hours validation
  if (hour < 6 || hour > 23) {
    throw new Error('Pickup time outside of service hours (6 AM - 11 PM)');
  }
  
  // Lead time validation
  const leadTime = datetime.getTime() - Date.now();
  const minimumLeadTime = 2 * 60 * 60 * 1000; // 2 hours
  
  if (leadTime < minimumLeadTime) {
    throw new Error('Minimum 2-hour advance booking required');
  }
};
```

## Multi-Tenant Business Rules

### Organization-Specific Logic
```typescript
// ✅ CORRECT: Organization type-specific behavior
const applyOrganizationRules = (organization: Organization, operation: string) => {
  switch (organization.organization_type) {
    case 'shared':
      // Shared affiliate network, common pricing
      return applySharedNetworkRules(organization, operation);
      
    case 'segregated':
      // Isolated network, custom pricing
      return applySegregatedNetworkRules(organization, operation);
      
    case 'isolated':
      // Complete isolation, enterprise rules
      return applyIsolatedNetworkRules(organization, operation);
      
    default:
      throw new Error('Invalid organization type');
  }
};
```

### Subscription-Based Feature Gates
```typescript
// ✅ CORRECT: Feature access based on subscription
const validateFeatureAccess = (organization: Organization, feature: string) => {
  const subscription = organization.subscription_plan;
  
  const featureMatrix = {
    free_trial: ['basic_quotes', 'basic_events'],
    professional: ['basic_quotes', 'basic_events', 'rate_negotiation', 'analytics'],
    enterprise: ['all_features']
  };
  
  const allowedFeatures = featureMatrix[subscription] || [];
  
  if (!allowedFeatures.includes(feature) && !allowedFeatures.includes('all_features')) {
    throw new Error(`Feature ${feature} not available for ${subscription} subscription`);
  }
};
```

## Validation Requirements

### Business Logic Checklist
- [ ] All temporal validations include timezone handling
- [ ] Geographic validations use proper coordinate systems
- [ ] State transitions follow defined workflows
- [ ] Pricing calculations include all business rules
- [ ] Multi-tenant rules are organization-type aware
- [ ] Subscription limits are enforced
- [ ] Capacity and availability checks are performed
- [ ] Service area boundaries are respected