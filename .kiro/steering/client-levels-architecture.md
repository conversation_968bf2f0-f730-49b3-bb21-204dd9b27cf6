---
inclusion: always
---

# WWMS DIY Client Levels Architecture

## 🏗️ Three-Tier Multi-Tenancy System

### Organization Types (Data Isolation Levels)
- **shared**: Basic SaaS model, shared affiliate network
- **segregated**: Custom branding, isolated customer bases  
- **isolated**: Complete data isolation, enterprise-level separation

### White Label Features (Can Apply to Any Organization Type)
- **has_white_labeling**: Custom branding and UI customization
- **has_custom_domain**: Custom domain support
- **has_custom_branding**: Logo, colors, and theme customization

### Subscription Plans
- **Free Trial** ($0): Basic features for new users
- **Professional** ($149/mo): Advanced features for growing businesses
- **Enterprise** ($499/mo): Full features for large organizations

### Permission Templates (Set by SUPER_ADMIN)
- **basic_client**: Core features, no financial access
- **premium_client**: Enhanced features, financial visibility
- **tnc_enterprise**: Full platform access, network management

## 🎛️ Super Admin Control
SUPER_ADMIN users control CLIENT capabilities through:
1. Organization creation with type/subscription selection
2. Permission template application (basic/premium/enterprise)
3. Granular permission customization
4. User-specific permission overrides

## 🔄 CLIENT Experience Variation
Same CLIENT role provides different experiences:
- CLIENT + shared + free_trial + basic_client = Limited capabilities
- CLIENT + segregated + professional + premium_client = Enhanced features
- CLIENT + isolated + enterprise + tnc_enterprise + white_labeling = Full platform access

## 🚀 CLIENT Organization Role Hierarchy

### Standard CLIENT Role
**Base CLIENT** provides core transportation booking capabilities:
- ✅ Create quotes and events
- ✅ Create and manage PASSENGER users within their organization
- ✅ View affiliate responses
- ✅ Accept/reject offers
- ✅ Basic analytics and reporting
- ❌ Cannot edit quotes after submission
- ❌ Cannot negotiate rates directly
- ❌ Limited financial visibility

### Enhanced CLIENT_COORDINATOR Role
**CLIENT_COORDINATOR** inherits all CLIENT permissions plus:
- ✅ **Edit quotes after submission**
- ✅ **Negotiate rates directly**
- ✅ **Manage rate cards**
- ✅ **Coordinate multiple events simultaneously**
- ✅ **Enhanced operational permissions**
- ✅ **Event-specific assignments** (can be assigned to coordinate specific events)
- ✅ **Cross-event visibility** (can see and manage multiple events)

### PASSENGER Role (Created by CLIENT users)
**PASSENGER** users have limited, consumer-focused capabilities:
- ✅ View their own trips and bookings
- ✅ Basic profile management
- ✅ Trip notifications and updates
- ❌ Cannot create events or quotes
- ❌ Cannot manage other users
- ❌ No administrative access

## 🏢 Account Type Classifications

```typescript
export type AccountType = 
  | 'super_admin'      // Platform administration
  | 'tnc_admin'        // TNC Admin - manages multiple client organizations
  | 'super_client'     // Super Client (custom permissions)
  | 'regular_client'   // Regular Client - standard SaaS customer
  | 'affiliate'        // Transportation service providers
  | 'driver'           // Vehicle operators
```

## 🔄 Permission Inheritance Model

```text
Organization Configuration (Set by Super Admin)
    ↓
Permission Template Applied (Basic/Premium/Enterprise)
    ↓
User Role Assignment (CLIENT, CLIENT_COORDINATOR, etc.)
    ↓
Individual User Permissions (Overrides & Customizations)
    ↓
Final User Experience
```

## 📊 Client Level Matrix

| Level | Org Type | Subscription | Template | Key Features |
|-------|----------|--------------|----------|--------------|
| **Basic** | `shared` | `free_trial` | `basic_client` | Core features, shared network |
| **Professional** | `segregated` | `professional` | `premium_client` | Enhanced features, custom branding |
| **Enterprise** | `isolated` | `enterprise` | `tnc_enterprise` | Full features, complete isolation |

### White Label Feature Matrix
| Organization Type | Can Have White Label | Custom Domain | Full Branding |
|-------------------|---------------------|---------------|---------------|
| **shared** | ✅ (with Enterprise subscription) | ❌ | ✅ |
| **segregated** | ✅ (with Professional+ subscription) | ✅ | ✅ |
| **isolated** | ✅ (included with Enterprise) | ✅ | ✅ |

## 🏗️ CLIENT Organization Structure

### Organization-Based Data Isolation
```text
CLIENT Organization: "City Tours Transportation"
├── CLIENT users (primary bookers/managers)
│   ├── <EMAIL> (CLIENT role)
│   └── <EMAIL> (CLIENT_COORDINATOR role)
├── PASSENGER users (created by CLIENT users)
│   ├── <EMAIL> (created by john)
│   └── <EMAIL> (created by sarah)
├── Events & Quotes (managed by CLIENT/CLIENT_COORDINATOR)
├── Organization Settings (subscription, permissions, branding)
└── Preferred Affiliate Network (organization-specific)
```

### CLIENT_COORDINATOR Event Assignments
```typescript
// Special coordinator-event relationships
client_coordinator_events: {
  coordinator_id: string,     // Links to CLIENT_COORDINATOR user
  event_id: string,          // Which event they coordinate
  permissions: JSONB,        // Event-specific permissions
  role: "primary" | "assistant" | "observer",
  created_at: timestamp
}
```

## 📋 Implementation Notes
- Always check organization permissions, not just user role
- Respect the permission template system in UI/API design
- Allow SUPER_ADMIN override capabilities
- Maintain organization-based data isolation
- CLIENT_COORDINATOR inherits all CLIENT permissions plus enhancements
- PASSENGER users inherit organization context from their creating CLIENT user
- Same base roles provide vastly different experiences based on configuration
- CLIENT organization roles are separate from affiliate company roles

## ⚠️ Architectural Debt - White Label Issue
**Current Problem**: `white_label` is treated as an organization type instead of a feature flag
**Correct Architecture**: 
- Organization types should be: `shared`, `segregated`, `isolated`
- White labeling should be feature flags: `has_white_labeling`, `has_custom_domain`, `has_custom_branding`
- Any organization type should be able to have white label features based on subscription level