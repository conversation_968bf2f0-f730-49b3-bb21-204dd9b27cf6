---
inclusion: always
---

# WWMS DIY User Roles - Critical Reference

## 🎯 PRIMARY PLATFORM ROLES (Always Valid)

```typescript
const PRIMARY_PLATFORM_ROLES = [
    'SUPER_ADMIN',      // Platform administration
    'CLIENT',           // Organizations that book transportation
    'PASSENGER',        // Individual passengers (created by <PERSON><PERSON><PERSON><PERSON>)
    'AFFILIATE',        // Transportation service providers
    'DISPATCHER',       // Affiliate sub-role for operations
    'DRIVER'            // Vehicle operators
];
```

## ✅ ENHANCED PLATFORM ROLES (Also Valid)

```typescript
const ENHANCED_PLATFORM_ROLES = [
    'CLIENT_COORDINATOR',   // Enhanced CLIENT with additional permissions
    'TNC_ADMIN',           // Transportation Network Company Admin
    'TENANT_ADMIN',        // Tenant-level administrator
    'TENANT_MANAGER'       // Tenant-level manager
];
```

## 🏢 ORGANIZATION-LEVEL ROLES

### CLIENT Organization Roles (Within CLIENT Organizations)
```typescript
const CLIENT_ORGANIZATION_ROLES = [
    'CLIENT',              // Primary organization booker/manager
    'CLIENT_COORDINATOR',  // Enhanced CLIENT with additional permissions
    'PASSENGER'            // Individual passengers (created by CLIENT users)
];
```

### Affiliate Company Roles (Within Affiliate Companies)
```typescript
const AFFILIATE_COMPANY_ROLES = [
    'MANAGER',             // Company-level management role
    'DISPATCHER',          // Company-level operations
    'DRIVER'              // Company-level vehicle operators
];
```

## 🔄 MULTI-TIER AUTHORIZATION PATTERNS

### CLIENT Organization Pattern
**Platform Level**: User has platform role (`CLIENT`, `CLIENT_COORDINATOR`, `PASSENGER`)
**Organization Level**: User belongs to specific CLIENT organization with permissions

**Example**: A CLIENT user in "City Tours Transportation" organization can create PASSENGER users and manage events within that organization.

### Affiliate Company Pattern  
**Platform Level**: User has platform role (`AFFILIATE`, `DISPATCHER`, `DRIVER`)
**Company Level**: User has company role (`MANAGER`, `DISPATCHER`, `DRIVER`) in affiliate_users table

**Example**: An AFFILIATE user with MANAGER role in a specific company can manage that company's vehicles.

### Special CLIENT_COORDINATOR Pattern
**Platform Level**: User has `CLIENT_COORDINATOR` role
**Organization Level**: Enhanced permissions within CLIENT organization
**Event Level**: Can be assigned to specific events with granular permissions

**Example**: A CLIENT_COORDINATOR can edit quotes after submission and manage rate cards across multiple events.

## 🚨 DEPRECATED ROLES (Never Use)
- ❌ 'CUSTOMER' → Use 'CLIENT'
- ❌ 'EVENT_MANAGER' → Use 'CLIENT' (portal is /event-manager/)
- ❌ 'AFFILIATE_ADMIN' → Use 'AFFILIATE'
- ❌ 'AFFILIATE_USER' → Use 'AFFILIATE' or 'DISPATCHER'
- ❌ 'CUSTOMER_COORDINATOR' → Use 'CLIENT_COORDINATOR'
- ❌ 'AFFILIATE_DISPATCHER' → Use 'DISPATCHER'

## 📍 Role-Portal Mapping
- SUPER_ADMIN → /super-admin/
- CLIENT → /event-manager/ (the "bookers")
- CLIENT_COORDINATOR → /event-manager/ (enhanced CLIENT)
- PASSENGER → Login access only (created by CLIENT)
- AFFILIATE → /affiliate/
- DISPATCHER → /affiliate/ (sub-role)
- DRIVER → /driver/
- TNC_ADMIN → /super-admin/ (network management)

## 🔑 Key Business Logic

### CLIENT Organization Hierarchy
- **CLIENT** creates and manages PASSENGER users within their organization
- **CLIENT_COORDINATOR** has enhanced permissions (edit quotes, manage rate cards, coordinate events)
- **PASSENGER** users are created BY CLIENT users and belong to the same organization
- Same CLIENT role has different capabilities based on organization settings (shared/segregated/white_label)

### Affiliate Company Hierarchy  
- **AFFILIATE** users can own/manage multiple affiliate companies
- **DISPATCHER** is a sub-role of AFFILIATE (both use /affiliate/ portal)
- **MANAGER** is a company-level role within specific affiliate companies
- **DRIVER** users belong to specific affiliate companies

### Cross-System Control
- **SUPER_ADMIN** controls all client levels, organizations, and permissions
- **TNC_ADMIN** manages multiple client organizations (network-level management)
- Organization-level roles (like MANAGER) are valid within their specific contexts

## 📋 Validation Guidelines

### Context-Specific Role Usage
- ✅ **Platform roles** in authentication and profile management
- ✅ **Organization roles** in CLIENT organization APIs (event management, passenger creation)
- ✅ **Company roles** in affiliate company APIs (vehicle management, driver assignment)
- ✅ **Multi-tier authorization**: platform role + organization/company role + permissions

### Role Validation Patterns
- ✅ CLIENT organization context: Check CLIENT/CLIENT_COORDINATOR + organization permissions
- ✅ Affiliate company context: Check AFFILIATE/DISPATCHER + company role (MANAGER/DISPATCHER/DRIVER)
- ✅ Cross-organization context: Check SUPER_ADMIN or TNC_ADMIN roles
- ❌ Never mix CLIENT organization roles with affiliate company roles in same validation

### Special Cases
- CLIENT_COORDINATOR can have event-specific assignments and permissions
- PASSENGER users inherit organization context from their creating CLIENT user
- SUPER_ADMIN can override all role-based restrictions