# Database Operations Standards

## Database Query Requirements

### Always Use access-db MCP Tool
- **MANDATORY**: All database queries MUST use the `mcp_access_db_query` tool
- **NO EXCEPTIONS**: Never use direct database connections, psql commands, or other database tools
- **READ-ONLY**: The access-db tool is for read-only operations only

### Database Write Operations Protocol
- **MIGRATION FILES ONLY**: All database schema changes, data modifications, and writes MUST go through Supabase migration files
- **LOCATION**: Create migration files in `supabase/migrations/` directory
- **NAMING**: Use format `XXX_descriptive_name.sql` where XXX is the next sequential number
- **PUSH COMMAND**: After creating migration files, use `supabase db push` to apply changes

### Prohibited Database Operations
- ❌ Direct SQL execution outside of migration files
- ❌ Using psql commands for schema changes
- ❌ Modifying database structure through scripts
- ❌ Any database writes that bypass the migration system

### Required Database Operation Flow
1. **For Queries**: Use `mcp_access_db_query` tool with SQL query
2. **For Schema Changes**: Create migration file in `supabase/migrations/`
3. **For Data Changes**: Create migration file with INSERT/UPDATE/DELETE statements
4. **Apply Changes**: Run `supabase db push` command
5. **Verify**: Use `mcp_access_db_query` to confirm changes applied correctly

### Tenant ID Elimination Compliance
- **MANDATORY**: All database operations must use `organization_id` ONLY
- **FORBIDDEN**: NO `tenant_id` usage in any database operations
- **VALIDATION**: All queries must include `organization_id` filtering for multi-tenant isolation

### Migration File Best Practices
- Include descriptive comments explaining the purpose
- Use transactions when appropriate
- Include rollback instructions in comments
- Test migration files before pushing
- Follow existing naming conventions

### Example Migration File Structure
```sql
-- Migration: XXX_descriptive_name.sql
-- Purpose: Brief description of what this migration does
-- Date: YYYY-MM-DD

BEGIN;

-- ✅ CORRECT: Use organization_id for multi-tenant tables
CREATE TABLE new_table (
  id UUID PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  data JSONB
);

-- ✅ CORRECT: RLS policy with organization_id
CREATE POLICY "new_table_organization_isolation" ON new_table
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id FROM user_profiles 
      WHERE user_id = auth.uid()
    )
  );

-- ❌ FORBIDDEN: Never use tenant_id
-- CREATE TABLE new_table (
--   id UUID PRIMARY KEY,
--   tenant_id UUID, -- ❌ FORBIDDEN
--   data JSONB
-- );

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- DROP POLICY "new_table_organization_isolation" ON new_table;
-- DROP TABLE new_table;
```

### Enforcement
- All database operations will be monitored
- Any direct database modifications outside of this protocol will be flagged
- Migration files are the single source of truth for database state
- Use access-db MCP tool for all database inspection and verification