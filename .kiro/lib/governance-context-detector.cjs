/**
 * Governance Context Detector
 * Analyzes file paths and content to determine appropriate governance context
 */

class GovernanceContextDetector {
  constructor() {
    this.contextPatterns = this.initializePatterns();
  }

  /**
   * Initialize file pattern mappings for different governance contexts
   * Patterns are ordered by specificity - more specific patterns first
   */
  initializePatterns() {
    return {
      // Database patterns (most specific)
      database: {
        patterns: [
          /supabase\/migrations\/.*\.sql$/,
          /migrations\/.*\.sql$/,
          /.*migration.*\.sql$/i,
          /.*schema.*\.sql$/i,
          /.*\.sql$/
        ],
        template: 'DATABASE_ARCHITECTURE',
        fileType: 'database migration',
        priority: 1
      },
      
      // Organization patterns (high specificity)
      organizations: {
        patterns: [
          /app\/api\/organizations\/.*\.(ts|js)$/,
          /app\/contexts\/OrganizationContext\.(tsx|jsx)$/,
          /app\/lib\/organization\/.*\.(ts|js)$/,
          /components\/.*\/organization.*\.(tsx|jsx)$/i,
          /.*organization.*\.(ts|js|tsx|jsx)$/i,
          /.*tenant.*\.(ts|js|tsx|jsx)$/i,
          /.*white.*label.*\.(ts|js|tsx|jsx)$/i
        ],
        template: 'ORGANIZATION_CONTROLS',
        fileType: 'organization-related',
        priority: 2
      },
      
      // Subscription patterns (high specificity)
      subscriptions: {
        patterns: [
          /app\/api\/subscriptions\/.*\.(ts|js)$/,
          /app\/api\/billing\/.*\.(ts|js)$/,
          /app\/lib\/billing\/.*\.(ts|js)$/,
          /app\/lib\/subscriptions\/.*\.(ts|js)$/,
          /components\/.*\/subscription.*\.(tsx|jsx)$/i,
          /components\/.*\/billing.*\.(tsx|jsx)$/i,
          /.*subscription.*\.(ts|js|tsx|jsx)$/i,
          /.*billing.*\.(ts|js|tsx|jsx)$/i,
          /.*payment.*\.(ts|js|tsx|jsx)$/i,
          /.*plan.*\.(ts|js|tsx|jsx)$/i
        ],
        template: 'SUBSCRIPTION_ARCHITECTURE',
        fileType: 'subscription-related',
        priority: 2
      },
      
      // Permission patterns (broader, lower priority)
      permissions: {
        patterns: [
          /app\/lib\/auth\/.*\.(ts|js)$/,
          /components\/.*\/permissions\/.*\.(tsx|jsx)$/,
          /app\/middleware\.(ts|js)$/,
          /lib\/auth\/.*\.(ts|js)$/,
          /.*permission.*\.(ts|js|tsx|jsx)$/i,
          /.*auth.*\.(ts|js|tsx|jsx)$/i,
          /.*role.*\.(ts|js|tsx|jsx)$/i,
          // Generic API routes last (lowest priority)
          /app\/api\/.*\.(ts|js)$/
        ],
        template: 'PERMISSION_ARCHITECTURE',
        fileType: 'permission-related',
        priority: 3
      }
    };
  }

  /**
   * Detect governance context based on file path
   * @param {string} filePath - Path of the modified file
   * @returns {Object} Context information including template type and file type
   */
  detectContext(filePath) {
    try {
      // Validate input
      if (!filePath || typeof filePath !== 'string') {
        return this.getDefaultContext('invalid-path');
      }

      // Normalize file path
      const normalizedPath = filePath.replace(/\\/g, '/');
      
      // Sort contexts by priority (lower number = higher priority)
      const sortedContexts = Object.entries(this.contextPatterns)
        .sort(([, a], [, b]) => (a.priority || 999) - (b.priority || 999));
      
      // Check each context pattern with error handling, in priority order
      for (const [contextName, contextConfig] of sortedContexts) {
        try {
          for (const pattern of contextConfig.patterns) {
            if (pattern.test(normalizedPath)) {
              return {
                context: contextName,
                template: contextConfig.template,
                fileType: contextConfig.fileType,
                filePath: normalizedPath,
                confidence: this.calculateConfidence(normalizedPath, contextConfig.patterns),
                matchedPattern: pattern.source,
                priority: contextConfig.priority || 999
              };
            }
          }
        } catch (patternError) {
          console.warn(`Error testing pattern for context ${contextName}:`, patternError.message);
          continue;
        }
      }

      // Default context if no specific pattern matches
      return this.getDefaultContext(normalizedPath);
    } catch (error) {
      console.error('Error in detectContext:', error);
      return this.getDefaultContext(filePath, error);
    }
  }

  /**
   * Calculate confidence score for context detection
   * @param {string} filePath - File path to analyze
   * @param {RegExp[]} patterns - Patterns to match against
   * @returns {number} Confidence score between 0 and 1
   */
  calculateConfidence(filePath, patterns) {
    let matchCount = 0;
    let totalPatterns = patterns.length;
    
    for (const pattern of patterns) {
      if (pattern.test(filePath)) {
        matchCount++;
      }
    }
    
    // Base confidence on pattern matches and file path specificity
    const patternScore = matchCount / totalPatterns;
    const specificityScore = this.calculateSpecificity(filePath);
    
    return Math.min((patternScore * 0.7) + (specificityScore * 0.3), 1.0);
  }

  /**
   * Calculate specificity score based on file path characteristics
   * @param {string} filePath - File path to analyze
   * @returns {number} Specificity score between 0 and 1
   */
  calculateSpecificity(filePath) {
    let score = 0.5; // Base score
    
    // Higher score for API routes
    if (filePath.includes('/api/')) score += 0.2;
    
    // Higher score for specific directories
    if (filePath.includes('/auth/') || filePath.includes('/permissions/')) score += 0.2;
    if (filePath.includes('/organizations/') || filePath.includes('/billing/')) score += 0.2;
    if (filePath.includes('/migrations/')) score += 0.3;
    
    // Higher score for TypeScript files
    if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) score += 0.1;
    
    // Lower score for very generic files
    if (filePath.includes('index.') || filePath.includes('utils.')) score -= 0.1;
    
    return Math.max(0, Math.min(score, 1.0));
  }

  /**
   * Detect multiple contexts if file affects multiple areas
   * @param {string} filePath - File path to analyze
   * @returns {Object[]} Array of context objects
   */
  detectMultipleContexts(filePath) {
    const contexts = [];
    const normalizedPath = filePath.replace(/\\/g, '/');
    
    for (const [contextName, contextConfig] of Object.entries(this.contextPatterns)) {
      for (const pattern of contextConfig.patterns) {
        if (pattern.test(normalizedPath)) {
          const confidence = this.calculateConfidence(normalizedPath, contextConfig.patterns);
          
          contexts.push({
            context: contextName,
            template: contextConfig.template,
            fileType: contextConfig.fileType,
            filePath: normalizedPath,
            confidence
          });
          break; // Only add each context once
        }
      }
    }
    
    // Sort by confidence score (highest first)
    return contexts.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Get recommended template based on file analysis
   * @param {string} filePath - File path to analyze
   * @param {string} [fileContent] - Optional file content for deeper analysis
   * @returns {string} Recommended template type
   */
  getRecommendedTemplate(filePath, fileContent = '') {
    const primaryContext = this.detectContext(filePath);
    
    // If we have file content, do additional analysis
    if (fileContent) {
      const contentContext = this.analyzeFileContent(fileContent);
      if (contentContext && contentContext.confidence > primaryContext.confidence) {
        return contentContext.template;
      }
    }
    
    return primaryContext.template;
  }

  /**
   * Analyze file content for governance context clues
   * @param {string} content - File content to analyze
   * @returns {Object|null} Context information or null if no strong indicators
   */
  analyzeFileContent(content) {
    const contentIndicators = {
      permissions: [
        /permission/gi,
        /role/gi,
        /auth/gi,
        /hasPermission/gi,
        /checkPermission/gi,
        /validateUserRole/gi,
        /SUPER_ADMIN/gi
      ],
      organizations: [
        /organization/gi,
        /tenant/gi,
        /white.?label/gi,
        /organization_type/gi,
        /shared|segregated|isolated/gi
      ],
      subscriptions: [
        /subscription/gi,
        /billing/gi,
        /plan/gi,
        /payment/gi,
        /free_trial|professional|enterprise/gi
      ],
      database: [
        /CREATE TABLE/gi,
        /ALTER TABLE/gi,
        /INSERT INTO/gi,
        /UPDATE.*SET/gi,
        /migration/gi,
        /RLS/gi,
        /POLICY/gi
      ]
    };
    
    let bestMatch = null;
    let highestScore = 0;
    
    for (const [contextName, indicators] of Object.entries(contentIndicators)) {
      let score = 0;
      for (const indicator of indicators) {
        const matches = content.match(indicator);
        if (matches) {
          score += matches.length;
        }
      }
      
      if (score > highestScore) {
        highestScore = score;
        bestMatch = {
          context: contextName,
          template: this.contextPatterns[contextName]?.template || 'GENERAL_GOVERNANCE',
          confidence: Math.min(score / 10, 1.0) // Normalize score
        };
      }
    }
    
    return bestMatch;
  }

  /**
   * Get default context when no patterns match or errors occur
   * @param {string} filePath - File path that caused the issue
   * @param {Error} [error] - Optional error that occurred
   * @returns {Object} Default context object
   */
  getDefaultContext(filePath, error = null) {
    return {
      context: 'general',
      template: 'GENERAL_GOVERNANCE',
      fileType: 'general',
      filePath: filePath || 'unknown',
      confidence: 0.1,
      error: error ? error.message : null,
      fallback: true
    };
  }

  /**
   * Validate file pattern matching system
   * @returns {Object} Validation results
   */
  validatePatternSystem() {
    const results = {
      valid: true,
      errors: [],
      warnings: [],
      contextCount: 0,
      patternCount: 0
    };

    try {
      for (const [contextName, contextConfig] of Object.entries(this.contextPatterns)) {
        results.contextCount++;
        
        // Validate context configuration
        if (!contextConfig.template) {
          results.errors.push(`Context ${contextName} missing template`);
          results.valid = false;
        }
        
        if (!contextConfig.fileType) {
          results.warnings.push(`Context ${contextName} missing fileType`);
        }
        
        if (!Array.isArray(contextConfig.patterns)) {
          results.errors.push(`Context ${contextName} patterns is not an array`);
          results.valid = false;
          continue;
        }
        
        // Validate each pattern
        for (let i = 0; i < contextConfig.patterns.length; i++) {
          results.patternCount++;
          const pattern = contextConfig.patterns[i];
          
          try {
            // Test pattern validity
            if (!(pattern instanceof RegExp)) {
              results.errors.push(`Context ${contextName} pattern ${i} is not a RegExp`);
              results.valid = false;
              continue;
            }
            
            // Test pattern with sample paths
            const testPaths = [
              'app/api/test.ts',
              'components/test.tsx',
              'supabase/migrations/test.sql'
            ];
            
            testPaths.forEach(testPath => {
              try {
                pattern.test(testPath);
              } catch (testError) {
                results.errors.push(`Context ${contextName} pattern ${i} failed test: ${testError.message}`);
                results.valid = false;
              }
            });
            
          } catch (patternError) {
            results.errors.push(`Context ${contextName} pattern ${i} error: ${patternError.message}`);
            results.valid = false;
          }
        }
      }
    } catch (systemError) {
      results.errors.push(`System validation error: ${systemError.message}`);
      results.valid = false;
    }

    return results;
  }

  /**
   * Test pattern matching against a set of sample files
   * @param {string[]} testFiles - Array of test file paths
   * @returns {Object} Test results
   */
  testPatternMatching(testFiles = []) {
    const defaultTestFiles = [
      'app/api/organizations/route.ts',
      'app/lib/auth/permissions.ts',
      'components/ui/permissions/PermissionGate.tsx',
      'app/contexts/OrganizationContext.tsx',
      'app/api/subscriptions/route.ts',
      'app/lib/billing/stripe.ts',
      'supabase/migrations/001_create_organizations.sql',
      'app/middleware.ts',
      'app/hooks/usePermissions.ts',
      'app/types/subscription.ts'
    ];

    const filesToTest = testFiles.length > 0 ? testFiles : defaultTestFiles;
    const results = {
      totalFiles: filesToTest.length,
      matched: 0,
      unmatched: 0,
      errors: 0,
      details: []
    };

    for (const filePath of filesToTest) {
      try {
        const context = this.detectContext(filePath);
        const detail = {
          filePath,
          context: context.context,
          template: context.template,
          confidence: context.confidence,
          matched: context.context !== 'general'
        };

        if (detail.matched) {
          results.matched++;
        } else {
          results.unmatched++;
        }

        results.details.push(detail);
      } catch (error) {
        results.errors++;
        results.details.push({
          filePath,
          error: error.message,
          matched: false
        });
      }
    }

    return results;
  }

  /**
   * Get context summary for debugging
   * @param {string} filePath - File path to analyze
   * @returns {Object} Detailed context analysis
   */
  getContextSummary(filePath) {
    try {
      const primaryContext = this.detectContext(filePath);
      const allContexts = this.detectMultipleContexts(filePath);
      
      return {
        primaryContext,
        allContexts,
        recommendations: {
          template: primaryContext.template,
          confidence: primaryContext.confidence,
          multiContext: allContexts.length > 1
        },
        systemHealth: this.validatePatternSystem()
      };
    } catch (error) {
      return {
        error: error.message,
        primaryContext: this.getDefaultContext(filePath, error),
        allContexts: [],
        recommendations: {
          template: 'GENERAL_GOVERNANCE',
          confidence: 0.1,
          multiContext: false
        }
      };
    }
  }
}

// Export singleton instance
module.exports = new GovernanceContextDetector();