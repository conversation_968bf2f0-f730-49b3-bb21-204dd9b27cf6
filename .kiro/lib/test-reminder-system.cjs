#!/usr/bin/env node

/**
 * Test Utility for Governance Reminder System
 * Allows testing of reminder generation for different file types
 */

const GovernanceReminderGenerator = require('./governance-reminder-generator.cjs');
const path = require('path');

class ReminderSystemTester {
  constructor() {
    this.generator = GovernanceReminderGenerator;
  }

  /**
   * Run comprehensive tests
   */
  runTests() {
    console.log('🧪 Testing Governance Reminder System\n');

    // Test file patterns
    this.testFilePatterns();
    
    // Test template generation
    this.testTemplateGeneration();
    
    // Test steering file validation
    this.testSteeringFileValidation();
    
    // Test multi-file scenarios
    this.testMultiFileScenarios();

    console.log('\n✅ All tests completed!');
  }

  /**
   * Test file pattern detection
   */
  testFilePatterns() {
    console.log('📁 Testing File Pattern Detection\n');

    const testFiles = [
      // Permission files
      'app/api/auth/route.ts',
      'app/lib/auth/permissions.ts',
      'components/ui/permissions/PermissionGate.tsx',
      'app/middleware.ts',
      
      // Organization files
      'app/api/organizations/route.ts',
      'app/contexts/OrganizationContext.tsx',
      'components/organization/OrgSelector.tsx',
      
      // Subscription files
      'app/api/subscriptions/route.ts',
      'app/lib/billing/stripe.ts',
      'components/billing/SubscriptionPanel.tsx',
      
      // Database files
      'supabase/migrations/001_create_permissions.sql',
      'migrations/002_add_organizations.sql',
      
      // Edge cases
      'app/utils/helper.ts',
      'README.md',
      'package.json'
    ];

    testFiles.forEach(filePath => {
      const result = this.generator.testReminderGeneration(filePath);
      console.log(`📄 ${filePath}`);
      console.log(`   Context: ${result.detectedContext.context}`);
      console.log(`   Template: ${result.templateUsed}`);
      console.log(`   Confidence: ${(result.confidence * 100).toFixed(1)}%`);
      console.log(`   File Type: ${result.detectedContext.fileType}\n`);
    });
  }

  /**
   * Test template generation
   */
  testTemplateGeneration() {
    console.log('🎨 Testing Template Generation\n');

    const templates = this.generator.getAvailableTemplates();
    
    templates.forEach(template => {
      const metadata = this.generator.getTemplateMetadata(template);
      console.log(`📋 Template: ${template}`);
      if (metadata) {
        console.log(`   Title: ${metadata.title}`);
        console.log(`   Sections: ${metadata.sections.length}`);
        metadata.sections.forEach(section => {
          console.log(`     - ${section}`);
        });
      }
      console.log();
    });
  }

  /**
   * Test steering file validation
   */
  testSteeringFileValidation() {
    console.log('📚 Testing Steering File Validation\n');

    const validation = this.generator.validateSteeringFile();
    
    console.log(`📁 Steering File: ${validation.path}`);
    console.log(`   Exists: ${validation.exists ? '✅' : '❌'}`);
    
    if (validation.exists) {
      console.log(`   Size: ${validation.size} bytes`);
      console.log(`   Has Content: ${validation.hasContent ? '✅' : '❌'}`);
      console.log(`   Last Modified: ${validation.lastModified}`);
      console.log('   Content Validation:');
      console.log(`     Permission Keys: ${validation.validation.hasPermissionKeys ? '✅' : '❌'}`);
      console.log(`     Organization Types: ${validation.validation.hasOrganizationTypes ? '✅' : '❌'}`);
      console.log(`     Subscription Plans: ${validation.validation.hasSubscriptionPlans ? '✅' : '❌'}`);
      console.log(`     Feature Flags: ${validation.validation.hasFeatureFlags ? '✅' : '❌'}`);
    } else if (validation.error) {
      console.log(`   Error: ${validation.error}`);
    }
    console.log();
  }

  /**
   * Test multi-file scenarios
   */
  testMultiFileScenarios() {
    console.log('📚 Testing Multi-File Scenarios\n');

    const scenarios = [
      {
        name: 'Mixed Permission Files',
        files: [
          'app/api/auth/route.ts',
          'app/lib/auth/permissions.ts',
          'components/ui/PermissionGate.tsx'
        ]
      },
      {
        name: 'Organization & Subscription',
        files: [
          'app/api/organizations/route.ts',
          'app/api/subscriptions/route.ts'
        ]
      },
      {
        name: 'Database Migration',
        files: [
          'supabase/migrations/001_permissions.sql'
        ]
      }
    ];

    scenarios.forEach(scenario => {
      console.log(`📋 Scenario: ${scenario.name}`);
      console.log(`   Files: ${scenario.files.length}`);
      
      const reminder = this.generator.generateMultiFileReminder(scenario.files);
      const lines = reminder.split('\n').length;
      const hasValidation = reminder.includes('Validation Checklist');
      const hasExamples = reminder.includes('```');
      
      console.log(`   Generated Lines: ${lines}`);
      console.log(`   Has Validation: ${hasValidation ? '✅' : '❌'}`);
      console.log(`   Has Examples: ${hasExamples ? '✅' : '❌'}`);
      console.log();
    });
  }

  /**
   * Generate sample reminder for demonstration
   */
  generateSampleReminder(filePath = 'app/api/auth/permissions.ts') {
    console.log(`🎯 Sample Reminder for: ${filePath}\n`);
    console.log('=' .repeat(80));
    
    const reminder = this.generator.generateReminder(filePath);
    console.log(reminder);
    
    console.log('=' .repeat(80));
  }

  /**
   * Test hook-based reminder generation
   */
  testHookReminders() {
    console.log('🪝 Testing Hook-Based Reminders\n');

    const hookScenarios = [
      {
        hook: 'permission-architecture-validator',
        files: ['app/lib/auth/permissions.ts']
      },
      {
        hook: 'organization-controls-validator',
        files: ['app/contexts/OrganizationContext.tsx']
      },
      {
        hook: 'subscription-architecture-validator',
        files: ['app/api/subscriptions/route.ts']
      },
      {
        hook: 'database-migration-governance',
        files: ['supabase/migrations/001_permissions.sql']
      }
    ];

    hookScenarios.forEach(scenario => {
      console.log(`🪝 Hook: ${scenario.hook}`);
      const reminder = this.generator.generateHookReminder(scenario.hook, scenario.files);
      const hasTitle = reminder.includes('VALIDATION');
      const hasChecklist = reminder.includes('Validation Checklist');
      
      console.log(`   Generated: ${hasTitle ? '✅' : '❌'}`);
      console.log(`   Has Checklist: ${hasChecklist ? '✅' : '❌'}`);
      console.log(`   Length: ${reminder.length} characters\n`);
    });
  }
}

// CLI interface
if (require.main === module) {
  const tester = new ReminderSystemTester();
  
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'test':
      tester.runTests();
      break;
      
    case 'sample':
      const filePath = args[1] || 'app/api/auth/permissions.ts';
      tester.generateSampleReminder(filePath);
      break;
      
    case 'hooks':
      tester.testHookReminders();
      break;
      
    case 'patterns':
      tester.testFilePatterns();
      break;
      
    case 'steering':
      tester.testSteeringFileValidation();
      break;
      
    default:
      console.log('🎛️ Governance Reminder System Tester\n');
      console.log('Usage:');
      console.log('  node test-reminder-system.js test     - Run all tests');
      console.log('  node test-reminder-system.js sample [file] - Generate sample reminder');
      console.log('  node test-reminder-system.js hooks    - Test hook reminders');
      console.log('  node test-reminder-system.js patterns - Test file patterns');
      console.log('  node test-reminder-system.js steering - Test steering file');
      console.log('\nExamples:');
      console.log('  node test-reminder-system.js sample app/api/auth/route.ts');
      console.log('  node test-reminder-system.js sample supabase/migrations/001_test.sql');
  }
}

module.exports = ReminderSystemTester;