#!/usr/bin/env node

/**
 * Complete Integration Test for Architectural Governance System
 * Tests all components working together in the development workflow
 */

const GovernanceReminderGenerator = require('./governance-reminder-generator.cjs');
const fs = require('fs');
const path = require('path');

class IntegrationTester {
  constructor() {
    this.generator = GovernanceReminderGenerator;
    this.testResults = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: []
    };
  }

  /**
   * Run complete integration test suite
   */
  async runCompleteTest() {
    console.log('🧪 ARCHITECTURAL GOVERNANCE SYSTEM - COMPLETE INTEGRATION TEST');
    console.log('=' .repeat(70));
    console.log('');

    // Test 1: System Health
    await this.testSystemHealth();

    // Test 2: Steering File Integration
    await this.testSteeringFileIntegration();

    // Test 3: Hook Configuration
    await this.testHookConfiguration();

    // Test 4: Reminder Generation
    await this.testReminderGeneration();

    // Test 5: Development Workflow Simulation
    await this.testDevelopmentWorkflow();

    // Test 6: Error Handling
    await this.testErrorHandling();

    // Print final results
    this.printFinalResults();

    return this.testResults;
  }

  /**
   * Test system health and component availability
   */
  async testSystemHealth() {
    console.log('1️⃣  Testing System Health...');
    
    try {
      const validation = this.generator.validateSystem();
      
      if (validation.healthy) {
        this.pass('System health check passed');
        console.log('   ✅ All components available and functional');
        
        // Check individual components
        if (validation.components.templateEngine?.available) {
          this.pass(`Template engine: ${validation.components.templateEngine.templateCount} templates`);
        }
        
        if (validation.components.contextDetector?.available) {
          this.pass(`Context detector: ${validation.components.contextDetector.contextCount} contexts`);
        }
        
        if (validation.components.steeringFile?.exists) {
          this.pass(`Steering file: ${validation.components.steeringFile.size} bytes`);
        }
      } else {
        this.fail('System health check failed');
        validation.errors.forEach(error => console.log(`   ❌ ${error}`));
      }

      validation.warnings.forEach(warning => {
        this.warn(warning);
      });

    } catch (error) {
      this.fail(`System health test error: ${error.message}`);
    }

    console.log('');
  }

  /**
   * Test steering file integration with AI context
   */
  async testSteeringFileIntegration() {
    console.log('2️⃣  Testing Steering File Integration...');
    
    try {
      const steeringValidation = this.generator.validateSteeringFile();
      
      if (steeringValidation.exists) {
        this.pass('Steering file exists and is accessible');
        
        // Check front-matter
        const content = fs.readFileSync(steeringValidation.path, 'utf8');
        if (content.includes('inclusion: always')) {
          this.pass('Steering file has correct front-matter for AI context inclusion');
        } else {
          this.fail('Steering file missing "inclusion: always" front-matter');
        }
        
        // Check content validation
        const validation = steeringValidation.validation;
        if (validation.hasPermissionKeys) {
          this.pass('Contains granular permission keys');
        } else {
          this.fail('Missing granular permission keys');
        }
        
        if (validation.hasOrganizationTypes) {
          this.pass('Contains organization type definitions');
        } else {
          this.fail('Missing organization type definitions');
        }
        
        if (validation.hasSubscriptionPlans) {
          this.pass('Contains subscription plan information');
        } else {
          this.fail('Missing subscription plan information');
        }
        
        if (validation.hasFeatureFlags) {
          this.pass('Contains feature flag definitions');
        } else {
          this.fail('Missing feature flag definitions');
        }
        
      } else {
        this.fail('Steering file not found or not accessible');
      }
      
    } catch (error) {
      this.fail(`Steering file integration test error: ${error.message}`);
    }

    console.log('');
  }

  /**
   * Test hook configuration and availability
   */
  async testHookConfiguration() {
    console.log('3️⃣  Testing Hook Configuration...');
    
    const expectedHooks = [
      'permission-architecture-validator.kiro.hook',
      'organization-controls-validator.kiro.hook',
      'subscription-architecture-validator.kiro.hook',
      'database-migration-governance.kiro.hook'
    ];

    try {
      const hooksDir = '.kiro/hooks';
      
      if (!fs.existsSync(hooksDir)) {
        this.fail('Hooks directory not found');
        return;
      }

      expectedHooks.forEach(hookFile => {
        const hookPath = path.join(hooksDir, hookFile);
        
        if (fs.existsSync(hookPath)) {
          try {
            const hookContent = fs.readFileSync(hookPath, 'utf8');
            const hookConfig = JSON.parse(hookContent);
            
            if (hookConfig.enabled) {
              this.pass(`Hook ${hookFile} is enabled and configured`);
            } else {
              this.warn(`Hook ${hookFile} is disabled`);
            }
            
            // Validate hook structure
            if (hookConfig.when && hookConfig.then) {
              this.pass(`Hook ${hookFile} has valid trigger configuration`);
            } else {
              this.fail(`Hook ${hookFile} has invalid configuration structure`);
            }
            
          } catch (parseError) {
            this.fail(`Hook ${hookFile} has invalid JSON: ${parseError.message}`);
          }
        } else {
          this.fail(`Hook ${hookFile} not found`);
        }
      });
      
    } catch (error) {
      this.fail(`Hook configuration test error: ${error.message}`);
    }

    console.log('');
  }

  /**
   * Test reminder generation for different file types
   */
  async testReminderGeneration() {
    console.log('4️⃣  Testing Reminder Generation...');
    
    const testCases = [
      {
        file: 'app/api/organizations/route.ts',
        expectedTemplate: 'ORGANIZATION_CONTROLS',
        description: 'Organization API route'
      },
      {
        file: 'app/lib/auth/permissions.ts',
        expectedTemplate: 'PERMISSION_ARCHITECTURE',
        description: 'Permission logic file'
      },
      {
        file: 'components/billing/SubscriptionPanel.tsx',
        expectedTemplate: 'SUBSCRIPTION_ARCHITECTURE',
        description: 'Subscription component'
      },
      {
        file: 'supabase/migrations/001_test.sql',
        expectedTemplate: 'DATABASE_ARCHITECTURE',
        description: 'Database migration'
      }
    ];

    try {
      testCases.forEach(testCase => {
        try {
          const result = this.generator.testReminderGeneration(testCase.file);
          
          if (result.templateUsed === testCase.expectedTemplate) {
            this.pass(`${testCase.description}: Correct template (${result.templateUsed})`);
          } else {
            this.warn(`${testCase.description}: Unexpected template (got ${result.templateUsed}, expected ${testCase.expectedTemplate})`);
          }
          
          if (result.generatedReminder && result.generatedReminder.length > 0) {
            this.pass(`${testCase.description}: Reminder generated (${result.generatedReminder.length} chars)`);
          } else {
            this.fail(`${testCase.description}: No reminder generated`);
          }
          
          if (result.confidence > 0) {
            this.pass(`${testCase.description}: Context detection confidence ${result.confidence.toFixed(2)}`);
          } else {
            this.warn(`${testCase.description}: Low context detection confidence`);
          }
          
        } catch (testError) {
          this.fail(`${testCase.description}: ${testError.message}`);
        }
      });
      
    } catch (error) {
      this.fail(`Reminder generation test error: ${error.message}`);
    }

    console.log('');
  }

  /**
   * Test development workflow simulation
   */
  async testDevelopmentWorkflow() {
    console.log('5️⃣  Testing Development Workflow Simulation...');
    
    const workflowScenarios = [
      {
        name: 'Single file modification',
        files: ['app/api/quotes/route.ts'],
        description: 'Developer modifies single API route'
      },
      {
        name: 'Multi-file permission change',
        files: [
          'app/lib/auth/permissions.ts',
          'components/permissions/PermissionGate.tsx',
          'app/api/auth/permissions/route.ts'
        ],
        description: 'Developer updates permission system across multiple files'
      },
      {
        name: 'Hook-triggered reminder',
        hookName: 'permission-architecture-validator',
        files: ['app/lib/auth/roles.ts'],
        description: 'Hook triggers reminder for permission-related file'
      }
    ];

    try {
      workflowScenarios.forEach(scenario => {
        try {
          let reminder;
          
          if (scenario.hookName) {
            // Test hook-triggered reminder
            reminder = this.generator.generateHookReminder(scenario.hookName, scenario.files);
            this.pass(`${scenario.name}: Hook reminder generated`);
          } else if (scenario.files.length === 1) {
            // Test single file reminder
            reminder = this.generator.generateReminder(scenario.files[0]);
            this.pass(`${scenario.name}: Single file reminder generated`);
          } else {
            // Test multi-file reminder
            reminder = this.generator.generateMultiFileReminder(scenario.files);
            this.pass(`${scenario.name}: Multi-file reminder generated`);
          }
          
          // Validate reminder content
          if (reminder.includes('SUPER_ADMIN')) {
            this.pass(`${scenario.name}: Contains SUPER_ADMIN references`);
          } else {
            this.warn(`${scenario.name}: Missing SUPER_ADMIN references`);
          }
          
          if (reminder.includes('steering/super-admin-controls-reference.md')) {
            this.pass(`${scenario.name}: References steering file`);
          } else {
            this.warn(`${scenario.name}: Missing steering file reference`);
          }
          
          if (reminder.includes('✅ CORRECT:') && reminder.includes('❌ WRONG:')) {
            this.pass(`${scenario.name}: Contains validation examples`);
          } else {
            this.warn(`${scenario.name}: Missing validation examples`);
          }
          
        } catch (scenarioError) {
          this.fail(`${scenario.name}: ${scenarioError.message}`);
        }
      });
      
    } catch (error) {
      this.fail(`Development workflow test error: ${error.message}`);
    }

    console.log('');
  }

  /**
   * Test error handling and graceful degradation
   */
  async testErrorHandling() {
    console.log('6️⃣  Testing Error Handling...');
    
    try {
      // Test with invalid file path
      const invalidFileReminder = this.generator.generateReminder('nonexistent/file.ts');
      if (invalidFileReminder.includes('SUPER_ADMIN CONTROLS VALIDATION')) {
        this.pass('Graceful handling of invalid file paths');
      } else {
        this.fail('Poor handling of invalid file paths');
      }
      
      // Test with empty file list
      const emptyListReminder = this.generator.generateMultiFileReminder([]);
      if (emptyListReminder.includes('Error') || emptyListReminder.includes('SUPER_ADMIN')) {
        this.pass('Graceful handling of empty file lists');
      } else {
        this.fail('Poor handling of empty file lists');
      }
      
      // Test with invalid hook name
      const invalidHookReminder = this.generator.generateHookReminder('nonexistent-hook', ['test.ts']);
      if (invalidHookReminder.includes('SUPER_ADMIN')) {
        this.pass('Graceful handling of invalid hook names');
      } else {
        this.fail('Poor handling of invalid hook names');
      }
      
    } catch (error) {
      this.fail(`Error handling test error: ${error.message}`);
    }

    console.log('');
  }

  /**
   * Record a passing test
   */
  pass(message) {
    this.testResults.passed++;
    this.testResults.details.push({ type: 'pass', message });
    console.log(`   ✅ ${message}`);
  }

  /**
   * Record a failing test
   */
  fail(message) {
    this.testResults.failed++;
    this.testResults.details.push({ type: 'fail', message });
    console.log(`   ❌ ${message}`);
  }

  /**
   * Record a warning
   */
  warn(message) {
    this.testResults.warnings++;
    this.testResults.details.push({ type: 'warn', message });
    console.log(`   ⚠️  ${message}`);
  }

  /**
   * Print final test results
   */
  printFinalResults() {
    console.log('📊 FINAL TEST RESULTS');
    console.log('=' .repeat(70));
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`⚠️  Warnings: ${this.testResults.warnings}`);
    console.log(`📈 Total Tests: ${this.testResults.passed + this.testResults.failed}`);
    
    const successRate = this.testResults.passed / (this.testResults.passed + this.testResults.failed) * 100;
    console.log(`🎯 Success Rate: ${successRate.toFixed(1)}%`);
    
    if (this.testResults.failed === 0) {
      console.log('');
      console.log('🎉 ALL TESTS PASSED! Architectural Governance System is fully integrated.');
    } else {
      console.log('');
      console.log('⚠️  Some tests failed. Review the issues above.');
    }
    
    console.log('');
    console.log('🔧 System Status: ' + (this.testResults.failed === 0 ? 'READY FOR PRODUCTION' : 'NEEDS ATTENTION'));
  }
}

// Run the test if called directly
if (require.main === module) {
  const tester = new IntegrationTester();
  tester.runCompleteTest().then(results => {
    process.exit(results.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Integration test failed:', error);
    process.exit(1);
  });
}

module.exports = IntegrationTester;