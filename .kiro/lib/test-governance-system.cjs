#!/usr/bin/env node

/**
 * Test script for Architectural Governance System
 * Tests hook trigger logic, file pattern matching, and error handling
 */

const GovernanceReminderGenerator = require('./governance-reminder-generator.cjs');
const GovernanceContextDetector = require('./governance-context-detector.cjs');
const ReminderTemplateEngine = require('./reminder-template-engine.cjs');

class GovernanceSystemTester {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: []
    };
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Testing Architectural Governance System\n');

    // System health tests
    await this.testSystemHealth();
    
    // File pattern matching tests
    await this.testFilePatternMatching();
    
    // Hook trigger logic tests
    await this.testHookTriggerLogic();
    
    // Error handling tests
    await this.testErrorHandling();
    
    // Template generation tests
    await this.testTemplateGeneration();

    // Print results
    this.printResults();
  }

  /**
   * Test system health and component initialization
   */
  async testSystemHealth() {
    console.log('🔍 Testing System Health...');
    
    try {
      // Test governance reminder generator
      const systemValidation = GovernanceReminderGenerator.validateSystem();
      this.addTest('System Health Check', systemValidation.healthy, 
        systemValidation.healthy ? 'All components initialized' : systemValidation.errors.join(', '));

      // Test context detector
      const patternValidation = GovernanceContextDetector.validatePatternSystem();
      this.addTest('Pattern System Validation', patternValidation.valid,
        patternValidation.valid ? `${patternValidation.contextCount} contexts, ${patternValidation.patternCount} patterns` : patternValidation.errors.join(', '));

      // Test template engine
      const templateValidation = ReminderTemplateEngine.validateTemplateSystem();
      this.addTest('Template System Validation', templateValidation.healthy,
        templateValidation.healthy ? `${templateValidation.templateCount} templates loaded` : templateValidation.errors.join(', '));

      // Test steering file
      const steeringValidation = GovernanceReminderGenerator.validateSteeringFile();
      this.addTest('Steering File Check', steeringValidation.exists && steeringValidation.hasContent,
        steeringValidation.exists ? `File exists (${steeringValidation.size} bytes)` : 'Steering file missing');

    } catch (error) {
      this.addTest('System Health Check', false, `System health test failed: ${error.message}`);
    }

    console.log('');
  }

  /**
   * Test file pattern matching
   */
  async testFilePatternMatching() {
    console.log('📁 Testing File Pattern Matching...');

    const testFiles = [
      // Permission-related files
      { path: 'app/api/auth/route.ts', expectedContext: 'permissions' },
      { path: 'app/lib/auth/permissions.ts', expectedContext: 'permissions' },
      { path: 'components/ui/permissions/PermissionGate.tsx', expectedContext: 'permissions' },
      { path: 'app/middleware.ts', expectedContext: 'permissions' },
      
      // Organization-related files
      { path: 'app/api/organizations/route.ts', expectedContext: 'organizations' },
      { path: 'app/contexts/OrganizationContext.tsx', expectedContext: 'organizations' },
      { path: 'app/lib/organization/settings.ts', expectedContext: 'organizations' },
      
      // Subscription-related files
      { path: 'app/api/subscriptions/route.ts', expectedContext: 'subscriptions' },
      { path: 'app/lib/billing/stripe.ts', expectedContext: 'subscriptions' },
      { path: 'components/billing/SubscriptionPanel.tsx', expectedContext: 'subscriptions' },
      
      // Database files
      { path: 'supabase/migrations/001_create_organizations.sql', expectedContext: 'database' },
      { path: 'migrations/002_add_permissions.sql', expectedContext: 'database' },
      
      // Edge cases
      { path: 'app/utils/random.ts', expectedContext: 'general' },
      { path: 'README.md', expectedContext: 'general' }
    ];

    for (const testFile of testFiles) {
      try {
        const context = GovernanceContextDetector.detectContext(testFile.path);
        const matched = context.context === testFile.expectedContext;
        
        this.addTest(
          `Pattern Match: ${testFile.path}`,
          matched,
          matched ? `Detected: ${context.context} (confidence: ${context.confidence.toFixed(2)})` : 
                   `Expected: ${testFile.expectedContext}, Got: ${context.context}`
        );
      } catch (error) {
        this.addTest(`Pattern Match: ${testFile.path}`, false, `Error: ${error.message}`);
      }
    }

    console.log('');
  }

  /**
   * Test hook trigger logic
   */
  async testHookTriggerLogic() {
    console.log('🪝 Testing Hook Trigger Logic...');

    const hookTests = [
      {
        hookName: 'permission-architecture-validator',
        filePaths: ['app/lib/auth/permissions.ts'],
        expectedTemplate: 'PERMISSION_ARCHITECTURE'
      },
      {
        hookName: 'organization-controls-validator',
        filePaths: ['app/contexts/OrganizationContext.tsx'],
        expectedTemplate: 'ORGANIZATION_CONTROLS'
      },
      {
        hookName: 'subscription-architecture-validator',
        filePaths: ['app/api/subscriptions/route.ts'],
        expectedTemplate: 'SUBSCRIPTION_ARCHITECTURE'
      },
      {
        hookName: 'database-migration-governance',
        filePaths: ['supabase/migrations/001_test.sql'],
        expectedTemplate: 'DATABASE_ARCHITECTURE'
      }
    ];

    for (const hookTest of hookTests) {
      try {
        const reminder = GovernanceReminderGenerator.generateHookReminder(
          hookTest.hookName,
          hookTest.filePaths
        );

        const hasExpectedContent = reminder.includes(hookTest.expectedTemplate.replace('_', ' '));
        const hasValidationChecklist = reminder.includes('Validation Checklist');
        const hasSteeringReference = reminder.includes('Reference Documentation');

        this.addTest(
          `Hook Trigger: ${hookTest.hookName}`,
          hasExpectedContent && hasValidationChecklist,
          hasExpectedContent ? 'Generated appropriate reminder' : 'Missing expected content'
        );

        if (!hasSteeringReference) {
          this.addWarning(`Hook ${hookTest.hookName} missing steering reference`);
        }

      } catch (error) {
        this.addTest(`Hook Trigger: ${hookTest.hookName}`, false, `Error: ${error.message}`);
      }
    }

    console.log('');
  }

  /**
   * Test error handling and graceful failures
   */
  async testErrorHandling() {
    console.log('⚠️  Testing Error Handling...');

    // Test invalid file paths
    try {
      const reminder1 = GovernanceReminderGenerator.generateReminder(null);
      this.addTest('Null File Path Handling', reminder1.includes('SUPER_ADMIN CONTROLS'), 
        'Generated fallback reminder');
    } catch (error) {
      this.addTest('Null File Path Handling', false, `Unexpected error: ${error.message}`);
    }

    // Test invalid template type
    try {
      const reminder2 = ReminderTemplateEngine.generateReminder('INVALID_TEMPLATE', {
        filePath: 'test.ts',
        fileType: 'test'
      });
      this.addTest('Invalid Template Handling', reminder2.includes('SUPER_ADMIN CONTROLS'),
        'Generated fallback reminder');
    } catch (error) {
      this.addTest('Invalid Template Handling', false, `Unexpected error: ${error.message}`);
    }

    // Test missing steering file scenario
    const originalPath = GovernanceReminderGenerator.steeringFilePath;
    GovernanceReminderGenerator.steeringFilePath = 'nonexistent/path.md';
    
    try {
      const reminder3 = GovernanceReminderGenerator.generateReminder('app/test.ts');
      this.addTest('Missing Steering File Handling', reminder3.includes('SUPER_ADMIN CONTROLS'),
        'Generated reminder without steering file');
    } catch (error) {
      this.addTest('Missing Steering File Handling', false, `Unexpected error: ${error.message}`);
    } finally {
      GovernanceReminderGenerator.steeringFilePath = originalPath;
    }

    // Test malformed file paths
    const malformedPaths = ['', '   ', '\\\\invalid\\path', 'file with spaces.ts'];
    for (const path of malformedPaths) {
      try {
        const context = GovernanceContextDetector.detectContext(path);
        this.addTest(`Malformed Path: "${path}"`, context.context === 'general',
          `Handled gracefully: ${context.context}`);
      } catch (error) {
        this.addTest(`Malformed Path: "${path}"`, false, `Error: ${error.message}`);
      }
    }

    console.log('');
  }

  /**
   * Test template generation for all contexts
   */
  async testTemplateGeneration() {
    console.log('📝 Testing Template Generation...');

    const templates = ReminderTemplateEngine.getAvailableTemplates();
    
    for (const templateType of templates) {
      try {
        const testContext = {
          filePath: 'test/file.ts',
          fileType: 'test file',
          context: 'test'
        };

        const reminder = ReminderTemplateEngine.generateReminder(templateType, testContext);
        
        const hasTitle = reminder.includes('**') && reminder.includes('VALIDATION');
        const hasChecklist = reminder.includes('Validation Checklist');
        const hasContent = reminder.length > 500; // Reasonable content length

        this.addTest(
          `Template Generation: ${templateType}`,
          hasTitle && hasChecklist && hasContent,
          hasContent ? `Generated ${reminder.length} characters` : 'Content too short'
        );

      } catch (error) {
        this.addTest(`Template Generation: ${templateType}`, false, `Error: ${error.message}`);
      }
    }

    console.log('');
  }

  /**
   * Add test result
   */
  addTest(name, passed, details) {
    if (passed) {
      this.testResults.passed++;
      console.log(`✅ ${name}: ${details}`);
    } else {
      this.testResults.failed++;
      console.log(`❌ ${name}: ${details}`);
    }

    this.testResults.details.push({ name, passed, details });
  }

  /**
   * Add warning
   */
  addWarning(message) {
    this.testResults.warnings++;
    console.log(`⚠️  Warning: ${message}`);
  }

  /**
   * Print final test results
   */
  printResults() {
    console.log('\n📊 Test Results Summary');
    console.log('========================');
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`⚠️  Warnings: ${this.testResults.warnings}`);
    console.log(`📊 Total: ${this.testResults.passed + this.testResults.failed}`);
    
    const successRate = (this.testResults.passed / (this.testResults.passed + this.testResults.failed) * 100).toFixed(1);
    console.log(`🎯 Success Rate: ${successRate}%`);

    if (this.testResults.failed === 0) {
      console.log('\n🎉 All tests passed! Governance system is working correctly.');
    } else {
      console.log('\n🔧 Some tests failed. Please review the errors above.');
    }

    // Return exit code for CI/CD
    return this.testResults.failed === 0 ? 0 : 1;
  }

  /**
   * Generate test report
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        warnings: this.testResults.warnings,
        successRate: (this.testResults.passed / (this.testResults.passed + this.testResults.failed) * 100).toFixed(1)
      },
      details: this.testResults.details,
      systemHealth: GovernanceReminderGenerator.validateSystem(),
      patternValidation: GovernanceContextDetector.validatePatternSystem(),
      templateValidation: ReminderTemplateEngine.validateTemplateSystem()
    };

    return report;
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new GovernanceSystemTester();
  tester.runAllTests().then(() => {
    process.exit(tester.testResults.failed === 0 ? 0 : 1);
  }).catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
  });
}

module.exports = GovernanceSystemTester;