/**
 * Governance Reminder Generator
 * Main interface for generating contextual SUPER_ADMIN governance reminders
 */

const ReminderTemplateEngine = require('./reminder-template-engine.cjs');
const GovernanceContextDetector = require('./governance-context-detector.cjs');
const fs = require('fs');
const path = require('path');

class GovernanceReminderGenerator {
  constructor() {
    this.templateEngine = ReminderTemplateEngine;
    this.contextDetector = GovernanceContextDetector;
    this.steeringFilePath = '.kiro/steering/super-admin-controls-reference.md';
  }

  /**
   * Generate reminder for a modified file
   * @param {string} filePath - Path of the modified file
   * @param {Object} options - Additional options
   * @returns {string} Generated reminder content
   */
  generateReminder(filePath, options = {}) {
    try {
      // Validate inputs
      if (!filePath) {
        return this.generateFallbackReminder('unknown', new Error('No file path provided'));
      }

      // Detect governance context with error handling
      const context = this.contextDetector.detectContext(filePath);
      
      // Check if context detection failed
      if (context.error) {
        console.warn(`Context detection warning for ${filePath}:`, context.error);
      }

      // Read file content if available for deeper analysis
      let fileContent = '';
      if (options.includeContent && this.isFileAccessible(filePath)) {
        try {
          fileContent = fs.readFileSync(filePath, 'utf8');
          // Re-analyze with content
          const contentContext = this.contextDetector.analyzeFileContent(fileContent);
          if (contentContext && contentContext.confidence > context.confidence) {
            context.template = contentContext.template;
            context.context = contentContext.context;
          }
        } catch (fileError) {
          console.warn(`Could not read file content for ${filePath}:`, fileError.message);
          // Continue without file content analysis
        }
      }

      // Validate template engine availability
      if (!this.templateEngine) {
        return this.generateFallbackReminder(filePath, new Error('Template engine not available'));
      }

      // Generate reminder using template engine with error handling
      let reminder;
      try {
        reminder = this.templateEngine.generateReminder(context.template, {
          filePath: context.filePath,
          fileType: context.fileType,
          context: context.context,
          confidence: context.confidence,
          ...options
        });
      } catch (templateError) {
        console.error('Template generation error:', templateError);
        reminder = this.templateEngine.generateReminder('GENERAL_GOVERNANCE', {
          filePath: context.filePath,
          fileType: context.fileType,
          context: 'general',
          confidence: 0.1,
          templateError: templateError.message,
          ...options
        });
      }

      // Add footer with steering file reference
      const reminderWithFooter = this.addSteeringReference(reminder);

      return reminderWithFooter;

    } catch (error) {
      console.error('Error generating governance reminder:', error);
      return this.generateFallbackReminder(filePath, error);
    }
  }

  /**
   * Generate reminder for multiple files
   * @param {string[]} filePaths - Array of file paths
   * @param {Object} options - Additional options
   * @returns {string} Combined reminder content
   */
  generateMultiFileReminder(filePaths, options = {}) {
    if (!filePaths || filePaths.length === 0) {
      return this.generateFallbackReminder('unknown', new Error('No files provided'));
    }

    if (filePaths.length === 1) {
      return this.generateReminder(filePaths[0], options);
    }

    // Analyze all files to determine primary context
    const contexts = filePaths.map(filePath => 
      this.contextDetector.detectContext(filePath)
    );

    // Group by template type
    const templateGroups = {};
    contexts.forEach((context, index) => {
      const template = context.template;
      if (!templateGroups[template]) {
        templateGroups[template] = [];
      }
      templateGroups[template].push({
        filePath: filePaths[index],
        context
      });
    });

    // Generate combined reminder
    let combinedReminder = `🎛️ **MULTI-FILE SUPER_ADMIN CONTROLS VALIDATION** 🎛️\n\n`;
    combinedReminder += `Multiple files have been modified that may affect SUPER_ADMIN controls:\n\n`;

    // List all files
    filePaths.forEach(filePath => {
      combinedReminder += `- \`${filePath}\`\n`;
    });
    combinedReminder += '\n';

    // Generate reminders for each template type
    for (const [template, files] of Object.entries(templateGroups)) {
      if (files.length > 0) {
        const primaryFile = files[0];
        const sectionReminder = this.templateEngine.generateReminder(template, {
          filePath: `${files.length} files`,
          fileType: primaryFile.context.fileType,
          context: primaryFile.context.context,
          multiFile: true,
          fileList: files.map(f => f.filePath)
        });

        combinedReminder += '\n---\n\n' + sectionReminder;
      }
    }

    return this.addSteeringReference(combinedReminder);
  }

  /**
   * Generate reminder based on hook trigger
   * @param {string} hookName - Name of the triggered hook
   * @param {string[]} filePaths - Array of modified file paths
   * @param {Object} options - Additional options
   * @returns {string} Generated reminder content
   */
  generateHookReminder(hookName, filePaths, options = {}) {
    try {
      // Map hook names to template types
      const hookTemplateMap = {
        'permission-architecture-validator': 'PERMISSION_ARCHITECTURE',
        'organization-controls-validator': 'ORGANIZATION_CONTROLS',
        'subscription-architecture-validator': 'SUBSCRIPTION_ARCHITECTURE',
        'database-migration-governance': 'DATABASE_ARCHITECTURE'
      };

      const templateType = hookTemplateMap[hookName];
      
      if (templateType && filePaths.length > 0) {
        const context = this.contextDetector.detectContext(filePaths[0]);
        const reminder = this.templateEngine.generateReminder(templateType, {
          filePath: filePaths.length === 1 ? filePaths[0] : `${filePaths.length} files`,
          fileType: context.fileType,
          context: context.context,
          hookTriggered: hookName,
          ...options
        });

        // Add steering reference for hook-triggered reminders
        return this.addSteeringReference(reminder);
      }

      // Fallback to multi-file reminder
      return this.generateMultiFileReminder(filePaths, options);
    } catch (error) {
      console.error('Error generating hook reminder:', error);
      return this.generateFallbackReminder(filePaths[0] || 'unknown', error);
    }
  }

  /**
   * Add steering file reference to reminder
   * @param {string} reminder - Original reminder content
   * @returns {string} Reminder with steering reference
   */
  addSteeringReference(reminder) {
    const steeringReference = `

---

## 📚 **Reference Documentation**

For complete SUPER_ADMIN control capabilities, see: \`${this.steeringFilePath}\`

This steering file contains:
- Complete list of 20 granular permission keys
- Organization types and subscription plans
- Permission template definitions
- Feature flags and white-label controls
- Code examples and usage patterns

---

**Remember: Always leverage existing SUPER_ADMIN capabilities instead of creating new permission systems!**`;

    return reminder + steeringReference;
  }

  /**
   * Generate fallback reminder when errors occur
   * @param {string} filePath - File path that caused the error
   * @param {Error} error - Error that occurred
   * @returns {string} Fallback reminder content
   */
  generateFallbackReminder(filePath, error) {
    return `🎛️ **SUPER_ADMIN CONTROLS VALIDATION** 🎛️

A file has been modified that may affect SUPER_ADMIN controls: \`${filePath}\`

⚠️ **Note**: Error occurred during reminder generation: ${error.message}

## Quick Validation Checklist

- [ ] Uses existing granular permission keys (20 available)
- [ ] Respects organization types (shared/segregated/isolated)
- [ ] Leverages subscription plans and permission templates
- [ ] Implements proper multi-tenant data isolation
- [ ] Considers feature flags and white-label controls

## Reference

Please refer to \`${this.steeringFilePath}\` for complete SUPER_ADMIN control capabilities.

**Always leverage existing capabilities instead of creating new permission systems!**`;
  }

  /**
   * Get available reminder templates
   * @returns {string[]} Array of available template names
   */
  getAvailableTemplates() {
    return this.templateEngine.getAvailableTemplates();
  }

  /**
   * Get template metadata
   * @param {string} templateType - Template type to get metadata for
   * @returns {Object|null} Template metadata or null if not found
   */
  getTemplateMetadata(templateType) {
    return this.templateEngine.getTemplateMetadata(templateType);
  }

  /**
   * Test reminder generation for a file
   * @param {string} filePath - File path to test
   * @returns {Object} Test results including context detection and reminder
   */
  testReminderGeneration(filePath) {
    const context = this.contextDetector.detectContext(filePath);
    const allContexts = this.contextDetector.detectMultipleContexts(filePath);
    const reminder = this.generateReminder(filePath);

    return {
      filePath,
      detectedContext: context,
      allPossibleContexts: allContexts,
      generatedReminder: reminder,
      templateUsed: context.template,
      confidence: context.confidence
    };
  }

  /**
   * Check if file is accessible for reading
   * @param {string} filePath - Path to check
   * @returns {boolean} True if file is accessible
   */
  isFileAccessible(filePath) {
    try {
      fs.accessSync(filePath, fs.constants.R_OK);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate system health and dependencies
   * @returns {Object} System validation results
   */
  validateSystem() {
    const results = {
      healthy: true,
      errors: [],
      warnings: [],
      components: {}
    };

    try {
      // Check template engine
      if (!this.templateEngine) {
        results.errors.push('Template engine not initialized');
        results.healthy = false;
      } else {
        try {
          const templates = this.templateEngine.getAvailableTemplates();
          results.components.templateEngine = {
            available: true,
            templateCount: templates.length,
            templates
          };
        } catch (templateError) {
          results.errors.push(`Template engine error: ${templateError.message}`);
          results.healthy = false;
        }
      }

      // Check context detector
      if (!this.contextDetector) {
        results.errors.push('Context detector not initialized');
        results.healthy = false;
      } else {
        try {
          const patternValidation = this.contextDetector.validatePatternSystem();
          results.components.contextDetector = {
            available: true,
            valid: patternValidation.valid,
            contextCount: patternValidation.contextCount,
            patternCount: patternValidation.patternCount,
            errors: patternValidation.errors,
            warnings: patternValidation.warnings
          };

          if (!patternValidation.valid) {
            results.warnings.push('Context detector has pattern validation issues');
          }
        } catch (detectorError) {
          results.errors.push(`Context detector error: ${detectorError.message}`);
          results.healthy = false;
        }
      }

      // Check steering file
      const steeringValidation = this.validateSteeringFile();
      results.components.steeringFile = steeringValidation;
      
      if (!steeringValidation.exists) {
        results.warnings.push('Steering file not found - using fallback reminders');
      } else if (!steeringValidation.hasContent) {
        results.warnings.push('Steering file is empty');
      }

    } catch (systemError) {
      results.errors.push(`System validation error: ${systemError.message}`);
      results.healthy = false;
    }

    return results;
  }

  /**
   * Validate steering file exists and is accessible
   * @returns {Object} Validation results
   */
  validateSteeringFile() {
    try {
      const exists = fs.existsSync(this.steeringFilePath);
      let content = '';
      let size = 0;

      if (exists) {
        const stats = fs.statSync(this.steeringFilePath);
        size = stats.size;
        content = fs.readFileSync(this.steeringFilePath, 'utf8');
      }

      return {
        exists,
        path: this.steeringFilePath,
        size,
        hasContent: content.length > 0,
        lastModified: exists ? fs.statSync(this.steeringFilePath).mtime : null,
        validation: {
          hasPermissionKeys: content.includes('GRANULAR_PERMISSIONS'),
          hasOrganizationTypes: content.includes('organization_type'),
          hasSubscriptionPlans: content.includes('subscription_plan'),
          hasFeatureFlags: content.includes('feature_flags')
        }
      };
    } catch (error) {
      return {
        exists: false,
        path: this.steeringFilePath,
        error: error.message,
        validation: {
          hasPermissionKeys: false,
          hasOrganizationTypes: false,
          hasSubscriptionPlans: false,
          hasFeatureFlags: false
        }
      };
    }
  }
}

// Export singleton instance
module.exports = new GovernanceReminderGenerator();