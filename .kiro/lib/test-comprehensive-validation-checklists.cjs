/**
 * Comprehensive Test for Validation Checklists
 * Tests all governance contexts and validation checklist functionality
 */

const ValidationChecklists = require('./governance-validation-checklists.cjs');
const ReminderEngine = require('./reminder-template-engine.cjs');

function testValidationChecklists() {
  console.log('🧪 COMPREHENSIVE VALIDATION CHECKLISTS TEST');
  console.log('='.repeat(60));
  
  const results = {
    passed: 0,
    failed: 0,
    errors: []
  };

  // Test 1: All validation contexts exist
  console.log('\n1. Testing Validation Context Availability...');
  const expectedContexts = [
    'PERMISSION_ARCHITECTURE',
    'ORGANIZATION_CONTROLS', 
    'SUBSCRIPTION_ARCHITECTURE',
    'DATABASE_MIGRATION'
  ];
  
  expectedContexts.forEach(context => {
    try {
      const checklist = ValidationChecklists.generateValidationChecklist(context);
      if (checklist && checklist.length > 100) {
        console.log(`   ✅ ${context}: Available and comprehensive`);
        results.passed++;
      } else {
        console.log(`   ❌ ${context}: Missing or too short`);
        results.failed++;
        results.errors.push(`${context} checklist is incomplete`);
      }
    } catch (error) {
      console.log(`   ❌ ${context}: Error - ${error.message}`);
      results.failed++;
      results.errors.push(`${context} error: ${error.message}`);
    }
  });

  // Test 2: File pattern detection
  console.log('\n2. Testing File Pattern Detection...');
  const testFiles = [
    { path: 'app/api/auth/permissions.ts', expected: 'PERMISSION_ARCHITECTURE' },
    { path: 'app/contexts/OrganizationContext.tsx', expected: 'ORGANIZATION_CONTROLS' },
    { path: 'app/api/subscriptions/billing.ts', expected: 'SUBSCRIPTION_ARCHITECTURE' },
    { path: 'supabase/migrations/001_create_permissions.sql', expected: 'DATABASE_MIGRATION' },
    { path: 'app/middleware.ts', expected: 'PERMISSION_ARCHITECTURE' },
    { path: 'app/api/organizations/settings.ts', expected: 'ORGANIZATION_CONTROLS' }
  ];

  testFiles.forEach(testFile => {
    try {
      const checklist = ValidationChecklists.getChecklistForFile(testFile.path);
      const contextMatch = checklist.includes(testFile.expected.replace('_', ' ')) || 
                        checklist.includes('DATABASE') || 
                        checklist.includes('MIGRATION');
      
      if (contextMatch) {
        console.log(`   ✅ ${testFile.path}: Correctly detected context`);
        results.passed++;
      } else {
        console.log(`   ❌ ${testFile.path}: Wrong context detected`);
        results.failed++;
        results.errors.push(`File ${testFile.path} context detection failed`);
      }
    } catch (error) {
      console.log(`   ❌ ${testFile.path}: Error - ${error.message}`);
      results.failed++;
      results.errors.push(`File detection error for ${testFile.path}: ${error.message}`);
    }
  });

  // Test 3: Checklist content validation
  console.log('\n3. Testing Checklist Content Quality...');
  expectedContexts.forEach(context => {
    try {
      const checklist = ValidationChecklists.generateValidationChecklist(context);
      
      // Check for required elements
      const hasTitle = checklist.includes('VALIDATION');
      const hasCheckboxes = checklist.includes('- [ ]');
      const hasCorrectExamples = checklist.includes('✅ CORRECT:');
      const hasIncorrectExamples = checklist.includes('❌ WRONG:');
      const hasGuidance = checklist.includes('💡 GUIDANCE:');
      const hasQuickReference = checklist.includes('Quick Validation Summary');
      
      const qualityScore = [hasTitle, hasCheckboxes, hasCorrectExamples, hasIncorrectExamples, hasGuidance, hasQuickReference].filter(Boolean).length;
      
      if (qualityScore >= 5) {
        console.log(`   ✅ ${context}: High quality content (${qualityScore}/6 elements)`);
        results.passed++;
      } else {
        console.log(`   ❌ ${context}: Low quality content (${qualityScore}/6 elements)`);
        results.failed++;
        results.errors.push(`${context} missing required content elements`);
      }
    } catch (error) {
      console.log(`   ❌ ${context}: Content validation error - ${error.message}`);
      results.failed++;
      results.errors.push(`Content validation error for ${context}: ${error.message}`);
    }
  });

  // Test 4: Integration with Reminder Template Engine
  console.log('\n4. Testing Reminder Template Engine Integration...');
  const templateTests = [
    { template: 'PERMISSION_ARCHITECTURE', context: { filePath: 'test.ts', fileType: 'test' } },
    { template: 'ORGANIZATION_CONTROLS', context: { filePath: 'test.tsx', fileType: 'test' } },
    { template: 'SUBSCRIPTION_ARCHITECTURE', context: { filePath: 'test.ts', fileType: 'test' } },
    { template: 'DATABASE_ARCHITECTURE', context: { filePath: 'test.sql', fileType: 'test' } }
  ];

  templateTests.forEach(test => {
    try {
      const reminder = ReminderEngine.generateReminder(test.template, test.context);
      
      if (reminder && reminder.length > 500 && reminder.includes('VALIDATION')) {
        console.log(`   ✅ ${test.template}: Template engine integration working`);
        results.passed++;
      } else {
        console.log(`   ❌ ${test.template}: Template engine integration failed`);
        results.failed++;
        results.errors.push(`Template engine integration failed for ${test.template}`);
      }
    } catch (error) {
      console.log(`   ❌ ${test.template}: Integration error - ${error.message}`);
      results.failed++;
      results.errors.push(`Integration error for ${test.template}: ${error.message}`);
    }
  });

  // Test 5: Specific validation checklist features
  console.log('\n5. Testing Specific Validation Features...');
  
  // Test permission keys availability
  try {
    const permissionChecklist = ValidationChecklists.VALIDATION_CHECKLISTS.PERMISSION_ARCHITECTURE;
    if (permissionChecklist.availablePermissions && permissionChecklist.availablePermissions.length >= 20) {
      console.log(`   ✅ Permission keys: ${permissionChecklist.availablePermissions.length} available`);
      results.passed++;
    } else {
      console.log(`   ❌ Permission keys: Insufficient permission keys available`);
      results.failed++;
      results.errors.push('Insufficient permission keys in checklist');
    }
  } catch (error) {
    console.log(`   ❌ Permission keys: Error - ${error.message}`);
    results.failed++;
    results.errors.push(`Permission keys test error: ${error.message}`);
  }

  // Test organization types
  try {
    const orgChecklist = ValidationChecklists.VALIDATION_CHECKLISTS.ORGANIZATION_CONTROLS;
    if (orgChecklist.organizationTypes && orgChecklist.organizationTypes.includes('shared') && 
        orgChecklist.organizationTypes.includes('segregated') && orgChecklist.organizationTypes.includes('isolated')) {
      console.log(`   ✅ Organization types: All three types available`);
      results.passed++;
    } else {
      console.log(`   ❌ Organization types: Missing required organization types`);
      results.failed++;
      results.errors.push('Missing required organization types');
    }
  } catch (error) {
    console.log(`   ❌ Organization types: Error - ${error.message}`);
    results.failed++;
    results.errors.push(`Organization types test error: ${error.message}`);
  }

  // Test subscription plans
  try {
    const subChecklist = ValidationChecklists.VALIDATION_CHECKLISTS.SUBSCRIPTION_ARCHITECTURE;
    if (subChecklist.subscriptionPlans && subChecklist.subscriptionPlans.length >= 4) {
      console.log(`   ✅ Subscription plans: ${subChecklist.subscriptionPlans.length} plans available`);
      results.passed++;
    } else {
      console.log(`   ❌ Subscription plans: Insufficient subscription plans`);
      results.failed++;
      results.errors.push('Insufficient subscription plans in checklist');
    }
  } catch (error) {
    console.log(`   ❌ Subscription plans: Error - ${error.message}`);
    results.failed++;
    results.errors.push(`Subscription plans test error: ${error.message}`);
  }

  // Test feature flags
  try {
    const subChecklist = ValidationChecklists.VALIDATION_CHECKLISTS.SUBSCRIPTION_ARCHITECTURE;
    if (subChecklist.featureFlags && subChecklist.featureFlags.length >= 8) {
      console.log(`   ✅ Feature flags: ${subChecklist.featureFlags.length} flags available`);
      results.passed++;
    } else {
      console.log(`   ❌ Feature flags: Insufficient feature flags`);
      results.failed++;
      results.errors.push('Insufficient feature flags in checklist');
    }
  } catch (error) {
    console.log(`   ❌ Feature flags: Error - ${error.message}`);
    results.failed++;
    results.errors.push(`Feature flags test error: ${error.message}`);
  }

  // Final Results
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.errors.length > 0) {
    console.log('\n🚨 ERRORS ENCOUNTERED:');
    results.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }

  if (results.failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Comprehensive validation checklists are working correctly.');
    return true;
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above.');
    return false;
  }
}

// Run the test
if (require.main === module) {
  testValidationChecklists();
}

module.exports = { testValidationChecklists };