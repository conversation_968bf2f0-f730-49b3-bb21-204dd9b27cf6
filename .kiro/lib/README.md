# Governance Reminder Template System

This directory contains the reminder template system for the Architectural Governance System. The system generates contextual reminders when files are modified that could affect SUPER_ADMIN control architecture.

## Components

### Core Files

- **`reminder-template-engine.cjs`** - Main template engine that generates contextual reminders
- **`governance-context-detector.cjs`** - Analyzes file paths to determine appropriate governance context
- **`governance-reminder-generator.cjs`** - Main interface that ties everything together
- **`test-reminder-system.cjs`** - Testing utility for the reminder system

### Template Types

The system supports four main template types:

1. **PERMISSION_ARCHITECTURE** - For permission-related files
2. **ORGANIZATION_CONTROLS** - For organization-related files  
3. **SUBSCRIPTION_ARCHITECTURE** - For subscription/billing files
4. **DATABASE_ARCHITECTURE** - For database migration files

## Usage

### Testing the System

```bash
# Run all tests
node .kiro/lib/test-reminder-system.cjs test

# Generate sample reminder for a specific file
node .kiro/lib/test-reminder-system.cjs sample app/api/auth/permissions.ts

# Test file pattern detection
node .kiro/lib/test-reminder-system.cjs patterns

# Test steering file validation
node .kiro/lib/test-reminder-system.cjs steering

# Test hook-based reminders
node .kiro/lib/test-reminder-system.cjs hooks
```

### Programmatic Usage

```javascript
const GovernanceReminderGenerator = require('.kiro/lib/governance-reminder-generator.cjs');

// Generate reminder for a single file
const reminder = GovernanceReminderGenerator.generateReminder('app/api/auth/permissions.ts');

// Generate reminder for multiple files
const multiReminder = GovernanceReminderGenerator.generateMultiFileReminder([
  'app/api/auth/permissions.ts',
  'app/lib/auth/roles.ts'
]);

// Generate hook-based reminder
const hookReminder = GovernanceReminderGenerator.generateHookReminder(
  'permission-architecture-validator',
  ['app/api/auth/permissions.ts']
);
```

## File Pattern Detection

The system automatically detects governance context based on file patterns:

### Permission Files
- `app/api/**/*.ts`
- `app/lib/auth/**/*.ts`
- `components/**/permissions/**/*.tsx`
- `app/middleware.ts`

### Organization Files
- `app/api/organizations/**/*.ts`
- `app/contexts/OrganizationContext.tsx`
- `app/lib/organization/**/*.ts`

### Subscription Files
- `app/api/subscriptions/**/*.ts`
- `app/lib/billing/**/*.ts`
- `components/**/subscription/**/*.tsx`

### Database Files
- `supabase/migrations/**/*.sql`
- `migrations/**/*.sql`

## Hook Integration

The system integrates with Kiro hooks to provide automatic reminders:

- **permission-architecture-validator.kiro.hook**
- **organization-controls-validator.kiro.hook**
- **subscription-architecture-validator.kiro.hook**
- **database-migration-governance.kiro.hook**

## Template Content

Each template includes:

1. **Available Controls** - List of existing SUPER_ADMIN capabilities
2. **Usage Patterns** - Code examples showing correct implementation
3. **Validation Checklist** - Specific points to verify compliance
4. **Anti-Patterns** - Common mistakes to avoid

## Steering File Integration

All reminders reference the main steering file:
`.kiro/steering/super-admin-controls-reference.md`

This file contains the complete SUPER_ADMIN control capabilities and should be kept up-to-date with any architectural changes.

## Maintenance

### Adding New Templates

1. Add template definition to `reminder-template-engine.cjs`
2. Add file patterns to `governance-context-detector.cjs`
3. Update hook mappings in `governance-reminder-generator.cjs`
4. Test with `test-reminder-system.cjs`

### Updating Existing Templates

1. Modify template generator methods in `reminder-template-engine.cjs`
2. Update steering file if needed
3. Test changes with the test utility

## Architecture Principles

The reminder system follows these principles:

1. **Contextual** - Reminders are specific to the type of file being modified
2. **Comprehensive** - Each reminder includes all necessary information
3. **Actionable** - Provides specific validation checklists and code examples
4. **Consistent** - All reminders follow the same structure and format
5. **Maintainable** - Template system allows easy updates and additions

## Integration with Development Workflow

The system integrates seamlessly with the development workflow:

1. Developer modifies a file
2. Hook detects file pattern match
3. Context detector analyzes file type
4. Template engine generates appropriate reminder
5. Reminder displays with validation checklist
6. Developer validates against SUPER_ADMIN architecture

This ensures developers always consider existing SUPER_ADMIN capabilities instead of creating duplicate systems.