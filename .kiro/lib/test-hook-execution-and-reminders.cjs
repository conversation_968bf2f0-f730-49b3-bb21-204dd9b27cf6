#!/usr/bin/env node

/**
 * Comprehensive Test Suite for Hook Execution and Reminder Display
 * Tests hook trigger patterns, file matching, reminder template loading, and error handling
 * 
 * Requirements covered:
 * - 1.1: Test hook trigger patterns and file matching
 * - 1.2: Test reminder template loading and content generation
 * - 1.3: Verify hooks trigger correctly for different file modification scenarios
 * - 1.4: Test error handling and graceful degradation
 */

const fs = require('fs');
const path = require('path');
const GovernanceReminderGenerator = require('./governance-reminder-generator.cjs');
const GovernanceContextDetector = require('./governance-context-detector.cjs');
const ReminderTemplateEngine = require('./reminder-template-engine.cjs');

class HookExecutionTester {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: [],
      categories: {
        hookTriggers: { passed: 0, failed: 0 },
        fileMatching: { passed: 0, failed: 0 },
        templateLoading: { passed: 0, failed: 0 },
        errorHandling: { passed: 0, failed: 0 },
        reminderDisplay: { passed: 0, failed: 0 }
      }
    };
    
    this.hookDirectory = path.join(__dirname, '../hooks');
    this.steeringDirectory = path.join(__dirname, '../steering');
  }

  /**
   * Run comprehensive hook execution tests
   */
  async runAllTests() {
    console.log('🧪 Testing Hook Execution and Reminder Display System\n');
    console.log('=' .repeat(60));

    // Test categories
    await this.testHookTriggerPatterns();
    await this.testFileMatchingAccuracy();
    await this.testReminderTemplateLoading();
    await this.testHookExecutionScenarios();
    await this.testErrorHandlingAndGracefulDegradation();
    await this.testReminderDisplayQuality();
    await this.testSystemIntegration();

    // Generate comprehensive report
    this.generateTestReport();
    
    return this.testResults.failed === 0;
  }

  /**
   * Test hook trigger patterns and file matching (Requirement 1.1)
   */
  async testHookTriggerPatterns() {
    console.log('\n🎯 Testing Hook Trigger Patterns and File Matching');
    console.log('-'.repeat(50));

    // Load all hook files
    const hookFiles = await this.loadHookFiles();
    
    // Test file patterns for each hook
    const testScenarios = [
      // Permission Architecture Hook Tests
      {
        hookName: 'Permission Architecture Validator',
        testFiles: [
          { path: 'app/api/auth/route.ts', shouldTrigger: true },
          { path: 'app/lib/auth/permissions.ts', shouldTrigger: true },
          { path: 'components/ui/permissions/PermissionGate.tsx', shouldTrigger: true },
          { path: 'app/middleware.ts', shouldTrigger: true },
          { path: 'app/contexts/AuthContext.tsx', shouldTrigger: true },
          { path: 'app/hooks/usePermissions.ts', shouldTrigger: true },
          { path: 'app/utils/permissions/validator.ts', shouldTrigger: true },
          { path: 'app/components/Layout.tsx', shouldTrigger: false },
          { path: 'README.md', shouldTrigger: false }
        ]
      },
      
      // Organization Controls Hook Tests
      {
        hookName: 'Organization Controls Validator',
        testFiles: [
          { path: 'app/api/organizations/route.ts', shouldTrigger: true },
          { path: 'app/contexts/OrganizationContext.tsx', shouldTrigger: true },
          { path: 'app/lib/organization/settings.ts', shouldTrigger: true },
          { path: 'components/organization/OrgSelector.tsx', shouldTrigger: true },
          { path: 'app/types/organization.ts', shouldTrigger: true },
          { path: 'app/api/quotes/route.ts', shouldTrigger: false },
          { path: 'package.json', shouldTrigger: false }
        ]
      },
      
      // Subscription Architecture Hook Tests
      {
        hookName: 'Subscription Architecture Validator',
        testFiles: [
          { path: 'app/api/subscriptions/route.ts', shouldTrigger: true },
          { path: 'app/api/billing/webhook/route.ts', shouldTrigger: true },
          { path: 'app/lib/billing/stripe.ts', shouldTrigger: true },
          { path: 'components/billing/SubscriptionPanel.tsx', shouldTrigger: true },
          { path: 'app/contexts/SubscriptionContext.tsx', shouldTrigger: true },
          { path: 'app/hooks/useSubscription.ts', shouldTrigger: true },
          { path: 'app/types/subscription.ts', shouldTrigger: true },
          { path: 'supabase/migrations/001_subscription_tables.sql', shouldTrigger: true },
          { path: 'app/components/Header.tsx', shouldTrigger: false }
        ]
      },
      
      // Database Migration Hook Tests
      {
        hookName: 'Database Migration Governance',
        testFiles: [
          { path: 'supabase/migrations/001_create_organizations.sql', shouldTrigger: true },
          { path: 'migrations/002_add_permissions.sql', shouldTrigger: true },
          { path: 'database/migrations/003_user_roles.sql', shouldTrigger: true },
          { path: 'supabase/migrations/004_subscription_tables.sql', shouldTrigger: true },
          { path: 'app/api/organizations/route.ts', shouldTrigger: false },
          { path: 'schema.sql', shouldTrigger: false }
        ]
      }
    ];

    for (const scenario of testScenarios) {
      const hook = hookFiles.find(h => h.name === scenario.hookName);
      
      if (!hook) {
        this.addTest('hookTriggers', `Hook File Exists: ${scenario.hookName}`, false, 
          `Hook file not found. Available hooks: ${hookFiles.map(h => h.name).join(', ')}`);
        continue;
      }

      this.addTest('hookTriggers', `Hook File Loaded: ${scenario.hookName}`, true, 
        `Loaded with ${hook.patterns.length} patterns`);

      // Test each file pattern
      for (const testFile of scenario.testFiles) {
        const shouldMatch = this.shouldHookTrigger(hook.patterns, testFile.path);
        const testPassed = shouldMatch === testFile.shouldTrigger;
        
        this.addTest('fileMatching', 
          `Pattern Match: ${testFile.path} -> ${scenario.hookName}`,
          testPassed,
          testPassed ? 
            `Correctly ${shouldMatch ? 'matched' : 'ignored'}` :
            `Expected ${testFile.shouldTrigger ? 'match' : 'ignore'}, got ${shouldMatch ? 'match' : 'ignore'}`
        );
      }
    }
  }

  /**
   * Test file matching accuracy across different scenarios
   */
  async testFileMatchingAccuracy() {
    console.log('\n📁 Testing File Matching Accuracy');
    console.log('-'.repeat(50));

    // Edge case file paths
    const edgeCases = [
      { path: '', expectedContext: 'general' },
      { path: '   ', expectedContext: 'general' },
      { path: 'app/api/organizations/../auth/route.ts', expectedContext: 'permissions' },
      { path: 'APP/API/ORGANIZATIONS/ROUTE.TS', expectedContext: 'general' }, // Case sensitivity
      { path: 'app\\api\\organizations\\route.ts', expectedContext: 'organizations' }, // Windows paths
      { path: 'very/deep/nested/app/api/organizations/route.ts', expectedContext: 'organizations' },
      { path: 'app/api/organizations/[id]/route.ts', expectedContext: 'organizations' },
      { path: 'app/(portals)/super-admin/organizations/page.tsx', expectedContext: 'organizations' },
      { path: 'node_modules/some-package/app/api/organizations/route.ts', expectedContext: 'organizations' }
    ];

    for (const testCase of edgeCases) {
      try {
        const context = GovernanceContextDetector.detectContext(testCase.path);
        const matched = context.context === testCase.expectedContext;
        
        this.addTest('fileMatching',
          `Edge Case: "${testCase.path}"`,
          matched,
          matched ? 
            `Correctly detected: ${context.context}` :
            `Expected: ${testCase.expectedContext}, Got: ${context.context}`
        );
      } catch (error) {
        this.addTest('fileMatching',
          `Edge Case: "${testCase.path}"`,
          false,
          `Error: ${error.message}`
        );
      }
    }

    // Test pattern priority and specificity
    const priorityTests = [
      { path: 'supabase/migrations/001_organizations.sql', expectedContext: 'database' },
      { path: 'app/api/organizations/route.ts', expectedContext: 'organizations' },
      { path: 'app/api/subscriptions/billing/route.ts', expectedContext: 'subscriptions' },
      { path: 'app/api/auth/permissions/route.ts', expectedContext: 'permissions' }
    ];

    for (const test of priorityTests) {
      const context = GovernanceContextDetector.detectContext(test.path);
      const correctPriority = context.context === test.expectedContext;
      
      this.addTest('fileMatching',
        `Priority Test: ${test.path}`,
        correctPriority,
        correctPriority ?
          `Correct priority: ${context.context} (confidence: ${context.confidence.toFixed(2)})` :
          `Expected: ${test.expectedContext}, Got: ${context.context}`
      );
    }
  }

  /**
   * Test reminder template loading and content generation (Requirement 1.2)
   */
  async testReminderTemplateLoading() {
    console.log('\n📝 Testing Reminder Template Loading and Content Generation');
    console.log('-'.repeat(50));

    // Test template system health
    const templateValidation = ReminderTemplateEngine.validateTemplateSystem();
    this.addTest('templateLoading', 'Template System Health', templateValidation.healthy,
      templateValidation.healthy ? 
        `${templateValidation.templateCount} templates loaded` :
        `Errors: ${templateValidation.errors.join(', ')}`
    );

    // Test each template type
    const templateTypes = ReminderTemplateEngine.getAvailableTemplates();
    
    for (const templateType of templateTypes) {
      try {
        const testContext = {
          filePath: 'test/sample.ts',
          fileType: 'test file',
          context: 'test'
        };

        const reminder = ReminderTemplateEngine.generateReminder(templateType, testContext);
        
        // Validate reminder content
        const hasTitle = reminder.includes('**') && reminder.includes('VALIDATION');
        const hasChecklist = reminder.includes('Validation Checklist') || reminder.includes('- [ ]');
        const hasContent = reminder.length > 300;
        const hasFilePath = reminder.includes(testContext.filePath);
        
        const contentValid = hasTitle && hasChecklist && hasContent && hasFilePath;
        
        this.addTest('templateLoading',
          `Template Generation: ${templateType}`,
          contentValid,
          contentValid ?
            `Generated ${reminder.length} chars with all required sections` :
            `Missing: ${!hasTitle ? 'title ' : ''}${!hasChecklist ? 'checklist ' : ''}${!hasContent ? 'content ' : ''}${!hasFilePath ? 'filepath' : ''}`
        );

        // Test template metadata
        const metadata = ReminderTemplateEngine.getTemplateMetadata(templateType);
        this.addTest('templateLoading',
          `Template Metadata: ${templateType}`,
          metadata && metadata.title && metadata.sections,
          metadata ? 
            `Title: "${metadata.title}", Sections: ${metadata.sections.length}` :
            'Missing metadata'
        );

      } catch (error) {
        this.addTest('templateLoading',
          `Template Generation: ${templateType}`,
          false,
          `Error: ${error.message}`
        );
      }
    }

    // Test template content quality
    await this.testTemplateContentQuality();
  }

  /**
   * Test hook execution scenarios (Requirement 1.3)
   */
  async testHookExecutionScenarios() {
    console.log('\n🔄 Testing Hook Execution Scenarios');
    console.log('-'.repeat(50));

    const executionScenarios = [
      {
        name: 'Single File Modification',
        files: ['app/lib/auth/permissions.ts'],
        expectedHooks: ['permission-architecture-validator']
      },
      {
        name: 'Multiple Related Files',
        files: [
          'app/api/organizations/route.ts',
          'app/contexts/OrganizationContext.tsx'
        ],
        expectedHooks: ['organization-controls-validator']
      },
      {
        name: 'Cross-Context Files',
        files: [
          'app/api/subscriptions/route.ts',
          'supabase/migrations/001_subscription_tables.sql'
        ],
        expectedHooks: ['subscription-architecture-validator', 'database-migration-governance']
      },
      {
        name: 'Mixed File Types',
        files: [
          'app/lib/auth/permissions.ts',
          'app/api/organizations/route.ts',
          'app/lib/billing/stripe.ts',
          'supabase/migrations/001_permissions.sql'
        ],
        expectedHooks: [
          'permission-architecture-validator',
          'organization-controls-validator', 
          'subscription-architecture-validator',
          'database-migration-governance'
        ]
      }
    ];

    for (const scenario of executionScenarios) {
      try {
        // Test reminder generation for each file
        const reminders = [];
        const triggeredHooks = new Set();

        for (const filePath of scenario.files) {
          const context = GovernanceContextDetector.detectContext(filePath);
          const reminder = GovernanceReminderGenerator.generateReminder(filePath);
          
          reminders.push({
            filePath,
            context: context.context,
            reminder,
            length: reminder.length
          });

          // Determine which hooks would trigger
          const hookFiles = await this.loadHookFiles();
          for (const hook of hookFiles) {
            if (this.shouldHookTrigger(hook.patterns, filePath)) {
              triggeredHooks.add(hook.name);
            }
          }
        }

        // Validate scenario execution
        const allRemindersGenerated = reminders.every(r => r.reminder.length > 0);
        const expectedHooksTriggered = scenario.expectedHooks.every(expectedHook =>
          Array.from(triggeredHooks).some(triggeredHook => 
            triggeredHook.toLowerCase().includes(expectedHook.replace('-', ' ').toLowerCase())
          )
        );

        this.addTest('hookTriggers',
          `Execution Scenario: ${scenario.name}`,
          allRemindersGenerated && expectedHooksTriggered,
          allRemindersGenerated && expectedHooksTriggered ?
            `Generated ${reminders.length} reminders, triggered ${triggeredHooks.size} hooks` :
            `Reminders: ${allRemindersGenerated}, Hooks: ${expectedHooksTriggered}`
        );

        // Test reminder quality for scenario
        const avgReminderLength = reminders.reduce((sum, r) => sum + r.length, 0) / reminders.length;
        this.addTest('reminderDisplay',
          `Reminder Quality: ${scenario.name}`,
          avgReminderLength > 500,
          `Average reminder length: ${Math.round(avgReminderLength)} characters`
        );

      } catch (error) {
        this.addTest('hookTriggers',
          `Execution Scenario: ${scenario.name}`,
          false,
          `Error: ${error.message}`
        );
      }
    }
  }

  /**
   * Test error handling and graceful degradation (Requirement 1.4)
   */
  async testErrorHandlingAndGracefulDegradation() {
    console.log('\n⚠️  Testing Error Handling and Graceful Degradation');
    console.log('-'.repeat(50));

    // Test invalid inputs
    const invalidInputs = [
      { input: null, description: 'Null file path' },
      { input: undefined, description: 'Undefined file path' },
      { input: '', description: 'Empty file path' },
      { input: '   ', description: 'Whitespace-only file path' },
      { input: 123, description: 'Non-string file path' },
      { input: {}, description: 'Object as file path' },
      { input: [], description: 'Array as file path' }
    ];

    for (const testCase of invalidInputs) {
      try {
        const reminder = GovernanceReminderGenerator.generateReminder(testCase.input);
        const gracefullyHandled = reminder.includes('SUPER_ADMIN CONTROLS') && reminder.length > 100;
        
        this.addTest('errorHandling',
          `Invalid Input: ${testCase.description}`,
          gracefullyHandled,
          gracefullyHandled ?
            'Generated fallback reminder' :
            'Failed to handle gracefully'
        );
      } catch (error) {
        this.addTest('errorHandling',
          `Invalid Input: ${testCase.description}`,
          false,
          `Unexpected error: ${error.message}`
        );
      }
    }

    // Test missing steering file scenario
    const originalSteeringPath = GovernanceReminderGenerator.steeringFilePath;
    GovernanceReminderGenerator.steeringFilePath = 'nonexistent/steering/file.md';
    
    try {
      const reminder = GovernanceReminderGenerator.generateReminder('app/test.ts');
      const handledMissingFile = reminder.includes('SUPER_ADMIN CONTROLS');
      
      this.addTest('errorHandling',
        'Missing Steering File',
        handledMissingFile,
        handledMissingFile ?
          'Generated reminder without steering file' :
          'Failed to handle missing steering file'
      );
    } catch (error) {
      this.addTest('errorHandling',
        'Missing Steering File',
        false,
        `Error: ${error.message}`
      );
    } finally {
      GovernanceReminderGenerator.steeringFilePath = originalSteeringPath;
    }

    // Test corrupted template scenarios
    const originalTemplates = ReminderTemplateEngine.templates;
    
    try {
      // Temporarily corrupt a template
      ReminderTemplateEngine.templates.set('TEST_CORRUPT', {
        title: 'Test Template',
        sections: ['Test'],
        generator: null // Invalid generator
      });

      const reminder = ReminderTemplateEngine.generateReminder('TEST_CORRUPT', {
        filePath: 'test.ts',
        fileType: 'test'
      });
      
      const handledCorruption = reminder.includes('SUPER_ADMIN CONTROLS');
      
      this.addTest('errorHandling',
        'Corrupted Template',
        handledCorruption,
        handledCorruption ?
          'Generated fallback for corrupted template' :
          'Failed to handle corrupted template'
      );
    } catch (error) {
      this.addTest('errorHandling',
        'Corrupted Template',
        false,
        `Error: ${error.message}`
      );
    } finally {
      // Restore original templates
      ReminderTemplateEngine.templates = originalTemplates;
    }

    // Test system resource constraints
    await this.testResourceConstraints();
  }

  /**
   * Test reminder display quality
   */
  async testReminderDisplayQuality() {
    console.log('\n🎨 Testing Reminder Display Quality');
    console.log('-'.repeat(50));

    const qualityTests = [
      {
        filePath: 'app/lib/auth/permissions.ts',
        expectedElements: [
          'PERMISSION ARCHITECTURE VALIDATION',
          'Validation Checklist',
          'permission',
          'template',
          '- [ ]' // Checkbox items
        ]
      },
      {
        filePath: 'app/contexts/OrganizationContext.tsx',
        expectedElements: [
          'ORGANIZATION CONTROLS VALIDATION',
          'Organization Types',
          'shared',
          'segregated',
          'isolated'
        ]
      },
      {
        filePath: 'supabase/migrations/001_permissions.sql',
        expectedElements: [
          'DATABASE ARCHITECTURE VALIDATION',
          'migration',
          'schema',
          'multi-tenant',
          'RLS'
        ]
      }
    ];

    for (const test of qualityTests) {
      try {
        const reminder = GovernanceReminderGenerator.generateReminder(test.filePath);
        
        let missingElements = [];
        let foundElements = [];
        
        for (const element of test.expectedElements) {
          if (reminder.includes(element)) {
            foundElements.push(element);
          } else {
            missingElements.push(element);
          }
        }
        
        const qualityScore = foundElements.length / test.expectedElements.length;
        const highQuality = qualityScore >= 0.8;
        
        this.addTest('reminderDisplay',
          `Display Quality: ${path.basename(test.filePath)}`,
          highQuality,
          highQuality ?
            `Found ${foundElements.length}/${test.expectedElements.length} expected elements` :
            `Missing: ${missingElements.join(', ')}`
        );
        
        // Test formatting quality
        const hasProperFormatting = this.validateReminderFormatting(reminder);
        this.addTest('reminderDisplay',
          `Formatting Quality: ${path.basename(test.filePath)}`,
          hasProperFormatting.valid,
          hasProperFormatting.valid ?
            'Proper markdown formatting' :
            `Issues: ${hasProperFormatting.issues.join(', ')}`
        );
        
      } catch (error) {
        this.addTest('reminderDisplay',
          `Display Quality: ${path.basename(test.filePath)}`,
          false,
          `Error: ${error.message}`
        );
      }
    }
  }

  /**
   * Test system integration
   */
  async testSystemIntegration() {
    console.log('\n🔗 Testing System Integration');
    console.log('-'.repeat(50));

    // Test component integration
    const systemHealth = GovernanceReminderGenerator.validateSystem();
    this.addTest('hookTriggers',
      'System Integration Health',
      systemHealth.healthy,
      systemHealth.healthy ?
        'All components integrated properly' :
        `Issues: ${systemHealth.errors.join(', ')}`
    );

    // Test steering file integration
    const steeringValidation = GovernanceReminderGenerator.validateSteeringFile();
    this.addTest('templateLoading',
      'Steering File Integration',
      steeringValidation.exists && steeringValidation.hasContent,
      steeringValidation.exists ?
        `Steering file loaded (${steeringValidation.size} bytes)` :
        'Steering file missing or empty'
    );

    // Test hook file integration
    const hookFiles = await this.loadHookFiles();
    const allHooksValid = hookFiles.every(hook => 
      hook.patterns && hook.patterns.length > 0 && hook.name
    );
    
    this.addTest('hookTriggers',
      'Hook Files Integration',
      allHooksValid,
      allHooksValid ?
        `${hookFiles.length} hook files loaded successfully` :
        'Some hook files have invalid configuration'
    );
  }

  /**
   * Test resource constraints and performance
   */
  async testResourceConstraints() {
    console.log('\n⚡ Testing Resource Constraints');
    console.log('-'.repeat(30));

    // Test large file path handling
    const longPath = 'app/' + 'very/'.repeat(50) + 'deep/nested/file.ts';
    try {
      const reminder = GovernanceReminderGenerator.generateReminder(longPath);
      const handledLongPath = reminder.length > 0;
      
      this.addTest('errorHandling',
        'Long File Path',
        handledLongPath,
        handledLongPath ?
          'Handled long file path gracefully' :
          'Failed to handle long file path'
      );
    } catch (error) {
      this.addTest('errorHandling',
        'Long File Path',
        false,
        `Error: ${error.message}`
      );
    }

    // Test multiple simultaneous requests
    const simultaneousRequests = Array.from({ length: 10 }, (_, i) => 
      `app/api/test${i}/route.ts`
    );

    try {
      const startTime = Date.now();
      const promises = simultaneousRequests.map(filePath =>
        Promise.resolve(GovernanceReminderGenerator.generateReminder(filePath))
      );
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      const allSuccessful = results.every(reminder => reminder.length > 0);
      const performanceAcceptable = (endTime - startTime) < 5000; // 5 seconds max
      
      this.addTest('errorHandling',
        'Concurrent Requests',
        allSuccessful && performanceAcceptable,
        allSuccessful && performanceAcceptable ?
          `Processed ${results.length} requests in ${endTime - startTime}ms` :
          `Success: ${allSuccessful}, Performance: ${endTime - startTime}ms`
      );
    } catch (error) {
      this.addTest('errorHandling',
        'Concurrent Requests',
        false,
        `Error: ${error.message}`
      );
    }
  }

  /**
   * Test template content quality
   */
  async testTemplateContentQuality() {
    const templateTypes = ['PERMISSION_ARCHITECTURE', 'ORGANIZATION_CONTROLS', 'SUBSCRIPTION_ARCHITECTURE', 'DATABASE_ARCHITECTURE'];
    
    for (const templateType of templateTypes) {
      try {
        const reminder = ReminderTemplateEngine.generateReminder(templateType, {
          filePath: 'test/file.ts',
          fileType: 'test file'
        });

        // Check for SUPER_ADMIN references
        const hasSuperAdminRefs = reminder.includes('SUPER_ADMIN');
        
        // Check for specific architectural elements
        const hasArchitecturalContent = this.hasArchitecturalContent(reminder, templateType);
        
        // Check for actionable guidance
        const hasActionableGuidance = reminder.includes('- [ ]') && reminder.includes('analyze');
        
        const contentQuality = hasSuperAdminRefs && hasArchitecturalContent && hasActionableGuidance;
        
        this.addTest('templateLoading',
          `Content Quality: ${templateType}`,
          contentQuality,
          contentQuality ?
            'Contains all required architectural elements' :
            `Missing: ${!hasSuperAdminRefs ? 'SUPER_ADMIN refs ' : ''}${!hasArchitecturalContent ? 'architectural content ' : ''}${!hasActionableGuidance ? 'actionable guidance' : ''}`
        );
        
      } catch (error) {
        this.addTest('templateLoading',
          `Content Quality: ${templateType}`,
          false,
          `Error: ${error.message}`
        );
      }
    }
  }

  /**
   * Check if reminder has appropriate architectural content
   */
  hasArchitecturalContent(reminder, templateType) {
    const architecturalElements = {
      'PERMISSION_ARCHITECTURE': [
        'permission', 'role', 'granular', 'template'
      ],
      'ORGANIZATION_CONTROLS': [
        'organization', 'shared', 'segregated', 'isolated', 'white-label'
      ],
      'SUBSCRIPTION_ARCHITECTURE': [
        'subscription', 'plan', 'billing', 'feature'
      ],
      'DATABASE_ARCHITECTURE': [
        'migration', 'schema', 'RLS', 'multi-tenant'
      ]
    };

    const requiredElements = architecturalElements[templateType] || [];
    const foundElements = requiredElements.filter(element =>
      reminder.toLowerCase().includes(element.toLowerCase())
    );

    return foundElements.length >= Math.ceil(requiredElements.length * 0.7); // 70% of elements found
  }

  /**
   * Validate reminder formatting quality
   */
  validateReminderFormatting(reminder) {
    const issues = [];
    
    // Check for proper markdown headers
    if (!reminder.includes('**') && !reminder.includes('#')) {
      issues.push('Missing markdown headers');
    }
    
    // Check for proper list formatting
    if (reminder.includes('- [ ]') && !reminder.includes('\n- [ ]')) {
      issues.push('Improper checklist formatting');
    }
    
    // Check for code blocks if they exist
    if (reminder.includes('```') && (reminder.split('```').length - 1) % 2 !== 0) {
      issues.push('Unclosed code blocks');
    }
    
    // Check for reasonable length
    if (reminder.length < 200) {
      issues.push('Content too short');
    }
    
    // Check for proper line breaks
    if (!reminder.includes('\n\n')) {
      issues.push('Missing paragraph breaks');
    }
    
    return {
      valid: issues.length === 0,
      issues
    };
  }

  /**
   * Load hook files from the hooks directory
   */
  async loadHookFiles() {
    const hookFiles = [];
    
    try {
      if (!fs.existsSync(this.hookDirectory)) {
        return hookFiles;
      }

      const files = fs.readdirSync(this.hookDirectory);
      
      for (const file of files) {
        if (file.endsWith('.kiro.hook')) {
          try {
            const filePath = path.join(this.hookDirectory, file);
            const content = fs.readFileSync(filePath, 'utf8');
            const hookConfig = JSON.parse(content);
            
            hookFiles.push({
              name: hookConfig.name || file,
              patterns: hookConfig.when?.patterns || [],
              enabled: hookConfig.enabled !== false,
              file: file
            });
          } catch (parseError) {
            console.warn(`Failed to parse hook file ${file}:`, parseError.message);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load hook files:', error.message);
    }
    
    return hookFiles;
  }

  /**
   * Check if a hook should trigger for a given file path
   */
  shouldHookTrigger(patterns, filePath) {
    if (!patterns || !Array.isArray(patterns)) {
      return false;
    }
    
    const normalizedPath = filePath.replace(/\\/g, '/');
    
    return patterns.some(pattern => {
      try {
        // Convert glob pattern to regex
        const regexPattern = pattern
          .replace(/\*\*/g, '.*')
          .replace(/\*/g, '[^/]*')
          .replace(/\./g, '\\.');
        
        const regex = new RegExp(`^${regexPattern}$`);
        return regex.test(normalizedPath);
      } catch (error) {
        console.warn(`Invalid pattern: ${pattern}`, error.message);
        return false;
      }
    });
  }

  /**
   * Add test result with category tracking
   */
  addTest(category, name, passed, details) {
    if (passed) {
      this.testResults.passed++;
      this.testResults.categories[category].passed++;
      console.log(`✅ ${name}: ${details}`);
    } else {
      this.testResults.failed++;
      this.testResults.categories[category].failed++;
      console.log(`❌ ${name}: ${details}`);
    }

    this.testResults.details.push({ 
      category, 
      name, 
      passed, 
      details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Generate comprehensive test report
   */
  generateTestReport() {
    console.log('\n📊 Comprehensive Test Results');
    console.log('='.repeat(60));
    
    // Overall summary
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`⚠️  Warnings: ${this.testResults.warnings}`);
    console.log(`📊 Total: ${this.testResults.passed + this.testResults.failed}`);
    
    const successRate = (this.testResults.passed / (this.testResults.passed + this.testResults.failed) * 100).toFixed(1);
    console.log(`🎯 Success Rate: ${successRate}%`);

    // Category breakdown
    console.log('\n📋 Results by Category:');
    for (const [category, results] of Object.entries(this.testResults.categories)) {
      const total = results.passed + results.failed;
      const rate = total > 0 ? (results.passed / total * 100).toFixed(1) : '0.0';
      console.log(`  ${category}: ${results.passed}/${total} (${rate}%)`);
    }

    // Requirements coverage
    console.log('\n✅ Requirements Coverage:');
    console.log('  1.1 Hook trigger patterns and file matching: ✅ Tested');
    console.log('  1.2 Reminder template loading and content generation: ✅ Tested');
    console.log('  1.3 Hook execution for different file modification scenarios: ✅ Tested');
    console.log('  1.4 Error handling and graceful degradation: ✅ Tested');

    // Final assessment
    if (this.testResults.failed === 0) {
      console.log('\n🎉 All tests passed! Hook execution and reminder display system is working correctly.');
      console.log('✅ Task 9 requirements fully satisfied.');
    } else {
      console.log('\n🔧 Some tests failed. Please review the errors above.');
      console.log('❌ Task 9 requirements partially satisfied.');
    }

    // Generate detailed report object
    return {
      timestamp: new Date().toISOString(),
      summary: {
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        warnings: this.testResults.warnings,
        successRate: parseFloat(successRate),
        totalTests: this.testResults.passed + this.testResults.failed
      },
      categories: this.testResults.categories,
      details: this.testResults.details,
      requirementsCoverage: {
        '1.1': 'Hook trigger patterns and file matching - TESTED',
        '1.2': 'Reminder template loading and content generation - TESTED',
        '1.3': 'Hook execution scenarios - TESTED',
        '1.4': 'Error handling and graceful degradation - TESTED'
      },
      systemHealth: {
        governance: GovernanceReminderGenerator.validateSystem(),
        contextDetector: GovernanceContextDetector.validatePatternSystem(),
        templateEngine: ReminderTemplateEngine.validateTemplateSystem()
      }
    };
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new HookExecutionTester();
  tester.runAllTests().then((success) => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
  });
}

module.exports = HookExecutionTester;