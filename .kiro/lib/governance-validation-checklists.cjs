/**
 * Comprehensive Validation Checklists for SUPER_ADMIN Control Compliance
 * 
 * This module provides detailed validation checklists for different governance contexts
 * to ensure developers leverage existing SUPER_ADMIN capabilities instead of creating
 * duplicate or conflicting systems.
 */

const VALIDATION_CHECKLISTS = {
  
  /**
   * Permission Architecture Validation Checklist
   * Used when permission-related files are modified
   */
  PERMISSION_ARCHITECTURE: {
    title: "🔐 PERMISSION ARCHITECTURE VALIDATION",
    description: "Ensure your permission changes leverage existing SUPER_ADMIN control capabilities",
    
    sections: [
      {
        title: "Core Permission System Compliance",
        items: [
          {
            text: "Uses existing granular permission keys instead of creating new ones",
            correct: "permissions.includes('quotes.edit')",
            incorrect: "Creating new permission like 'quotes.modify'",
            guidance: "Use one of the 20 existing granular permission keys: quotes.create, quotes.edit, quotes.delete, events.create, events.manage, users.manage, analytics.view, etc."
          },
          {
            text: "Respects permission template hierarchy (basic → premium → enterprise)",
            correct: "Check template level before granting advanced permissions",
            incorrect: "Hardcoding permissions without template consideration",
            guidance: "basic_client gets core features, premium_client gets enhanced features, tnc_enterprise gets full access"
          },
          {
            text: "Checks organization-level permissions before user-level permissions",
            correct: "organization.permissions.includes(permission) && user.permissions.includes(permission)",
            incorrect: "Only checking user permissions",
            guidance: "Permission hierarchy: Organization Template → User Permissions → Final Access"
          },
          {
            text: "Implements SUPER_ADMIN override capabilities",
            correct: "user.role === 'SUPER_ADMIN' || hasPermission(user, permission)",
            incorrect: "No SUPER_ADMIN bypass logic",
            guidance: "SUPER_ADMIN should always be able to override permission restrictions"
          },
          {
            text: "Maintains organization-based data isolation",
            correct: "All queries include organization_id filtering",
            incorrect: "Global queries without organization scope",
            guidance: "Every database query must be scoped to the user's organization"
          }
        ]
      },
      
      {
        title: "Permission Template Integration",
        items: [
          {
            text: "Validates against permission template capabilities",
            guidance: "basic_client: Core features only | premium_client: Enhanced features | tnc_enterprise: Full access"
          },
          {
            text: "Handles permission template upgrades/downgrades gracefully",
            correct: "Graceful feature enablement/disablement",
            incorrect: "Breaking functionality on template changes"
          },
          {
            text: "Provides fallback for missing permissions",
            correct: "Default to most restrictive permission level",
            incorrect: "Failing silently or granting excessive access"
          }
        ]
      },
      
      {
        title: "API Route Permission Validation",
        items: [
          {
            text: "Every API route validates user permissions",
            example: `
// ✅ CORRECT Pattern
const { user, organization, permissions } = await validateUserAccess(request);
if (!permissions.includes('required.permission')) {
  return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
}`
          },
          {
            text: "Permission checks happen before business logic",
            guidance: "Validate permissions first, then execute business logic"
          },
          {
            text: "Error messages are consistent and informative",
            guidance: "Use standard error format with clear permission requirements"
          }
        ]
      }
    ],
    
    availablePermissions: [
      'quotes.create', 'quotes.edit', 'quotes.delete', 'can_create_quotes',
      'events.create', 'events.manage',
      'users.manage', 'can_manage_users', 'manage_users',
      'analytics.view', 'analytics.export', 'view_analytics',
      'billing.view', 'manage_billing',
      'affiliates.manage', 'api.access',
      'manage_settings', 'manage_all_organizations', 'super_admin',
      'white_label.branding'
    ]
  },

  /**
   * Organization Controls Validation Checklist
   * Used when organization-related files are modified
   */
  ORGANIZATION_CONTROLS: {
    title: "🏢 ORGANIZATION CONTROLS VALIDATION",
    description: "Ensure your organization changes respect the three-tier architecture and white-label capabilities",
    
    sections: [
      {
        title: "Organization Type Architecture Compliance",
        items: [
          {
            text: "Considers organization type in data access patterns",
            guidance: "shared: Shared affiliate network | segregated: Isolated networks | isolated: Complete data isolation"
          },
          {
            text: "Respects data isolation levels",
            correct: "shared orgs can access shared affiliate network",
            incorrect: "Mixing data across isolation levels"
          },
          {
            text: "Implements organization type-specific business logic",
            example: `
// ✅ CORRECT: Type-specific behavior
switch (organization.organization_type) {
  case 'shared':
    return getSharedAffiliateNetwork();
  case 'segregated':
    return getOrganizationAffiliateNetwork(orgId);
  case 'isolated':
    return getIsolatedAffiliateNetwork(orgId);
}`
          }
        ]
      },
      
      {
        title: "White-Label Feature Implementation",
        items: [
          {
            text: "Uses white-label feature flags correctly",
            guidance: "has_white_labeling: Custom branding | has_custom_domain: Custom domain | has_custom_branding: Logo/colors"
          },
          {
            text: "White-label features work with any organization type",
            correct: "shared + has_white_labeling = custom branding on shared network",
            incorrect: "Restricting white-label to specific organization types"
          },
          {
            text: "Graceful degradation when white-label disabled",
            correct: "Falls back to default branding",
            incorrect: "Breaking UI when white-label flags are false"
          }
        ]
      },
      
      {
        title: "Organization Settings Integration",
        items: [
          {
            text: "Uses organization settings for operational parameters",
            example: `
// ✅ CORRECT: Settings-based limits
const monthlyLimit = organization.settings?.maxQuotesPerMonth || 100;
if (currentQuotes >= monthlyLimit) {
  throw new Error('Monthly quote limit exceeded');
}`
          },
          {
            text: "Validates settings against subscription plan",
            guidance: "Ensure settings are appropriate for the organization's subscription tier"
          },
          {
            text: "Provides sensible defaults for missing settings",
            guidance: "Always have fallback values for optional settings"
          }
        ]
      }
    ],
    
    organizationTypes: ['shared', 'segregated', 'isolated'],
    whiteLabelFlags: ['has_white_labeling', 'has_custom_domain', 'has_custom_branding']
  },

  /**
   * Subscription Architecture Validation Checklist
   * Used when subscription/billing-related files are modified
   */
  SUBSCRIPTION_ARCHITECTURE: {
    title: "💳 SUBSCRIPTION ARCHITECTURE VALIDATION",
    description: "Ensure your subscription changes integrate with permission templates and feature flags",
    
    sections: [
      {
        title: "Subscription Plan Integration",
        items: [
          {
            text: "Maps subscription plans to permission templates correctly",
            guidance: "free_trial → basic_client | professional → premium_client | enterprise → tnc_enterprise"
          },
          {
            text: "Enforces subscription-based limits",
            guidance: "free_trial: 5 quotes/month | professional: 100 quotes/month | enterprise: Unlimited"
          },
          {
            text: "Validates feature access against subscription tier",
            example: `
// ✅ CORRECT: Subscription-based feature gating
if (organization.subscription_plan === 'free_trial' && requestedFeature === 'api_access') {
  throw new Error('API access requires Professional or Enterprise subscription');
}`
          }
        ]
      },
      
      {
        title: "Feature Flag System Integration",
        items: [
          {
            text: "Uses feature flags for conditional functionality",
            guidance: "Available flags: apiAccess, whiteLabeling, realTimeTracking, advancedAnalytics, prioritySupport, customIntegrations, bulkOperations, advancedReporting"
          },
          {
            text: "Feature flags override subscription defaults when enabled",
            correct: "free_trial + apiAccess flag = API access enabled",
            incorrect: "Ignoring feature flags in favor of subscription defaults"
          },
          {
            text: "Handles feature flag changes dynamically",
            correct: "Real-time feature enablement/disablement",
            incorrect: "Requiring app restart for feature flag changes"
          }
        ]
      },
      
      {
        title: "Billing Integration Consistency",
        items: [
          {
            text: "Subscription changes update permission templates",
            guidance: "When subscription changes, ensure permission template is updated accordingly"
          },
          {
            text: "Billing limits are enforced in real-time",
            guidance: "Check limits before allowing actions, not after"
          },
          {
            text: "Graceful handling of subscription downgrades",
            correct: "Disable features, preserve data",
            incorrect: "Breaking functionality or losing data"
          }
        ]
      }
    ],
    
    subscriptionPlans: [
      { name: 'free_trial', price: '$0', features: 'Basic quotes, email support' },
      { name: 'starter', price: '$49', features: 'Basic analytics, enhanced support' },
      { name: 'professional', price: '$149', features: 'API access, priority support, advanced analytics' },
      { name: 'enterprise', price: '$499', features: 'White labeling, full feature access, dedicated support' }
    ],
    
    permissionTemplates: ['basic_client', 'premium_client', 'tnc_enterprise'],
    
    featureFlags: [
      'apiAccess', 'whiteLabeling', 'realTimeTracking', 'advancedAnalytics',
      'prioritySupport', 'customIntegrations', 'bulkOperations', 'advancedReporting'
    ]
  },

  /**
   * Database Migration Validation Checklist
   * Used when database migration files are created/modified
   */
  DATABASE_MIGRATION: {
    title: "🗄️ DATABASE MIGRATION VALIDATION",
    description: "Ensure your database changes preserve SUPER_ADMIN control architecture and multi-tenant isolation",
    
    sections: [
      {
        title: "Schema Architecture Preservation",
        items: [
          {
            text: "Preserves existing permission template structure",
            guidance: "Don't modify core permission template tables. Extend through additional tables if needed."
          },
          {
            text: "Maintains organization type architecture",
            guidance: "Keep organization_type enum values: shared, segregated, isolated"
          },
          {
            text: "Respects granular permission key naming conventions",
            guidance: "Use dot notation: 'resource.action' (e.g., 'quotes.edit'). Maintain consistency with existing 20 permission keys."
          }
        ]
      },
      
      {
        title: "Multi-Tenant Data Isolation",
        items: [
          {
            text: "Includes proper RLS policies for multi-tenant isolation",
            example: `
-- ✅ CORRECT: Organization-based RLS policy
CREATE POLICY "table_organization_isolation" ON table_name
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id FROM user_profiles 
      WHERE user_id = auth.uid()
    )
    OR 
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.uid() 
      AND role = 'SUPER_ADMIN'
    )
  );`
          },
          {
            text: "Every table with organization_id has RLS policies",
            guidance: "Multi-tenant tables must have organization-based access control"
          },
          {
            text: "SUPER_ADMIN bypass is explicitly included in policies",
            guidance: "SUPER_ADMIN should always be able to access all organization data"
          }
        ]
      },
      
      {
        title: "SUPER_ADMIN Control Impact Assessment",
        items: [
          {
            text: "Documents impact on SUPER_ADMIN control capabilities",
            guidance: "How does this migration affect organization creation, permission templates, or user controls?"
          },
          {
            text: "Maintains SUPER_ADMIN override capabilities",
            guidance: "SUPER_ADMIN must retain ability to access all organizations and modify all permissions"
          },
          {
            text: "Preserves existing control patterns",
            guidance: "Don't break existing SUPER_ADMIN workflows. Extend rather than replace."
          }
        ]
      }
    ],
    
    requiredPolicies: [
      "Organization-based RLS policies for all multi-tenant tables",
      "SUPER_ADMIN bypass clauses in all policies",
      "Proper indexing on organization_id columns"
    ]
  }
};

/**
 * Generate a formatted validation checklist for a specific context
 */
function generateValidationChecklist(context, fileType = '') {
  const checklist = VALIDATION_CHECKLISTS[context];
  if (!checklist) {
    return `❌ Unknown validation context: ${context}`;
  }

  let output = `\n${checklist.title}\n`;
  output += `${'='.repeat(checklist.title.length)}\n\n`;
  output += `${checklist.description}\n\n`;

  // Add file type specific guidance
  if (fileType) {
    output += `📁 **File Type**: ${fileType}\n\n`;
  }

  // Add sections
  checklist.sections.forEach(section => {
    output += `## ${section.title}\n\n`;
    
    section.items.forEach(item => {
      output += `- [ ] **${item.text}**\n`;
      
      if (item.correct && item.incorrect) {
        output += `  - ✅ CORRECT: ${item.correct}\n`;
        output += `  - ❌ WRONG: ${item.incorrect}\n`;
      }
      
      if (item.guidance) {
        output += `  - 💡 GUIDANCE: ${item.guidance}\n`;
      }
      
      if (item.example) {
        output += `  - 📝 EXAMPLE:${item.example}\n`;
      }
      
      output += '\n';
    });
  });

  // Add context-specific reference information
  if (checklist.availablePermissions) {
    output += `## 🔑 Available Granular Permissions\n\n`;
    checklist.availablePermissions.forEach(permission => {
      output += `- \`${permission}\`\n`;
    });
    output += '\n';
  }

  if (checklist.organizationTypes) {
    output += `## 🏢 Organization Types\n\n`;
    checklist.organizationTypes.forEach(type => {
      output += `- \`${type}\`\n`;
    });
    output += '\n';
  }

  if (checklist.subscriptionPlans) {
    output += `## 💳 Subscription Plans\n\n`;
    checklist.subscriptionPlans.forEach(plan => {
      output += `- **${plan.name}** (${plan.price}): ${plan.features}\n`;
    });
    output += '\n';
  }

  if (checklist.featureFlags) {
    output += `## 🚀 Available Feature Flags\n\n`;
    checklist.featureFlags.forEach(flag => {
      output += `- \`${flag}\`\n`;
    });
    output += '\n';
  }

  // Add quick validation summary
  output += `## 📋 Quick Validation Summary\n\n`;
  output += `### Before Implementing:\n`;
  output += `1. Check existing SUPER_ADMIN controls - Can this be achieved with current capabilities?\n`;
  output += `2. Identify required permission level - Which granular permissions are needed?\n`;
  output += `3. Determine subscription requirements - What subscription tier should this require?\n`;
  output += `4. Consider organization type impact - How does this behave across shared/segregated/isolated?\n`;
  output += `5. Plan feature flag integration - Should this be toggleable via feature flags?\n\n`;

  output += `### Red Flags to Avoid:\n`;
  output += `- ❌ Creating new permission systems instead of using existing granular permissions\n`;
  output += `- ❌ Hardcoding subscription or organization type assumptions\n`;
  output += `- ❌ Bypassing SUPER_ADMIN control architecture\n`;
  output += `- ❌ Breaking multi-tenant data isolation\n`;
  output += `- ❌ Ignoring permission template hierarchy\n\n`;

  output += `### Green Lights for Implementation:\n`;
  output += `- ✅ Uses existing granular permission keys\n`;
  output += `- ✅ Respects organization type architecture\n`;
  output += `- ✅ Integrates with subscription plan system\n`;
  output += `- ✅ Supports feature flag control\n`;
  output += `- ✅ Maintains SUPER_ADMIN override capabilities\n`;
  output += `- ✅ Preserves multi-tenant data isolation\n\n`;

  return output;
}

/**
 * Get validation checklist for specific file patterns
 */
function getChecklistForFile(filePath) {
  const path = filePath.toLowerCase();
  
  // Database migration files (check first to avoid conflicts)
  if (path.includes('migration') || (path.endsWith('.sql') && path.includes('supabase'))) {
    return generateValidationChecklist('DATABASE_MIGRATION', 'Database Migration');
  }
  
  // Permission-related files
  if (path.includes('permission') || path.includes('auth') || path.includes('middleware')) {
    return generateValidationChecklist('PERMISSION_ARCHITECTURE', 'Permission/Auth');
  }
  
  // Organization-related files
  if (path.includes('organization') || path.includes('tenant') || path.includes('white-label')) {
    return generateValidationChecklist('ORGANIZATION_CONTROLS', 'Organization');
  }
  
  // Subscription-related files
  if (path.includes('subscription') || path.includes('billing') || path.includes('plan')) {
    return generateValidationChecklist('SUBSCRIPTION_ARCHITECTURE', 'Subscription/Billing');
  }
  
  // API routes - determine context based on path
  if (path.includes('api/')) {
    if (path.includes('organization') || path.includes('tenant')) {
      return generateValidationChecklist('ORGANIZATION_CONTROLS', 'API Route - Organization');
    } else if (path.includes('subscription') || path.includes('billing')) {
      return generateValidationChecklist('SUBSCRIPTION_ARCHITECTURE', 'API Route - Subscription');
    } else {
      return generateValidationChecklist('PERMISSION_ARCHITECTURE', 'API Route - General');
    }
  }
  
  // Default to permission architecture for other files
  return generateValidationChecklist('PERMISSION_ARCHITECTURE', 'General');
}

module.exports = {
  VALIDATION_CHECKLISTS,
  generateValidationChecklist,
  getChecklistForFile
};