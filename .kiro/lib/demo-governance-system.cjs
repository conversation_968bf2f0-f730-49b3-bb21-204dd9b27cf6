#!/usr/bin/env node

/**
 * Demonstration script for Architectural Governance System
 * Shows the hook trigger logic and file pattern matching in action
 */

const GovernanceReminderGenerator = require('./governance-reminder-generator.cjs');

console.log('🎛️ Architectural Governance System Demo\n');

// Demo 1: File Pattern Matching
console.log('📁 Demo 1: File Pattern Matching');
console.log('================================\n');

const testFiles = [
  'app/api/organizations/route.ts',
  'app/lib/auth/permissions.ts', 
  'app/api/subscriptions/billing.ts',
  'supabase/migrations/001_create_orgs.sql',
  'app/utils/helper.ts'
];

testFiles.forEach(filePath => {
  const context = require('./governance-context-detector.cjs').detectContext(filePath);
  console.log(`📄 ${filePath}`);
  console.log(`   Context: ${context.context}`);
  console.log(`   Template: ${context.template}`);
  console.log(`   Confidence: ${(context.confidence * 100).toFixed(1)}%\n`);
});

// Demo 2: Hook Trigger Simulation
console.log('🪝 Demo 2: Hook Trigger Simulation');
console.log('==================================\n');

const hookDemos = [
  {
    hookName: 'permission-architecture-validator',
    filePath: 'app/lib/auth/permissions.ts',
    description: 'Permission file modification'
  },
  {
    hookName: 'organization-controls-validator', 
    filePath: 'app/contexts/OrganizationContext.tsx',
    description: 'Organization context modification'
  }
];

hookDemos.forEach(demo => {
  console.log(`🔧 Hook: ${demo.hookName}`);
  console.log(`📄 File: ${demo.filePath}`);
  console.log(`📝 Scenario: ${demo.description}\n`);
  
  const reminder = GovernanceReminderGenerator.generateHookReminder(
    demo.hookName,
    [demo.filePath]
  );
  
  // Show first few lines of reminder
  const lines = reminder.split('\n');
  console.log('Generated Reminder Preview:');
  console.log('---------------------------');
  lines.slice(0, 8).forEach(line => console.log(line));
  console.log('...\n');
});

// Demo 3: Error Handling
console.log('⚠️  Demo 3: Error Handling');
console.log('=========================\n');

console.log('Testing graceful error handling...\n');

// Test invalid file path
const invalidReminder = GovernanceReminderGenerator.generateReminder(null);
console.log('✅ Null file path handled gracefully');

// Test invalid hook
const invalidHookReminder = GovernanceReminderGenerator.generateHookReminder(
  'nonexistent-hook',
  ['test.ts']
);
console.log('✅ Invalid hook handled gracefully');

// Test malformed path
const malformedReminder = GovernanceReminderGenerator.generateReminder('\\\\invalid\\path');
console.log('✅ Malformed path handled gracefully\n');

// Demo 4: System Health
console.log('🔍 Demo 4: System Health Check');
console.log('==============================\n');

const systemHealth = GovernanceReminderGenerator.validateSystem();
console.log(`System Status: ${systemHealth.healthy ? '✅ Healthy' : '❌ Issues Found'}`);
console.log(`Components: ${Object.keys(systemHealth.components).length}`);

Object.entries(systemHealth.components).forEach(([component, status]) => {
  const icon = status.available ? '✅' : '❌';
  console.log(`  ${icon} ${component}: ${status.available ? 'Available' : 'Unavailable'}`);
});

if (systemHealth.warnings.length > 0) {
  console.log('\nWarnings:');
  systemHealth.warnings.forEach(warning => console.log(`  ⚠️  ${warning}`));
}

console.log('\n🎉 Demo completed! The Architectural Governance System is working correctly.');
console.log('\nKey Features Demonstrated:');
console.log('• File pattern matching with confidence scoring');
console.log('• Hook trigger logic with contextual reminders');
console.log('• Graceful error handling without blocking operations');
console.log('• System health validation and diagnostics');
console.log('• Integration with SUPER_ADMIN controls reference\n');