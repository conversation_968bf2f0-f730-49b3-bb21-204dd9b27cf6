# Architectural Governance System - Maintenance Guide

## Overview

The Architectural Governance System provides contextual reminders and validation checklists to ensure developers leverage existing SUPER_ADMIN control capabilities when modifying code. This guide covers how to maintain and update the governance rules.

## System Components

### 1. Steering File
- **Location**: `.kiro/steering/super-admin-controls-reference.md`
- **Purpose**: Contains complete SUPER_ADMIN control capabilities reference
- **Inclusion**: Always included in AI assistant context (`inclusion: always`)

### 2. Governance Hooks
- **Location**: `.kiro/hooks/`
- **Key Hooks**:
  - `permission-architecture-validator.kiro.hook`
  - `organization-controls-validator.kiro.hook`
  - `subscription-architecture-validator.kiro.hook`
  - `database-migration-governance.kiro.hook`

### 3. Reminder Generation System
- **Location**: `.kiro/lib/`
- **Components**:
  - `governance-reminder-generator.cjs` - Main interface
  - `reminder-template-engine.cjs` - Template system
  - `governance-context-detector.cjs` - File pattern matching
  - `governance-validation-checklists.cjs` - Validation logic

## Maintenance Tasks

### Updating SUPER_ADMIN Controls Reference

When new SUPER_ADMIN capabilities are added to the platform:

1. **Update the steering file** (`.kiro/steering/super-admin-controls-reference.md`):
   ```markdown
   ## New Control Section
   ### New Feature Controls
   - Add new control descriptions
   - Include code examples
   - Update validation checklists
   ```

2. **Test the update**:
   ```bash
   node -e "
   const generator = require('./.kiro/lib/governance-reminder-generator.cjs');
   const validation = generator.validateSteeringFile();
   console.log('Steering file validation:', validation);
   "
   ```

### Adding New File Patterns

To add new file patterns that should trigger governance reminders:

1. **Update hook patterns** in relevant `.kiro.hook` files:
   ```json
   {
     "when": {
       "type": "fileEdited",
       "patterns": [
         "existing/pattern/**/*.ts",
         "new/pattern/**/*.tsx"  // Add new pattern
       ]
     }
   }
   ```

2. **Update context detector** (`.kiro/lib/governance-context-detector.cjs`):
   ```javascript
   const GOVERNANCE_PATTERNS = {
     permissions: [
       /existing\/pattern/,
       /new\/pattern/  // Add new pattern
     ]
   };
   ```

### Creating New Reminder Templates

To add new types of governance reminders:

1. **Add template to engine** (`.kiro/lib/reminder-template-engine.cjs`):
   ```javascript
   const REMINDER_TEMPLATES = {
     NEW_TEMPLATE_TYPE: {
       title: "🔧 NEW VALIDATION TYPE",
       sections: [
         "New Validation Section",
         "Code Examples",
         "Checklist Items"
       ]
     }
   };
   ```

2. **Create template content**:
   ```javascript
   generateNewTemplateReminder(context) {
     return `
   🔧 **NEW VALIDATION TYPE** 🔧
   
   Validation content here...
   `;
   }
   ```

### Adding New Validation Checklists

To add new validation points:

1. **Update validation checklists** (`.kiro/lib/governance-validation-checklists.cjs`):
   ```javascript
   const NEW_VALIDATION_CHECKLIST = [
     {
       category: "New Category",
       items: [
         {
           description: "New validation point",
           correct: "✅ CORRECT: Example",
           wrong: "❌ WRONG: Counter-example",
           guidance: "💡 GUIDANCE: How to implement correctly"
         }
       ]
     }
   ];
   ```

## Testing and Validation

### System Health Check

Run comprehensive system validation:

```bash
node -e "
const generator = require('./.kiro/lib/governance-reminder-generator.cjs');
const validation = generator.validateSystem();
console.log('System Health:', validation.healthy ? 'HEALTHY' : 'ISSUES FOUND');
console.log('Errors:', validation.errors);
console.log('Warnings:', validation.warnings);
console.log('Components:', Object.keys(validation.components));
"
```

### Test Reminder Generation

Test reminder generation for specific file types:

```bash
node -e "
const generator = require('./.kiro/lib/governance-reminder-generator.cjs');

// Test different file types
const testFiles = [
  'app/api/organizations/route.ts',
  'app/lib/auth/permissions.ts',
  'components/subscription/SubscriptionPanel.tsx',
  'supabase/migrations/001_new_migration.sql'
];

testFiles.forEach(file => {
  const result = generator.testReminderGeneration(file);
  console.log(\`\${file}: \${result.templateUsed} (confidence: \${result.confidence})\`);
});
"
```

### Test Hook Integration

Test that hooks trigger correctly:

```bash
node -e "
const generator = require('./.kiro/lib/governance-reminder-generator.cjs');

// Test hook-triggered reminders
const hooks = [
  'permission-architecture-validator',
  'organization-controls-validator',
  'subscription-architecture-validator',
  'database-migration-governance'
];

hooks.forEach(hook => {
  const reminder = generator.generateHookReminder(hook, ['test-file.ts']);
  console.log(\`Hook \${hook}: \${reminder.length} characters generated\`);
});
"
```

## Troubleshooting

### Common Issues

1. **Steering file not found**:
   - Verify `.kiro/steering/super-admin-controls-reference.md` exists
   - Check file permissions
   - Ensure proper front-matter: `inclusion: always`

2. **Hooks not triggering**:
   - Verify hook files are valid JSON
   - Check file patterns match actual file paths
   - Ensure hooks are enabled: `"enabled": true`

3. **Template generation errors**:
   - Check template engine initialization
   - Verify all required templates exist
   - Test with fallback templates

4. **Context detection issues**:
   - Validate file pattern matching
   - Check confidence scores
   - Test with multiple file types

### Debug Commands

Enable debug logging:

```bash
node -e "
const generator = require('./.kiro/lib/governance-reminder-generator.cjs');

// Enable verbose logging
process.env.DEBUG = 'governance:*';

// Test with debug output
const result = generator.testReminderGeneration('app/api/test.ts');
console.log('Debug result:', result);
"
```

## Best Practices

### Maintaining Consistency

1. **Keep steering file synchronized** with actual SUPER_ADMIN capabilities
2. **Update hook patterns** as codebase structure evolves
3. **Regular review** of reminder effectiveness
4. **Community feedback** integration for improvements

### Performance Considerations

1. **File pattern optimization** - Use specific patterns to avoid false triggers
2. **Template caching** - Cache frequently used templates
3. **Minimal file reading** - Only read file content when necessary
4. **Error handling** - Graceful degradation when components fail

### Documentation Standards

1. **Clear examples** in steering file
2. **Actionable guidance** in validation checklists
3. **Code snippets** showing correct patterns
4. **Regular updates** to reflect platform changes

## Integration with Development Workflow

### IDE Integration

The governance system integrates with the development workflow through:

1. **File modification triggers** - Hooks activate when relevant files are edited
2. **Contextual reminders** - AI assistant displays appropriate guidance
3. **Validation checklists** - Developers can verify compliance
4. **Reference documentation** - Always available steering file

### Continuous Improvement

1. **Monitor hook effectiveness** - Track which reminders are most helpful
2. **Gather developer feedback** - Improve based on actual usage
3. **Update patterns regularly** - Keep pace with codebase evolution
4. **Refine templates** - Make reminders more actionable over time

## Version History

- **v1.0.0** - Initial implementation with core governance hooks
- **v1.1.0** - Added comprehensive validation checklists
- **v1.2.0** - Enhanced template system with better error handling
- **v1.3.0** - Integrated with existing steering system

## Support

For issues or questions about the governance system:

1. Check this maintenance guide
2. Run system validation commands
3. Review hook configuration files
4. Test reminder generation manually
5. Update steering file if capabilities have changed

Remember: The governance system is designed to help developers leverage existing SUPER_ADMIN capabilities instead of creating duplicate systems. Keep it updated and relevant to maintain its effectiveness.