#!/usr/bin/env node

/**
 * Integration Test for Hook System
 * Tests the complete hook execution flow from file modification to reminder display
 */

const GovernanceReminderGenerator = require('./governance-reminder-generator.cjs');
const fs = require('fs');
const path = require('path');

class HookIntegrationTester {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      details: []
    };
  }

  async runIntegrationTests() {
    console.log('🔗 Running Hook System Integration Tests\n');

    // Test complete hook execution flow
    await this.testCompleteHookFlow();
    
    // Test hook reminder generation
    await this.testHookReminderGeneration();
    
    // Test system resilience
    await this.testSystemResilience();

    this.printResults();
    return this.testResults.failed === 0;
  }

  async testCompleteHookFlow() {
    console.log('🔄 Testing Complete Hook Execution Flow');
    console.log('-'.repeat(40));

    const testFiles = [
      'app/lib/auth/permissions.ts',
      'app/contexts/OrganizationContext.tsx',
      'app/api/subscriptions/route.ts',
      'supabase/migrations/001_test.sql'
    ];

    for (const filePath of testFiles) {
      try {
        // Simulate hook execution
        const reminder = GovernanceReminderGenerator.generateReminder(filePath);
        
        // Validate reminder was generated
        const hasContent = reminder && reminder.length > 0;
        const hasValidation = reminder.includes('VALIDATION');
        const hasChecklist = reminder.includes('- [ ]') || reminder.includes('Validation Checklist');
        const hasSuperAdminRef = reminder.includes('SUPER_ADMIN');
        
        const integrationSuccess = hasContent && hasValidation && hasChecklist && hasSuperAdminRef;
        
        this.addTest(
          `Integration Flow: ${path.basename(filePath)}`,
          integrationSuccess,
          integrationSuccess ? 
            `Generated complete reminder (${reminder.length} chars)` :
            `Missing: ${!hasContent ? 'content ' : ''}${!hasValidation ? 'validation ' : ''}${!hasChecklist ? 'checklist ' : ''}${!hasSuperAdminRef ? 'SUPER_ADMIN ref' : ''}`
        );

      } catch (error) {
        this.addTest(
          `Integration Flow: ${path.basename(filePath)}`,
          false,
          `Error: ${error.message}`
        );
      }
    }
  }

  async testHookReminderGeneration() {
    console.log('\n📝 Testing Hook Reminder Generation');
    console.log('-'.repeat(40));

    const hookTests = [
      {
        hookName: 'permission-architecture-validator',
        files: ['app/lib/auth/permissions.ts'],
        expectedContent: ['PERMISSION', 'architecture', 'granular']
      },
      {
        hookName: 'organization-controls-validator', 
        files: ['app/contexts/OrganizationContext.tsx'],
        expectedContent: ['ORGANIZATION', 'shared', 'segregated']
      },
      {
        hookName: 'database-migration-governance',
        files: ['supabase/migrations/001_test.sql'],
        expectedContent: ['DATABASE', 'migration', 'RLS']
      }
    ];

    for (const test of hookTests) {
      try {
        const reminder = GovernanceReminderGenerator.generateHookReminder(test.hookName, test.files);
        
        let foundContent = [];
        let missingContent = [];
        
        for (const expectedItem of test.expectedContent) {
          if (reminder.toLowerCase().includes(expectedItem.toLowerCase())) {
            foundContent.push(expectedItem);
          } else {
            missingContent.push(expectedItem);
          }
        }
        
        const contentScore = foundContent.length / test.expectedContent.length;
        const hasGoodContent = contentScore >= 0.6; // 60% of expected content
        
        this.addTest(
          `Hook Reminder: ${test.hookName}`,
          hasGoodContent,
          hasGoodContent ?
            `Found ${foundContent.length}/${test.expectedContent.length} expected elements` :
            `Missing: ${missingContent.join(', ')}`
        );

      } catch (error) {
        this.addTest(
          `Hook Reminder: ${test.hookName}`,
          false,
          `Error: ${error.message}`
        );
      }
    }
  }

  async testSystemResilience() {
    console.log('\n🛡️ Testing System Resilience');
    console.log('-'.repeat(40));

    // Test with invalid hook names
    try {
      const reminder = GovernanceReminderGenerator.generateHookReminder('invalid-hook-name', ['test.ts']);
      const handledGracefully = reminder.includes('SUPER_ADMIN CONTROLS');
      
      this.addTest(
        'Invalid Hook Name',
        handledGracefully,
        handledGracefully ? 'Generated fallback reminder' : 'Failed to handle gracefully'
      );
    } catch (error) {
      this.addTest(
        'Invalid Hook Name',
        false,
        `Error: ${error.message}`
      );
    }

    // Test with empty file list
    try {
      const reminder = GovernanceReminderGenerator.generateHookReminder('permission-architecture-validator', []);
      const handledEmptyList = reminder.includes('SUPER_ADMIN CONTROLS');
      
      this.addTest(
        'Empty File List',
        handledEmptyList,
        handledEmptyList ? 'Generated reminder for empty file list' : 'Failed to handle empty list'
      );
    } catch (error) {
      this.addTest(
        'Empty File List',
        false,
        `Error: ${error.message}`
      );
    }

    // Test system health
    const systemHealth = GovernanceReminderGenerator.validateSystem();
    this.addTest(
      'System Health Check',
      systemHealth.healthy,
      systemHealth.healthy ? 
        'All system components healthy' :
        `Issues: ${systemHealth.errors.join(', ')}`
    );
  }

  addTest(name, passed, details) {
    if (passed) {
      this.testResults.passed++;
      console.log(`✅ ${name}: ${details}`);
    } else {
      this.testResults.failed++;
      console.log(`❌ ${name}: ${details}`);
    }

    this.testResults.details.push({ name, passed, details });
  }

  printResults() {
    console.log('\n📊 Integration Test Results');
    console.log('='.repeat(40));
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`📊 Total: ${this.testResults.passed + this.testResults.failed}`);
    
    const successRate = (this.testResults.passed / (this.testResults.passed + this.testResults.failed) * 100).toFixed(1);
    console.log(`🎯 Success Rate: ${successRate}%`);

    if (this.testResults.failed === 0) {
      console.log('\n🎉 All integration tests passed! Hook system is fully functional.');
    } else {
      console.log('\n🔧 Some integration tests failed. Please review the errors above.');
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new HookIntegrationTester();
  tester.runIntegrationTests().then((success) => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Integration test error:', error);
    process.exit(1);
  });
}

module.exports = HookIntegrationTester;