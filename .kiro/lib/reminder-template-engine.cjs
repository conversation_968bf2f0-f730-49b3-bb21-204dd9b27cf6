/**
 * <PERSON>minder Template Engine for Architectural Governance System
 * Generates contextual reminders based on file modifications and governance context
 */

const ValidationChecklists = require('./governance-validation-checklists.cjs');

class ReminderTemplateEngine {
  constructor() {
    this.templates = new Map();
    this.validationChecklists = ValidationChecklists;
    this.loadTemplates();
  }

  /**
   * Load all reminder templates
   */
  loadTemplates() {
    // Permission Architecture Template
    this.templates.set('PERMISSION_ARCHITECTURE', {
      title: '🔐 PERMISSION ARCHITECTURE VALIDATION',
      sections: [
        'Available Granular Permissions (20 keys)',
        'Permission Template System',
        'Role-Based Access Patterns',
        'Validation Checklist'
      ],
      generator: this.generatePermissionReminder.bind(this)
    });

    // Organization Controls Template
    this.templates.set('ORGANIZATION_CONTROLS', {
      title: '🏢 ORGANIZATION CONTROLS VALIDATION',
      sections: [
        'Organization Types (shared/segregated/isolated)',
        'White-Label Capabilities',
        'Feature Flag System',
        'Validation Checklist'
      ],
      generator: this.generateOrganizationReminder.bind(this)
    });

    // Subscription Architecture Template
    this.templates.set('SUBSCRIPTION_ARCHITECTURE', {
      title: '💳 SUBSCRIPTION ARCHITECTURE VALIDATION',
      sections: [
        'Subscription Plans & Permission Templates',
        'Feature Access Patterns',
        'Billing Integration Points',
        'Validation Checklist'
      ],
      generator: this.generateSubscriptionReminder.bind(this)
    });

    // Database Architecture Template
    this.templates.set('DATABASE_ARCHITECTURE', {
      title: '🗄️ DATABASE ARCHITECTURE VALIDATION',
      sections: [
        'Existing Permission Templates',
        'Role Hierarchy Structure',
        'Organization Schema Patterns',
        'Validation Checklist'
      ],
      generator: this.generateDatabaseReminder.bind(this)
    });

    // General Governance Template
    this.templates.set('GENERAL_GOVERNANCE', {
      title: '🎛️ SUPER_ADMIN CONTROLS VALIDATION',
      sections: [
        'Available SUPER_ADMIN Controls',
        'Validation Checklist'
      ],
      generator: this.generateGeneralReminder.bind(this)
    });
  }

  /**
   * Generate reminder based on context
   * @param {string} templateType - Type of reminder template
   * @param {Object} context - File modification context
   * @returns {string} Generated reminder content
   */
  generateReminder(templateType, context = {}) {
    try {
      // Validate inputs
      if (!templateType || typeof templateType !== 'string') {
        return this.generateDefaultReminder('INVALID_TEMPLATE', context);
      }

      const template = this.templates.get(templateType);
      if (!template) {
        console.warn(`Template not found: ${templateType}, using default`);
        return this.generateDefaultReminder(templateType, context);
      }

      // Validate template structure
      if (!template.generator || typeof template.generator !== 'function') {
        console.error(`Template ${templateType} has invalid generator function`);
        return this.generateDefaultReminder(templateType, context);
      }

      // Generate reminder with error handling
      try {
        return template.generator(context);
      } catch (generatorError) {
        console.error(`Error in template generator for ${templateType}:`, generatorError);
        return this.generateDefaultReminder(templateType, {
          ...context,
          generatorError: generatorError.message
        });
      }

    } catch (error) {
      console.error('Error in generateReminder:', error);
      return this.generateDefaultReminder(templateType || 'ERROR', {
        ...context,
        systemError: error.message
      });
    }
  }

  /**
   * Generate permission architecture reminder
   */
  generatePermissionReminder(context) {
    const { filePath = '', fileType = 'unknown' } = context;
    
    // Use comprehensive validation checklist
    const checklist = this.validationChecklists.generateValidationChecklist('PERMISSION_ARCHITECTURE', fileType);
    
    return `🔐 **PERMISSION ARCHITECTURE VALIDATION** 🔐

A ${fileType} file has been modified that may affect permission logic: \`${filePath}\`

${checklist}

**Please analyze the modified file for SUPER_ADMIN permission architecture compliance.**`;
  }

  /**
   * Generate organization controls reminder
   */
  generateOrganizationReminder(context) {
    const { filePath = '', fileType = 'unknown' } = context;
    
    // Use comprehensive validation checklist
    const checklist = this.validationChecklists.generateValidationChecklist('ORGANIZATION_CONTROLS', fileType);
    
    return `🏢 **ORGANIZATION CONTROLS VALIDATION** 🏢

A ${fileType} file has been modified that may affect organization logic: \`${filePath}\`

${checklist}

**Please analyze the modified file for SUPER_ADMIN organization controls compliance.**`;
  }

  /**
   * Generate subscription architecture reminder
   */
  generateSubscriptionReminder(context) {
    const { filePath = '', fileType = 'unknown' } = context;
    
    // Use comprehensive validation checklist
    const checklist = this.validationChecklists.generateValidationChecklist('SUBSCRIPTION_ARCHITECTURE', fileType);
    
    return `💳 **SUBSCRIPTION ARCHITECTURE VALIDATION** 💳

A ${fileType} file has been modified that may affect subscription logic: \`${filePath}\`

${checklist}

**Please analyze the modified file for SUPER_ADMIN subscription architecture compliance.**`;
  }

  /**
   * Generate database architecture reminder
   */
  generateDatabaseReminder(context) {
    const { filePath = '', fileType = 'unknown' } = context;
    
    // Use comprehensive validation checklist
    const checklist = this.validationChecklists.generateValidationChecklist('DATABASE_MIGRATION', fileType);
    
    return `🗄️ **DATABASE ARCHITECTURE VALIDATION** 🗄️

A ${fileType} file has been modified that may affect database schema: \`${filePath}\`

${checklist}

**Please analyze the migration file for SUPER_ADMIN control architecture compliance.**`;
  }

  /**
   * Generate general governance reminder
   */
  generateGeneralReminder(context) {
    const { 
      filePath = '', 
      fileType = 'unknown',
      generatorError = null,
      systemError = null,
      templateError = null
    } = context;

    return this.generateDefaultReminder('GENERAL_GOVERNANCE', context);
  }

  /**
   * Generate default reminder when template not found
   */
  generateDefaultReminder(templateType, context) {
    const { 
      filePath = '', 
      fileType = 'unknown',
      generatorError = null,
      systemError = null,
      templateError = null
    } = context;
    
    let errorInfo = '';
    if (generatorError || systemError || templateError) {
      errorInfo = `\n⚠️ **Note**: Template generation encountered an issue: ${generatorError || systemError || templateError}\n`;
    }

    return `🎛️ **SUPER_ADMIN CONTROLS VALIDATION** 🎛️

A ${fileType} file has been modified that may affect SUPER_ADMIN controls: \`${filePath}\`
${errorInfo}
## Available SUPER_ADMIN Controls

Please refer to the SUPER_ADMIN Controls Reference for:
- Organization Types (shared/segregated/isolated)
- Subscription Plans (free_trial/starter/professional/enterprise)
- Permission Templates (basic_client/premium_client/tnc_enterprise)
- Granular Permissions (20 available keys)
- Feature Flags (8+ toggleable features)
- White-Label Controls (3 boolean flags)

## Validation Checklist

- [ ] Uses existing capabilities instead of creating new ones
- [ ] Considers organization types and subscription plans
- [ ] Leverages permission templates and granular permissions
- [ ] Respects SUPER_ADMIN control architecture

## Quick Reference

### Granular Permission Keys (Sample)
\`\`\`typescript
const PERMISSIONS = [
  'quotes.create', 'quotes.edit', 'quotes.delete',
  'events.create', 'events.manage',
  'users.manage', 'analytics.view',
  'billing.view', 'manage_billing',
  'affiliates.manage', 'api.access',
  'manage_settings', 'super_admin'
];
\`\`\`

### Organization Types
- **shared**: Basic SaaS model, shared affiliate network
- **segregated**: Custom branding, isolated customer bases
- **isolated**: Complete data isolation, enterprise-level separation

### Permission Templates
- **basic_client**: Core features, no financial access
- **premium_client**: Enhanced features, financial visibility
- **tnc_enterprise**: Full platform access, network management

**Please analyze the file for SUPER_ADMIN control compliance.**

---

*Template used: ${templateType} (fallback)*`;
  }

  /**
   * Validate template system health
   * @returns {Object} Validation results
   */
  validateTemplateSystem() {
    const results = {
      healthy: true,
      errors: [],
      warnings: [],
      templateCount: this.templates.size,
      templates: {}
    };

    try {
      for (const [templateType, template] of this.templates.entries()) {
        const templateResult = {
          exists: true,
          hasTitle: !!template.title,
          hasSections: Array.isArray(template.sections),
          hasGenerator: typeof template.generator === 'function',
          sectionCount: Array.isArray(template.sections) ? template.sections.length : 0
        };

        // Test generator function
        try {
          const testContext = {
            filePath: 'test/file.ts',
            fileType: 'test',
            context: 'test'
          };
          const testResult = template.generator(testContext);
          templateResult.generatorWorks = typeof testResult === 'string' && testResult.length > 0;
        } catch (generatorError) {
          templateResult.generatorWorks = false;
          templateResult.generatorError = generatorError.message;
          results.errors.push(`Template ${templateType} generator failed: ${generatorError.message}`);
          results.healthy = false;
        }

        results.templates[templateType] = templateResult;

        // Validate required properties
        if (!templateResult.hasTitle) {
          results.warnings.push(`Template ${templateType} missing title`);
        }
        if (!templateResult.hasSections) {
          results.warnings.push(`Template ${templateType} missing sections array`);
        }
        if (!templateResult.hasGenerator) {
          results.errors.push(`Template ${templateType} missing generator function`);
          results.healthy = false;
        }
      }
    } catch (systemError) {
      results.errors.push(`Template system validation error: ${systemError.message}`);
      results.healthy = false;
    }

    return results;
  }

  /**
   * Get available template types
   */
  getAvailableTemplates() {
    return Array.from(this.templates.keys());
  }

  /**
   * Get template metadata
   */
  getTemplateMetadata(templateType) {
    const template = this.templates.get(templateType);
    if (!template) return null;
    
    return {
      title: template.title,
      sections: template.sections
    };
  }
}

// Export singleton instance
module.exports = new ReminderTemplateEngine();