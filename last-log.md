[Warning] Failed to parse cookie string:
SyntaxError: JSON Parse error: Unexpected identifier "base64"
parse
parseSupabase<PERSON>ookie
getItem
(anonymous function)
getItemAsync
(anonymous function)
__loadSession
(anonymous function)
_useSession
(anonymous function)
(anonymous function)
(anonymous function)
(anonymous function)
(anonymous function)
[Warning] Failed to parse cookie string:
SyntaxError: JSON Parse error: Unexpected identifier "base64"
parse
parseSupabaseCookie
getItem
(anonymous function)
getItemAsync
(anonymous function)
__loadSession
(anonymous function)
_useSession
(anonymous function)
_emitInitialSession
(anonymous function)
(anonymous function)
(anonymous function)
(anonymous function)
(anonymous function)
(anonymous function)
[Error] Failed to load resource: the server responded with a status of 500 (Internal Server Error) (user_settings, line 0)
[Error] Failed to load resource: the server responded with a status of 500 (Internal Server Error) (user_settings, line 0)
[Error] Failed to load resource: the server responded with a status of 500 (Internal Server Error) (user_organizations, line 0)
[Error] Error loading user organizations:
Object

code: "42P17"

details: null

hint: null

message: "infinite recursion detected in policy for relation \"organizations\""

Object Prototype
	(anonymous function) (app-index.js:33)
	(anonymous function) (hydration-error-info.js:63)
	(anonymous function) (OrganizationContext.tsx:45)
[Error] Error fetching user organizations:
Object

code: "PGRST203"

details: null

hint: "Try renaming the parameters or the function itself in the database so function overloading can be resolved"

message: "Could not choose the best candidate function between: public.get_user_organizations(user_uuid => uuid)…"

Object Prototype
	(anonymous function) (app-index.js:33)
	(anonymous function) (hydration-error-info.js:63)
	(anonymous function) (tenant-context.ts:52)
[Error] Error fetching user organizations:
Object

code: "PGRST203"

details: null

hint: "Try renaming the parameters or the function itself in the database so function overloading can be resolved"

message: "Could not choose the best candidate function between: public.get_user_organizations(user_uuid => uuid)…"

Object Prototype
	(anonymous function) (app-index.js:33)
	(anonymous function) (hydration-error-info.js:63)
	(anonymous function) (tenant-context.ts:52)
[Error] Failed to load resource: the server responded with a status of 500 (Internal Server Error) (user_organizations, line 0)
[Error] Error loading user organizations:
Object

code: "42P17"

details: null

hint: null

message: "infinite recursion detected in policy for relation \"organizations\""

Object Prototype
	(anonymous function) (app-index.js:33)
	(anonymous function) (hydration-error-info.js:63)
	(anonymous function) (OrganizationContext.tsx:45)
[Log] 🔍 Raw API response: – Object
Object
[Log] 🎯 Transformed organizations:
Array (22)
0
{id: "ec8cc554-e1d8-40a4-a15b-f1b468d98331", name: "TransFlow Shared", slug: "transflow-shared", domain: undefined, account_type: "direct_client", …}
1
{id: "********-1111-1111-1111-********1111", name: "Transflow Shared Network", slug: "wwms-platform-admin", domain: undefined, account_type: "transflow_super_admin", …}
2
{id: "4336b836-ded6-48b8-8ef4-cb56aeb66114", name: "TransFlow Global", slug: "transflow-global", domain: undefined, account_type: "direct_client", …}
3
{id: "********-3333-3333-3333-************", name: "Metro Ride Network", slug: "metro-ride-network", domain: undefined, account_type: "tnc_account", …}
4
{id: "a4c309a8-60f0-45fe-bafd-00e272371e7f", name: "Luxury Concierge Services", slug: "luxury-concierge", domain: undefined, account_type: "tnc_account", …}
5
{id: "********-2222-2222-2222-************", name: "Elite Corporate Travel", slug: "elite-corporate-travel", domain: undefined, account_type: "tnc_account", …}
6
{id: "7c9dc63a-3382-46f9-aee4-9688a93e2bee", name: "Elite City Transport Network", slug: "elite-city-tnc", domain: undefined, account_type: "tnc_account", …}
7
{id: "096f5bca-497f-4877-8c69-a9ab26796443", name: "Metro Ride Alliance", slug: "metro-ride-alliance", domain: undefined, account_type: "tnc_account", …}
8
{id: "2ce07786-52bb-4e0f-9d35-b8d121b26b11", name: "Urban Transport Collective", slug: "urban-transport-collective", domain: undefined, account_type: "tnc_account", …}
9
{id: "e3e21883-c66a-4537-8bd1-cb3a8399a0a2", name: "Corporate Travel Solutions", slug: "corporate-travel-solutions", domain: undefined, account_type: "tnc_account", …}
10
{id: "c1e371a2-6c2a-41b7-b6e8-d1dbc36f4e5b", name: "VIP Transport Partners", slug: "vip-transport-partners", domain: undefined, account_type: "tnc_account", …}
11
{id: "810008c3-9ff0-4292-ae58-257a993b01de", name: "Test White Label Organization", slug: "test-white-label-organization", domain: undefined, account_type: "tnc_account", …}
12
{id: "0d390d14-f861-4cc5-8963-74cf285d179c", name: "Demo Transportation Hub", slug: "demo-transport-hub", domain: undefined, account_type: "direct_client", …}
13
{id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac", name: "Default Organization", slug: "default-organization", domain: undefined, account_type: "direct_client", …}
14
{id: "3228d476-071b-4d88-871e-c29599066583", name: "Test Shared Organization", slug: "test-shared-organization", domain: undefined, account_type: "direct_client", …}
15
{id: "3fdd5e69-60e9-4172-a7dd-aa434380cb59", name: "Test Segregated Organization", slug: "test-segregated-organization", domain: undefined, account_type: "tnc_account", …}
16
{id: "8f527c20-9f45-470a-a90d-95da54b0292f", name: "Demo Transportation", slug: "demo-**********.164528", domain: undefined, account_type: "tnc_account", …}
17
{id: "bc8d0a04-02a0-4d41-8ddf-2fc6819a4e7d", name: "Elite City Transportation", slug: "elite-city-**********.164528", domain: undefined, account_type: "tnc_account", …}
18
{id: "e8e891b3-77f2-4d8e-8b33-2d8d216e8c24", name: "Luxury Concierge Transportation", slug: "luxury-concierge-**********.164528", domain: undefined, account_type: "tnc_account", …}
19
{id: "9e0680e7-0fb2-4cb2-939d-8bac21e4b70f", name: "Metro Boston Downtown Office", slug: "metro-boston-downtown", domain: undefined, account_type: "tnc_customer", …}
20
{id: "e1437b32-e679-403d-91c0-66508acee722", name: "Elite Boston Corporate Branch", slug: "elite-boston-corporate", domain: undefined, account_type: "tnc_customer", …}
21
{id: "d72a68a4-b26b-46f7-8bbf-e54b5098abfd", name: "Luxury Boston Hotel Services", slug: "luxury-boston-hotels", domain: undefined, account_type: "tnc_customer", …}

Array Prototype
[Log] 🔍 API currentOrganization: – "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"
[Log] 🔍 Looking for current organization with ID: – "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"
[Log] 🔍 Found current organization:
Object

account_type: "direct_client"

branding: {}

can_have_custom_branding: true

can_have_custom_domain: true

can_have_white_labeling: true

client_level: "Basic"

created_at: "2025-08-15T19:01:39.895929+00:00"

description: undefined

domain: undefined

feature_flags: {}

has_custom_branding: false

has_custom_domain: false

has_white_labeling: false

id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"

last_active: "2025-08-15T19:01:40.374667+00:00"

name: "Default Organization"

organization_type: "shared"

permission_template: "basic_client"

slug: "default-organization"

status: "active"

subscription_plan: "free_trial"

users_count: undefined

Object Prototype
[Log] ✅ Setting current organization to: – "Default Organization"
[Log] 🔍 Raw API response:
Object

currentOrganization: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"

data: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …] (22)

message: "SUPER_ADMIN organizations retrieved successfully"

meta: {total: 22, timestamp: "2025-08-15T19:17:48.272Z"}

success: true

Object Prototype
[Log] 🎯 Transformed organizations:
Array (22)
0
{id: "e3e21883-c66a-4537-8bd1-cb3a8399a0a2", name: "Corporate Travel Solutions", slug: "corporate-travel-solutions", domain: undefined, account_type: "tnc_account", …}
1
{id: "e1437b32-e679-403d-91c0-66508acee722", name: "Elite Boston Corporate Branch", slug: "elite-boston-corporate", domain: undefined, account_type: "tnc_customer", …}
2
{id: "d72a68a4-b26b-46f7-8bbf-e54b5098abfd", name: "Luxury Boston Hotel Services", slug: "luxury-boston-hotels", domain: undefined, account_type: "tnc_customer", …}
3
{id: "a4c309a8-60f0-45fe-bafd-00e272371e7f", name: "Luxury Concierge Services", slug: "luxury-concierge", domain: undefined, account_type: "tnc_account", …}
4
{id: "********-3333-3333-3333-************", name: "Metro Ride Network", slug: "metro-ride-network", domain: undefined, account_type: "tnc_account", …}
5
{id: "810008c3-9ff0-4292-ae58-257a993b01de", name: "Test White Label Organization", slug: "test-white-label-organization", domain: undefined, account_type: "tnc_account", …}
6
{id: "********-1111-1111-1111-********1111", name: "Transflow Shared Network", slug: "wwms-platform-admin", domain: undefined, account_type: "transflow_super_admin", …}
7
{id: "c1e371a2-6c2a-41b7-b6e8-d1dbc36f4e5b", name: "VIP Transport Partners", slug: "vip-transport-partners", domain: undefined, account_type: "tnc_account", …}
8
{id: "7c9dc63a-3382-46f9-aee4-9688a93e2bee", name: "Elite City Transport Network", slug: "elite-city-tnc", domain: undefined, account_type: "tnc_account", …}
9
{id: "********-2222-2222-2222-************", name: "Elite Corporate Travel", slug: "elite-corporate-travel", domain: undefined, account_type: "tnc_account", …}
10
{id: "9e0680e7-0fb2-4cb2-939d-8bac21e4b70f", name: "Metro Boston Downtown Office", slug: "metro-boston-downtown", domain: undefined, account_type: "tnc_customer", …}
11
{id: "096f5bca-497f-4877-8c69-a9ab26796443", name: "Metro Ride Alliance", slug: "metro-ride-alliance", domain: undefined, account_type: "tnc_account", …}
12
{id: "3fdd5e69-60e9-4172-a7dd-aa434380cb59", name: "Test Segregated Organization", slug: "test-segregated-organization", domain: undefined, account_type: "tnc_account", …}
13
{id: "2ce07786-52bb-4e0f-9d35-b8d121b26b11", name: "Urban Transport Collective", slug: "urban-transport-collective", domain: undefined, account_type: "tnc_account", …}
14
{id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac", name: "Default Organization", slug: "default-organization", domain: undefined, account_type: "direct_client", …}
15
{id: "8f527c20-9f45-470a-a90d-95da54b0292f", name: "Demo Transportation", slug: "demo-**********.164528", domain: undefined, account_type: "tnc_account", …}
16
{id: "0d390d14-f861-4cc5-8963-74cf285d179c", name: "Demo Transportation Hub", slug: "demo-transport-hub", domain: undefined, account_type: "direct_client", …}
17
{id: "bc8d0a04-02a0-4d41-8ddf-2fc6819a4e7d", name: "Elite City Transportation", slug: "elite-city-**********.164528", domain: undefined, account_type: "tnc_account", …}
18
{id: "e8e891b3-77f2-4d8e-8b33-2d8d216e8c24", name: "Luxury Concierge Transportation", slug: "luxury-concierge-**********.164528", domain: undefined, account_type: "tnc_account", …}
19
{id: "3228d476-071b-4d88-871e-c29599066583", name: "Test Shared Organization", slug: "test-shared-organization", domain: undefined, account_type: "direct_client", …}
20
{id: "4336b836-ded6-48b8-8ef4-cb56aeb66114", name: "TransFlow Global", slug: "transflow-global", domain: undefined, account_type: "direct_client", …}
21
{id: "ec8cc554-e1d8-40a4-a15b-f1b468d98331", name: "TransFlow Shared", slug: "transflow-shared", domain: undefined, account_type: "direct_client", …}

Array Prototype
[Log] 🔍 API currentOrganization: – "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"
[Log] 🔍 Looking for current organization with ID: – "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"
[Log] 🔍 Found current organization:
Object

account_type: "direct_client"

branding: {}

can_have_custom_branding: true

can_have_custom_domain: true

can_have_white_labeling: true

client_level: "Basic"

created_at: "2025-08-15T19:01:39.895929+00:00"

description: undefined

domain: undefined

feature_flags: {}

has_custom_branding: false

has_custom_domain: false

has_white_labeling: false

id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"

last_active: "2025-08-15T19:01:40.374667+00:00"

name: "Default Organization"

organization_type: "shared"

permission_template: "basic_client"

slug: "default-organization"

status: "active"

subscription_plan: "free_trial"

users_count: undefined

Object Prototype
[Log] ✅ Setting current organization to: – "Default Organization"
[Log] 🔍 Current organization state changed:
Object

account_type: "direct_client"

branding: {}

can_have_custom_branding: true

can_have_custom_domain: true

can_have_white_labeling: true

client_level: "Basic"

created_at: "2025-08-15T19:01:39.895929+00:00"

description: undefined

domain: undefined

feature_flags: {}

has_custom_branding: false

has_custom_domain: false

has_white_labeling: false

id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"

last_active: "2025-08-15T19:01:40.374667+00:00"

name: "Default Organization"

organization_type: "shared"

permission_template: "basic_client"

slug: "default-organization"

status: "active"

subscription_plan: "free_trial"

users_count: undefined

Object Prototype
[Log] 🔍 Raw API response:
Object

currentOrganization: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"

data: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …] (22)

message: "SUPER_ADMIN organizations retrieved successfully"

meta: {total: 22, timestamp: "2025-08-15T19:17:48.285Z"}

success: true

Object Prototype
[Log] 🎯 Transformed organizations:
Array (22)
0
{id: "e3e21883-c66a-4537-8bd1-cb3a8399a0a2", name: "Corporate Travel Solutions", slug: "corporate-travel-solutions", domain: undefined, account_type: "tnc_account", …}
1
{id: "e1437b32-e679-403d-91c0-66508acee722", name: "Elite Boston Corporate Branch", slug: "elite-boston-corporate", domain: undefined, account_type: "tnc_customer", …}
2
{id: "d72a68a4-b26b-46f7-8bbf-e54b5098abfd", name: "Luxury Boston Hotel Services", slug: "luxury-boston-hotels", domain: undefined, account_type: "tnc_customer", …}
3
{id: "a4c309a8-60f0-45fe-bafd-00e272371e7f", name: "Luxury Concierge Services", slug: "luxury-concierge", domain: undefined, account_type: "tnc_account", …}
4
{id: "********-3333-3333-3333-************", name: "Metro Ride Network", slug: "metro-ride-network", domain: undefined, account_type: "tnc_account", …}
5
{id: "810008c3-9ff0-4292-ae58-257a993b01de", name: "Test White Label Organization", slug: "test-white-label-organization", domain: undefined, account_type: "tnc_account", …}
6
{id: "********-1111-1111-1111-********1111", name: "Transflow Shared Network", slug: "wwms-platform-admin", domain: undefined, account_type: "transflow_super_admin", …}
7
{id: "c1e371a2-6c2a-41b7-b6e8-d1dbc36f4e5b", name: "VIP Transport Partners", slug: "vip-transport-partners", domain: undefined, account_type: "tnc_account", …}
8
{id: "7c9dc63a-3382-46f9-aee4-9688a93e2bee", name: "Elite City Transport Network", slug: "elite-city-tnc", domain: undefined, account_type: "tnc_account", …}
9
{id: "********-2222-2222-2222-************", name: "Elite Corporate Travel", slug: "elite-corporate-travel", domain: undefined, account_type: "tnc_account", …}
10
{id: "9e0680e7-0fb2-4cb2-939d-8bac21e4b70f", name: "Metro Boston Downtown Office", slug: "metro-boston-downtown", domain: undefined, account_type: "tnc_customer", …}
11
{id: "096f5bca-497f-4877-8c69-a9ab26796443", name: "Metro Ride Alliance", slug: "metro-ride-alliance", domain: undefined, account_type: "tnc_account", …}
12
{id: "3fdd5e69-60e9-4172-a7dd-aa434380cb59", name: "Test Segregated Organization", slug: "test-segregated-organization", domain: undefined, account_type: "tnc_account", …}
13
{id: "2ce07786-52bb-4e0f-9d35-b8d121b26b11", name: "Urban Transport Collective", slug: "urban-transport-collective", domain: undefined, account_type: "tnc_account", …}
14
{id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac", name: "Default Organization", slug: "default-organization", domain: undefined, account_type: "direct_client", …}
15
{id: "8f527c20-9f45-470a-a90d-95da54b0292f", name: "Demo Transportation", slug: "demo-**********.164528", domain: undefined, account_type: "tnc_account", …}
16
{id: "0d390d14-f861-4cc5-8963-74cf285d179c", name: "Demo Transportation Hub", slug: "demo-transport-hub", domain: undefined, account_type: "direct_client", …}
17
{id: "bc8d0a04-02a0-4d41-8ddf-2fc6819a4e7d", name: "Elite City Transportation", slug: "elite-city-**********.164528", domain: undefined, account_type: "tnc_account", …}
18
{id: "e8e891b3-77f2-4d8e-8b33-2d8d216e8c24", name: "Luxury Concierge Transportation", slug: "luxury-concierge-**********.164528", domain: undefined, account_type: "tnc_account", …}
19
{id: "3228d476-071b-4d88-871e-c29599066583", name: "Test Shared Organization", slug: "test-shared-organization", domain: undefined, account_type: "direct_client", …}
20
{id: "4336b836-ded6-48b8-8ef4-cb56aeb66114", name: "TransFlow Global", slug: "transflow-global", domain: undefined, account_type: "direct_client", …}
21
{id: "ec8cc554-e1d8-40a4-a15b-f1b468d98331", name: "TransFlow Shared", slug: "transflow-shared", domain: undefined, account_type: "direct_client", …}

Array Prototype
[Log] 🔍 API currentOrganization: – "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"
[Log] 🔍 Looking for current organization with ID: – "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"
[Log] 🔍 Found current organization:
Object

account_type: "direct_client"

branding: {}

can_have_custom_branding: true

can_have_custom_domain: true

can_have_white_labeling: true

client_level: "Basic"

created_at: "2025-08-15T19:01:39.895929+00:00"

description: undefined

domain: undefined

feature_flags: {}

has_custom_branding: false

has_custom_domain: false

has_white_labeling: false

id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"

last_active: "2025-08-15T19:01:40.374667+00:00"

name: "Default Organization"

organization_type: "shared"

permission_template: "basic_client"

slug: "default-organization"

status: "active"

subscription_plan: "free_trial"

users_count: undefined

Object Prototype
[Log] ✅ Setting current organization to: – "Default Organization"
[Log] 🔍 Current organization state changed:
Object

account_type: "direct_client"

branding: {}

can_have_custom_branding: true

can_have_custom_domain: true

can_have_white_labeling: true

client_level: "Basic"

created_at: "2025-08-15T19:01:39.895929+00:00"

description: undefined

domain: undefined

feature_flags: {}

has_custom_branding: false

has_custom_domain: false

has_white_labeling: false

id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"

last_active: "2025-08-15T19:01:40.374667+00:00"

name: "Default Organization"

organization_type: "shared"

permission_template: "basic_client"

slug: "default-organization"

status: "active"

subscription_plan: "free_trial"

users_count: undefined

Object Prototype
[Log] 🔍 Raw API response:
Object

currentOrganization: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"

data: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …] (22)

message: "SUPER_ADMIN organizations retrieved successfully"

meta: {total: 22, timestamp: "2025-08-15T19:17:48.304Z"}

success: true

Object Prototype
[Log] 🎯 Transformed organizations:
Array (22)
0
{id: "e3e21883-c66a-4537-8bd1-cb3a8399a0a2", name: "Corporate Travel Solutions", slug: "corporate-travel-solutions", domain: undefined, account_type: "tnc_account", …}
1
{id: "e1437b32-e679-403d-91c0-66508acee722", name: "Elite Boston Corporate Branch", slug: "elite-boston-corporate", domain: undefined, account_type: "tnc_customer", …}
2
{id: "d72a68a4-b26b-46f7-8bbf-e54b5098abfd", name: "Luxury Boston Hotel Services", slug: "luxury-boston-hotels", domain: undefined, account_type: "tnc_customer", …}
3
{id: "a4c309a8-60f0-45fe-bafd-00e272371e7f", name: "Luxury Concierge Services", slug: "luxury-concierge", domain: undefined, account_type: "tnc_account", …}
4
{id: "********-3333-3333-3333-************", name: "Metro Ride Network", slug: "metro-ride-network", domain: undefined, account_type: "tnc_account", …}
5
{id: "810008c3-9ff0-4292-ae58-257a993b01de", name: "Test White Label Organization", slug: "test-white-label-organization", domain: undefined, account_type: "tnc_account", …}
6
{id: "********-1111-1111-1111-********1111", name: "Transflow Shared Network", slug: "wwms-platform-admin", domain: undefined, account_type: "transflow_super_admin", …}
7
{id: "c1e371a2-6c2a-41b7-b6e8-d1dbc36f4e5b", name: "VIP Transport Partners", slug: "vip-transport-partners", domain: undefined, account_type: "tnc_account", …}
8
{id: "7c9dc63a-3382-46f9-aee4-9688a93e2bee", name: "Elite City Transport Network", slug: "elite-city-tnc", domain: undefined, account_type: "tnc_account", …}
9
{id: "********-2222-2222-2222-************", name: "Elite Corporate Travel", slug: "elite-corporate-travel", domain: undefined, account_type: "tnc_account", …}
10
{id: "9e0680e7-0fb2-4cb2-939d-8bac21e4b70f", name: "Metro Boston Downtown Office", slug: "metro-boston-downtown", domain: undefined, account_type: "tnc_customer", …}
11
{id: "096f5bca-497f-4877-8c69-a9ab26796443", name: "Metro Ride Alliance", slug: "metro-ride-alliance", domain: undefined, account_type: "tnc_account", …}
12
{id: "3fdd5e69-60e9-4172-a7dd-aa434380cb59", name: "Test Segregated Organization", slug: "test-segregated-organization", domain: undefined, account_type: "tnc_account", …}
13
{id: "2ce07786-52bb-4e0f-9d35-b8d121b26b11", name: "Urban Transport Collective", slug: "urban-transport-collective", domain: undefined, account_type: "tnc_account", …}
14
{id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac", name: "Default Organization", slug: "default-organization", domain: undefined, account_type: "direct_client", …}
15
{id: "8f527c20-9f45-470a-a90d-95da54b0292f", name: "Demo Transportation", slug: "demo-**********.164528", domain: undefined, account_type: "tnc_account", …}
16
{id: "0d390d14-f861-4cc5-8963-74cf285d179c", name: "Demo Transportation Hub", slug: "demo-transport-hub", domain: undefined, account_type: "direct_client", …}
17
{id: "bc8d0a04-02a0-4d41-8ddf-2fc6819a4e7d", name: "Elite City Transportation", slug: "elite-city-**********.164528", domain: undefined, account_type: "tnc_account", …}
18
{id: "e8e891b3-77f2-4d8e-8b33-2d8d216e8c24", name: "Luxury Concierge Transportation", slug: "luxury-concierge-**********.164528", domain: undefined, account_type: "tnc_account", …}
19
{id: "3228d476-071b-4d88-871e-c29599066583", name: "Test Shared Organization", slug: "test-shared-organization", domain: undefined, account_type: "direct_client", …}
20
{id: "4336b836-ded6-48b8-8ef4-cb56aeb66114", name: "TransFlow Global", slug: "transflow-global", domain: undefined, account_type: "direct_client", …}
21
{id: "ec8cc554-e1d8-40a4-a15b-f1b468d98331", name: "TransFlow Shared", slug: "transflow-shared", domain: undefined, account_type: "direct_client", …}

Array Prototype
[Log] 🔍 API currentOrganization: – "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"
[Log] 🔍 Looking for current organization with ID: – "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"
[Log] 🔍 Found current organization:
Object

account_type: "direct_client"

branding: {}

can_have_custom_branding: true

can_have_custom_domain: true

can_have_white_labeling: true

client_level: "Basic"

created_at: "2025-08-15T19:01:39.895929+00:00"

description: undefined

domain: undefined

feature_flags: {}

has_custom_branding: false

has_custom_domain: false

has_white_labeling: false

id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"

last_active: "2025-08-15T19:01:40.374667+00:00"

name: "Default Organization"

organization_type: "shared"

permission_template: "basic_client"

slug: "default-organization"

status: "active"

subscription_plan: "free_trial"

users_count: undefined

Object Prototype
[Log] ✅ Setting current organization to: – "Default Organization"
[Log] 🔍 Current organization state changed:
Object

account_type: "direct_client"

branding: {}

can_have_custom_branding: true

can_have_custom_domain: true

can_have_white_labeling: true

client_level: "Basic"

created_at: "2025-08-15T19:01:39.895929+00:00"

description: undefined

domain: undefined

feature_flags: {}

has_custom_branding: false

has_custom_domain: false

has_white_labeling: false

id: "fc5449b6-f5a2-4d71-b7bc-31c5d209c6ac"

last_active: "2025-08-15T19:01:40.374667+00:00"

name: "Default Organization"

organization_type: "shared"

permission_template: "basic_client"

slug: "default-organization"

status: "active"

subscription_plan: "free_trial"

users_count: undefined

Object Prototype
> Selected Element
< <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">…</div>
[Warning] Failed to parse cookie string:
SyntaxError: JSON Parse error: Unexpected identifier "base64"
parse
parseSupabaseCookie
getItem
(anonymous function)
getItemAsync
(anonymous function)
__loadSession
(anonymous function)
_useSession
(anonymous function)
(anonymous function)
(anonymous function)
(anonymous function)
(anonymous function)
(anonymous function)