To update your account to use zsh, please run `chsh -s /bin/zsh`.
For more details, please visit https://support.apple.com/kb/HT208050.
192:wwms-diy ameen$ psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual FROM pg_policies ORDER BY tablename, policyname;"
 schemaname |            tablename            |                      policyname                      | permissive |      roles      |  cmd   |                                                                                                          qual
------------+---------------------------------+------------------------------------------------------+------------+-----------------+--------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 public     | affiliate_companies             | Service role can access all affiliate_companies      | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | affiliate_companies             | affiliate_companies_access                           | PERMISSIVE | {public}        | ALL    | ((EXISTS ( SELECT 1                                                                                                                                                                                                    +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (profiles.roles))))) OR (organization_id IN ( SELECT uo.organization_id                                                                        +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.status = 'active'::text) AND (((uo.role)::text = 'AFFILIATE'::text) OR (EXISTS ( SELECT 1                                                                                   +
            |                                 |                                                      |            |                 |        |            FROM profiles p                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |           WHERE ((p.id = auth.uid()) AND ('AFFILIATE'::user_role = ANY (p.roles))))))))) OR (id IN ( SELECT au.affiliate_company_id                                                                                    +
            |                                 |                                                      |            |                 |        |    FROM affiliate_users au                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |   WHERE ((au.user_id = auth.uid()) AND (au.employment_status = 'active'::text)))))
 public     | affiliate_companies             | affiliate_companies_organization_access              | PERMISSIVE | {public}        | ALL    | ((auth.role() = 'service_role'::text) OR ((auth.uid() IS NOT NULL) AND (EXISTS ( SELECT 1                                                                                                                              +
            |                                 |                                                      |            |                 |        |    FROM auth.users                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |   WHERE ((users.id = auth.uid()) AND ((users.raw_user_meta_data ->> 'role'::text) = 'SUPER_ADMIN'::text))))) OR (organization_id IN ( SELECT user_organizations.organization_id                                        +
            |                                 |                                                      |            |                 |        |    FROM user_organizations                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |   WHERE ((user_organizations.user_id = auth.uid()) AND (user_organizations.status = 'active'::text)))))
 public     | affiliate_counter_offers        | affiliate_counter_offers_access                      | PERMISSIVE | {authenticated} | ALL    | (EXISTS ( SELECT 1                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |    FROM (affiliate_companies ac                                                                                                                                                                                        +
            |                                 |                                                      |            |                 |        |      JOIN user_organizations uo ON ((ac.organization_id = uo.organization_id)))                                                                                                                                        +
            |                                 |                                                      |            |                 |        |   WHERE ((ac.id = affiliate_counter_offers.affiliate_company_id) AND (uo.user_id = auth.uid()) AND (uo.status = 'active'::text))))
 public     | affiliate_date_blocks           | affiliate_date_blocks_access                         | PERMISSIVE | {authenticated} | ALL    | (EXISTS ( SELECT 1                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |    FROM (affiliate_companies ac                                                                                                                                                                                        +
            |                                 |                                                      |            |                 |        |      JOIN user_organizations uo ON ((ac.organization_id = uo.organization_id)))                                                                                                                                        +
            |                                 |                                                      |            |                 |        |   WHERE ((ac.id = affiliate_date_blocks.affiliate_company_id) AND (uo.user_id = auth.uid()) AND (uo.status = 'active'::text))))
 public     | affiliate_drivers               | affiliate_drivers_company_access                     | PERMISSIVE | {public}        | ALL    | (affiliate_company_id IN ( SELECT au.affiliate_company_id                                                                                                                                                              +
            |                                 |                                                      |            |                 |        |    FROM ((affiliate_users au                                                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |      JOIN user_organizations uo ON ((au.user_id = uo.user_id)))                                                                                                                                                        +
            |                                 |                                                      |            |                 |        |      JOIN affiliate_companies ac ON ((au.affiliate_company_id = ac.id)))                                                                                                                                               +
            |                                 |                                                      |            |                 |        |   WHERE ((au.user_id = auth.uid()) AND (uo.organization_id = ac.organization_id))))
 public     | affiliate_users                 | Service role can access all affiliate_users          | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | affiliate_users                 | affiliate_users_access                               | PERMISSIVE | {public}        | ALL    | ((EXISTS ( SELECT 1                                                                                                                                                                                                    +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (profiles.roles))))) OR (user_id = auth.uid()) OR user_has_affiliate_company_access(auth.uid(), affiliate_company_id, 'read'::text))
 public     | audit_logs                      | Service role can access all audit_logs               | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | audit_logs                      | audit_logs_own_records                               | PERMISSIVE | {authenticated} | SELECT | (user_id = auth.uid())
 public     | counter_offers                  | Service role can access all counter_offers           | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | document_uploads                | Service role can access all document_uploads         | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | intermediate_stops              | intermediate_stops_quote_access                      | PERMISSIVE | {public}        | ALL    | ((quote_id IN ( SELECT quotes.id                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |    FROM quotes                                                                                                                                                                                                         +
            |                                 |                                                      |            |                 |        |   WHERE (quotes.organization_id IN ( SELECT user_organizations.organization_id                                                                                                                                         +
            |                                 |                                                      |            |                 |        |            FROM user_organizations                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |           WHERE ((user_organizations.user_id = auth.uid()) AND (user_organizations.status = 'active'::text)))))) OR is_super_admin_safe())
 public     | intermediate_stops              | intermediate_stops_service_role_access               | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | network_ownership               | network_ownership_organization_isolation             | PERMISSIVE | {public}        | ALL    | ((owner_organization_id IN ( SELECT user_organizations.organization_id                                                                                                                                                 +
            |                                 |                                                      |            |                 |        |    FROM user_organizations                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |   WHERE (user_organizations.user_id = auth.uid()))) OR (EXISTS ( SELECT 1                                                                                                                                              +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (profiles.roles))))))
 public     | notifications                   | Service role can access all notifications            | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | organization_branding           | organization_branding_authenticated_access           | PERMISSIVE | {public}        | ALL    | (EXISTS ( SELECT 1                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = organization_branding.organization_id) AND (uo.status = 'active'::text))))
 public     | organization_branding           | organization_branding_service_role_access            | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | organization_permissions        | Only SUPER_ADMIN can modify organization permissions | PERMISSIVE | {public}        | ALL    | is_super_admin_safe()
 public     | organization_permissions        | Users can view permissions for their organizations   | PERMISSIVE | {public}        | SELECT | (is_super_admin_safe() OR (EXISTS ( SELECT 1                                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = organization_permissions.organization_id) AND (uo.status = 'active'::text)))))
 public     | organization_permissions        | organization_permissions_authenticated_access        | PERMISSIVE | {public}        | ALL    | (EXISTS ( SELECT 1                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = organization_permissions.organization_id) AND (uo.status = 'active'::text))))
 public     | organization_permissions        | organization_permissions_service_role_access         | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | organizations                   | Four_tier_organization_access                        | PERMISSIVE | {public}        | SELECT | (is_super_admin_safe() OR (EXISTS ( SELECT 1                                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND ((uo.organization_id = organizations.id) OR (organizations.parent_tnc_id = uo.organization_id)) AND (uo.status = 'active'::text) AND (EXISTS ( SELECT 1                         +
            |                                 |                                                      |            |                 |        |            FROM auth.users au                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |           WHERE ((au.id = auth.uid()) AND ((au.raw_user_meta_data ->> 'role'::text) = 'TNC_ADMIN'::text))))))) OR (EXISTS ( SELECT 1                                                                                   +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = organizations.id) AND (uo.status = 'active'::text)))))
 public     | organizations                   | Service role can access all organizations            | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | organizations                   | organizations_multi_tenant_policy                    | PERMISSIVE | {public}        | ALL    | (check_super_admin_role() OR (id IN ( SELECT user_organizations.organization_id                                                                                                                                        +
            |                                 |                                                      |            |                 |        |    FROM user_organizations                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |   WHERE (user_organizations.user_id = auth.uid()))))
 public     | organizations                   | organizations_production_policy                      | PERMISSIVE | {public}        | ALL    | (is_super_admin_safe() OR (id IN ( SELECT uo.organization_id                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE (uo.user_id = auth.uid()))))
 public     | organizations                   | organizations_service_role_access                    | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | organizations                   | organizations_super_admin_access                     | PERMISSIVE | {authenticated} | ALL    | is_super_admin_safe()
 public     | organizations                   | organizations_super_admin_can_read_all               | PERMISSIVE | {public}        | SELECT | is_super_admin_safe()
 public     | organizations                   | organizations_user_can_read_own                      | PERMISSIVE | {public}        | SELECT | (EXISTS ( SELECT 1                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = organizations.id) AND (uo.status = 'active'::text))))
 public     | passengers                      | passengers_organization_access                       | PERMISSIVE | {public}        | ALL    | (organization_id IN ( SELECT uo.organization_id                                                                                                                                                                        +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE (uo.user_id = auth.uid())))
 public     | platform_config                 | Super admins can read platform config                | PERMISSIVE | {public}        | SELECT | (EXISTS ( SELECT 1                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (profiles.roles)))))
 public     | platform_config                 | Super admins can update platform config              | PERMISSIVE | {public}        | UPDATE | (EXISTS ( SELECT 1                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (profiles.roles)))))
 public     | profiles                        | profiles_authenticated_users_own_access              | PERMISSIVE | {public}        | ALL    | (auth.uid() = id)
 public     | profiles                        | profiles_multi_tenant_policy                         | PERMISSIVE | {public}        | ALL    | ((auth.uid() = id) OR check_super_admin_role())
 public     | profiles                        | profiles_organization_access                         | PERMISSIVE | {authenticated} | SELECT | (EXISTS ( SELECT 1                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |    FROM (user_organizations uo1                                                                                                                                                                                        +
            |                                 |                                                      |            |                 |        |      JOIN user_organizations uo2 ON ((uo1.organization_id = uo2.organization_id)))                                                                                                                                     +
            |                                 |                                                      |            |                 |        |   WHERE ((uo1.user_id = auth.uid()) AND (uo2.user_id = profiles.id))))
 public     | profiles                        | profiles_own_access                                  | PERMISSIVE | {authenticated} | ALL    | (auth.uid() = id)
 public     | profiles                        | profiles_production_policy                           | PERMISSIVE | {public}        | ALL    | ((auth.uid() = id) OR is_super_admin_safe())
 public     | profiles                        | profiles_service_role_full_access                    | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | profiles                        | profiles_super_admin_access                          | PERMISSIVE | {authenticated} | ALL    | is_super_admin_safe()
 public     | quote_affiliate_offers          | quote_affiliate_offers_company_access                | PERMISSIVE | {public}        | ALL    | (affiliate_company_id IN ( SELECT au.affiliate_company_id                                                                                                                                                              +
            |                                 |                                                      |            |                 |        |    FROM ((affiliate_users au                                                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |      JOIN user_organizations uo ON ((au.user_id = uo.user_id)))                                                                                                                                                        +
            |                                 |                                                      |            |                 |        |      JOIN affiliate_companies ac ON ((au.affiliate_company_id = ac.id)))                                                                                                                                               +
            |                                 |                                                      |            |                 |        |   WHERE ((au.user_id = auth.uid()) AND (uo.organization_id = ac.organization_id))))
 public     | quote_offers                    | Service role can access all quote_offers             | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | quote_responses                 | quote_responses_organization_isolation               | PERMISSIVE | {public}        | ALL    | ((EXISTS ( SELECT 1                                                                                                                                                                                                    +
            |                                 |                                                      |            |                 |        |    FROM quotes q                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((q.id = quote_responses.quote_id) AND (q.organization_id IN ( SELECT q.organization_id                                                                                                                        +
            |                                 |                                                      |            |                 |        |            FROM user_profiles                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |           WHERE (user_profiles.id = auth.uid())))))) OR (EXISTS ( SELECT 1                                                                                                                                             +
            |                                 |                                                      |            |                 |        |    FROM user_profiles                                                                                                                                                                                                  +
            |                                 |                                                      |            |                 |        |   WHERE ((user_profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (user_profiles.roles))))))
 public     | quote_status_history            | quote_status_history_organization_access             | PERMISSIVE | {authenticated} | SELECT | (EXISTS ( SELECT 1                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |    FROM (quotes q                                                                                                                                                                                                      +
            |                                 |                                                      |            |                 |        |      JOIN user_organizations uo ON ((q.organization_id = uo.organization_id)))                                                                                                                                         +
            |                                 |                                                      |            |                 |        |   WHERE ((q.id = quote_status_history.quote_id) AND (uo.user_id = auth.uid()))))
 public     | quote_status_history            | quote_status_history_super_admin_access              | PERMISSIVE | {authenticated} | ALL    | is_super_admin_safe()
 public     | quotes                          | Service role can access all quotes                   | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | quotes                          | quotes_organization_access                           | PERMISSIVE | {public}        | ALL    | (is_super_admin_safe() OR (auth.role() = 'service_role'::text) OR (organization_id IN ( SELECT user_organizations.organization_id                                                                                      +
            |                                 |                                                      |            |                 |        |    FROM user_organizations                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |   WHERE ((user_organizations.user_id = auth.uid()) AND (user_organizations.status = 'active'::text)))))
 public     | quotes                          | quotes_service_role_access                           | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | quotes                          | quotes_super_admin_access                            | PERMISSIVE | {public}        | ALL    | is_super_admin_safe()
 public     | rate_cards                      | Service role can access all rate_cards               | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | rate_cards                      | rate_cards_company_access                            | PERMISSIVE | {public}        | ALL    | (affiliate_company_id IN ( SELECT au.affiliate_company_id                                                                                                                                                              +
            |                                 |                                                      |            |                 |        |    FROM ((affiliate_users au                                                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |      JOIN user_organizations uo ON ((au.user_id = uo.user_id)))                                                                                                                                                        +
            |                                 |                                                      |            |                 |        |      JOIN affiliate_companies ac ON ((au.affiliate_company_id = ac.id)))                                                                                                                                               +
            |                                 |                                                      |            |                 |        |   WHERE ((au.user_id = auth.uid()) AND (uo.organization_id = ac.organization_id))))
 public     | role_permissions                | role_permissions_organization_access                 | PERMISSIVE | {public}        | ALL    | (is_super_admin_safe() OR (auth.role() = 'service_role'::text) OR (organization_id IN ( SELECT role_permissions.organization_id                                                                                        +
            |                                 |                                                      |            |                 |        |    FROM user_profiles                                                                                                                                                                                                  +
            |                                 |                                                      |            |                 |        |   WHERE (user_profiles.id = auth.uid()))))
 public     | subscription_plans              | subscription_plans_public_read                       | PERMISSIVE | {authenticated} | SELECT | (is_public = true)
 public     | system_configuration            | system_config_public_read                            | PERMISSIVE | {authenticated} | SELECT | (is_public = true)
 public     | tnc_customer_branding_overrides | tnc_branding_overrides_access                        | PERMISSIVE | {public}        | ALL    | (is_super_admin_safe() OR (EXISTS ( SELECT 1                                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_customer_branding_overrides.parent_tnc_org_id) AND (uo.status = 'active'::text) AND (EXISTS ( SELECT 1                                                +
            |                                 |                                                      |            |                 |        |            FROM auth.users au                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |           WHERE ((au.id = auth.uid()) AND ((au.raw_user_meta_data ->> 'role'::text) = 'TNC_ADMIN'::text))))))) OR (EXISTS ( SELECT 1                                                                                   +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_customer_branding_overrides.tnc_customer_org_id) AND (uo.status = 'active'::text)))))
 public     | tnc_customer_permissions        | tnc_customer_permissions_access                      | PERMISSIVE | {public}        | ALL    | (is_super_admin_safe() OR (EXISTS ( SELECT 1                                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_customer_permissions.parent_tnc_org_id) AND (uo.status = 'active'::text) AND (EXISTS ( SELECT 1                                                       +
            |                                 |                                                      |            |                 |        |            FROM auth.users au                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |           WHERE ((au.id = auth.uid()) AND ((au.raw_user_meta_data ->> 'role'::text) = 'TNC_ADMIN'::text))))))) OR (EXISTS ( SELECT 1                                                                                   +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_customer_permissions.tnc_customer_org_id) AND (uo.status = 'active'::text)))))
 public     | tnc_customer_portal_configs     | tnc_portal_configs_access                            | PERMISSIVE | {public}        | ALL    | (is_super_admin_safe() OR (EXISTS ( SELECT 1                                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_customer_portal_configs.parent_tnc_org_id) AND (uo.status = 'active'::text) AND (EXISTS ( SELECT 1                                                    +
            |                                 |                                                      |            |                 |        |            FROM auth.users au                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |           WHERE ((au.id = auth.uid()) AND ((au.raw_user_meta_data ->> 'role'::text) = 'TNC_ADMIN'::text))))))) OR (EXISTS ( SELECT 1                                                                                   +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_customer_portal_configs.tnc_customer_org_id) AND (uo.status = 'active'::text)))))
 public     | tnc_customer_portal_routes      | tnc_customer_portal_routes_access                    | PERMISSIVE | {public}        | ALL    | (is_super_admin_safe() OR (EXISTS ( SELECT 1                                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_customer_portal_routes.parent_tnc_org_id) AND (uo.status = 'active'::text) AND (EXISTS ( SELECT 1                                                     +
            |                                 |                                                      |            |                 |        |            FROM auth.users au                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |           WHERE ((au.id = auth.uid()) AND ((au.raw_user_meta_data ->> 'role'::text) = 'TNC_ADMIN'::text))))))) OR (EXISTS ( SELECT 1                                                                                   +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_customer_portal_routes.tnc_customer_org_id) AND (uo.status = 'active'::text)))))
 public     | tnc_customer_sessions           | tnc_customer_sessions_access                         | PERMISSIVE | {public}        | ALL    | ((user_id = auth.uid()) OR is_super_admin_safe() OR (EXISTS ( SELECT 1                                                                                                                                                 +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_customer_sessions.parent_tnc_org_id) AND (uo.status = 'active'::text) AND (EXISTS ( SELECT 1                                                          +
            |                                 |                                                      |            |                 |        |            FROM auth.users au                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |           WHERE ((au.id = auth.uid()) AND ((au.raw_user_meta_data ->> 'role'::text) = 'TNC_ADMIN'::text))))))))
 public     | tnc_network_inheritance         | tnc_network_inheritance_access                       | PERMISSIVE | {public}        | ALL    | (is_super_admin_safe() OR (EXISTS ( SELECT 1                                                                                                                                                                           +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_network_inheritance.parent_tnc_org_id) AND (uo.status = 'active'::text) AND (EXISTS ( SELECT 1                                                        +
            |                                 |                                                      |            |                 |        |            FROM auth.users au                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |           WHERE ((au.id = auth.uid()) AND ((au.raw_user_meta_data ->> 'role'::text) = 'TNC_ADMIN'::text))))))) OR (EXISTS ( SELECT 1                                                                                   +
            |                                 |                                                      |            |                 |        |    FROM user_organizations uo                                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |   WHERE ((uo.user_id = auth.uid()) AND (uo.organization_id = tnc_network_inheritance.tnc_customer_org_id) AND (uo.status = 'active'::text)))))
 public     | trips                           | trips_organization_isolation                         | PERMISSIVE | {public}        | ALL    | ((organization_id IN ( SELECT trips.organization_id                                                                                                                                                                    +
            |                                 |                                                      |            |                 |        |    FROM user_profiles                                                                                                                                                                                                  +
            |                                 |                                                      |            |                 |        |   WHERE (user_profiles.id = auth.uid()))) OR (EXISTS ( SELECT 1                                                                                                                                                        +
            |                                 |                                                      |            |                 |        |    FROM user_profiles                                                                                                                                                                                                  +
            |                                 |                                                      |            |                 |        |   WHERE ((user_profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (user_profiles.roles))))))
 public     | user_organizations              | Service role can access all user_organizations       | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | user_organizations              | user_organizations_isolation                         | PERMISSIVE | {public}        | ALL    | ((user_id = auth.uid()) OR (EXISTS ( SELECT 1                                                                                                                                                                          +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (profiles.roles))))))
 public     | user_organizations              | user_organizations_multi_tenant_policy               | PERMISSIVE | {public}        | ALL    | ((auth.uid() = user_id) OR check_super_admin_role())
 public     | user_organizations              | user_organizations_own_records                       | PERMISSIVE | {authenticated} | ALL    | (user_id = auth.uid())
 public     | user_organizations              | user_organizations_production_policy                 | PERMISSIVE | {public}        | ALL    | ((auth.uid() = user_id) OR is_super_admin_safe())
 public     | user_organizations              | user_organizations_service_role_access               | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | user_organizations              | user_organizations_super_admin_access                | PERMISSIVE | {authenticated} | ALL    | is_super_admin_safe()
 public     | user_organizations              | user_organizations_users_can_read_own                | PERMISSIVE | {public}        | SELECT | (auth.uid() = user_id)
 public     | user_settings                   | user_settings_access_policy                          | PERMISSIVE | {public}        | ALL    | ((auth.uid() = user_id) OR is_super_admin_safe())
 public     | user_settings                   | user_settings_authenticated_access                   | PERMISSIVE | {public}        | ALL    | (auth.uid() = user_id)
 public     | user_settings                   | user_settings_own_access                             | PERMISSIVE | {authenticated} | ALL    | (auth.uid() = user_id)
 public     | user_settings                   | user_settings_service_role_full_access               | PERMISSIVE | {public}        | ALL    | (auth.role() = 'service_role'::text)
 public     | user_settings                   | user_settings_super_admin_access                     | PERMISSIVE | {authenticated} | ALL    | is_super_admin_safe()
 public     | vehicles                        | vehicles_delete_policy                               | PERMISSIVE | {public}        | DELETE | ((EXISTS ( SELECT 1                                                                                                                                                                                                    +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (profiles.roles))))) OR ((EXISTS ( SELECT 1                                                                                                    +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND (profiles.roles && ARRAY['AFFILIATE'::user_role, 'DISPATCHER'::user_role])))) AND (EXISTS ( SELECT 1                                                                           +
            |                                 |                                                      |            |                 |        |    FROM affiliate_users au                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |   WHERE ((au.user_id = auth.uid()) AND (au.affiliate_company_id = vehicles.affiliate_company_id) AND (au.role = 'MANAGER'::text) AND (au.employment_status = 'active'::text))))))
 public     | vehicles                        | vehicles_insert_policy                               | PERMISSIVE | {public}        | INSERT |
 public     | vehicles                        | vehicles_select_policy                               | PERMISSIVE | {public}        | SELECT | ((EXISTS ( SELECT 1                                                                                                                                                                                                    +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (profiles.roles))))) OR (organization_id IN ( SELECT user_organizations.organization_id                                                        +
            |                                 |                                                      |            |                 |        |    FROM user_organizations                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |   WHERE ((user_organizations.user_id = auth.uid()) AND (user_organizations.status = 'active'::text)))) OR (affiliate_company_id IN ( SELECT affiliate_users.affiliate_company_id                                       +
            |                                 |                                                      |            |                 |        |    FROM affiliate_users                                                                                                                                                                                                +
            |                                 |                                                      |            |                 |        |   WHERE ((affiliate_users.user_id = auth.uid()) AND (affiliate_users.employment_status = 'active'::text)))))
 public     | vehicles                        | vehicles_update_policy                               | PERMISSIVE | {public}        | UPDATE | ((EXISTS ( SELECT 1                                                                                                                                                                                                    +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND ('SUPER_ADMIN'::user_role = ANY (profiles.roles))))) OR ((EXISTS ( SELECT 1                                                                                                    +
            |                                 |                                                      |            |                 |        |    FROM profiles                                                                                                                                                                                                       +
            |                                 |                                                      |            |                 |        |   WHERE ((profiles.id = auth.uid()) AND (profiles.roles && ARRAY['AFFILIATE'::user_role, 'DISPATCHER'::user_role])))) AND (EXISTS ( SELECT 1                                                                           +
            |                                 |                                                      |            |                 |        |    FROM affiliate_users au                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |   WHERE ((au.user_id = auth.uid()) AND (au.affiliate_company_id = vehicles.affiliate_company_id) AND (au.role = 'MANAGER'::text) AND (au.employment_status = 'active'::text))))))
 public     | white_label_branding            | white_label_branding_organization_access             | PERMISSIVE | {public}        | ALL    | (((auth.uid() IS NOT NULL) AND (EXISTS ( SELECT 1                                                                                                                                                                      +
            |                                 |                                                      |            |                 |        |    FROM auth.users                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |   WHERE ((users.id = auth.uid()) AND ((users.raw_user_meta_data ->> 'role'::text) = 'SUPER_ADMIN'::text))))) OR (auth.role() = 'service_role'::text) OR (organization_id IN ( SELECT user_organizations.organization_id+
            |                                 |                                                      |            |                 |        |    FROM user_organizations                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |   WHERE ((user_organizations.user_id = auth.uid()) AND (user_organizations.status = 'active'::text)))))
 public     | white_label_domains             | white_label_domains_organization_access              | PERMISSIVE | {public}        | ALL    | (((auth.uid() IS NOT NULL) AND (EXISTS ( SELECT 1                                                                                                                                                                      +
            |                                 |                                                      |            |                 |        |    FROM auth.users                                                                                                                                                                                                     +
            |                                 |                                                      |            |                 |        |   WHERE ((users.id = auth.uid()) AND ((users.raw_user_meta_data ->> 'role'::text) = 'SUPER_ADMIN'::text))))) OR (auth.role() = 'service_role'::text) OR (organization_id IN ( SELECT user_organizations.organization_id+
            |                                 |                                                      |            |                 |        |    FROM user_organizations                                                                                                                                                                                             +
            |                                 |                                                      |            |                 |        |   WHERE ((user_organizations.user_id = auth.uid()) AND (user_organizations.status = 'active'::text)))))
(80 rows)

192:wwms-diy ameen$