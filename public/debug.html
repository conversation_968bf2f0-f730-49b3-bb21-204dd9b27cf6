<!DOCTYPE html>
<html>
<head>
    <title>Network Switcher Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { margin: 5px; padding: 10px 15px; }
    </style>
</head>
<body>
    <h1>🔍 Network Switcher Debug Tool</h1>
    
    <div class="test-section">
        <h3>1. Authentication Test</h3>
        <button onclick="testAuth()">Test Auth Status</button>
        <div id="auth-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Organizations API Test</h3>
        <button onclick="testOrganizations()">Test Organizations API</button>
        <div id="orgs-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Tenant Switch Test</h3>
        <button onclick="testTenantSwitch()">Test Tenant Switch</button>
        <div id="switch-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Network Switcher Component Test</h3>
        <button onclick="testNetworkSwitcher()">Test Component State</button>
        <div id="component-result" class="result"></div>
    </div>

    <script>
        async function testAuth() {
            const result = document.getElementById('auth-result');
            try {
                const response = await fetch('/api/debug/current-user', {
                    credentials: 'include'
                });
                const data = await response.json();
                
                result.className = 'result ' + (data.authenticated ? 'success' : 'error');
                result.innerHTML = `
                    <strong>Auth Status:</strong> ${data.authenticated ? 'Authenticated' : 'Not Authenticated'}<br>
                    <strong>User:</strong> ${data.user?.email || 'None'}<br>
                    <strong>Roles:</strong> ${JSON.stringify(data.user?.roles || [])}<br>
                    <strong>Response:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }
        
        async function testOrganizations() {
            const result = document.getElementById('orgs-result');
            try {
                const response = await fetch('/api/user/organizations', {
                    credentials: 'include'
                });
                const data = await response.json();
                
                result.className = 'result ' + (data.success ? 'success' : 'error');
                result.innerHTML = `
                    <strong>Success:</strong> ${data.success}<br>
                    <strong>Count:</strong> ${data.data?.length || 0}<br>
                    <strong>First Org:</strong> ${data.data?.[0]?.name || 'None'}<br>
                    <strong>Sample Org:</strong> <pre>${JSON.stringify(data.data?.[0] || {}, null, 2)}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }
        
        async function testTenantSwitch() {
            const result = document.getElementById('switch-result');
            try {
                const response = await fetch('/api/tenant/switch', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        organizationId: 'fa4bbd2c-642e-49f4-8bc1-ae770a6792fd'
                    })
                });
                const data = await response.json();
                
                result.className = 'result ' + (data.success ? 'success' : 'error');
                result.innerHTML = `
                    <strong>Success:</strong> ${data.success}<br>
                    <strong>Message:</strong> ${data.message || 'None'}<br>
                    <strong>Response:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }
        
        async function testNetworkSwitcher() {
            const result = document.getElementById('component-result');
            try {
                // Check if EnhancedNetworkSwitcher component exists
                const switchers = document.querySelectorAll('[data-testid="network-switcher"], .network-switcher, [class*="NetworkSwitcher"]');
                
                result.className = 'result';
                result.innerHTML = `
                    <strong>Network Switcher Elements Found:</strong> ${switchers.length}<br>
                    <strong>Console Errors:</strong> Check browser console for React errors<br>
                    <strong>Instructions:</strong> 
                    <ol>
                        <li>Open browser DevTools (F12)</li>
                        <li>Go to Console tab</li>
                        <li>Look for any React/Network Switcher errors</li>
                        <li>Check Network tab for failed API calls</li>
                        <li>Look for "EnhancedNetworkSwitcher" component logs</li>
                    </ol>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }
        
        // Auto-run tests on page load
        window.onload = function() {
            console.log('🔍 Network Switcher Debug Tool Loaded');
            console.log('Run tests manually or check console for component errors');
        };
    </script>
</body>
</html>