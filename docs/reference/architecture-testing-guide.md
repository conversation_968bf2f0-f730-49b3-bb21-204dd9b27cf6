# Architecture Testing Guide

## Quick Testing of Networks vs Organizations Architecture

This guide shows you how to quickly validate the key architectural decisions from our Networks vs Organizations analysis using the existing testing infrastructure.

---

## 🚀 Quick Start Commands

### **1. Test Networks vs Organizations Architecture**
```bash
# Run our new specific test
node scripts/test-networks-vs-organizations-architecture.js
```

### **2. Test Four-Tier System Integration**
```bash
# Run network inheritance validation
node scripts/four-tier-tests/4-network-inheritance-validation.js

# Run portal access validation  
node scripts/four-tier-tests/2-portal-access-validation.js
```

### **3. Run Complete Architecture Validation Suite**
```bash
# Run all architecture tests together
bash scripts/run-architecture-validation-suite.sh
```

### **4. Run Full Four-Tier Test Suite**
```bash
# Run comprehensive four-tier validation
node scripts/four-tier-tests/run-all-tests.js
```

---

## 🎯 What Each Test Validates

### **Networks vs Organizations Test**
- ✅ **Metro Ride Network Classification**: Confirms it's an Organization (TNC Account), not a Network
- ✅ **Network Switcher Logic**: Validates visibility rules for different user types
- ✅ **Service Tier Mapping**: Tests Marriott luxury/business/express scenario
- ✅ **Geographic Networks**: Tests Global Car Service multi-region scenario
- ✅ **User Role Mapping**: Confirms Ritz-Carlton uses TNC_CUSTOMER → CLIENT roles

### **Network Inheritance Test**
- ✅ **TNC Customer Inheritance**: Validates customers inherit network from parent TNC
- ✅ **Direct Client Restrictions**: Confirms direct clients use shared network only
- ✅ **Network Isolation**: Tests isolation between different TNC networks
- ✅ **Feature Flag Inheritance**: Validates feature inheritance patterns

### **Portal Access Test**
- ✅ **Super Admin Access**: Tests TransFlow Super Admin portal access
- ✅ **TNC Admin Access**: Tests TNC Admin portal access (stripped super admin)
- ✅ **TNC Customer Access**: Tests customer portal with inherited branding
- ✅ **Direct Client Access**: Tests event-manager portal access

---

## 📊 Expected Test Results

### **✅ PASSING SCENARIOS**

**Metro Ride Network Classification:**
```
✅ CORRECT: Metro Ride Network is a TNC Account Organization
✅ CORRECT: It provides "Metro Ride Transportation Network" (the service)
✅ CORRECT: It manages TNC customers like "Metro Boston Downtown Office"
```

**Network Switcher Visibility:**
```
User Type                    | Networks | Show Switcher | Reason
-----------------------------|----------|---------------|------------------
TRANSFLOW_SUPER_ADMIN        | 3        | YES           | ✅ Platform administration needs
TNC_ADMIN (Single Network)   | 1        | NO            | ✅ Only one network - no choice needed
TNC_ADMIN (Multi Network)    | 2        | YES           | ✅ Multiple networks - choice needed
TNC_CUSTOMER                 | 1        | NO            | ✅ Network inherited - no choice
DIRECT_CLIENT                | 1        | NO            | ✅ Locked to shared - no choice
```

**Service Tier Mapping:**
```
TNC Organization: Marriott International
   Account Type: tnc_account
   Role: TNC_ADMIN
   Portal: /super-admin (stripped)

TNC Customers (Service Tiers):
📄 Ritz-Carlton Boston:
   Account Type: tnc_customer
   Role: CLIENT/CLIENT_COORDINATOR
   Portal: /event-manager (Marriott-branded)
   Service Tier: luxury
   ✅ Correct service tier mapping
```

---

## 🔧 Troubleshooting

### **If Tests Fail:**

**1. Database Connection Issues:**
```bash
# Check if Supabase is running
supabase status

# Reset database if needed
supabase db reset
```

**2. Missing Test Data:**
```bash
# Apply test data migration
supabase db push

# Or run test data script
node scripts/apply-comprehensive-test-data.js
```

**3. MCP Tool Issues:**
```bash
# Check MCP configuration
cat ~/.kiro/settings/mcp.json

# Test MCP connection
echo "SELECT 1 as test" | npx @modelcontextprotocol/cli call mcp_access_db_query
```

---

## 📋 Test Scenarios Covered

### **1. Single Network TNC (90% of cases)**
- **Example**: Metro Ride Network
- **Networks**: 1 (Metro Ride Transportation Network)
- **Switcher**: Hidden (no choice needed)
- **Implementation**: Service tiers via rate cards

### **2. Multi-Network TNC (10% of cases)**  
- **Example**: Global Car Service International
- **Networks**: 3 (North America, Europe, Asia Pacific)
- **Switcher**: Visible (geographic choice needed)
- **Implementation**: Regional network assignment

### **3. Service Tier Differentiation**
- **Example**: Marriott International
- **Approach**: Single network + rate card tiers
- **Customers**: Ritz-Carlton (luxury), Marriott (business), Courtyard (express)
- **Portal**: All use /event-manager with Marriott branding

### **4. Direct Client Scenario**
- **Example**: Independent hotel
- **Network**: TransFlow Shared Network only
- **Switcher**: Hidden (no choice)
- **Portal**: /event-manager with TransFlow branding

---

## 🎯 Success Criteria

### **Architecture Validation Passes If:**
- ✅ **80%+ test success rate**
- ✅ **Metro Ride Network correctly classified as Organization**
- ✅ **Network switcher logic validates correctly**
- ✅ **Service tier mapping is architecturally sound**
- ✅ **User role inheritance works properly**

### **Ready for Implementation When:**
- ✅ **All core tests pass**
- ✅ **Network inheritance patterns validated**
- ✅ **Portal access controls working**
- ✅ **Four-tier architecture stable**

---

## 🚀 Implementation Readiness

### **Current Status:**
- ✅ **Architecture Analysis**: Complete and documented
- ✅ **Test Infrastructure**: Comprehensive test suite available
- ✅ **Validation Logic**: Network switcher logic defined
- ✅ **User Role Mapping**: Service tier roles clarified

### **Next Steps:**
1. **Run test suite** to validate current implementation
2. **Fix any failing tests** before proceeding
3. **Implement network switcher** with validated logic
4. **Focus on MVP completion** (defer service tier features)
5. **Use tests for ongoing validation** during development

---

## 📞 Quick Reference Commands

```bash
# Quick architecture validation
node scripts/test-networks-vs-organizations-architecture.js

# Full test suite
bash scripts/run-architecture-validation-suite.sh

# Network inheritance only
node scripts/four-tier-tests/4-network-inheritance-validation.js

# Portal access only  
node scripts/four-tier-tests/2-portal-access-validation.js

# All four-tier tests
node scripts/four-tier-tests/run-all-tests.js
```

**This testing approach ensures our architectural decisions are validated against real implementation patterns!** 🎯