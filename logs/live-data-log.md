I want you to read the last 15 created documentation files in this app (files ending with .md) so you can proper context of what the issue is (you can read more about it here: NETWORK_SWITCHER_COMPREHENSIVE_TROUBLESHOOTING_REPORT.md)

Also, check the migration files under supabase/migrations and run a supabase db reset, then start troubleshooting based on that. 

But remember, we have rules to follow under the .kiro folder