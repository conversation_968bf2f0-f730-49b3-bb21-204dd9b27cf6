# Network Switcher - Login Credentials & Fix

## 🔑 **LOGIN CREDENTIALS**

### Use These Credentials:
- **Email**: `<EMAIL>`
- **Password**: `password123`

OR

- **Email**: `<EMAIL>`  
- **Password**: `Admin123!` (try this first, if it doesn't work, try `password123`)

## 🐛 **Root Cause Identified**

The issue is that the `/api/user/organizations` route is **NOT using** the `get_user_accessible_organizations()` function we just fixed. Instead, it's querying the `user_organizations` table directly, which explains why SUPER_ADMIN users aren't seeing all organizations.

## 🔧 **The Fix Needed**

The API route needs to be updated to use our database function for SUPER_ADMIN users:

```typescript
// For SUPER_ADMIN users, use the database function
if (user.is_super_admin) {
  const { data: accessibleOrgs, error } = await supabase
    .rpc('get_user_accessible_organizations', { user_uuid: user.id });
  
  // Transform the results...
}
```

## 🚀 **Quick Test Steps**

1. **Login** with `<EMAIL>` / `password123`
2. **Open Developer Tools** (F12)
3. **Go to Network tab**
4. **Look for API calls** to `/api/user/organizations`
5. **Check the response** - it should show organizations but might be limited

## 🎯 **Expected Behavior After Fix**

Once we update the API route to use `get_user_accessible_organizations()`:
- SUPER_ADMIN users will see all 22+ organizations in the dropdown
- Regular users will see only their assigned organizations
- Network switcher will work correctly

## 📝 **Next Steps**

1. **Try logging in** with the credentials above
2. **Check if you can access the platform**
3. **Let me know what you see** in the network switcher
4. **I'll update the API route** to use the correct database function

The backend database function is working perfectly - we just need to connect the frontend API to use it!