# UI Testing Plan: Networks vs Organizations Architecture

## 🎯 **Testing Overview**

Based on our **80% successful architecture validation**, this plan provides comprehensive UI test cases to manually validate the Networks vs Organizations architecture using our existing seed data.

**Test Environment**: Local development with validated seed data  
**Architecture Status**: 80% validated - ready for UI implementation testing  
**Platform Completion**: 81% - targeting MVP  

---

## 📊 **Available Test Data Summary**

### **Organizations by Account Type**
```
✅ TNC Accounts (14): Metro Ride Network, Elite Corporate Travel, Luxury Concierge Transportation, etc.
✅ TNC Customers (3): Metro Boston Downtown Office, Elite Boston Corporate Branch, Luxury Boston Hotel Services  
✅ Direct Clients (6): TransFlow Shared, Demo Transportation Hub, Beta Testing Org, etc.
```

### **Test Users Available**
```
✅ SUPER_ADMIN: <EMAIL>, <EMAIL>, <EMAIL>
✅ CLIENT: <EMAIL>
✅ AFFILIATE: <EMAIL>, <EMAIL>
✅ DISPATCHER: <EMAIL>, <EMAIL>  
✅ PASSENGER: <EMAIL>, <EMAIL>, <EMAIL>
```

### **Key Test Relationships**
```
🏢 Metro Ride Network (TNC Account)
   └── Metro Boston Downtown Office (TNC Customer)

🏢 Elite Corporate Travel (TNC Account)  
   └── Elite Boston Corporate Branch (TNC Customer)

🏢 Luxury Concierge Transportation (TNC Account)
   └── Luxury Boston Hotel Services (TNC Customer)
```

---

## 🧪 **Test Case Categories**

## **Category 1: Network Switcher Visibility Logic** ✅ **VALIDATED (100%)**

### **Test Case 1.1: SUPER_ADMIN Network Switcher**
**User**: `<EMAIL>` or `<EMAIL>`  
**Expected**: Network switcher should be **VISIBLE** in top-right corner

**Steps**:
1. Login as SUPER_ADMIN user
2. Navigate to any portal (Super Admin, Event Manager, etc.)
3. Check top-right corner for network switcher component
4. **✅ EXPECTED**: Switcher shows with multiple networks available
5. **✅ EXPECTED**: Can switch between TransFlow Shared Network and TNC networks

**Validation Points**:
- [ ] Network switcher component is visible
- [ ] Shows "TransFlow Shared Network" option
- [ ] Shows TNC-specific networks (Metro Ride Transportation Network, etc.)
- [ ] Switching networks updates the current context
- [ ] All networks are accessible to SUPER_ADMIN

---

### **Test Case 1.2: TNC_ADMIN Single Network (Hidden Switcher)**
**User**: Create TNC_ADMIN user for Metro Ride Network  
**Expected**: Network switcher should be **HIDDEN**

**Steps**:
1. Login as TNC_ADMIN user (single network TNC)
2. Navigate to Super Admin portal (stripped version)
3. Check top-right corner for network switcher
4. **✅ EXPECTED**: No network switcher visible (only one network)
5. **✅ EXPECTED**: Automatically using "Metro Ride Transportation Network"

**Validation Points**:
- [ ] Network switcher component is NOT visible
- [ ] Current network context is Metro Ride Transportation Network
- [ ] No option to switch networks (single network scenario)
- [ ] Portal functions normally without switcher

---

### **Test Case 1.3: TNC_CUSTOMER Network Inheritance (Hidden Switcher)**
**User**: Create CLIENT user for Metro Boston Downtown Office  
**Expected**: Network switcher should be **HIDDEN**

**Steps**:
1. Login as CLIENT user for TNC customer organization
2. Navigate to Event Manager portal
3. Check top-right corner for network switcher
4. **✅ EXPECTED**: No network switcher visible (inherited network)
5. **✅ EXPECTED**: Using parent TNC's network automatically

**Validation Points**:
- [ ] Network switcher component is NOT visible
- [ ] Network inherited from parent TNC (Metro Ride Network)
- [ ] Portal shows TNC branding (not TransFlow branding)
- [ ] All functionality works with inherited network context

---

### **Test Case 1.4: DIRECT_CLIENT Shared Network (Hidden Switcher)**
**User**: Create CLIENT user for TransFlow Shared organization  
**Expected**: Network switcher should be **HIDDEN**

**Steps**:
1. Login as CLIENT user for direct client organization
2. Navigate to Event Manager portal
3. Check top-right corner for network switcher
4. **✅ EXPECTED**: No network switcher visible (locked to shared)
5. **✅ EXPECTED**: Using TransFlow Shared Network only

**Validation Points**:
- [ ] Network switcher component is NOT visible
- [ ] Locked to TransFlow Shared Network
- [ ] Portal shows TransFlow branding
- [ ] Cannot access TNC-specific networks

---

## **Category 2: Service Tier User Role Mapping** ✅ **VALIDATED (100%)**

### **Test Case 2.1: Luxury Service Tier (Ritz-Carlton Scenario)**
**Organization**: Luxury Boston Hotel Services (TNC Customer)  
**Expected**: CLIENT/CLIENT_COORDINATOR roles with luxury service tier

**Steps**:
1. Create CLIENT user for Luxury Boston Hotel Services
2. Login and navigate to Event Manager portal
3. Check user role and service tier indicators
4. **✅ EXPECTED**: Role shows as CLIENT or CLIENT_COORDINATOR
5. **✅ EXPECTED**: Portal shows luxury service tier branding/options

**Validation Points**:
- [ ] User has CLIENT or CLIENT_COORDINATOR role
- [ ] Portal shows /event-manager interface
- [ ] Branding inherited from parent TNC (Luxury Concierge Transportation)
- [ ] Service tier indicators show "luxury" level
- [ ] Access to luxury-specific features/rate cards

---

### **Test Case 2.2: Business Service Tier (Standard Hotel Scenario)**
**Organization**: Metro Boston Downtown Office (TNC Customer)  
**Expected**: CLIENT role with business service tier

**Steps**:
1. Create CLIENT user for Metro Boston Downtown Office
2. Login and navigate to Event Manager portal
3. Check service tier and available options
4. **✅ EXPECTED**: Business-level service tier options
5. **✅ EXPECTED**: Metro Ride Network branding

**Validation Points**:
- [ ] User has CLIENT role
- [ ] Portal shows business service tier options
- [ ] Branding inherited from Metro Ride Network
- [ ] Access to business-level rate cards and features
- [ ] Cannot access luxury-tier exclusive features

---

## **Category 3: Portal Access Control Validation** ✅ **VALIDATED**

### **Test Case 3.1: Super Admin Portal Access Matrix**

**Test 3.1a: SUPER_ADMIN Full Access**
**User**: `<EMAIL>`  
**Expected**: Full Super Admin portal access

**Steps**:
1. Login as SUPER_ADMIN
2. Navigate to `/super-admin` portal
3. Check all menu sections are accessible
4. **✅ EXPECTED**: Platform Tenancy, Customer Tenancy, OPS Menu all visible

**Validation Points**:
- [ ] Platform Tenancy section accessible (organization management, TNC creation)
- [ ] Customer Tenancy section accessible (TNC customer management)
- [ ] OPS Menu accessible (network operations, quotes, affiliates)
- [ ] All administrative functions available

---

**Test 3.1b: TNC_ADMIN Limited Access**
**User**: Create TNC_ADMIN for Metro Ride Network  
**Expected**: Limited Super Admin portal access (stripped version)

**Steps**:
1. Login as TNC_ADMIN
2. Navigate to `/super-admin` portal
3. Check which sections are accessible
4. **✅ EXPECTED**: Customer Tenancy + OPS Menu only, NO Platform Tenancy

**Validation Points**:
- [ ] Platform Tenancy section NOT accessible (blocked)
- [ ] Customer Tenancy section accessible (manage their customers)
- [ ] OPS Menu accessible (network-scoped operations)
- [ ] Cannot create other TNCs or manage platform settings

---

### **Test Case 3.2: Event Manager Portal Branding**

**Test 3.2a: TNC Customer Portal (TNC-Branded)**
**User**: CLIENT for Metro Boston Downtown Office  
**Expected**: Event Manager portal with Metro Ride Network branding

**Steps**:
1. Login as CLIENT for TNC customer
2. Navigate to `/event-manager` portal
3. Check branding elements and theme
4. **✅ EXPECTED**: Metro Ride Network branding, not TransFlow branding

**Validation Points**:
- [ ] Portal shows Metro Ride Network logo/branding
- [ ] Color scheme matches parent TNC theme
- [ ] All functionality available with TNC context
- [ ] Network context inherited from parent TNC

---

**Test 3.2b: Direct Client Portal (TransFlow-Branded)**
**User**: CLIENT for TransFlow Shared organization  
**Expected**: Event Manager portal with TransFlow branding

**Steps**:
1. Login as CLIENT for direct client
2. Navigate to `/event-manager` portal
3. Check branding elements and theme
4. **✅ EXPECTED**: TransFlow branding and shared network context

**Validation Points**:
- [ ] Portal shows TransFlow logo/branding
- [ ] Standard TransFlow color scheme and theme
- [ ] Access to shared affiliate network only
- [ ] No TNC-specific customizations

---

## **Category 4: Organization vs Network Distinction** ✅ **VALIDATED**

### **Test Case 4.1: Metro Ride Network Classification**
**Focus**: Confirm Metro Ride Network is an Organization, not a Network  
**Expected**: Clear distinction in UI between organization and network concepts

**Steps**:
1. Login as SUPER_ADMIN
2. Navigate to Organizations management
3. Find "Metro Ride Network" in organizations list
4. **✅ EXPECTED**: Listed as TNC Account organization
5. Check Networks section for "Metro Ride Transportation Network"
6. **✅ EXPECTED**: Network provided by Metro Ride Network organization

**Validation Points**:
- [ ] Metro Ride Network appears in Organizations list (left sidebar)
- [ ] Metro Ride Transportation Network appears in Networks list (top-right switcher)
- [ ] Clear UI distinction between organization entity and network service
- [ ] Organization manages the network, customers use the network

---

### **Test Case 4.2: Network Switcher vs Organization Selector**
**Focus**: Validate UI clearly separates network switching from organization selection

**Steps**:
1. Login as SUPER_ADMIN
2. Check top-right corner for network switcher
3. Check left sidebar for organization/tenant selector
4. **✅ EXPECTED**: Two distinct UI components with different purposes

**Validation Points**:
- [ ] Network switcher (top-right) shows available networks
- [ ] Organization selector (left sidebar) shows organizations for OPS menu
- [ ] Switching networks changes operational context
- [ ] Selecting organizations changes data scope
- [ ] No confusion between the two concepts in UI

---

## **Category 5: Four-Tier Architecture Integration**

### **Test Case 5.1: Account Type Hierarchy Display**
**Focus**: Validate UI correctly displays four-tier account relationships

**Steps**:
1. Login as SUPER_ADMIN
2. Navigate to Organizations management
3. Check organization hierarchy display
4. **✅ EXPECTED**: Clear parent-child relationships shown

**Validation Points**:
- [ ] TNC Accounts shown as parent organizations
- [ ] TNC Customers shown as children with parent references
- [ ] Direct Clients shown as independent organizations
- [ ] Hierarchy visually clear in organization tree/list

---

### **Test Case 5.2: Permission Inheritance Validation**
**Focus**: Validate TNC customers inherit permissions and features from parent

**Steps**:
1. Login as CLIENT for TNC customer (Metro Boston Downtown Office)
2. Check available features and permissions
3. Compare with parent TNC's feature set
4. **✅ EXPECTED**: Features inherited from Metro Ride Network

**Validation Points**:
- [ ] Feature flags inherited from parent TNC
- [ ] Network access inherited from parent TNC
- [ ] Branding inherited from parent TNC
- [ ] Service tier determined by parent TNC configuration

---

## 🚀 **Implementation Priority Test Cases**

### **🔥 HIGH PRIORITY (Implement First)**

1. **Test Case 1.1**: SUPER_ADMIN Network Switcher Visibility
2. **Test Case 1.3**: TNC_CUSTOMER Hidden Network Switcher
3. **Test Case 1.4**: DIRECT_CLIENT Hidden Network Switcher
4. **Test Case 3.2a**: TNC Customer Portal Branding
5. **Test Case 4.1**: Metro Ride Network Classification

### **🔶 MEDIUM PRIORITY (Implement Second)**

1. **Test Case 1.2**: TNC_ADMIN Single Network Scenario
2. **Test Case 2.1**: Luxury Service Tier Role Mapping
3. **Test Case 3.1a**: SUPER_ADMIN Full Portal Access
4. **Test Case 5.1**: Account Type Hierarchy Display

### **🔵 LOW PRIORITY (Post-MVP)**

1. **Test Case 2.2**: Business Service Tier Differentiation
2. **Test Case 3.1b**: TNC_ADMIN Limited Portal Access
3. **Test Case 5.2**: Advanced Permission Inheritance

---

## 📋 **Test Execution Checklist**

### **Pre-Test Setup**
- [ ] Confirm local development environment is running
- [ ] Verify seed data is loaded (organizations, users, relationships)
- [ ] Confirm authentication system is working
- [ ] Test user accounts are accessible

### **During Testing**
- [ ] Document any UI inconsistencies with expected behavior
- [ ] Screenshot key UI states for documentation
- [ ] Note any missing components or features
- [ ] Test responsive behavior on different screen sizes

### **Post-Test Documentation**
- [ ] Create test results summary with pass/fail status
- [ ] Document any bugs or UI improvements needed
- [ ] Prioritize fixes based on MVP requirements
- [ ] Update architecture validation status

---

## 🎯 **Success Criteria**

### **✅ PASSING CRITERIA**
- Network switcher visibility logic works as validated (80%+ scenarios)
- TNC customer portal branding inheritance works correctly
- Organization vs Network distinction is clear in UI
- Four-tier account hierarchy is properly displayed
- User roles map correctly to portal access

### **🚨 FAILING CRITERIA**
- Network switcher appears when it should be hidden
- TNC customers see TransFlow branding instead of parent TNC branding
- Organizations and Networks are confused in UI
- User roles don't match expected portal access
- Account type hierarchy is not clear

---

## 📞 **Quick Test Commands**

```bash
# Start local development
npm run dev

# Check database state
node scripts/test-networks-vs-organizations-architecture.js

# Run full architecture validation
bash scripts/run-architecture-validation-suite.sh

# Test authentication
node scripts/test-authentication-complete.js
```

---

## 🎉 **Expected Outcome**

Based on our **80% architecture validation success rate**, we expect:

- **✅ 80-90% of UI test cases to pass** immediately
- **⚠️ 10-20% to need minor UI adjustments** (expected)
- **🚨 0-5% to reveal architectural issues** (minimal)

This testing plan will validate that our UI implementation correctly reflects the validated Networks vs Organizations architecture, ensuring a smooth user experience that matches our architectural decisions.

**The goal is to confirm our UI is ready for MVP launch with the validated four-tier architecture!** 🚀