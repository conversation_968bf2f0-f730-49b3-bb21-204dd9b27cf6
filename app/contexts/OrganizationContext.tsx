"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useAuth } from "@/lib/auth/context";
import { getSupabaseClient } from "@/lib/supabase";
import { 
  tenant<PERSON>ontext<PERSON>anager, 
  TenantContext,
  OrganizationAccess 
} from "@/app/lib/auth/tenant-context";

export interface Organization {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  parent_organization_id?: string;
  organization_type: "shared" | "segregated" | "white_label";
  status: "active" | "inactive" | "suspended";
  branding: {
    logo_url?: string;
    primary_color?: string;
    secondary_color?: string;
    background_color?: string;
    custom_css?: string;
  };
  settings: {
    allowGlobalNetwork?: boolean;
    upsell_threshold?: number;
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
}

export interface OrganizationBranding {
  id: string;
  organization_id: string;
  logo_url?: string;
  favicon_url?: string;
  primary_color?: string;
  secondary_color?: string;
  background_color?: string;
  custom_css?: string;
  email_templates: Record<string, string>;
}

interface OrganizationContextType {
  currentOrganization: Organization | null;
  setCurrentOrganization: (organization: Organization | null) => void;
  organizations: Organization[];
  setOrganizations: (organizations: Organization[]) => void;
  loading: boolean;
  error: string | null;
  switchOrganization: (organizationId: string) => Promise<void>;
  refreshOrganizations: () => Promise<void>;
  branding: OrganizationBranding | null;
  setBranding: (branding: OrganizationBranding | null) => void;
  // Enhanced multi-tenant features
  tenantContext: TenantContext | null;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  canSwitchTenants: boolean;
  validateAccess: (organizationId: string) => Promise<boolean>;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(
  undefined
);

export function OrganizationProvider({ children }: { children: ReactNode }) {
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [branding, setBranding] = useState<OrganizationBranding | null>(null);
  const [tenantContext, setTenantContext] = useState<TenantContext | null>(null);
  const { user } = useAuth();

  const supabase = getSupabaseClient();

  // Load user's organizations
  const loadOrganizations = async () => {
    if (!user) {
      setOrganizations([]);
      setCurrentOrganization(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Direct query to user_organizations with proper joins
      const { data: userOrgs, error: userOrgsError } = await supabase
        .from("user_organizations")
        .select(`
          role,
          permissions,
          organization_id,
          organizations!inner (
            id,
            name,
            slug,
            status,
            organization_type,
            settings,
            created_at,
            updated_at
          )
        `)
        .eq("user_id", user.id);

      if (userOrgsError) {
        console.error("Error loading user organizations:", userOrgsError);
        
        // If the error is about permissions, try a simpler approach
        if (userOrgsError.code === '42501') {
          console.log("Permission denied, trying alternative approach...");
          
          // Try using the profiles view instead
          const { data: profileData, error: profileError } = await supabase
            .from("profiles")
            .select("id, email, roles")
            .eq("id", user.id)
            .single();

          if (profileError) {
            console.error("Profile query failed:", profileError);
            setError("Authentication failed. Please try logging out and back in.");
            return;
          }

          // For SUPER_ADMIN users, get the super admin organization
          if (profileData.roles?.includes('SUPER_ADMIN')) {
            const { data: superAdminOrg, error: orgError } = await supabase
              .from("organizations")
              .select("*")
              .eq("account_type", "transflow_super_admin")
              .single();

            if (orgError) {
              console.error("Super admin org query failed:", orgError);
              setError("Failed to load super admin organization");
              return;
            }

            const orgs = [{
              ...superAdminOrg,
              user_role: 'SUPER_ADMIN',
              branding: superAdminOrg.branding || {}
            }];

            setOrganizations(orgs);
            setCurrentOrganization(orgs[0]);
            await loadOrganizationBranding(orgs[0].id);
            return;
          }
        }
        
        setError("Failed to load organizations");
        return;
      }

      const orgs = userOrgs?.map((uo: any) => ({
        ...uo.organizations,
        user_role: uo.role,
        branding: uo.organizations.branding || {}
      })) || [];

      setOrganizations(orgs);

      // Load current organization from user settings or default to first
      const { data: currentOrgSetting } = await supabase
        .from("user_settings")
        .select("setting_value")
        .eq("user_id", user.id)
        .eq("setting_name", "app.current_organization_id")
        .maybeSingle();

      let currentOrgId = currentOrgSetting?.setting_value;
      
      // If no current org set or org not accessible, use first available
      if (!currentOrgId || !orgs.find(org => org.id === currentOrgId)) {
        currentOrgId = orgs[0]?.id;
      }

      if (currentOrgId) {
        const currentOrg = orgs.find(org => org.id === currentOrgId);
        if (currentOrg) {
          setCurrentOrganization(currentOrg);
          await loadOrganizationBranding(currentOrgId);
        }
      }
    } catch (err) {
      console.error("Error in loadOrganizations:", err);
      setError("Failed to load organizations");
    } finally {
      setLoading(false);
    }
  };

  // Load organization branding
  const loadOrganizationBranding = async (organizationId: string) => {
    try {
      const { data: brandingData, error: brandingError } = await supabase
        .from("organization_branding")
        .select("*")
        .eq("organization_id", organizationId)
        .single();

      if (brandingError && brandingError.code !== "PGRST116") {
        console.error("Error loading organization branding:", brandingError);
        return;
      }

      setBranding(brandingData || null);
    } catch (err) {
      console.error("Error loading organization branding:", err);
    }
  };

  // Switch to a different organization with enhanced security
  const switchOrganization = async (organizationId: string) => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // Use tenant context manager for secure switching
      const newContext = await tenantContextManager.switchOrganization(user.id, organizationId);
      
      if (!newContext) {
        setError("Failed to switch organization");
        return;
      }

      // Update tenant context
      setTenantContext(newContext);

      // Find and set current organization
      const targetOrg = organizations.find(org => org.id === organizationId);
      if (targetOrg) {
        setCurrentOrganization(targetOrg);
        await loadOrganizationBranding(organizationId);
      }

    } catch (err) {
      console.error("Error switching organization:", err);
      setError(err instanceof Error ? err.message : "Failed to switch organization");
    } finally {
      setLoading(false);
    }
  };

  // Refresh organizations list
  const refreshOrganizations = async () => {
    await loadOrganizations();
  };

  // Enhanced permission and role checking
  const hasPermission = (permission: string): boolean => {
    return tenantContextManager.hasPermission(permission);
  };

  const hasRole = (role: string): boolean => {
    return tenantContext?.userRole === role || tenantContext?.isSuperAdmin || false;
  };

  const canSwitchTenants = tenantContext?.canSwitchTenants || false;

  const validateAccess = async (organizationId: string): Promise<boolean> => {
    if (!user) return false;
    return await tenantContextManager.validateOrganizationAccess(user.id, organizationId);
  };

  // Initialize tenant context when user changes
  useEffect(() => {
    const initializeTenant = async () => {
      if (user) {
        try {
          const context = await tenantContextManager.initializeTenantContext(user.id);
          setTenantContext(context);
        } catch (error) {
          console.error("Error initializing tenant context:", error);
        }
      } else {
        tenantContextManager.clearContext();
        setTenantContext(null);
      }
    };

    initializeTenant();
    loadOrganizations();
  }, [user]);

  // Listen for tenant context changes
  useEffect(() => {
    const handleContextChange = (context: TenantContext | null) => {
      setTenantContext(context);
    };

    tenantContextManager.addContextChangeListener(handleContextChange);

    return () => {
      tenantContextManager.removeContextChangeListener(handleContextChange);
    };
  }, []);

  const value: OrganizationContextType = {
    currentOrganization,
    setCurrentOrganization,
    organizations,
    setOrganizations,
    loading,
    error,
    switchOrganization,
    refreshOrganizations,
    branding,
    setBranding,
    // Enhanced multi-tenant features
    tenantContext,
    hasPermission,
    hasRole,
    canSwitchTenants,
    validateAccess,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error("useOrganization must be used within an OrganizationProvider");
  }
  return context;
}

// Hook for organization-aware queries with enhanced security
export function useOrganizationAwareQuery() {
  const { currentOrganization, tenantContext } = useOrganization();

  const getOrganizationAwareQuery = (query: any) => {
    // Use tenant context for more secure filtering
    const filter = tenantContextManager.getOrganizationFilter();
    if (!filter) {
      return query.eq("organization_id", "00000000-0000-0000-0000-000000000000"); // No results
    }
    return query.eq("organization_id", filter.organization_id);
  };

  const getSecureQuery = (query: any, additionalFilters?: Record<string, any>) => {
    let secureQuery = getOrganizationAwareQuery(query);
    
    // Apply additional filters if provided
    if (additionalFilters) {
      Object.entries(additionalFilters).forEach(([key, value]) => {
        secureQuery = secureQuery.eq(key, value);
      });
    }
    
    return secureQuery;
  };

  return { 
    getOrganizationAwareQuery, 
    getSecureQuery,
    currentOrganization, 
    tenantContext,
    organizationId: tenantContext?.organizationId || null
  };
}