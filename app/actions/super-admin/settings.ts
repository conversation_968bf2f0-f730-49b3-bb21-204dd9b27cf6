'use server'

import { z } from 'zod'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { revalidatePath } from 'next/cache'
import { hasRole } from '@/app/lib/auth';
import { UserRole } from '@/app/lib/auth/roles';

// Define the schema for platform settings using Zod
const platformConfigSchema = z.object({
  platform_name: z.string().min(1, 'Platform name is required'),
  default_timezone: z.string().min(1, 'Default timezone is required'),
  billing_email: z.string().email('Invalid email address').nullable().or(z.literal('')),
  notifications_enabled: z.boolean(),
  slack_integration_enabled: z.boolean(),
  webhook_url: z.string().url('Invalid URL').nullable().or(z.literal('')),
  // Note: Security/Operations settings are handled in their respective actions
})

export type PlatformConfig = z.infer<typeof platformConfigSchema>;

// --- Server Action: Get Platform Settings ---
export async function getPlatformSettings() {
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          cookieStore.set(name, value, options)
        },
        remove(name: string, options: any) {
          cookieStore.set(name, '', options)
        },
      },
    }
  )

  // Verify user is Super Admin
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { error: 'Unauthorized' }
  }

  // Get user's roles from the profiles table
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('roles')
    .eq('id', user.id)
    .single()

  if (profileError || !profile) {
    console.error('Error fetching user profile:', profileError)
    return { error: 'Forbidden' }
  }

  const roles = profile.roles || []
  if (!hasRole(roles, 'SUPER_ADMIN')) {
    return { error: 'Forbidden' }
  }

  try {
    const { data, error } = await supabase
      .from('platform_config')
      .select('*')
      .eq('id', 1) // Select the singleton row
      .limit(1) // Ensure we only get one row

    if (error) throw error
    if (!data || data.length === 0) throw new Error('Platform configuration not found.')

    // Use the first row if multiple exist
    const configData = Array.isArray(data) ? data[0] : data;

    // Select only the fields relevant to the general settings page for now
    const settingsData: PlatformConfig = {
        platform_name: configData.platform_name,
        default_timezone: configData.default_timezone,
        billing_email: configData.billing_email,
        notifications_enabled: configData.notifications_enabled,
        slack_integration_enabled: configData.slack_integration_enabled,
        webhook_url: configData.webhook_url,
    };

    return { data: settingsData }
  } catch (error: any) {
    console.error('Error fetching platform settings:', error)
    return { error: error.message || 'Failed to fetch settings' }
  }
}

// --- Server Action: Update Platform Settings ---
export async function updatePlatformSettings(formData: PlatformConfig) {
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          cookieStore.set(name, value, options)
        },
        remove(name: string, options: any) {
          cookieStore.set(name, '', options)
        },
      },
    }
  )

  // Verify user is Super Admin
  const { data: { user } } = await supabase.auth.getUser()
  // Robustly extract roles from user metadata
  let roles: string[] = [];
  if (user?.app_metadata?.roles && Array.isArray(user.app_metadata.roles)) {
    roles = user.app_metadata.roles;
  } else if (user?.app_metadata?.role) {
    roles = [user.app_metadata.role];
  }
  // Map legacy ADMIN to SUPER_ADMIN for compatibility
  roles = roles.map(r => r === 'ADMIN' ? 'SUPER_ADMIN' : r);
  if (!hasRole(roles as UserRole[], 'SUPER_ADMIN')) {
    return { error: 'Forbidden' }
  }

  // Validate the incoming data
  const validationResult = platformConfigSchema.safeParse(formData)
  if (!validationResult.success) {
    console.error("Validation Errors:", validationResult.error.flatten().fieldErrors);
    return { error: 'Invalid data', details: validationResult.error.flatten().fieldErrors }
  }

  const validatedData = validationResult.data;

  try {
    const { error } = await supabase
      .from('platform_config')
      .update({
        platform_name: validatedData.platform_name,
        default_timezone: validatedData.default_timezone,
        billing_email: validatedData.billing_email || null, // Store empty string as null
        notifications_enabled: validatedData.notifications_enabled,
        slack_integration_enabled: validatedData.slack_integration_enabled,
        webhook_url: validatedData.webhook_url || null, // Store empty string as null
        // updated_at is handled by the trigger
      })
      .eq('id', 1) // Update the singleton row

    if (error) throw error

    revalidatePath('/super-admin/settings') // Revalidate the path to show updated data
    return { data: 'Settings updated successfully' }

  } catch (error: any) {
    console.error('Error updating platform settings:', error)
    return { error: error.message || 'Failed to update settings' }
  }
} 