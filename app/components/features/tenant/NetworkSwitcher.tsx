"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { Badge } from "@/app/components/ui/badge";
import { Building2, ChevronDown, Network, Globe, AlertCircle } from "lucide-react";
import { getSupabaseClient } from "@/lib/supabase";

interface Network {
  id: string;
  name: string;
  slug: string;
  account_type: "platform" | "tnc_managed" | "tnc_account" | "tnc_customer" | "direct_client";
  organization_type: "shared" | "segregated" | "isolated"; // Architecture level
  status: "active" | "inactive" | "suspended";
  description?: string;
  is_default?: boolean;
  domain?: string;
}

interface NetworkSwitcherProps {
  className?: string;
}

export function NetworkSwitcher({ className }: NetworkSwitcherProps) {
  const [networks, setNetworks] = useState<Network[]>([]);
  const [currentNetwork, setCurrentNetwork] = useState<Network | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [switching, setSwitching] = useState(false);
  const supabase = getSupabaseClient(); 

  useEffect(() => {
    fetchNetworks();
  }, []);

  const fetchNetworks = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the new networks API to get available networks
      const response = await fetch("/api/networks", {
        credentials: 'include', // Required for authentication
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: Failed to fetch networks`);
      }

      const data = await response.json();
      console.log('Networks API response:', data);
      
      setNetworks(data.networks || data.availableTenants || []);

      // Set current network from API response
      if (data.currentNetwork) {
        setCurrentNetwork(data.currentNetwork);
      } else if (data.currentTenant) {
        const current = data.networks?.find(
          (n: Network) => n.id === data.currentTenant
        );
        if (current) {
          setCurrentNetwork(current);
        }
      } else {
        // Set default network (TransFlow shared)
        const defaultNetwork = data.networks?.find(
          (n: Network) => n.is_default || n.account_type === "platform"
        ) || data.networks?.[0];
        if (defaultNetwork) {
          setCurrentNetwork(defaultNetwork);
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("Error fetching networks:", error);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const switchNetwork = async (network: Network) => {
    if (network.id === currentNetwork?.id) {
      return; // Already selected
    }

    try {
      setSwitching(true);

      // For now, just update the current network in the UI
      // In a full implementation, this would call a network switching API
      setCurrentNetwork(network);

      // Store the selected network in session storage
      sessionStorage.setItem("currentNetwork", JSON.stringify(network));

      console.log(`Switched to network: ${network.name}`);
      
      // Show success message
      // In a real implementation, you might want to show a toast notification
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("Error switching network:", error);
      setError(errorMessage);
    } finally {
      setSwitching(false);
    }
  };

  const getNetworkIcon = (network: Network) => {
    // Use account_type for network type identification
    switch (network.account_type) {
      case "platform":
        return <Globe className="h-4 w-4 text-blue-600" />;
      case "tnc_managed":
        return <Network className="h-4 w-4 text-green-600" />;
      case "tnc_account":
        return <Network className="h-4 w-4 text-green-600" />;
      case "tnc_customer":
        return <Building2 className="h-4 w-4 text-orange-600" />;
      case "direct_client":
        return <Building2 className="h-4 w-4 text-gray-600" />;
      default:
        return <Network className="h-4 w-4" />;
    }
  };

  const getNetworkTypeLabel = (network: Network) => {
    // Use account_type for network type labels
    switch (network.account_type) {
      case "platform":
        return "Platform Network";
      case "tnc_managed":
        return "TNC Network";
      case "tnc_account":
        return "TNC Network";
      case "tnc_customer":
        return "Customer Portal";
      case "direct_client":
        return "Direct Client";
      default:
        return "Network";
    }
  };

  const getNetworkTypeBadgeVariant = (network: Network) => {
    // Use account_type for badge styling
    switch (network.account_type) {
      case "platform":
        return "default" as const;
      case "tnc_managed":
        return "secondary" as const;
      case "tnc_account":
        return "secondary" as const;
      case "tnc_customer":
        return "outline" as const;
      case "direct_client":
        return "destructive" as const;
      default:
        return "default" as const;
    }
  };

  if (loading) {
    return (
      <Button variant="outline" size="sm" disabled className={className}>
        <Network className="h-4 w-4 mr-2" />
        Loading...
      </Button>
    );
  }

  if (error) {
    return (
      <Button 
        variant="destructive" 
        size="sm" 
        className={className}
        onClick={() => {
          setError(null);
          fetchNetworks();
        }}
        title={`Error: ${error}. Click to retry.`}
      >
        <AlertCircle className="h-4 w-4 mr-2" />
        <span className="hidden sm:inline">Network Error</span>
        <span className="sm:hidden">Error</span>
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={className} disabled={switching}>
          {switching ? (
            <Network className="h-4 w-4 animate-spin" />
          ) : currentNetwork ? (
            getNetworkIcon(currentNetwork)
          ) : (
            <Network className="h-4 w-4" />
          )}
          <span className="ml-2 hidden sm:inline">
            {switching ? "Switching..." : currentNetwork?.name || "Select Network"}
          </span>
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel>Switch Network Context</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {networks.map((network) => (
          <DropdownMenuItem
            key={network.id}
            onClick={() => switchNetwork(network)}
            className="flex items-center justify-between p-3 cursor-pointer"
          >
            <div className="flex items-center space-x-3">
              {getNetworkIcon(network)}
              <div className="flex flex-col">
                <span className="font-medium">{network.name}</span>
                {network.domain && (
                  <span className="text-xs text-muted-foreground">
                    {network.domain}
                  </span>
                )}
              </div>
            </div>
            <div className="flex flex-col items-end space-y-1">
              <Badge
                variant={getNetworkTypeBadgeVariant(network)}
                className="text-xs"
              >
                {getNetworkTypeLabel(network)}
              </Badge>
              {currentNetwork?.id === network.id && (
                <Badge variant="default" className="text-xs">
                  Current
                </Badge>
              )}
            </div>
          </DropdownMenuItem>
        ))}

        {networks.length === 0 && (
          <DropdownMenuItem disabled>No networks available</DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
