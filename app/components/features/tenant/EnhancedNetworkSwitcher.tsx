" use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { ScrollArea } from "@/app/components/ui/scroll-area";
import { Separator } from "@/app/components/ui/separator";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/app/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/app/components/ui/command";
import {
  Building2,
  ChevronDown,
  Network,
  Globe,
  Crown,
  Search,
  Check,
  Loader2,
  Users,
  Calendar,
  Activity,
  Shield,
  Star,
  Zap,
  AlertCircle,
  Settings,
  ExternalLink
} from "lucide-react";
import { useToast } from "@/app/components/ui/use-toast";

// Enhanced interface based on corrected white label architecture
interface OrganizationCapability {
  id: string;
  name: string;
  slug: string;
  domain?: string;

  // Four-tier business architecture
  account_type: "transflow_super_admin" | "tnc_account" | "tnc_customer" | "direct_client";

  // Core architecture fields
  organization_type: "shared" | "segregated" | "isolated";
  subscription_plan: "free_trial" | "professional" | "enterprise";
  permission_template: "basic_client" | "premium_client" | "tnc_enterprise";

  // White label feature flags
  has_white_labeling: boolean;
  has_custom_domain: boolean;
  has_custom_branding: boolean;

  // Computed capabilities
  can_have_white_labeling: boolean;
  can_have_custom_domain: boolean;
  can_have_custom_branding: boolean;
  client_level: "Basic" | "Professional" | "Enterprise" | "Custom";

  // Status and metadata
  status: "active" | "inactive" | "suspended";
  users_count?: number;
  created_at?: string;
  last_active?: string;
  description?: string;

  // Branding data
  branding?: {
    logo_url?: string;
    primary_color?: string;
    secondary_color?: string;
    custom_css?: string;
  };

  // Feature flags
  feature_flags?: {
    advanced_analytics?: boolean;
    api_access?: boolean;
    white_label_portal?: boolean;
    custom_integrations?: boolean;
  };
}

interface EnhancedNetworkSwitcherProps {
  className?: string;
  showSearch?: boolean;
  showDetails?: boolean;
  showCapabilities?: boolean;
  maxHeight?: string;
  variant?: "default" | "compact" | "detailed";
  onTenantChange?: (tenant: OrganizationCapability) => void;
  filterByRole?: boolean;
}

// Helper function to determine client level
function getClientLevel(subscriptionPlan?: string, permissionTemplate?: string): "Basic" | "Professional" | "Enterprise" | "Custom" {
  if (permissionTemplate === "tnc_enterprise") return "Enterprise";
  if (permissionTemplate === "premium_client") return "Professional";
  if (permissionTemplate === "basic_client") return "Basic";

  // Fallback to subscription plan
  if (subscriptionPlan === "enterprise") return "Enterprise";
  if (subscriptionPlan === "professional") return "Professional";
  if (subscriptionPlan === "free_trial") return "Basic";

  return "Custom";
}

export function EnhancedNetworkSwitcher({
  className,
  showSearch = true,
  showDetails = true,
  showCapabilities = true,
  maxHeight = "400px",
  variant = "default",
  onTenantChange,
  filterByRole = true
}: EnhancedNetworkSwitcherProps) {
  const [organizations, setOrganizations] = useState<OrganizationCapability[]>([]);
  const [currentOrganization, setCurrentOrganization] = useState<OrganizationCapability | null>(null);
  const [loading, setLoading] = useState(true);
  const [switching, setSwitching] = useState(false);
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchOrganizations();
  }, []);

  const fetchOrganizations = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // FIXED: Use user organizations API to get all accessible organizations
      const response = await fetch("/api/user/organizations", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include', // Required for authentication
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: Failed to fetch organizations`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || "Failed to fetch organization capabilities");
      }

      console.log('🔍 Raw API response:', data);
      console.log('🔍 Organizations count:', data.data?.length);
      console.log('🔍 First organization:', data.data?.[0]);

      // Transform user organizations to OrganizationCapability format
      const rawOrganizations = data.data || [];
      const organizationCapabilities = rawOrganizations
        .filter((org: any) => org && org.id && org.name) // Filter out invalid organizations
        .map((org: any) => ({
          id: org.id,
          name: org.name || 'Unnamed Organization',
          slug: org.slug || 'unnamed',
          domain: org.domain,
          account_type: org.account_type || "direct_client",
          organization_type: org.organization_type || "shared",
          subscription_plan: org.subscription_plan || "professional",
          permission_template: org.permission_template || "basic_client",
          has_white_labeling: org.has_white_labeling || false,
          has_custom_domain: org.has_custom_domain || false,
          has_custom_branding: org.has_custom_branding || false,
          can_have_white_labeling: true,
          can_have_custom_domain: true,
          can_have_custom_branding: true,
          client_level: getClientLevel(org.subscription_plan, org.permission_template),
          status: org.status || "active",
          users_count: org.users_count,
          created_at: org.created_at,
          last_active: org.updated_at,
          description: org.description,
          branding: org.branding,
          feature_flags: org.feature_flags || {}
        }));

      console.log('🎯 Transformed organizations:', organizationCapabilities);
      setOrganizations(organizationCapabilities);

      // Set current organization from API response or user settings
      if (data.currentOrganization) {
        const current = organizationCapabilities.find(
          (org: OrganizationCapability) => org.id === data.currentOrganization
        );
        if (current) {
          setCurrentOrganization(current);
        }
      } else {
        // Fallback to first available organization
        const defaultOrg = organizationCapabilities[0];
        if (defaultOrg) {
          setCurrentOrganization(defaultOrg);
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("Error fetching organizations:", error);
      setError(errorMessage);

      toast({
        title: "Error Loading Organizations",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const switchOrganization = useCallback(async (organization: OrganizationCapability) => {
    console.log('🔄 Switching to organization:', organization.name, organization.id);

    try {
      setSwitching(true);

      // Call the enhanced tenant switch API
      const response = await fetch("/api/tenant/switch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          organizationId: organization.id,
          organizationType: organization.organization_type,
          subscriptionPlan: organization.subscription_plan,
          permissionTemplate: organization.permission_template,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to switch organization");
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Organization switch failed");
      }

      // Update current organization
      setCurrentOrganization(organization);
      setOpen(false);

      // Store the selected organization with full capabilities
      sessionStorage.setItem("currentOrganization", JSON.stringify(organization));

      // Update user settings for persistent organization selection
      await fetch("/api/user/settings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          setting_name: "app.current_organization_id",
          setting_value: organization.id,
        }),
      }).catch(console.warn); // Non-critical operation

      // Call callback if provided
      onTenantChange?.(organization);

      console.log('✅ Successfully switched to:', organization.name);
      toast({
        title: "Organization Switched",
        description: `Switched to ${organization.name} (${organization.client_level})`,
        duration: 3000,
      });

      // Apply organization-specific branding if available
      if (organization.has_custom_branding && organization.branding) {
        applyCustomBranding(organization.branding);
      }

      // Refresh the page to apply the new organization context
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ Error switching organization:", error);

      toast({
        title: "Switch Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setSwitching(false);
    }
  }, [currentOrganization?.id, onTenantChange, toast]);

  const applyCustomBranding = useCallback((branding: NonNullable<OrganizationCapability['branding']>) => {
    try {
      // Apply custom CSS if available
      if (branding.custom_css) {
        const existingStyle = document.getElementById('organization-custom-styles');
        if (existingStyle) {
          existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = 'organization-custom-styles';
        style.textContent = branding.custom_css;
        document.head.appendChild(style);
      }

      // Apply CSS custom properties for colors
      if (branding.primary_color || branding.secondary_color) {
        const root = document.documentElement;
        if (branding.primary_color) {
          root.style.setProperty('--organization-primary', branding.primary_color);
        }
        if (branding.secondary_color) {
          root.style.setProperty('--organization-secondary', branding.secondary_color);
        }
      }
    } catch (error) {
      console.warn("Failed to apply custom branding:", error);
    }
  }, []);

  // Enhanced filtering with search and role-based filtering
  const filteredOrganizations = useMemo(() => {
    let filtered = organizations;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter((org) =>
        org.name.toLowerCase().includes(query) ||
        org.slug.toLowerCase().includes(query) ||
        org.domain?.toLowerCase().includes(query) ||
        org.client_level.toLowerCase().includes(query) ||
        org.organization_type.toLowerCase().includes(query) ||
        org.subscription_plan.toLowerCase().includes(query)
      );
    }

    // Sort by client level and name for better UX
    return filtered.sort((a, b) => {
      // Sort by client level priority first
      const levelPriority = { "Enterprise": 0, "Professional": 1, "Basic": 2, "Custom": 3 };
      const aPriority = levelPriority[a.client_level] ?? 4;
      const bPriority = levelPriority[b.client_level] ?? 4;

      if (aPriority !== bPriority) {
        return aPriority - bPriority;
      }

      // Then sort by name (with null checks)
      const aName = a.name || '';
      const bName = b.name || '';
      return aName.localeCompare(bName);
    });
  }, [organizations, searchQuery]);

  // Group organizations by client level for better organization
  const groupedOrganizations = useMemo(() => {
    const groups: Record<string, OrganizationCapability[]> = {
      "Enterprise": [],
      "Professional": [],
      "Basic": [],
      "Custom": []
    };

    filteredOrganizations.forEach((org) => {
      if (groups[org.client_level]) {
        groups[org.client_level].push(org);
      }
    });

    return groups;
  }, [filteredOrganizations]);

  // Enhanced icon system based on account type and capabilities
  const getOrganizationIcon = useCallback((org: OrganizationCapability) => {
    // Priority: Custom branding > Account type > Client level
    if (org.has_custom_branding && org.branding?.logo_url) {
      return (
        <img
          src={org.branding.logo_url}
          alt={`${org.name} logo`}
          className="h-4 w-4 rounded object-contain"
          onError={(e) => {
            // Fallback to icon if image fails to load
            e.currentTarget.style.display = 'none';
          }}
        />
      );
    }

    // Use account_type for four-tier business logic
    switch (org.account_type) {
      case "transflow_super_admin":
        return <Crown className="h-4 w-4 text-yellow-600" />;
      case "tnc_account":
        return <Network className="h-4 w-4 text-blue-600" />;
      case "tnc_customer":
        return <Building2 className="h-4 w-4 text-green-600" />;
      case "direct_client":
        return <Globe className="h-4 w-4 text-gray-600" />;
      default:
        // Fallback to client level if account_type not available
        switch (org.client_level) {
          case "Enterprise":
            return <Crown className="h-4 w-4 text-yellow-600" />;
          case "Professional":
            return <Star className="h-4 w-4 text-blue-600" />;
          case "Basic":
            return <Globe className="h-4 w-4 text-gray-600" />;
          default:
            return <Building2 className="h-4 w-4 text-gray-500" />;
        }
    }
  }, []);

  const getClientLevelBadgeVariant = useCallback((clientLevel: string) => {
    switch (clientLevel) {
      case "Enterprise":
        return "default" as const;
      case "Professional":
        return "secondary" as const;
      case "Basic":
        return "outline" as const;
      default:
        return "outline" as const;
    }
  }, []);

  const getSubscriptionPlanIcon = useCallback((plan: string) => {
    switch (plan) {
      case "enterprise":
        return <Crown className="h-3 w-3" />;
      case "professional":
        return <Star className="h-3 w-3" />;
      case "free_trial":
        return <Zap className="h-3 w-3" />;
      default:
        return <Settings className="h-3 w-3" />;
    }
  }, []);

  const getCapabilityBadges = useCallback((org: OrganizationCapability) => {
    const badges = [];

    if (org.has_white_labeling) {
      badges.push({ label: "White Label", variant: "default" as const, icon: <Crown className="h-3 w-3" /> });
    }

    if (org.has_custom_domain) {
      badges.push({ label: "Custom Domain", variant: "secondary" as const, icon: <ExternalLink className="h-3 w-3" /> });
    }

    if (org.feature_flags?.api_access) {
      badges.push({ label: "API Access", variant: "outline" as const, icon: <Settings className="h-3 w-3" /> });
    }

    if (org.feature_flags?.advanced_analytics) {
      badges.push({ label: "Analytics", variant: "outline" as const, icon: <Activity className="h-3 w-3" /> });
    }

    return badges;
  }, []);

  const formatDate = useCallback((dateString?: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  }, []);

  const getCompactDisplay = useCallback(() => {
    if (!currentOrganization) return null;

    return (
      <div className="flex items-center gap-2 min-w-0">
        {getOrganizationIcon(currentOrganization)}
        <span className="font-medium truncate">{currentOrganization.name}</span>
        <Badge
          variant={getClientLevelBadgeVariant(currentOrganization.client_level)}
          className="text-xs hidden sm:inline-flex"
        >
          {currentOrganization.client_level}
        </Badge>
      </div>
    );
  }, [currentOrganization, getOrganizationIcon, getClientLevelBadgeVariant]);

  // Loading state
  if (loading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="animate-pulse bg-gray-200 h-9 w-48 rounded-md"></div>
        {variant === "detailed" && (
          <div className="animate-pulse bg-gray-200 h-6 w-20 rounded-md"></div>
        )}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Error Loading Organizations
        </Badge>
      </div>
    );
  }

  // No organization selected state
  if (!currentOrganization) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          No Organization Selected
        </Badge>
      </div>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={`flex items-center gap-2 min-w-[200px] justify-between ${className}`}
          disabled={switching}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {switching ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              getOrganizationIcon(currentOrganization)
            )}
            {variant === "compact" ? (
              <span className="font-medium truncate">{currentOrganization.name}</span>
            ) : (
              <div className="flex flex-col items-start min-w-0">
                <span className="font-medium truncate">{currentOrganization.name}</span>
                {variant === "detailed" && (
                  <span className="text-xs text-muted-foreground truncate">
                    {currentOrganization.client_level} • {currentOrganization.organization_type}
                  </span>
                )}
              </div>
            )}
            <Badge
              variant={getClientLevelBadgeVariant(currentOrganization.client_level)}
              className="text-xs hidden sm:inline-flex"
            >
              {currentOrganization.client_level}
            </Badge>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50 flex-shrink-0" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[500px] p-0" align="start">
        <style>{`
          [cmdk-item] {
            opacity: 1 !important;
            pointer-events: auto !important;
          }
          [data-disabled="false"] {
            opacity: 1 !important;
            pointer-events: auto !important;
          }
          .cursor-pointer {
            cursor: pointer !important;
          }
        `}</style>
        <Command disabled={false}>
          {showSearch && (
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <CommandInput
                placeholder="Search organizations..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          )}
          <ScrollArea style={{ maxHeight }}>
            <CommandList>
              <CommandEmpty>
                <div className="flex flex-col items-center justify-center py-6 text-center">
                  <Building2 className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">No organizations found</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Try adjusting your search terms
                  </p>
                </div>
              </CommandEmpty>

              {Object.entries(groupedOrganizations).map(([level, levelOrgs]) => {
                if (levelOrgs.length === 0) return null;

                return (
                  <CommandGroup key={level} heading={`${level} Organizations`}>
                    {levelOrgs.map((org) => {
                      console.log('🔍 Rendering organization:', org.name, 'ID:', org.id);
                      return (
                        <CommandItem
                          key={org.id}
                          value={org.id}
                          onSelect={() => {
                            console.log('🖱️ Clicked organization:', org.name);
                            switchOrganization(org);
                          }}
                          disabled={false}
                          className="flex items-start gap-3 p-4 cursor-pointer hover:bg-accent !opacity-100 [&:not([aria-disabled])]:opacity-100 [&[data-disabled='false']]:opacity-100"
                        >
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          {getOrganizationIcon(org)}
                          <div className="flex flex-col min-w-0 flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium truncate">{org.name}</span>
                              {currentOrganization?.id === org.id && (
                                <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                              )}
                            </div>

                            {org.domain && (
                              <span className="text-xs text-muted-foreground truncate mb-1">
                                {org.domain}
                              </span>
                            )}

                            {showDetails && (
                              <div className="flex items-center gap-3 text-xs text-muted-foreground mb-2">
                                <div className="flex items-center gap-1">
                                  {getSubscriptionPlanIcon(org.subscription_plan)}
                                  <span className="capitalize">{org.subscription_plan.replace('_', ' ')}</span>
                                </div>
                                {org.users_count !== undefined && (
                                  <div className="flex items-center gap-1">
                                    <Users className="h-3 w-3" />
                                    <span>{org.users_count} users</span>
                                  </div>
                                )}
                                {org.created_at && (
                                  <div className="flex items-center gap-1">
                                    <Calendar className="h-3 w-3" />
                                    <span>{formatDate(org.created_at)}</span>
                                  </div>
                                )}
                              </div>
                            )}

                            {showCapabilities && (
                              <div className="flex flex-wrap gap-1">
                                {getCapabilityBadges(org).map((badge, index) => (
                                  <Badge
                                    key={index}
                                    variant={badge.variant}
                                    className="text-xs flex items-center gap-1"
                                  >
                                    {badge.icon}
                                    {badge.label}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-col items-end gap-1 flex-shrink-0">
                          <Badge
                            variant={getClientLevelBadgeVariant(org.client_level)}
                            className="text-xs"
                          >
                            {org.client_level}
                          </Badge>
                          <Badge
                            variant={org.status === 'active' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {org.status}
                          </Badge>
                          {org.has_white_labeling && (
                            <Badge variant="outline" className="text-xs">
                              <Crown className="h-3 w-3 mr-1" />
                              White Label
                            </Badge>
                          )}
                        </div>
                      </CommandItem>
                      );
                    })}
                    {level !== "Custom" && <Separator className="my-2" />}
                  </CommandGroup>
                );
              })}
            </CommandList>
          </ScrollArea>
        </Command>
      </PopoverContent>
    </Popover>
  );
}