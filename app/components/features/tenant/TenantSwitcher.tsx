'use client'

import { useState } from 'react'
import { useOrganization } from '@/app/contexts/OrganizationContext'
import { Button } from '@/app/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/app/components/ui/dropdown-menu'
import { Badge } from '@/app/components/ui/badge'
import { ChevronDown, Building2, Globe, Crown, Network } from 'lucide-react'

interface TenantSwitcherProps {
  className?: string
}

export function TenantSwitcher({ className }: TenantSwitcherProps) {
  const { currentOrganization, organizations, switchOrganization, loading } = useOrganization()
  const [isOpen, setIsOpen] = useState(false)
  const [switching, setSwitching] = useState(false)

  const getTenantIcon = (tenantType: string) => {
    switch (tenantType) {
      case 'shared':
        return <Globe className="h-4 w-4" />
      case 'segregated':
        return <Building2 className="h-4 w-4" />
      case 'white_label':
        return <Crown className="h-4 w-4" />
      case 'isolated':
        return <Network className="h-4 w-4" />
      default:
        return <Building2 className="h-4 w-4" />
    }
  }

  const getTenantTypeLabel = (tenantType: string) => {
    switch (tenantType) {
      case 'shared':
        return 'Shared SaaS'
      case 'segregated':
        return 'TNC Network'
      case 'white_label':
        return 'White Label'
      case 'isolated':
        return 'Enterprise'
      default:
        return 'Unknown'
    }
  }

  const getTenantTypeBadgeVariant = (tenantType: string) => {
    switch (tenantType) {
      case 'shared':
        return 'default' as const
      case 'segregated':
        return 'secondary' as const
      case 'white_label':
        return 'outline' as const
      case 'isolated':
        return 'destructive' as const
      default:
        return 'outline' as const
    }
  }

  const handleSwitchOrganization = async (organizationId: string) => {
    if (organizationId === currentOrganization?.id) {
      setIsOpen(false)
      return
    }

    try {
      setSwitching(true)
      await switchOrganization(organizationId)
      setIsOpen(false)
    } catch (error) {
      console.error('Error switching organization:', error)
    } finally {
      setSwitching(false)
    }
  }

  if (loading) {
    return (
      <Button variant="outline" size="sm" disabled className={className}>
        <Network className="h-4 w-4 mr-2" />
        Loading...
      </Button>
    )
  }

  if (!currentOrganization) {
    return (
      <Button variant="outline" size="sm" disabled className={className}>
        <Network className="h-4 w-4 mr-2" />
        No Organization
      </Button>
    )
  }

  // Don't show switcher if user only has access to one organization
  if (organizations.length <= 1) {
    return (
      <Button variant="outline" size="sm" disabled className={className}>
        {getTenantIcon(currentOrganization.organization_type)}
        <span className="ml-2 hidden sm:inline">{currentOrganization.name}</span>
      </Button>
    )
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`flex items-center gap-2 ${className}`}
          disabled={switching}
        >
          {switching ? (
            <Network className="h-4 w-4 animate-spin" />
          ) : (
            getTenantIcon(currentOrganization.organization_type)
          )}
          <span className="font-medium hidden sm:inline">
            {switching ? 'Switching...' : currentOrganization.name}
          </span>
          <Badge 
            variant={getTenantTypeBadgeVariant(currentOrganization.organization_type)}
            className="text-xs hidden md:inline-flex"
          >
            {getTenantTypeLabel(currentOrganization.organization_type)}
          </Badge>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel>Switch Organization ({organizations.length} available)</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {organizations.map((organization) => (
          <DropdownMenuItem
            key={organization.id}
            onClick={() => handleSwitchOrganization(organization.id)}
            className="flex items-center gap-3 p-3 cursor-pointer"
          >
            <div className="flex items-center gap-2 flex-1">
              {getTenantIcon(organization.organization_type)}
              <div className="flex flex-col">
                <span className="font-medium">{organization.name}</span>
                <span className="text-xs text-muted-foreground">{organization.slug}</span>
              </div>
            </div>
            <div className="flex flex-col items-end gap-1">
              <Badge 
                variant={getTenantTypeBadgeVariant(organization.organization_type)}
                className="text-xs"
              >
                {getTenantTypeLabel(organization.organization_type)}
              </Badge>
              {currentOrganization.id === organization.id && (
                <Badge variant="default" className="text-xs">
                  Current
                </Badge>
              )}
            </div>
          </DropdownMenuItem>
        ))}
        
        {organizations.length === 0 && (
          <DropdownMenuItem disabled>
            No organizations available
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
