"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { Badge } from "@/app/components/ui/badge";
import { Building2, ChevronDown, Network, Globe, Crown } from "lucide-react";

interface Tenant {
  id: string;
  name: string;
  slug: string;
  account_type: "transflow_super_admin" | "tnc_account" | "tnc_customer" | "direct_client";
  organization_type: "shared" | "segregated" | "isolated"; // Architecture level
  status: "active" | "inactive" | "suspended";
  domain?: string;
}

interface UnifiedTenantSwitcherProps {
  className?: string;
}

export function UnifiedTenantSwitcher({
  className,
}: UnifiedTenantSwitcherProps) {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [currentTenant, setCurrentTenant] = useState<Tenant | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTenants();
  }, []);

  const fetchTenants = async () => {
    try {
      setLoading(true);

      // Use the tenant API to get available networks
      const response = await fetch("/api/tenant/switch");
      if (!response.ok) {
        throw new Error("Failed to fetch tenants");
      }

      const data = await response.json();
      setTenants(data.availableTenants || []);

      // Set current tenant from API response or default to shared
      if (data.currentTenant) {
        const current = data.availableTenants?.find(
          (t: Tenant) => t.id === data.currentTenant
        );
        if (current) {
          setCurrentTenant(current);
        }
      } else {
        // Set default tenant (TransFlow shared)
        const defaultTenant =
          data.availableTenants?.find(
            (t: Tenant) => t.organization_type === "shared"
          ) || data.availableTenants?.[0];
        if (defaultTenant) {
          setCurrentTenant(defaultTenant);
        }
      }
    } catch (error) {
      console.error("Error fetching tenants:", error);
    } finally {
      setLoading(false);
    }
  };

  const switchTenant = async (tenant: Tenant) => {
    try {
      // Call the tenant switch API
      const response = await fetch("/api/tenant/switch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          organizationId: tenant.id,
          organizationId: null, // Network switching doesn't specify organization
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to switch tenant");
      }

      setCurrentTenant(tenant);

      // Store the selected tenant in session storage
      sessionStorage.setItem("currentTenant", JSON.stringify(tenant));

      // Refresh the page to apply the new tenant context
      window.location.reload();
    } catch (error) {
      console.error("Error switching tenant:", error);
    }
  };

  const getTenantIcon = (tenant: Tenant) => {
    // Use account_type for four-tier business logic
    switch (tenant.account_type) {
      case "transflow_super_admin":
        return <Crown className="h-4 w-4 text-yellow-600" />;
      case "tnc_account":
        return <Network className="h-4 w-4 text-blue-600" />;
      case "tnc_customer":
        return <Building2 className="h-4 w-4 text-green-600" />;
      case "direct_client":
        return <Globe className="h-4 w-4 text-gray-600" />;
      default:
        return <Building2 className="h-4 w-4" />;
    }
  };

  const getTenantTypeLabel = (tenant: Tenant) => {
    // Use account_type for four-tier business logic
    switch (tenant.account_type) {
      case "transflow_super_admin":
        return "Platform Admin";
      case "tnc_account":
        return "TNC Network";
      case "tnc_customer":
        return "TNC Customer";
      case "direct_client":
        return "Direct Client";
      default:
        return "Unknown";
    }
  };

  const getTenantTypeBadgeVariant = (tenant: Tenant) => {
    // Use account_type for four-tier business logic
    switch (tenant.account_type) {
      case "transflow_super_admin":
        return "default" as const;
      case "tnc_account":
        return "secondary" as const;
      case "tnc_customer":
        return "outline" as const;
      case "direct_client":
        return "destructive" as const;
      default:
        return "default" as const;
    }
  };

  if (loading) {
    return (
      <Button variant="outline" size="sm" disabled className={className}>
        <Network className="h-4 w-4 mr-2" />
        Loading...
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          {currentTenant ? (
            getTenantIcon(currentTenant)
          ) : (
            <Network className="h-4 w-4" />
          )}
          <span className="ml-2 hidden sm:inline">
            {currentTenant?.name || "Select Network"}
          </span>
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel>Switch Network Context</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {tenants.map((tenant) => (
          <DropdownMenuItem
            key={tenant.id}
            onClick={() => switchTenant(tenant)}
            className="flex items-center justify-between p-3 cursor-pointer"
          >
            <div className="flex items-center space-x-3">
              {getTenantIcon(tenant)}
              <div className="flex flex-col">
                <span className="font-medium">{tenant.name}</span>
                {tenant.domain && (
                  <span className="text-xs text-muted-foreground">
                    {tenant.domain}
                  </span>
                )}
              </div>
            </div>
            <div className="flex flex-col items-end space-y-1">
              <Badge
                variant={getTenantTypeBadgeVariant(tenant)}
                className="text-xs"
              >
                {getTenantTypeLabel(tenant)}
              </Badge>
              {currentTenant?.id === tenant.id && (
                <Badge variant="default" className="text-xs">
                  Current
                </Badge>
              )}
            </div>
          </DropdownMenuItem>
        ))}

        {tenants.length === 0 && (
          <DropdownMenuItem disabled>No networks available</DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
