"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Switch } from "@/app/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs";
import { Alert, AlertDescription } from "@/app/components/ui/alert";
import { Loader2, Shield, Users, BarChart3, Database, Palette, Plug, FileText, Calendar } from "lucide-react";
import { toast } from "@/app/components/ui/use-toast";

interface OrganizationPermission {
  id: string;
  organization_id: string;
  permission_category: string;
  permission_key: string;
  enabled: boolean;
  configuration: Record<string, any>;
}

interface PermissionTemplate {
  id: string;
  template_name: string;
  description: string;
  permissions: any[];
}

interface OrganizationPermissionsTabProps {
  organizationId: string;
}

const CATEGORY_ICONS = {
  quotes: FileText,
  events: Calendar,
  users: Users,
  analytics: BarChart3,
  data: Database,
  branding: Palette,
  integrations: Plug,
};

const CATEGORY_COLORS = {
  quotes: "bg-blue-100 text-blue-800",
  events: "bg-green-100 text-green-800",
  users: "bg-purple-100 text-purple-800",
  analytics: "bg-orange-100 text-orange-800",
  data: "bg-gray-100 text-gray-800",
  branding: "bg-pink-100 text-pink-800",
  integrations: "bg-indigo-100 text-indigo-800",
};

export function OrganizationPermissionsTab({ organizationId }: OrganizationPermissionsTabProps) {
  const [permissions, setPermissions] = useState<OrganizationPermission[]>([]);
  const [permissionsByCategory, setPermissionsByCategory] = useState<Record<string, OrganizationPermission[]>>({});
  const [templates, setTemplates] = useState<PermissionTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>("");

  useEffect(() => {
    fetchPermissions();
  }, [organizationId]);

  const fetchPermissions = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/super-admin/organizations/${organizationId}/permissions`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch permissions');
      }

      const data = await response.json();
      setPermissions(data.permissions || []);
      setPermissionsByCategory(data.permissions_by_category || {});
      setTemplates(data.available_templates || []);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      toast({
        title: "Error",
        description: "Failed to load organization permissions",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updatePermission = async (category: string, key: string, enabled: boolean, configuration?: Record<string, any>) => {
    try {
      setSaving(true);
      const response = await fetch(`/api/super-admin/organizations/${organizationId}/permissions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          permission_category: category,
          permission_key: key,
          enabled,
          configuration: configuration || {}
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update permission');
      }

      await fetchPermissions(); // Refresh permissions
      toast({
        title: "Success",
        description: `Permission ${enabled ? 'enabled' : 'disabled'} successfully`,
      });
    } catch (error) {
      console.error('Error updating permission:', error);
      toast({
        title: "Error",
        description: "Failed to update permission",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const applyTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      setSaving(true);
      const response = await fetch(`/api/super-admin/organizations/${organizationId}/permissions`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ template_name: selectedTemplate })
      });

      if (!response.ok) {
        throw new Error('Failed to apply template');
      }

      await fetchPermissions(); // Refresh permissions
      setSelectedTemplate("");
      toast({
        title: "Success",
        description: "Permission template applied successfully",
      });
    } catch (error) {
      console.error('Error applying template:', error);
      toast({
        title: "Error",
        description: "Failed to apply permission template",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const getCategoryDisplayName = (category: string) => {
    const names: Record<string, string> = {
      quotes: "Quote Management",
      events: "Event Management", 
      users: "User Management",
      analytics: "Analytics & Reporting",
      data: "Data Management",
      branding: "Branding & Customization",
      integrations: "Integrations & API"
    };
    return names[category] || category.charAt(0).toUpperCase() + category.slice(1);
  };

  const getPermissionDisplayName = (key: string) => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading permissions...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Organization Permissions</h3>
          <p className="text-sm text-muted-foreground">
            Set baseline capabilities for this organization. Individual users can be granted more specific permissions within these limits.
          </p>
        </div>
        <Badge variant="secondary" className="flex items-center gap-1">
          <Shield className="h-3 w-3" />
          Org-Level Controls
        </Badge>
      </div>

      {/* Permission Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Quick Setup</CardTitle>
          <CardDescription>
            Apply a permission template to quickly configure common permission sets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="Select a template" />
              </SelectTrigger>
              <SelectContent>
                {templates.map((template) => (
                  <SelectItem key={template.id} value={template.template_name}>
                    <div>
                      <div className="font-medium">{template.template_name.replace(/_/g, ' ').toUpperCase()}</div>
                      <div className="text-xs text-muted-foreground">{template.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              onClick={applyTemplate} 
              disabled={!selectedTemplate || saving}
              className="flex items-center gap-2"
            >
              {saving && <Loader2 className="h-4 w-4 animate-spin" />}
              Apply Template
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Permissions by Category */}
      <div className="space-y-4">
        {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => {
          const IconComponent = CATEGORY_ICONS[category as keyof typeof CATEGORY_ICONS] || Shield;
          const colorClass = CATEGORY_COLORS[category as keyof typeof CATEGORY_COLORS] || "bg-gray-100 text-gray-800";

          return (
            <Card key={category}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <IconComponent className="h-4 w-4" />
                  {getCategoryDisplayName(category)}
                  <Badge variant="secondary" className={colorClass}>
                    {categoryPermissions.length} permissions
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {categoryPermissions.map((permission) => (
                    <div key={permission.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{getPermissionDisplayName(permission.permission_key)}</div>
                        {permission.configuration && Object.keys(permission.configuration).length > 0 && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Config: {JSON.stringify(permission.configuration)}
                          </div>
                        )}
                      </div>
                      <Switch
                        checked={permission.enabled}
                        onCheckedChange={(enabled) => 
                          updatePermission(permission.permission_category, permission.permission_key, enabled, permission.configuration)
                        }
                        disabled={saving}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Info Alert */}
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <strong>Two-Tier Permission System:</strong> Organization permissions set the baseline capabilities. 
          Individual users can then be granted specific permissions within these organizational limits via the user permissions page.
        </AlertDescription>
      </Alert>
    </div>
  );
}