import { createServerClient, type CookieOptions } from '@supabase/ssr'

export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import { hasRole } from '@/app/lib/auth'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
// Get all quotes for an event
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options)
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options)
        },
      },
    }
  )

  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError

    // Check if the user has access to this event
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('customer_id')
      .eq('id', params.id)
      .single()

    if (eventError) throw eventError

    if (event.customer_id !== user?.id) {
      // Check if user has admin role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('roles')
        .eq('id', user?.id)
        .single()
      
      if (profileError) throw profileError

      const isAdmin = hasRole(profile.roles, 'SUPER_ADMIN')
      if (!isAdmin) {
        return NextResponse.json(
          { error: 'Unauthorized access to event quotes' },
          { status: 403 }
        )
      }
    }

    // Fetch quotes for the event
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select('*')
      .eq('event_id', params.id)
      .order('created_at', { ascending: false })

    if (quotesError) throw quotesError

    return NextResponse.json(quotes)
  } catch (error: any) {
    console.error('[Server] Error fetching quotes:', error.message)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}

// Create a new quote for an event
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options)
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options)
        },
      },
    }
  )

  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError

    // Check if the user has access to this event
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('customer_id')
      .eq('id', params.id)
      .single()

    if (eventError) throw eventError

    if (event.customer_id !== user?.id) {
      // Check if user has admin role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('roles')
        .eq('id', user?.id)
        .single()
      
      if (profileError) throw profileError

      const isAdmin = hasRole(profile.roles, 'SUPER_ADMIN')
      if (!isAdmin) {
        return NextResponse.json(
          { error: 'Unauthorized access to create quotes for this event' },
          { status: 403 }
        )
      }
    }

    // Parse the request body
    const body = await request.json()
    const {
      title,
      description,
      passengerCount,
      specialRequirements,
      pickupLocation,
      dropoffLocation,
      pickupDate,
      pickupTime,
      vehicleType,
      status = 'pending',
    } = body

    // Validate required fields
    if (!title || !pickupLocation || !dropoffLocation || !pickupDate || !pickupTime || !vehicleType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Create the quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .insert({
        event_id: params.id,
        title,
        description,
        passenger_count: passengerCount,
        special_requirements: specialRequirements,
        pickup_location: pickupLocation,
        dropoff_location: dropoffLocation,
        pickup_date: pickupDate,
        pickup_time: pickupTime,
        vehicle_type: vehicleType,
        status,
        customer_id: user?.id,
      })
      .select()
      .single()

    if (quoteError) throw quoteError

    return NextResponse.json(quote)
  } catch (error: any) {
    console.error('[Server] Error creating quote:', error.message)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}