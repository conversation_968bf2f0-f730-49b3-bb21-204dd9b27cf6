import { NextResponse } from "next/server"
import { query } from "@/lib/db"

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function PATCH(
  request: Request,
  { params }: { params: { id: string; quoteId: string } }
) {
  try {
    const body = await request.json()
    const { status } = body

    const { data, error } = await query(
      `UPDATE event_quotes 
       SET status = $1 
       WHERE id = $2 AND event_id = $3
       RETURNING *`,
      [status, params.quoteId, params.id]
    )

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data[0])
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}