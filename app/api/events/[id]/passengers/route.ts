import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const eventId = params.id

    // Get passengers linked to this event
    const { data: eventPassengers, error } = await supabase
      .from('event_passengers')
      .select(`
        id,
        passengers (
          id,
          first_name,
          last_name,
          email,
          phone_number,
          passenger_type,
          company,
          dietary_restrictions,
          special_requirements
        )
      `)
      .eq('event_id', eventId)

    if (error) {
      console.error('Error fetching event passengers:', error)
      return NextResponse.json({ error: 'Failed to fetch passengers' }, { status: 500 })
    }

    const passengers = eventPassengers?.map(ep => ep.passengers) || []

    return NextResponse.json({ passengers })
  } catch (error) {
    console.error('Event passengers API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const eventId = params.id
    const body = await request.json()
    const { passengerIds } = body

    if (!passengerIds || !Array.isArray(passengerIds) || passengerIds.length === 0) {
      return NextResponse.json({ error: 'Invalid passenger IDs' }, { status: 400 })
    }

    // Verify event exists and user has access
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('id, name')
      .eq('id', eventId)
      .eq('created_by', user.id)
      .single()

    if (eventError || !event) {
      return NextResponse.json({ error: 'Event not found or access denied' }, { status: 404 })
    }

    // Create event-passenger links
    const eventPassengerLinks = passengerIds.map((passengerId: string) => ({
      event_id: eventId,
      passenger_id: passengerId
    }))

    const { data: links, error: linkError } = await supabase
      .from('event_passengers')
      .insert(eventPassengerLinks)
      .select(`
        id,
        passengers (
          id,
          first_name,
          last_name,
          email,
          passenger_type
        )
      `)

    if (linkError) {
      console.error('Error linking passengers to event:', linkError)
      return NextResponse.json({ error: 'Failed to link passengers' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      linked_count: passengerIds.length,
      passengers: links?.map(link => link.passengers) || []
    })
  } catch (error) {
    console.error('Link passengers API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
