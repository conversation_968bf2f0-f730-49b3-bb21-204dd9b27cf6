import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; passengerId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const eventId = params.id
    const passengerId = params.passengerId

    // Verify event exists and user has access
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('id, name')
      .eq('id', eventId)
      .eq('created_by', user.id)
      .single()

    if (eventError || !event) {
      return NextResponse.json({ error: 'Event not found or access denied' }, { status: 404 })
    }

    // Remove the event-passenger link
    const { error: unlinkError } = await supabase
      .from('event_passengers')
      .delete()
      .eq('event_id', eventId)
      .eq('passenger_id', passengerId)

    if (unlinkError) {
      console.error('Error unlinking passenger from event:', unlinkError)
      return NextResponse.json({ error: 'Failed to unlink passenger' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Passenger unlinked from event successfully' 
    })
  } catch (error) {
    console.error('Unlink passenger API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
