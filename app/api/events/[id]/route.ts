import { createServerClient, type CookieOptions } from '@supabase/ssr'

export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import { hasRole } from '@/app/lib/auth'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
// Get a single event by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            cookieStore.set(name, value, options)
          },
          remove(name: string, options: CookieOptions) {
            cookieStore.set(name, '', options)
          },
        },
      }
    )
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError

    // Fetch the event
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('*')
      .eq('id', params.id)
      .single()

    if (eventError) throw eventError

    // Check if the user has access to this event
    if (event.customer_id !== user?.id) {
      // Check if user has admin role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('roles')
        .eq('id', user?.id)
        .single()
      
      if (profileError) throw profileError

      const isAdmin = hasRole(profile.roles, 'SUPER_ADMIN')
      if (!isAdmin) {
        return NextResponse.json(
          { error: 'Unauthorized access to event' },
          { status: 403 }
        )
      }
    }

    return NextResponse.json(event)
  } catch (error: any) {
    console.error('[Server] Error fetching event:', error.message)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}

// Update an event
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            cookieStore.set(name, value, options)
          },
          remove(name: string, options: CookieOptions) {
            cookieStore.set(name, '', options)
          },
        },
      }
    )
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError

    // Parse the request body
    const body = await request.json()
    const {
      name,
      description,
      start_date,
      end_date,
      location,
      total_passengers,
      status,
      // Add any other fields needed
    } = body

    // Validate required fields
    if (!name || !start_date || !end_date || !location) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check if the user has access to this event
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('customer_id')
      .eq('id', params.id)
      .single()

    if (eventError) throw eventError

    if (event.customer_id !== user?.id) {
      // Check if user has admin role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('roles')
        .eq('id', user?.id)
        .single()
      
      if (profileError) throw profileError

      const isAdmin = hasRole(profile.roles, 'SUPER_ADMIN')
      if (!isAdmin) {
        return NextResponse.json(
          { error: 'Unauthorized access to event' },
          { status: 403 }
        )
      }
    }

    // Update the event
    const { data: updatedEvent, error: updateError } = await supabase
      .from('events')
      .update({
        name,
        description,
        start_date,
        end_date,
        location,
        total_passengers,
        status,
        updated_at: new Date().toISOString(),
        // Add any other fields needed
      })
      .eq('id', params.id)
      .select()
      .single()

    if (updateError) throw updateError

    return NextResponse.json(updatedEvent)
  } catch (error: any) {
    console.error('[Server] Error updating event:', error.message)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}

// Delete an event
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            cookieStore.set(name, value, options)
          },
          remove(name: string, options: CookieOptions) {
            cookieStore.set(name, '', options)
          },
        },
      }
    )
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError

    // Check if the user has access to this event
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('customer_id')
      .eq('id', params.id)
      .single()

    if (eventError) throw eventError

    if (event.customer_id !== user?.id) {
      // Check if user has admin role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('roles')
        .eq('id', user?.id)
        .single()
      
      if (profileError) throw profileError

      const isAdmin = hasRole(profile.roles, 'SUPER_ADMIN')
      if (!isAdmin) {
        return NextResponse.json(
          { error: 'Unauthorized access to event' },
          { status: 403 }
        )
      }
    }

    // Delete related quotes first (cascade delete)
    const { error: quotesError } = await supabase
      .from('quotes')
      .delete()
      .eq('event_id', params.id)

    if (quotesError) throw quotesError

    // Delete the event
    const { error: deleteError } = await supabase
      .from('events')
      .delete()
      .eq('id', params.id)

    if (deleteError) throw deleteError

    return NextResponse.json({ success: true })
  } catch (error: any) {
    console.error('[Server] Error deleting event:', error.message)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
} 