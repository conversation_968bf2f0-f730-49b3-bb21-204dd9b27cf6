import { NextResponse } from "next/server"
import { query } from "@/lib/db"

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { data: requirements, error } = await query(
      'SELECT * FROM event_requirements WHERE event_id = $1',
      [params.id]
    )

    if (error) {
      return NextResponse.json({ error }, { status: 500 })
    }

    return NextResponse.json({ requirements })
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { vehicle_requirements, special_requirements } = body

    const { data, error } = await query(
      'INSERT INTO event_requirements (event_id, vehicle_requirements, special_requirements) VALUES ($1, $2, $3) RETURNING *',
      [params.id, vehicle_requirements, special_requirements]
    )

    if (error) {
      return NextResponse.json({ error }, { status: 500 })
    }

    return NextResponse.json(data[0])
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}