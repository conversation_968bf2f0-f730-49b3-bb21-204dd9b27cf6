import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithPermissions } from '@/lib/auth/api-authentication'
import { createSuccessResponse, ErrorResponses } from '@/app/lib/utils/api-responses'
import { z } from 'zod'
// Input validation schemas
const createEventSchema = z.object({
  name: z.string().min(1, 'Event name is required'),
  description: z.string().optional(),
  start_date: z.string().min(1, 'Start date is required'),
  end_date: z.string().min(1, 'End date is required'),
  location: z.string().min(1, 'Location is required'),
  total_passengers: z.number().min(1).max(500).default(1),
  status: z.enum(['draft', 'active', 'completed', 'cancelled']).default('draft')
});

export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/events - Starting request');

    // Authenticate user with proper role and permission validation
    const authResult = await authenticateWithPermissions(request, {
      allowedRoles: ['CLIENT', 'CLIENT_COORDINATOR', 'SUPER_ADMIN'],
      requiredPermissions: ['events.create'], // Users need at least event creation permission to view events
      requireOrganization: true
    });

    if (!authResult.success) {
      return ErrorResponses.unauthorized(authResult.error || 'Authentication failed');
    }

    const { user, organization } = authResult;
    console.log('GET /api/events - User authenticated:', user!.id);

    // Create service role client for database operations
    const supabase = createClient();

    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 100);

    // Build organization-scoped query
    let query = supabase
      .from('events')
      .select(`
        *,
        customer:profiles!events_customer_id_fkey(
          id, email, full_name
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Apply organization-based filtering for multi-tenant isolation
    if (!user!.is_super_admin) {
      if (organization) {
        query = query.eq('organization_id', organization.id);
      }
    }

    // Add status filter if provided
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    const { data: events, error } = await query;

    if (error) {
      console.error('GET /api/events - Error fetching events:', error);
      return ErrorResponses.internalError('Failed to fetch events');
    }

    console.log('GET /api/events - Found', events?.length || 0, 'events');

    return createSuccessResponse(events, 'Events retrieved successfully');
  } catch (error: any) {
    console.error('GET /api/events - Unhandled error:', error);
    return ErrorResponses.internalError('Internal server error');
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('POST /api/events - Starting event creation request');

    // Authenticate user with proper role and permission validation
    const authResult = await authenticateWithPermissions(request, {
      allowedRoles: ['CLIENT', 'CLIENT_COORDINATOR'],
      requiredPermissions: ['events.create'],
      requiredSubscription: 'free_trial', // Minimum subscription required
      requireOrganization: true
    });

    if (!authResult.success) {
      return ErrorResponses.unauthorized(authResult.error || 'Authentication failed');
    }

    const { user, organization } = authResult;
    console.log('POST /api/events - User authenticated:', user!.id);

    // Create service role client for database operations
    const supabase = createClient();

    // Get and validate the request body
    const body = await request.json();
    
    // Validate input with Zod schema
    const validatedInput = createEventSchema.parse(body);

    // Business logic validation
    const startDate = new Date(validatedInput.start_date);
    const endDate = new Date(validatedInput.end_date);
    
    if (startDate <= new Date()) {
      return ErrorResponses.badRequest('Event start date must be in the future');
    }
    
    if (endDate <= startDate) {
      return ErrorResponses.badRequest('Event end date must be after start date');
    }

    // Check subscription limits
    if (!user!.is_super_admin) {
      const limits = await authResult.checkSubscriptionAccess!('free_trial');
      if (!limits.allowed) {
        return ErrorResponses.subscriptionRequired('free_trial');
      }
    }

    // Create the event with organization-based isolation
    const eventData = {
      name: validatedInput.name,
      description: validatedInput.description || null,
      customer_id: user!.id,
      organization_id: organization?.id || user!.organization_id,
      start_date: validatedInput.start_date,
      end_date: validatedInput.end_date,
      location: validatedInput.location,
      total_passengers: validatedInput.total_passengers,
      status: validatedInput.status,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: event, error: eventError } = await supabase
      .from('events')
      .insert([eventData])
      .select()
      .single();

    if (eventError) {
      console.error('POST /api/events - Error creating event:', eventError);
      return ErrorResponses.internalError('Failed to create event');
    }

    console.log('POST /api/events - Event created successfully:', event.id);

    return createSuccessResponse(event, 'Event created successfully', 201);
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return ErrorResponses.badRequest(`Validation error: ${error.errors.map((e: any) => e.message).join(', ')}`);
    }
    
    console.error('POST /api/events - Unhandled error:', error);
    return ErrorResponses.internalError('Internal server error');
  }
}
