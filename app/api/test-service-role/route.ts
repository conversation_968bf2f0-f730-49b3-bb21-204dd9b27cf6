import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    console.log('Testing service role key...')
    console.log('Service role key available:', !!process.env.SUPABASE_SERVICE_ROLE_KEY)
    console.log('Service role key length:', process.env.SUPABASE_SERVICE_ROLE_KEY?.length || 0)

    // Create service role client
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookies().get(name)?.value
          },
        },
      }
    )

    // Test basic query
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select('id, reference_number')
      .limit(1)

    console.log('Quotes query result:', { quotes, quotesError })

    // Test quote_affiliate_offers query
    const { data: offers, error: offersError } = await supabase
      .from('quote_affiliate_offers')
      .select('id, quote_id, company_id')
      .limit(1)

    console.log('Offers query result:', { offers, offersError })

    return NextResponse.json({
      success: true,
      serviceRoleKeyAvailable: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      serviceRoleKeyLength: process.env.SUPABASE_SERVICE_ROLE_KEY?.length || 0,
      quotesQuery: { data: quotes, error: quotesError },
      offersQuery: { data: offers, error: offersError }
    })

  } catch (error) {
    console.error('Error in service role test:', error)
    return NextResponse.json(
      { error: 'Service role test failed', details: error },
      { status: 500 }
    )
  }
}
