import { NextRequest } from "next/server"
import { authenticateWithPermissions } from '@/lib/auth/api-authentication'
import { createSuccessResponse, ErrorResponses } from '@/app/lib/utils/api-responses'
import { createClient } from '@supabase/supabase-js'
import { z } from 'zod'

export const runtime = 'nodejs'

// Input validation schema
const querySchema = z.object({
  userId: z.string().uuid().optional()
});

export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/profiles - Starting request');

    // Authenticate user - all authenticated users can view their own profile
    const authResult = await authenticateWithPermissions(request, {
      allowedRoles: ['SUPER_ADMIN', 'TNC_ADMIN', 'CLIENT', 'CLIENT_COORDINATOR', 'AFFILIATE', 'DISPATCHER', 'DRIVER', 'PASSENGER'],
      requireOrganization: false // Profile access doesn't require organization
    });

    if (!authResult.success) {
      return ErrorResponses.unauthorized(authResult.error || 'Authentication failed');
    }

    const { user } = authResult;
    console.log('GET /api/profiles - User authenticated:', user!.id, 'role:', user!.role);

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = querySchema.parse({
      userId: url.searchParams.get('userId')
    });

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const userId = queryParams.userId;

    // Determine which profile to fetch
    let targetUserId = user!.id; // Default to current user

    // SUPER_ADMIN can view any profile, others can only view their own
    if (userId && userId !== user!.id) {
      if (!user!.is_super_admin) {
        return ErrorResponses.forbidden('You can only view your own profile');
      }
      targetUserId = userId;
      console.log('GET /api/profiles - SUPER_ADMIN accessing profile:', targetUserId);
    }

    // Get the user's profile with organization data
    const { data: profile, error } = await supabase
      .from('profiles')
      .select(`
        *,
        organization:organizations(
          id, name, slug, organization_type, subscription_plan, permission_template
        )
      `)
      .eq('id', targetUserId)
      .maybeSingle();

    if (error) {
      console.error('GET /api/profiles - Error fetching profile:', error);
      return ErrorResponses.internalError('Failed to fetch profile');
    }

    if (!profile) {
      console.warn('GET /api/profiles - No profile found for user:', targetUserId);
      return ErrorResponses.notFound('Profile not found');
    }

    // Remove sensitive data for non-super-admin users
    if (!user!.is_super_admin && targetUserId !== user!.id) {
      delete profile.role;
      delete profile.organization_id;
    }

    console.log('GET /api/profiles - Profile retrieved successfully for user:', targetUserId);

    return createSuccessResponse({ profile }, 'Profile retrieved successfully');
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return ErrorResponses.badRequest(`Validation error: ${error.errors.map((e: any) => e.message).join(', ')}`);
    }

    console.error('GET /api/profiles - Unhandled error:', error);
    return ErrorResponses.internalError('Internal server error');
  }
}
