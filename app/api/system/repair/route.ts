// System Repair API Endpoint
// Automated repair for common architectural issues

import { NextRequest, NextResponse } from "next/server";
import { createAuthenticatedSupabaseClient } from "@/lib/auth/server";
import { createServiceRoleClient } from "@/app/lib/auth/standardized";

export async function POST(request: NextRequest) {
  try {
    // Use service role for system repairs to bypass RLS
    const supabase = createServiceRoleClient();
    const { repairType } = await request.json();

    const results: any = {
      timestamp: new Date().toISOString(),
      repairType,
      success: false,
      details: {}
    };

    switch (repairType) {
      case 'orphaned_users':
        // Repair orphaned users using the database function
        const { data: repairResult, error: repairError } = await supabase
          .rpc('repair_orphaned_users');

        if (repairError) {
          results.error = repairError.message;
          results.details = repairError;
        } else {
          results.success = true;
          results.details = repairResult;
        }
        break;

      case 'default_organization':
        // Create default organization if missing
        const { data: existingOrg, error: checkError } = await supabase
          .from('organizations')
          .select('id')
          .eq('slug', 'transflow-shared')
          .single();

        if (checkError && checkError.code === 'PGRST116') {
          // Organization doesn't exist, create it
          const { data: newOrg, error: createError } = await supabase
            .from('organizations')
            .insert({
              name: 'TransFlow Shared',
              slug: 'transflow-shared',
              organization_type: 'shared',
              status: 'active',
              settings: { allowGlobalNetwork: true, upsell_threshold: 10 }
            })
            .select('id')
            .single();

          if (createError) {
            results.error = createError.message;
            results.details = createError;
          } else {
            results.success = true;
            results.details = { created: true, organizationId: newOrg.id };
          }
        } else if (existingOrg) {
          results.success = true;
          results.details = { exists: true, organizationId: existingOrg.id };
        } else {
          results.error = checkError?.message || 'Unknown error checking organization';
          results.details = checkError;
        }
        break;

      case 'field_consistency':
        // Check and report field consistency issues
        const { data: consistencyResult, error: consistencyError } = await supabase
          .rpc('check_field_consistency');

        if (consistencyError) {
          results.error = consistencyError.message;
          results.details = consistencyError;
        } else {
          results.success = true;
          results.details = consistencyResult;
          
          if (consistencyResult?.inconsistent_fields > 0) {
            results.warning = 'Field inconsistencies detected - manual migration required';
          }
        }
        break;

      case 'all':
        // Run all repairs in sequence
        const allResults = [];

        // 1. Default organization
        try {
          const orgRepair = await fetch(request.url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ repairType: 'default_organization' })
          });
          const orgResult = await orgRepair.json();
          allResults.push({ type: 'default_organization', ...orgResult });
        } catch (err) {
          allResults.push({ type: 'default_organization', error: 'Failed to run repair' });
        }

        // 2. Orphaned users
        try {
          const userRepair = await fetch(request.url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ repairType: 'orphaned_users' })
          });
          const userResult = await userRepair.json();
          allResults.push({ type: 'orphaned_users', ...userResult });
        } catch (err) {
          allResults.push({ type: 'orphaned_users', error: 'Failed to run repair' });
        }

        results.success = allResults.every(r => r.success);
        results.details = { repairs: allResults };
        break;

      default:
        results.error = `Unknown repair type: ${repairType}`;
        break;
    }

    return NextResponse.json(results);

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}