// Registration Repair API - Fix new user registration issues
import { NextRequest, NextResponse } from "next/server";
import { createServiceRoleClient } from "@/app/lib/auth/standardized";

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json({
        success: false,
        error: "Email is required"
      }, { status: 400 });
    }

    const supabase = createServiceRoleClient();
    
    // Get the auth user
    const { data: authUsers, error: authError } = await supabase
      .from('auth.users')
      .select('id, email, raw_user_meta_data')
      .eq('email', email);

    if (authError || !authUsers || authUsers.length === 0) {
      return NextResponse.json({
        success: false,
        error: "User not found in auth.users"
      }, { status: 404 });
    }

    const authUser = authUsers[0];
    
    // Check if profile exists
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, roles')
      .eq('id', authUser.id)
      .single();

    let profileFixed = false;
    let orgFixed = false;
    
    // Get default organization
    const { data: defaultOrg } = await supabase
      .from('organizations')
      .select('id')
      .eq('slug', 'transflow-shared')
      .single();

    if (!defaultOrg) {
      return NextResponse.json({
        success: false,
        error: "Default organization not found"
      }, { status: 500 });
    }

    // Fix profile if missing or has wrong role
    if (profileError || !profile) {
      // Create missing profile
      const { error: createProfileError } = await supabase
        .from('profiles')
        .insert({
          id: authUser.id,
          email: authUser.email,
          full_name: authUser.email.split('@')[0],
          roles: ['AFFILIATE'],
          status: 'active',
          email_verified: true
        });

      if (createProfileError) {
        return NextResponse.json({
          success: false,
          error: `Failed to create profile: ${createProfileError.message}`
        }, { status: 500 });
      }
      profileFixed = true;
    } else if (profile.roles && !profile.roles.includes('AFFILIATE')) {
      // Fix role if wrong
      const { error: updateRoleError } = await supabase
        .from('profiles')
        .update({ roles: ['AFFILIATE'] })
        .eq('id', authUser.id);

      if (updateRoleError) {
        return NextResponse.json({
          success: false,
          error: `Failed to update role: ${updateRoleError.message}`
        }, { status: 500 });
      }
      profileFixed = true;
    }

    // Check and fix organization association
    const { data: userOrg, error: userOrgError } = await supabase
      .from('user_organizations')
      .select('id')
      .eq('user_id', authUser.id)
      .single();

    if (userOrgError || !userOrg) {
      // Create missing organization association
      const { error: createOrgError } = await supabase
        .from('user_organizations')
        .insert({
          user_id: authUser.id,
          organization_id: defaultOrg.id,
          role: 'AFFILIATE',
          status: 'active',
          joined_at: new Date().toISOString()
        });

      if (createOrgError) {
        return NextResponse.json({
          success: false,
          error: `Failed to create organization association: ${createOrgError.message}`
        }, { status: 500 });
      }
      orgFixed = true;
    }

    return NextResponse.json({
      success: true,
      message: "User registration repaired successfully",
      fixes: {
        profileFixed,
        orgFixed,
        userId: authUser.id,
        email: authUser.email
      }
    });

  } catch (error) {
    console.error("Registration repair error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}