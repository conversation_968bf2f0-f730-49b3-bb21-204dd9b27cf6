// Comprehensive Health Check API Endpoint
// Based on developer's recommendations and current system analysis

import { NextRequest, NextResponse } from "next/server";
import { } from "@/lib/auth/server";
import { createServiceRoleClient } from "@/app/lib/auth/standardized";

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
interface HealthCheckResult {
  component: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
  timestamp: string;
}

export async function GET(request: NextRequest) {
  const results: HealthCheckResult[] = [];
  const timestamp = new Date().toISOString();

  try {
    // Use service role for system health checks to bypass RLS
    const supabase = createServiceRoleClient();

    // 1. Database Connection Check
    try {
      const { data, error } = await supabase.from('organizations').select('count').limit(1);
      results.push({
        component: 'Database Connection',
        status: error ? 'FAIL' : 'PASS',
        message: error ? `Connection failed: ${error.message}` : 'Database accessible',
        details: error,
        timestamp
      });
    } catch (err) {
      results.push({
        component: 'Database Connection',
        status: 'FAIL',
        message: 'Critical database connection failure',
        details: err,
        timestamp
      });
    }

    // 2. Default Organization Check
    const { data: defaultOrg, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .eq('slug', 'transflow-shared')
      .single();

    results.push({
      component: 'Default Organization',
      status: orgError ? 'FAIL' : 'PASS',
      message: orgError ? 'transflow-shared organization missing' : 'Default organization exists',
      details: defaultOrg || orgError,
      timestamp
    });

    // 3. Orphaned Users Check
    const { data: orphanedUsers, error: orphanError } = await supabase
      .rpc('check_orphaned_users');

    if (!orphanError) {
      const orphanCount = orphanedUsers || 0;
      results.push({
        component: 'User Organization Associations',
        status: orphanCount > 0 ? 'WARNING' : 'PASS',
        message: orphanCount > 0 ? `${orphanCount} users without organization associations` : 'All users properly associated',
        details: { orphanedCount: orphanCount },
        timestamp
      });
    } else {
      results.push({
        component: 'User Organization Associations',
        status: 'WARNING',
        message: 'Could not check orphaned users - function may not exist',
        details: orphanError,
        timestamp
      });
    }

    // 4. Field Naming Consistency Check
    const { data: schemaInfo, error: schemaError } = await supabase
      .rpc('check_field_consistency');

    if (!schemaError) {
      results.push({
        component: 'Field Naming Consistency',
        status: schemaInfo?.inconsistent_fields > 0 ? 'WARNING' : 'PASS',
        message: schemaInfo?.inconsistent_fields > 0 ? 'Field naming inconsistencies detected' : 'Field naming consistent',
        details: schemaInfo,
        timestamp
      });
    } else {
      // Manual check for field consistency
      const { data: columns } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', 'affiliate_companies')
        .in('column_name', ['contact_email', 'primary_contact_email']);
      
      const hasContactEmail = columns?.some(c => c.column_name === 'contact_email');
      const hasPrimaryContactEmail = columns?.some(c => c.column_name === 'primary_contact_email');
      
      results.push({
        component: 'Field Naming Consistency',
        status: hasContactEmail && hasPrimaryContactEmail ? 'WARNING' : 'PASS',
        message: hasContactEmail && hasPrimaryContactEmail ? 'Both contact_email and primary_contact_email exist' : 'Field naming consistent',
        details: { hasContactEmail, hasPrimaryContactEmail },
        timestamp
      });
    }

    // 5. API Authentication Check
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    results.push({
      component: 'API Authentication',
      status: authError ? 'FAIL' : 'PASS',
      message: authError ? 'Authentication system failure' : 'Authentication working',
      details: user ? { userId: user.id, email: user.email } : authError,
      timestamp
    });

    // 6. Critical Tables Check
    const criticalTables = ['organizations', 'profiles', 'user_organizations', 'affiliate_companies'];
    for (const table of criticalTables) {
      try {
        const { data, error } = await supabase.from(table).select('count').limit(1);
        results.push({
          component: `Table: ${table}`,
          status: error ? 'FAIL' : 'PASS',
          message: error ? `Table ${table} inaccessible: ${error.message}` : `Table ${table} accessible`,
          details: error,
          timestamp
        });
      } catch (err) {
        results.push({
          component: `Table: ${table}`,
          status: 'FAIL',
          message: `Critical error accessing ${table}`,
          details: err,
          timestamp
        });
      }
    }

    // Overall Health Assessment
    const failCount = results.filter(r => r.status === 'FAIL').length;
    const warningCount = results.filter(r => r.status === 'WARNING').length;
    
    let overallStatus: 'HEALTHY' | 'DEGRADED' | 'CRITICAL';
    if (failCount > 0) {
      overallStatus = 'CRITICAL';
    } else if (warningCount > 0) {
      overallStatus = 'DEGRADED';
    } else {
      overallStatus = 'HEALTHY';
    }

    return NextResponse.json({
      overall: {
        status: overallStatus,
        timestamp,
        summary: {
          total: results.length,
          passed: results.filter(r => r.status === 'PASS').length,
          warnings: warningCount,
          failures: failCount
        }
      },
      checks: results,
      recommendations: generateRecommendations(results)
    });

  } catch (error) {
    return NextResponse.json({
      overall: {
        status: 'CRITICAL',
        timestamp,
        error: 'Health check system failure'
      },
      checks: results,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

function generateRecommendations(results: HealthCheckResult[]): string[] {
  const recommendations: string[] = [];
  
  results.forEach(result => {
    if (result.status === 'FAIL') {
      switch (result.component) {
        case 'Default Organization':
          recommendations.push('Run migration 021 to create missing default organization');
          break;
        case 'Database Connection':
          recommendations.push('Check database connectivity and credentials');
          break;
        case 'API Authentication':
          recommendations.push('Verify Supabase authentication configuration');
          break;
        default:
          if (result.component.startsWith('Table:')) {
            recommendations.push(`Check RLS policies and permissions for ${result.component.split(':')[1]}`);
          }
      }
    } else if (result.status === 'WARNING') {
      switch (result.component) {
        case 'User Organization Associations':
          recommendations.push('Run user association repair via /api/debug/fix-organizations');
          break;
        case 'Field Naming Consistency':
          recommendations.push('Deploy field standardization migration to fix contact_email vs primary_contact_email');
          break;
      }
    }
  });

  if (recommendations.length === 0) {
    recommendations.push('System is healthy - continue monitoring');
  }

  return recommendations;
}