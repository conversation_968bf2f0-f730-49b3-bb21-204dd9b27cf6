import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { authenticateEnterpriseRequest, createEnterpriseSupabaseClient, logAPIUsage } from '@/lib/auth/enterprise';

/**
 * Enterprise Quote Management API
 * GET: List quotes for the client
 * POST: Create new quote request
 */

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  let clientId = '';
  
  try {
    // Authenticate request
    const authResult = await authenticateEnterpriseRequest(request);
    if (!authResult.success || !authResult.client) {
      await logAPIUsage('unknown', '/api/v1/enterprise/quotes', 'GET', 401, Date.now() - startTime);
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    clientId = authResult.client.id;
    const supabase = await createEnterpriseSupabaseClient(authResult.client.organization_id);

    // Parse query parameters
    const url = new URL(request.url);
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 100);
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const status = url.searchParams.get('status');
    const startDate = url.searchParams.get('start_date');
    const endDate = url.searchParams.get('end_date');

    // Build query
    let query = supabase
      .from('quotes')
      .select(`
        id,
        reference_number,
        status,
        pickup_location,
        destination,
        pickup_datetime,
        passenger_count,
        total_amount,
        currency,
        created_at,
        updated_at,
        events!inner(
          id,
          name,
          customer_id
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    
    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    const { data: quotes, error, count } = await query;

    if (error) {
      console.error('Error fetching quotes:', error);
      await logAPIUsage(clientId, '/api/v1/enterprise/quotes', 'GET', 500, Date.now() - startTime);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch quotes' },
        { status: 500 }
      );
    }

    // Transform data for API response
    const transformedQuotes = quotes?.map(quote => ({
      id: quote.id,
      reference_number: quote.reference_number,
      status: quote.status,
      pickup: {
        location: quote.pickup_location,
        datetime: quote.pickup_datetime
      },
      destination: quote.destination,
      passenger_count: quote.passenger_count,
      amount: {
        total: quote.total_amount,
        currency: quote.currency || 'USD'
      },
      event: {
        id: quote.events?.id,
        name: quote.events?.name
      },
      created_at: quote.created_at,
      updated_at: quote.updated_at
    })) || [];

    await logAPIUsage(clientId, '/api/v1/enterprise/quotes', 'GET', 200, Date.now() - startTime);

    return NextResponse.json({
      success: true,
      data: {
        quotes: transformedQuotes,
        pagination: {
          limit,
          offset,
          total: count || 0,
          has_more: (count || 0) > offset + limit
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        api_version: '1.0.0'
      }
    });

  } catch (error) {
    console.error('Enterprise quotes API error:', error);
    await logAPIUsage(clientId, '/api/v1/enterprise/quotes', 'GET', 500, Date.now() - startTime);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let clientId = '';
  
  try {
    // Authenticate request
    const authResult = await authenticateEnterpriseRequest(request);
    if (!authResult.success || !authResult.client) {
      await logAPIUsage('unknown', '/api/v1/enterprise/quotes', 'POST', 401, Date.now() - startTime);
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    clientId = authResult.client.id;
    const supabase = await createEnterpriseSupabaseClient(authResult.client.organization_id);

    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['pickup_location', 'destination', 'pickup_datetime', 'passenger_count'];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      await logAPIUsage(clientId, '/api/v1/enterprise/quotes', 'POST', 400, Date.now() - startTime);
      return NextResponse.json(
        { 
          success: false, 
          error: `Missing required fields: ${missingFields.join(', ')}` 
        },
        { status: 400 }
      );
    }

    // Generate reference number
    const referenceNumber = `TF-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    // Create quote record
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .insert({
        reference_number: referenceNumber,
        pickup_location: body.pickup_location,
        destination: body.destination,
        pickup_datetime: body.pickup_datetime,
        passenger_count: body.passenger_count,
        special_requirements: body.special_requirements,
        status: 'pending',
        created_via: 'enterprise_api',
        api_client_id: clientId
      })
      .select()
      .single();

    if (quoteError) {
      console.error('Error creating quote:', quoteError);
      await logAPIUsage(clientId, '/api/v1/enterprise/quotes', 'POST', 500, Date.now() - startTime);
      return NextResponse.json(
        { success: false, error: 'Failed to create quote' },
        { status: 500 }
      );
    }

    await logAPIUsage(clientId, '/api/v1/enterprise/quotes', 'POST', 201, Date.now() - startTime);

    return NextResponse.json({
      success: true,
      data: {
        quote: {
          id: quote.id,
          reference_number: quote.reference_number,
          status: quote.status,
          pickup: {
            location: quote.pickup_location,
            datetime: quote.pickup_datetime
          },
          destination: quote.destination,
          passenger_count: quote.passenger_count,
          created_at: quote.created_at
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        api_version: '1.0.0'
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Enterprise quotes POST API error:', error);
    await logAPIUsage(clientId, '/api/v1/enterprise/quotes', 'POST', 500, Date.now() - startTime);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
