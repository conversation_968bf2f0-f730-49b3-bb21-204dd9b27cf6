import { NextRequest, NextResponse } from 'next/server';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
/**
 * Enterprise API v1 Root Endpoint
 * Provides API information and health check
 */
export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      version: "1.0.0",
      name: "TransFlow Enterprise API",
      description: "Enterprise-grade transportation management API",
      endpoints: {
        quotes: "/api/v1/enterprise/quotes",
        events: "/api/v1/enterprise/events", 
        tracking: "/api/v1/enterprise/tracking",
        analytics: "/api/v1/enterprise/analytics",
        webhooks: "/api/v1/enterprise/webhooks"
      },
      documentation: "https://docs.transflow.com/api/v1",
      support: "<EMAIL>",
      status: "operational",
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Enterprise API root error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
