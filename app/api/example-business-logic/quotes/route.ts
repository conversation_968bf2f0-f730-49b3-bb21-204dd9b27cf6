/**
 * Example API Route with Complete Business Logic Permission Enforcement
 * Demonstrates how to use the new business logic system in API routes
 */

import { NextRequest, NextResponse } from 'next/server';
import { withQuotePermission, getRequestMetadata } from '@/lib/business-logic/workflow-middleware';
import { permissionEnforcer } from '@/lib/business-logic/permission-enforcement';
import { auditTrailManager } from '@/lib/business-logic/audit-trail';
import { createClient } from '@/lib/supabase/server';

/**
 * GET /api/example-business-logic/quotes
 * List quotes with permission enforcement
 */
export const GET = withQuotePermission('view')(async (request: NextRequest, context) => {
  const startTime = Date.now();
  const metadata = getRequestMetadata(request);
  
  try {
    const supabase = createClient();
    
    // Get permission context for additional checks
    const permissionContext = await permissionEnforcer.getPermissionContext(
      context.userId,
      context.organizationId
    );

    if (!permissionContext) {
      return NextResponse.json(
        { error: 'Unable to determine permission context' },
        { status: 500 }
      );
    }

    // Check subscription limits for viewing (if applicable)
    const limits = permissionEnforcer.getSubscriptionLimits(permissionContext.organization);
    
    // Get quotes with organization isolation
    const { data: quotes, error } = await supabase
      .from('quotes')
      .select(`
        id,
        pickup_location,
        dropoff_location,
        pickup_datetime,
        passenger_count,
        status,
        created_at,
        organization:organizations(name, subscription_plan)
      `)
      .eq('organization_id', context.organizationId)
      .order('created_at', { ascending: false })
      .limit(limits.advancedAnalytics ? 100 : 25); // Limit based on subscription

    if (error) {
      console.error('Error fetching quotes:', error);
      
      // Log the failure
      await auditTrailManager.logWorkflowExecution(
        {
          userId: context.userId,
          organizationId: context.organizationId,
          ...metadata
        },
        'quotes',
        'list',
        undefined,
        Date.now() - startTime,
        'failure',
        { error: error.message }
      );

      return NextResponse.json(
        { error: 'Failed to fetch quotes' },
        { status: 500 }
      );
    }

    // Log successful execution
    await auditTrailManager.logWorkflowExecution(
      {
        userId: context.userId,
        organizationId: context.organizationId,
        ...metadata
      },
      'quotes',
      'list',
      undefined,
      Date.now() - startTime,
      'success',
      { 
        quotesCount: quotes?.length || 0,
        subscriptionPlan: permissionContext.organization.subscription_plan,
        organizationType: permissionContext.organization.organization_type
      }
    );

    return NextResponse.json({
      success: true,
      quotes: quotes || [],
      metadata: {
        count: quotes?.length || 0,
        subscriptionPlan: permissionContext.organization.subscription_plan,
        limits: {
          advancedAnalytics: limits.advancedAnalytics,
          maxQuotesPerMonth: limits.maxQuotesPerMonth
        }
      }
    });

  } catch (error) {
    console.error('Error in quotes list API:', error);
    
    // Log the error
    await auditTrailManager.logWorkflowExecution(
      {
        userId: context.userId,
        organizationId: context.organizationId,
        ...metadata
      },
      'quotes',
      'list',
      undefined,
      Date.now() - startTime,
      'failure',
      { error: error instanceof Error ? error.message : 'Unknown error' }
    );

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/example-business-logic/quotes
 * Create quote with comprehensive permission and limit enforcement
 */
export const POST = withQuotePermission('create')(async (request: NextRequest, context) => {
  const startTime = Date.now();
  const metadata = getRequestMetadata(request);
  
  try {
    const body = await request.json();
    const supabase = createClient();
    
    // Get permission context
    const permissionContext = await permissionEnforcer.getPermissionContext(
      context.userId,
      context.organizationId
    );

    if (!permissionContext) {
      return NextResponse.json(
        { error: 'Unable to determine permission context' },
        { status: 500 }
      );
    }

    // Check subscription limits for quote creation
    const limitCheck = await permissionEnforcer.checkSubscriptionLimit(
      permissionContext,
      'quotes',
      1
    );

    if (!limitCheck.allowed) {
      // Log limit exceeded
      await auditTrailManager.logEvent(
        {
          userId: context.userId,
          organizationId: context.organizationId,
          ...metadata
        },
        'subscription_limit_exceeded',
        'subscription',
        'quotes',
        {
          currentCount: limitCheck.currentCount,
          limit: limitCheck.limit,
          reason: limitCheck.reason
        },
        'warning'
      );

      return NextResponse.json(
        { 
          error: 'Subscription limit exceeded',
          reason: limitCheck.reason,
          currentCount: limitCheck.currentCount,
          limit: limitCheck.limit,
          subscriptionUpgradeRequired: true
        },
        { status: 402 }
      );
    }

    // Validate required fields
    const { pickup_location, dropoff_location, pickup_datetime, passenger_count } = body;
    
    if (!pickup_location || !dropoff_location || !pickup_datetime || !passenger_count) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create the quote
    const { data: quote, error: createError } = await supabase
      .from('quotes')
      .insert({
        organization_id: context.organizationId,
        user_id: context.userId,
        pickup_location,
        dropoff_location,
        pickup_datetime,
        passenger_count,
        status: 'draft'
      })
      .select()
      .single();

    if (createError || !quote) {
      console.error('Error creating quote:', createError);
      
      // Log the failure
      await auditTrailManager.logWorkflowExecution(
        {
          userId: context.userId,
          organizationId: context.organizationId,
          ...metadata
        },
        'quotes',
        'create',
        undefined,
        Date.now() - startTime,
        'failure',
        { error: createError?.message, requestBody: body }
      );

      return NextResponse.json(
        { error: 'Failed to create quote' },
        { status: 500 }
      );
    }

    // Log successful creation
    await auditTrailManager.logWorkflowExecution(
      {
        userId: context.userId,
        organizationId: context.organizationId,
        ...metadata
      },
      'quotes',
      'create',
      quote.id,
      Date.now() - startTime,
      'success',
      {
        quoteId: quote.id,
        pickup_location,
        dropoff_location,
        passenger_count,
        subscriptionPlan: permissionContext.organization.subscription_plan,
        organizationType: permissionContext.organization.organization_type,
        currentQuoteCount: limitCheck.currentCount + 1,
        quotesLimit: limitCheck.limit
      }
    );

    return NextResponse.json({
      success: true,
      quote,
      metadata: {
        subscriptionUsage: {
          currentCount: limitCheck.currentCount + 1,
          limit: limitCheck.limit,
          utilizationPercentage: limitCheck.limit > 0 ? 
            Math.round(((limitCheck.currentCount + 1) / limitCheck.limit) * 100) : 0
        }
      }
    });

  } catch (error) {
    console.error('Error in quote creation API:', error);
    
    // Log the error
    await auditTrailManager.logWorkflowExecution(
      {
        userId: context.userId,
        organizationId: context.organizationId,
        ...metadata
      },
      'quotes',
      'create',
      undefined,
      Date.now() - startTime,
      'failure',
      { error: error instanceof Error ? error.message : 'Unknown error' }
    );

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});