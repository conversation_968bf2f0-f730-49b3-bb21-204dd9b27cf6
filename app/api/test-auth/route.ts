import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Use environment variables with fallback to demo project
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://127.0.0.1:54321'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'

// Use the new route segment config format
export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    console.log('Test Auth API - Starting request')
    
    // Create a new Supabase client for this request
    const supabase = createClient(supabaseUrl, supabaseAnonKey)
    
    // Get the session from the request
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      console.error('Test Auth API - Error getting session:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }
    
    console.log('Test Auth API - Session:', session ? 'exists' : 'null')
    
    if (!session) {
      return NextResponse.json({ 
        authenticated: false,
        message: 'No session found',
        cookies: request.headers.get('cookie')
      })
    }
    
    // Get the user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.user.id)
      .single()
    
    if (profileError) {
      console.error('Test Auth API - Error getting profile:', profileError)
      return NextResponse.json({ 
        authenticated: true,
        user: session.user,
        error: profileError.message
      })
    }
    
    return NextResponse.json({
      authenticated: true,
      user: session.user,
      profile,
      session: {
        expires_at: session.expires_at
      }
    })
  } catch (error) {
    console.error('Test Auth API - Unexpected error:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}

// New POST endpoint to assign Boston affiliate and create test quote offer
export async function POST(request: Request) {
  try {
    console.log('Test Auth API - Starting POST request to assign Boston affiliate')
    
    // Create a new Supabase client for this request
    const supabase = createClient(supabaseUrl, supabaseAnonKey)
    
    // Find the Boston affiliate
    const { data: bostonAffiliate, error: affiliateError } = await supabase
      .from('affiliate_companies')
      .select('id, name')
      .eq('name', 'Boston Limo')
      .single()
    
    if (affiliateError) {
      console.error('Test Auth API - Error finding Boston affiliate:', affiliateError)
      
      // Create Boston affiliate if it doesn't exist
      const { data: newAffiliate, error: createError } = await supabase
        .from('affiliate_companies')
        .insert({
          name: 'Boston Limo',
          status: 'active',
          city: 'Boston',
          state: 'MA'
        })
        .select()
        .single()
      
      if (createError) {
        console.error('Test Auth API - Error creating Boston affiliate:', createError)
        return NextResponse.json({ error: 'Failed to create Boston affiliate' }, { status: 500 })
      }
      
      console.log('Test Auth API - Created Boston affiliate:', newAffiliate)
    }
    
    // Get affiliate ID
    const affiliateId = bostonAffiliate?.id
    
    if (!affiliateId) {
      return NextResponse.json({ error: 'Failed to find or create Boston affiliate' }, { status: 500 })
    }
    
    // Find a test quote to use
    const { data: testQuote, error: quoteError } = await supabase
      .from('quotes')
      .select('id, status')
      .eq('status', 'pending')
      .limit(1)
      .single()
    
    if (quoteError) {
      console.error('Test Auth API - Error finding test quote:', quoteError)
      
      // Create a test quote if none exists
      const { data: newQuote, error: createQuoteError } = await supabase
        .from('quotes')
        .insert({
          reference_number: `TEST-${Date.now()}`,
          status: 'pending',
          service_type: 'airport_transfer',
          vehicle_type: 'sedan',
          pickup_location: 'Boston Logan Airport',
          dropoff_location: 'Downtown Boston',
          date: new Date().toISOString().split('T')[0],
          time: '14:00',
          passenger_count: 2,
          luggage_count: 2,
          special_requests: 'Test quote for affiliate offers',
          total_amount: 120
        })
        .select()
        .single()
      
      if (createQuoteError) {
        console.error('Test Auth API - Error creating test quote:', createQuoteError)
        return NextResponse.json({ error: 'Failed to create test quote' }, { status: 500 })
      }
      
      console.log('Test Auth API - Created test quote:', newQuote)
    }
    
    const quoteId = testQuote?.id
    
    if (!quoteId) {
      return NextResponse.json({ error: 'Failed to find or create test quote' }, { status: 500 })
    }
    
    // Calculate expiry time (24 hours from now)
    const expiryTime = new Date()
    expiryTime.setHours(expiryTime.getHours() + 24)
    
    // Create a quote offer
    const { data: quoteOffer, error: offerError } = await supabase
      .from('quote_offers')
      .insert({
        quote_id: quoteId,
        company_id: affiliateId,
        rate_amount: 120,
        rate_currency: 'USD',
        status: 'pending',
        timeout_at: expiryTime.toISOString()
      })
      .select()
    
    if (offerError) {
      console.error('Test Auth API - Error creating quote offer:', offerError)
      return NextResponse.json({ error: 'Failed to create quote offer' }, { status: 500 })
    }
    
    console.log('Test Auth API - Created quote offer:', quoteOffer)
    
    // Find the affiliate user
    const { data: affiliateUser, error: userError } = await supabase
      .from('profiles')
      .select('id, email')
      .eq('email', '<EMAIL>')
      .single()
    
    if (userError) {
      console.log('Test Auth API - No affiliate user found, skipping assignment')
    } else {
      // Assign the Boston affiliate to the test account
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ company_id: affiliateId })
        .eq('id', affiliateUser.id)
      
      if (updateError) {
        console.error('Test Auth API - Error assigning affiliate to user:', updateError)
      } else {
        console.log(`Test Auth API - Assigned Boston affiliate to user ${affiliateUser.email}`)
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Boston affiliate assigned and test quote offer created',
      affiliateId,
      quoteId,
      quoteOffer
    })
  } catch (error) {
    console.error('Test Auth API - Unexpected error:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
} 