// API endpoint for affiliate service area configuration

export const runtime = 'nodejs'
// This allows affiliates to define their service areas with radius parameters
// from central points (e.g., downtown) as specified in the requirements

import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server'

export async function GET(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const url = new URL(req.url)
    const affiliate_company_id = url.searchParams.get('affiliate_company_id')
    const city = url.searchParams.get('city')

    let query = supabase
      .from('affiliate_service_areas')
      .select(`
        id,
        affiliate_company_id,
        city,
        central_point_lat,
        central_point_lng,
        radius_miles,
        is_active,
        notes,
        created_at,
        updated_at,
        affiliate_companies!inner(
          id,
          name
        )
      `)
      .eq('is_active', true)
      .order('city', { ascending: true })

    if (affiliate_company_id) {
      query = query.eq('affiliate_company_id', affiliate_company_id)
    }

    if (city) {
      query = query.ilike('city', `%${city}%`)
    }

    const { data: serviceAreas, error } = await query

    if (error) {
      console.error('Error fetching service areas:', error)
      return NextResponse.json({
        error: "Failed to fetch service areas",
        details: error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        service_areas: serviceAreas || [],
        total_count: serviceAreas?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in service areas API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const {
      affiliate_company_id,
      city,
      central_point_lat,
      central_point_lng,
      radius_miles,
      notes
    } = body

    // Validate required parameters
    if (!affiliate_company_id || !city || !central_point_lat || !central_point_lng || !radius_miles) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "affiliate_company_id, city, central_point_lat, central_point_lng, and radius_miles are required"
      }, { status: 400 })
    }

    // Validate numeric values
    if (isNaN(central_point_lat) || isNaN(central_point_lng) || isNaN(radius_miles)) {
      return NextResponse.json({
        error: "Invalid numeric values",
        details: "central_point_lat, central_point_lng, and radius_miles must be valid numbers"
      }, { status: 400 })
    }

    if (radius_miles <= 0) {
      return NextResponse.json({
        error: "Invalid radius",
        details: "radius_miles must be greater than 0"
      }, { status: 400 })
    }

    // Check if user has permission to create service areas for this affiliate
    const { data: userCompanies, error: permissionError } = await supabase
      .from('affiliate_users')
      .select('role')
      .eq('user_id', session.user.id)
      .eq('affiliate_company_id', affiliate_company_id)
      .eq('status', 'ACTIVE')
      .in('role', ['OWNER', 'DISPATCHER'])

    if (permissionError || !userCompanies || userCompanies.length === 0) {
      return NextResponse.json({
        error: "Insufficient permissions",
        details: "You don't have permission to create service areas for this affiliate"
      }, { status: 403 })
    }

    // Create the service area
    const { data: serviceArea, error: createError } = await supabase
      .from('affiliate_service_areas')
      .insert({
        affiliate_company_id,
        city,
        central_point_lat: parseFloat(central_point_lat),
        central_point_lng: parseFloat(central_point_lng),
        radius_miles: parseInt(radius_miles),
        notes,
        is_active: true
      })
      .select(`
        id,
        affiliate_company_id,
        city,
        central_point_lat,
        central_point_lng,
        radius_miles,
        is_active,
        notes,
        created_at,
        updated_at
      `)
      .single()

    if (createError) {
      console.error('Error creating service area:', createError)
      return NextResponse.json({
        error: "Failed to create service area",
        details: createError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        service_area: serviceArea
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Error in service areas creation API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function PUT(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const {
      id,
      city,
      central_point_lat,
      central_point_lng,
      radius_miles,
      notes,
      is_active
    } = body

    // Validate required parameters
    if (!id) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "id is required"
      }, { status: 400 })
    }

    // Get the existing service area to check permissions
    const { data: existingArea, error: fetchError } = await supabase
      .from('affiliate_service_areas')
      .select('affiliate_company_id')
      .eq('id', id)
      .single()

    if (fetchError || !existingArea) {
      return NextResponse.json({
        error: "Service area not found",
        details: "The specified service area does not exist"
      }, { status: 404 })
    }

    // Check if user has permission to update service areas for this affiliate
    const { data: userCompanies, error: permissionError } = await supabase
      .from('affiliate_users')
      .select('role')
      .eq('user_id', session.user.id)
      .eq('affiliate_company_id', existingArea.affiliate_company_id)
      .eq('status', 'ACTIVE')
      .in('role', ['OWNER', 'DISPATCHER'])

    if (permissionError || !userCompanies || userCompanies.length === 0) {
      return NextResponse.json({
        error: "Insufficient permissions",
        details: "You don't have permission to update service areas for this affiliate"
      }, { status: 403 })
    }

    // Build update object with only provided fields
    const updateData: any = {}
    if (city !== undefined) updateData.city = city
    if (central_point_lat !== undefined) updateData.central_point_lat = parseFloat(central_point_lat)
    if (central_point_lng !== undefined) updateData.central_point_lng = parseFloat(central_point_lng)
    if (radius_miles !== undefined) updateData.radius_miles = parseInt(radius_miles)
    if (notes !== undefined) updateData.notes = notes
    if (is_active !== undefined) updateData.is_active = is_active

    // Validate numeric values if provided
    if (updateData.central_point_lat && isNaN(updateData.central_point_lat)) {
      return NextResponse.json({
        error: "Invalid central_point_lat",
        details: "central_point_lat must be a valid number"
      }, { status: 400 })
    }

    if (updateData.central_point_lng && isNaN(updateData.central_point_lng)) {
      return NextResponse.json({
        error: "Invalid central_point_lng",
        details: "central_point_lng must be a valid number"
      }, { status: 400 })
    }

    if (updateData.radius_miles && (isNaN(updateData.radius_miles) || updateData.radius_miles <= 0)) {
      return NextResponse.json({
        error: "Invalid radius_miles",
        details: "radius_miles must be a valid number greater than 0"
      }, { status: 400 })
    }

    // Update the service area
    const { data: updatedArea, error: updateError } = await supabase
      .from('affiliate_service_areas')
      .update(updateData)
      .eq('id', id)
      .select(`
        id,
        affiliate_company_id,
        city,
        central_point_lat,
        central_point_lng,
        radius_miles,
        is_active,
        notes,
        created_at,
        updated_at
      `)
      .single()

    if (updateError) {
      console.error('Error updating service area:', updateError)
      return NextResponse.json({
        error: "Failed to update service area",
        details: updateError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        service_area: updatedArea
      }
    })

  } catch (error) {
    console.error('Error in service areas update API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function DELETE(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const url = new URL(req.url)
    const id = url.searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "id is required"
      }, { status: 400 })
    }

    // Get the existing service area to check permissions
    const { data: existingArea, error: fetchError } = await supabase
      .from('affiliate_service_areas')
      .select('affiliate_company_id')
      .eq('id', id)
      .single()

    if (fetchError || !existingArea) {
      return NextResponse.json({
        error: "Service area not found",
        details: "The specified service area does not exist"
      }, { status: 404 })
    }

    // Check if user has permission to delete service areas for this affiliate
    const { data: userCompanies, error: permissionError } = await supabase
      .from('affiliate_users')
      .select('role')
      .eq('user_id', session.user.id)
      .eq('affiliate_company_id', existingArea.affiliate_company_id)
      .eq('status', 'ACTIVE')
      .eq('role', 'OWNER') // Only owners can delete

    if (permissionError || !userCompanies || userCompanies.length === 0) {
      return NextResponse.json({
        error: "Insufficient permissions",
        details: "You don't have permission to delete service areas for this affiliate"
      }, { status: 403 })
    }

    // Delete the service area
    const { error: deleteError } = await supabase
      .from('affiliate_service_areas')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('Error deleting service area:', deleteError)
      return NextResponse.json({
        error: "Failed to delete service area",
        details: deleteError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: "Service area deleted successfully"
    })

  } catch (error) {
    console.error('Error in service areas deletion API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}