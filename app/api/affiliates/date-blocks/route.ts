// API endpoint for affiliate date blocks configuration

export const runtime = 'nodejs'
// This allows affiliates to configure date blocks during special events
// (like SXSW) as specified in the requirements

import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server'

export async function GET(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const url = new URL(req.url)
    const affiliate_company_id = url.searchParams.get('affiliate_company_id')
    const city = url.searchParams.get('city')
    const start_date = url.searchParams.get('start_date')
    const end_date = url.searchParams.get('end_date')
    const active_only = url.searchParams.get('active_only') === 'true'

    let query = supabase
      .from('affiliate_date_blocks')
      .select(`
        id,
        affiliate_company_id,
        city,
        event_name,
        block_type,
        start_date,
        end_date,
        pricing_multiplier,
        description,
        is_active,
        created_at,
        updated_at,
        affiliate_companies!inner(
          id,
          name
        )
      `)
      .order('start_date', { ascending: true })

    if (affiliate_company_id) {
      query = query.eq('affiliate_company_id', affiliate_company_id)
    }

    if (city) {
      query = query.ilike('city', `%${city}%`)
    }

    if (active_only) {
      query = query.eq('is_active', true)
    }

    if (start_date) {
      query = query.gte('end_date', start_date)
    }

    if (end_date) {
      query = query.lte('start_date', end_date)
    }

    const { data: dateBlocks, error } = await query

    if (error) {
      console.error('Error fetching date blocks:', error)
      return NextResponse.json({
        error: "Failed to fetch date blocks",
        details: error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        date_blocks: dateBlocks || [],
        total_count: dateBlocks?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in date blocks API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const {
      affiliate_company_id,
      city,
      event_name,
      block_type = 'SPECIAL_EVENT',
      start_date,
      end_date,
      pricing_multiplier = 1.0,
      description
    } = body

    // Validate required parameters
    if (!affiliate_company_id || !city || !event_name || !start_date || !end_date) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "affiliate_company_id, city, event_name, start_date, and end_date are required"
      }, { status: 400 })
    }

    // Validate date format and logic
    const startDate = new Date(start_date)
    const endDate = new Date(end_date)
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json({
        error: "Invalid date format",
        details: "start_date and end_date must be valid dates in YYYY-MM-DD format"
      }, { status: 400 })
    }

    if (startDate >= endDate) {
      return NextResponse.json({
        error: "Invalid date range",
        details: "start_date must be before end_date"
      }, { status: 400 })
    }

    // Validate pricing multiplier
    if (isNaN(pricing_multiplier) || pricing_multiplier < 0) {
      return NextResponse.json({
        error: "Invalid pricing multiplier",
        details: "pricing_multiplier must be a valid number >= 0"
      }, { status: 400 })
    }

    // Validate block type
    const validBlockTypes = ['SPECIAL_EVENT', 'BLACKOUT', 'PREMIUM_PRICING', 'HOLIDAY']
    if (!validBlockTypes.includes(block_type)) {
      return NextResponse.json({
        error: "Invalid block type",
        details: `block_type must be one of: ${validBlockTypes.join(', ')}`
      }, { status: 400 })
    }

    // Check if user has permission to create date blocks for this affiliate
    const { data: userCompanies, error: permissionError } = await supabase
      .from('affiliate_users')
      .select('role')
      .eq('user_id', session.user.id)
      .eq('affiliate_company_id', affiliate_company_id)
      .eq('status', 'ACTIVE')
      .in('role', ['OWNER', 'DISPATCHER'])

    if (permissionError || !userCompanies || userCompanies.length === 0) {
      return NextResponse.json({
        error: "Insufficient permissions",
        details: "You don't have permission to create date blocks for this affiliate"
      }, { status: 403 })
    }

    // Check for overlapping date blocks
    const { data: overlapping, error: overlapError } = await supabase
      .from('affiliate_date_blocks')
      .select('id, event_name, start_date, end_date')
      .eq('affiliate_company_id', affiliate_company_id)
      .eq('city', city)
      .eq('is_active', true)
      .or(`and(start_date.lte.${end_date},end_date.gte.${start_date})`)

    if (overlapError) {
      console.error('Error checking for overlapping date blocks:', overlapError)
      return NextResponse.json({
        error: "Failed to validate date range",
        details: overlapError.message
      }, { status: 500 })
    }

    if (overlapping && overlapping.length > 0) {
      return NextResponse.json({
        error: "Overlapping date block",
        details: `Date range overlaps with existing block: ${overlapping[0].event_name} (${overlapping[0].start_date} to ${overlapping[0].end_date})`
      }, { status: 409 })
    }

    // Create the date block
    const { data: dateBlock, error: createError } = await supabase
      .from('affiliate_date_blocks')
      .insert({
        affiliate_company_id,
        city,
        event_name,
        block_type,
        start_date,
        end_date,
        pricing_multiplier: parseFloat(pricing_multiplier),
        description,
        is_active: true
      })
      .select(`
        id,
        affiliate_company_id,
        city,
        event_name,
        block_type,
        start_date,
        end_date,
        pricing_multiplier,
        description,
        is_active,
        created_at,
        updated_at
      `)
      .single()

    if (createError) {
      console.error('Error creating date block:', createError)
      return NextResponse.json({
        error: "Failed to create date block",
        details: createError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        date_block: dateBlock
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Error in date blocks creation API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function PUT(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const {
      id,
      city,
      event_name,
      block_type,
      start_date,
      end_date,
      pricing_multiplier,
      description,
      is_active
    } = body

    // Validate required parameters
    if (!id) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "id is required"
      }, { status: 400 })
    }

    // Get the existing date block to check permissions
    const { data: existingBlock, error: fetchError } = await supabase
      .from('affiliate_date_blocks')
      .select('affiliate_company_id')
      .eq('id', id)
      .single()

    if (fetchError || !existingBlock) {
      return NextResponse.json({
        error: "Date block not found",
        details: "The specified date block does not exist"
      }, { status: 404 })
    }

    // Check if user has permission to update date blocks for this affiliate
    const { data: userCompanies, error: permissionError } = await supabase
      .from('affiliate_users')
      .select('role')
      .eq('user_id', session.user.id)
      .eq('affiliate_company_id', existingBlock.affiliate_company_id)
      .eq('status', 'ACTIVE')
      .in('role', ['OWNER', 'DISPATCHER'])

    if (permissionError || !userCompanies || userCompanies.length === 0) {
      return NextResponse.json({
        error: "Insufficient permissions",
        details: "You don't have permission to update date blocks for this affiliate"
      }, { status: 403 })
    }

    // Build update object with only provided fields
    const updateData: any = {}
    if (city !== undefined) updateData.city = city
    if (event_name !== undefined) updateData.event_name = event_name
    if (block_type !== undefined) updateData.block_type = block_type
    if (start_date !== undefined) updateData.start_date = start_date
    if (end_date !== undefined) updateData.end_date = end_date
    if (pricing_multiplier !== undefined) updateData.pricing_multiplier = parseFloat(pricing_multiplier)
    if (description !== undefined) updateData.description = description
    if (is_active !== undefined) updateData.is_active = is_active

    // Validate dates if provided
    if (updateData.start_date || updateData.end_date) {
      const startDate = new Date(updateData.start_date || start_date)
      const endDate = new Date(updateData.end_date || end_date)
      
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return NextResponse.json({
          error: "Invalid date format",
          details: "start_date and end_date must be valid dates in YYYY-MM-DD format"
        }, { status: 400 })
      }

      if (startDate >= endDate) {
        return NextResponse.json({
          error: "Invalid date range",
          details: "start_date must be before end_date"
        }, { status: 400 })
      }
    }

    // Validate pricing multiplier if provided
    if (updateData.pricing_multiplier && (isNaN(updateData.pricing_multiplier) || updateData.pricing_multiplier < 0)) {
      return NextResponse.json({
        error: "Invalid pricing multiplier",
        details: "pricing_multiplier must be a valid number >= 0"
      }, { status: 400 })
    }

    // Validate block type if provided
    if (updateData.block_type) {
      const validBlockTypes = ['SPECIAL_EVENT', 'BLACKOUT', 'PREMIUM_PRICING', 'HOLIDAY']
      if (!validBlockTypes.includes(updateData.block_type)) {
        return NextResponse.json({
          error: "Invalid block type",
          details: `block_type must be one of: ${validBlockTypes.join(', ')}`
        }, { status: 400 })
      }
    }

    // Update the date block
    const { data: updatedBlock, error: updateError } = await supabase
      .from('affiliate_date_blocks')
      .update(updateData)
      .eq('id', id)
      .select(`
        id,
        affiliate_company_id,
        city,
        event_name,
        block_type,
        start_date,
        end_date,
        pricing_multiplier,
        description,
        is_active,
        created_at,
        updated_at
      `)
      .single()

    if (updateError) {
      console.error('Error updating date block:', updateError)
      return NextResponse.json({
        error: "Failed to update date block",
        details: updateError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        date_block: updatedBlock
      }
    })

  } catch (error) {
    console.error('Error in date blocks update API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function DELETE(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const url = new URL(req.url)
    const id = url.searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "id is required"
      }, { status: 400 })
    }

    // Get the existing date block to check permissions
    const { data: existingBlock, error: fetchError } = await supabase
      .from('affiliate_date_blocks')
      .select('affiliate_company_id')
      .eq('id', id)
      .single()

    if (fetchError || !existingBlock) {
      return NextResponse.json({
        error: "Date block not found",
        details: "The specified date block does not exist"
      }, { status: 404 })
    }

    // Check if user has permission to delete date blocks for this affiliate
    const { data: userCompanies, error: permissionError } = await supabase
      .from('affiliate_users')
      .select('role')
      .eq('user_id', session.user.id)
      .eq('affiliate_company_id', existingBlock.affiliate_company_id)
      .eq('status', 'ACTIVE')
      .eq('role', 'OWNER') // Only owners can delete

    if (permissionError || !userCompanies || userCompanies.length === 0) {
      return NextResponse.json({
        error: "Insufficient permissions",
        details: "You don't have permission to delete date blocks for this affiliate"
      }, { status: 403 })
    }

    // Delete the date block
    const { error: deleteError } = await supabase
      .from('affiliate_date_blocks')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('Error deleting date block:', deleteError)
      return NextResponse.json({
        error: "Failed to delete date block",
        details: deleteError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: "Date block deleted successfully"
    })

  } catch (error) {
    console.error('Error in date blocks deletion API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}