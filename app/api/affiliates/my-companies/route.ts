import { createServerClient, type CookieOptions } from '@supabase/ssr'

export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    console.log('[api/affiliates/my-companies] Starting request')
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            cookieStore.set(name, value, options)
          },
          remove(name: string, options: CookieOptions) {
            cookieStore.set(name, '', options)
          },
        },
      }
    )
    
    // Get the current user's session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      console.error('[api/affiliates/my-companies] No session found')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log(`[api/affiliates/my-companies] Fetching companies for user: ${session.user.id}`)

    // Get the affiliate companies for the user
    const { data: companies, error } = await supabase
      .from('affiliate_companies')
      .select('*')
      .eq('owner_id', session.user.id)
      .or(`status.eq.active,status.eq.approved`)

    if (error) {
      console.error('[api/affiliates/my-companies] Error fetching companies:', error)
      return NextResponse.json({ error: 'Failed to fetch companies', details: error.message }, { status: 500 })
    }

    // Return the list of companies (empty array if none found)
    console.log(`[api/affiliates/my-companies] Found ${companies?.length || 0} companies`)
    return NextResponse.json(companies || [])
  } catch (error) {
    console.error('[api/affiliates/my-companies] Error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
} 