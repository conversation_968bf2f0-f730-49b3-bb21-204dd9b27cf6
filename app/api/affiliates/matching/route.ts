// API endpoint for enhanced affiliate matching based on configured rates

export const runtime = 'nodejs'
// This implements the new affiliate matching rules specified in the requirements

import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'
import { authenticateApiRequest } from '@/lib/auth/api-authentication'

export async function POST(req: Request) {
  try {
    const auth = await authenticateApiRequest(req, ['CLIENT', 'CLIENT_COORDINATOR', 'SUPER_ADMIN'])
    if (!auth.success || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: auth.statusCode || 401 })
    }
    const organizationId = auth.organization?.id || auth.user.organization_id
    if (!organizationId) {
      return NextResponse.json({ error: 'Organization context required' }, { status: 400 })
    }

    const body = await req.json()
    const {
      pickup_lat,
      pickup_lng,
      pickup_city,
      vehicle_type,
      pickup_date,
      service_type = 'point_to_point'
    } = body

    // Validate required parameters
    if (!pickup_lat || !pickup_lng || !vehicle_type) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "pickup_lat, pickup_lng, and vehicle_type are required"
      }, { status: 400 })
    }

    // Find matching affiliates using the new logic
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      { auth: { autoRefreshToken: false, persistSession: false } }
    )

    const { data: matchingAffiliates, error: matchingError } = await supabase
      .rpc('find_matching_affiliates_for_quote', {
        p_organization_id: organizationId,
        p_pickup_lat: pickup_lat,
        p_pickup_lng: pickup_lng,
        p_pickup_city: pickup_city,
        p_vehicle_type: vehicle_type,
        p_pickup_date: pickup_date || new Date().toISOString().split('T')[0],
        p_service_type: service_type
      })

    if (matchingError) {
      console.error('Error finding matching affiliates:', matchingError)
      return NextResponse.json({
        error: "Failed to find matching affiliates",
        details: matchingError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        matching_affiliates: matchingAffiliates || [],
        total_count: matchingAffiliates?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in affiliate matching API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(req: Request) {
  try {
    const auth = await authenticateApiRequest(req, ['CLIENT', 'CLIENT_COORDINATOR', 'SUPER_ADMIN'])
    if (!auth.success || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: auth.statusCode || 401 })
    }
    const organizationId = auth.organization?.id || auth.user.organization_id
    if (!organizationId) {
      return NextResponse.json({ error: 'Organization context required' }, { status: 400 })
    }

    const url = new URL(req.url)
    const pickup_lat = parseFloat(url.searchParams.get('pickup_lat') || '0')
    const pickup_lng = parseFloat(url.searchParams.get('pickup_lng') || '0')
    const pickup_city = url.searchParams.get('pickup_city')
    const vehicle_type = url.searchParams.get('vehicle_type')
    const pickup_date = url.searchParams.get('pickup_date')
    const service_type = url.searchParams.get('service_type') || 'point_to_point'

    // Validate required parameters
    if (!pickup_lat || !pickup_lng || !vehicle_type) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "pickup_lat, pickup_lng, and vehicle_type are required"
      }, { status: 400 })
    }

    // Find matching affiliates using the new logic
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      { auth: { autoRefreshToken: false, persistSession: false } }
    )

    const { data: matchingAffiliates, error: matchingError } = await supabase
      .rpc('find_matching_affiliates_for_quote', {
        p_organization_id: organizationId,
        p_pickup_lat: pickup_lat,
        p_pickup_lng: pickup_lng,
        p_pickup_city: pickup_city,
        p_vehicle_type: vehicle_type,
        p_pickup_date: pickup_date || new Date().toISOString().split('T')[0],
        p_service_type: service_type
      })

    if (matchingError) {
      console.error('Error finding matching affiliates:', matchingError)
      return NextResponse.json({
        error: "Failed to find matching affiliates",
        details: matchingError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        matching_affiliates: matchingAffiliates || [],
        total_count: matchingAffiliates?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in affiliate matching API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}