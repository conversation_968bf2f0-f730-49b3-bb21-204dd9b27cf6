import { NextResponse } from "next/server"

export const runtime = 'nodejs'
// import { hash } from 'bcrypt'
// import { createUser } from '@/lib/db'
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from "next/headers"

export async function POST(req: Request) {
  console.log('[API /auth/register] Received request');
  try {
    const body = await req.json();
    console.log('[API /auth/register] Request body:', body);
    const { email, password, options, ...rest } = body;

    if (!email || !password) {
      console.error('[API /auth/register] Email or password missing');
      return NextResponse.json(
        { message: "Email and password are required" },
        { status: 400 }
      )
    }
    console.log(`[API /auth/register] Registering user: ${email}`);

    console.log('[API /auth/register] Initializing Supabase client...');
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value, ...options })
            } catch (error) {}
          },
          remove(name: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value: '', ...options })
            } catch (error) {}
          },
        },
      }
    );
    console.log('[API /auth/register] Supabase client initialized.');
    console.log('[API /auth/register] Enumerable keys of supabase object in API route:', Object.keys(supabase));
    console.log('[API /auth/register] ALL property names of supabase object in API route:', Object.getOwnPropertyNames(supabase));
    console.log('[API /auth/register] supabase.auth exists in API route:', !!supabase.auth);
    console.log('[API /auth/register] typeof supabase.auth in API route:', typeof supabase.auth);
    console.log('[API /auth/register] supabase.from exists in API route:', !!supabase.from);
    console.log('[API /auth/register] typeof supabase.from in API route:', typeof supabase.from);

    const redirectTo = req.headers.get("X-Client-Info")
      ? new URL(req.headers.get("X-Client-Info")!).origin + "/auth/callback"
      : "http://localhost:3000/auth/callback";
    console.log('[API /auth/register] Redirect URL set to:', redirectTo);

    const metadata = { ...rest };
    // Enforce only a single role in metadata
    let { role, roles } = metadata;
    if (!role && Array.isArray(roles) && roles.length > 0) {
      role = roles[0];
    }
    if (!roles || !Array.isArray(roles) || roles.length === 0) {
      roles = [role];
    }
    // Only allow a single role (the selected one)
    roles = [role];
    metadata.role = role;
    metadata.roles = roles;
    console.log('[API /auth/register] Metadata for signup:', metadata);

    console.log('[API /auth/register] Attempting to call supabase.auth.signUp...');
    // Pre-flight check to ensure supabase.auth is callable
    if (typeof supabase.auth?.signUp !== 'function') {
        console.error('[API /auth/register] CRITICAL: supabase.auth or supabase.auth.signUp is not a function before call!');
        console.error('[API /auth/register] supabase.auth object is:', supabase.auth);
        throw new TypeError('supabase.auth.signUp is not a function or supabase.auth is undefined');
    }

    const { data, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        // emailRedirectTo: redirectTo, // Optional: if you want email confirmation to redirect
        data: metadata, // Pass additional data here
        ...(options || {}),
      },
    });

    if (signUpError) {
      console.error('[API /auth/register] Supabase signUp error:', signUpError);
      return NextResponse.json({ message: signUpError.message || 'Supabase sign-up failed' }, { status: 401 });
    }

    console.log('[API /auth/register] Supabase signUp successful. Data:', data);

    return NextResponse.json(
      { data, message: "User registered. Please check your email for confirmation." }, 
      { status: 201 }
    );

  } catch (error: any) {
    console.error('[API /auth/register] CATCH BLOCK Error:', error);
    console.error('[API /auth/register] Error Name:', error.name);
    console.error('[API /auth/register] Error Message:', error.message);
    console.error('[API /auth/register] Error Cause:', error.cause);
    console.error('[API /auth/register] Error Stack:', error.stack);
    return NextResponse.json(
      { message: error.message || "An unexpected error occurred", errorDetail: JSON.stringify(error, Object.getOwnPropertyNames(error)) },
      { status: 500 }
    );
  }
}
