import { createServerClient, type CookieOptions } from "@supabase/ssr";

export const runtime = 'nodejs'
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { NextRequest } from "next/server";
import { Database } from "../../../types/supabase";
import { ensureUserProfile } from "@/lib/auth/profile-manager";

// Local development JWT secret from supabase status
const JWT_SECRET =
  process.env.SUPABASE_JWT_SECRET ||
  "super-secret-jwt-token-with-at-least-32-characters-long";

// Helper function to create a timeout promise
function timeout(ms: number) {
  return new Promise((_, reject) =>
    setTimeout(() => reject(new Error(`Operation timed out after ${ms}ms`)), ms)
  );
}

// Helper function to run a promise with a timeout
async function withTimeout<T>(
  promise: any,
  ms: number,
  operationName: string
): Promise<T> {
  try {
    // For Supabase queries, need to call .then() to get the promise
    const actualPromise =
      typeof promise.then === "function"
        ? promise
        : promise.then((res: T) => res);

    return (await Promise.race([
      actualPromise,
      timeout(ms).then(() => {
        throw new Error(`Operation timed out after ${ms}ms`);
      }),
    ])) as T;
  } catch (error) {
    console.error(`Session endpoint: ${operationName} timed out after ${ms}ms`);
    throw error;
  }
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  console.log(
    `Session endpoint: Starting request at ${new Date().toISOString()}`
  );

  try {
    const cookieStore = cookies();

    // Log all cookies for debugging
    const allCookies = cookieStore.getAll();
    console.log(
      "Session endpoint: Available cookies:",
      allCookies.map((c) => ({
        name: c.name,
        value: c.value.substring(0, 20) + "...",
      }))
    );

    console.log("Session endpoint: Creating Supabase client");
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value, ...options });
            } catch (error) {
              // The `set` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
          remove(name: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value: "", ...options });
            } catch (error) {
              // The `delete` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Get session from Supabase with timeout
    console.log("Session endpoint: Getting session from Supabase");
    const {
      data: { session },
      error,
    } = await withTimeout<{ data: { session: any }; error: any }>(
      supabase.auth.getSession(),
      5000, // 5 second timeout
      "getSession"
    );

    if (error) {
      console.error("Session endpoint: Error getting session:", error);
      return NextResponse.json(
        {
          session: null,
          error: "Failed to get session",
          errorDetails: error.message,
        },
        { status: 200 }
      );
    }

    if (!session) {
      console.log("Session endpoint: No session found");
      return NextResponse.json({ session: null }, { status: 200 });
    }

    console.log(
      `Session endpoint: Session found for user: ${session.user.email} (${session.user.id})`
    );

    // Special handling for test accounts
    if (session.user.email?.endsWith("@test.com")) {
      const emailPrefix = session.user.email.split("@")[0].toUpperCase();
      let role;

      if (emailPrefix === "ADMIN") {
        role = "ADMIN";
      } else if (emailPrefix === "CUSTOMER") {
        role = "CUSTOMER";
      } else if (emailPrefix === "AFFILIATE") {
        role = "AFFILIATE";
      } else if (emailPrefix === "EVENTMANAGER") {
        role = "EVENT_MANAGER";
      }

      if (role) {
        console.log(
          `Session endpoint: Special handling for test account ${session.user.email} with role ${role}`
        );

        try {
          // First check if profile already exists to avoid upsert that might violate RLS
          const { data: existingProfile, error: profileCheckError } =
            await withTimeout<{ data: any; error: any }>(
              supabase
                .from("profiles")
                .select("id, roles")
                .eq("id", session.user.id)
                .single(),
              3000,
              "checkExistingProfile"
            );

          if (profileCheckError && profileCheckError.code !== "PGRST116") {
            console.error(
              "Session endpoint: Error checking existing profile:",
              profileCheckError
            );
          } else if (!existingProfile) {
            // Only try to create profile if it doesn't exist
            console.log(
              "Session endpoint: Profile not found, attempting to create"
            );
            try {
              const profile = await ensureUserProfile({
                userId: session.user.id,
                email: session.user.email!,
                role: role
              });

              if (profile) {
                console.log(
                  `Session endpoint: Successfully created profile for ${session.user.email}`
                );
              } else {
                console.error(
                  "Session endpoint: Failed to create profile, but continuing with authentication"
                );
              }
            } catch (insertError) {
              console.error(
                "Session endpoint: Exception during profile creation:",
                insertError
              );
              // Continue with the authentication flow
            }
          } else {
            // Profile exists, so just log and continue
            console.log(
              `Session endpoint: Profile already exists for ${session.user.email} with roles:`,
              existingProfile.roles
            );
          }
        } catch (profileError) {
          console.error(
            "Session endpoint: Error during profile check/create:",
            profileError
          );
          // Continue even if profile operations fail
        }

        // Create response with session data and role
        const response = NextResponse.json(
          {
            session: {
              ...session,
              user: {
                ...session.user,
                roles: [role],
              },
            },
          },
          { status: 200 }
        );

        // Copy all auth-related cookies from the cookie store to the response
        const authCookies = [
          "sb-access-token",
          "sb-refresh-token",
          "sb-127-auth-token",
        ];
        allCookies
          .filter((cookie) => authCookies.includes(cookie.name))
          .forEach((cookie) => {
            response.headers.append("Set-Cookie", cookie.value);
          });

        const endTime = Date.now();
        console.log(`Session endpoint: Completed in ${endTime - startTime}ms`);
        return response;
      }
    }

    // Get user roles from profiles table
    console.log(`Session endpoint: Getting roles for user ${session.user.id}`);
    let profile;
    let profileError;

    try {
      const { data, error } = await withTimeout<{ data: any; error: any }>(
        supabase
          .from("profiles")
          .select("roles")
          .eq("id", session.user.id)
          .single(),
        3000, // 3 second timeout
        "getProfile"
      );

      profile = data;
      profileError = error;
    } catch (error) {
      console.error(
        "Session endpoint: Error or timeout getting profile:",
        error
      );
      profileError = { message: "Profile fetch timed out or failed" };
    }

    if (profileError) {
      console.error("Session endpoint: Error getting profile:", profileError);

      // If profile not found, create one with default role based on email
      if (profileError.code === "PGRST116") {
        const email = session.user.email?.toLowerCase() || "";
        let roles = [];

        if (email.includes("affiliate")) {
          roles = ["AFFILIATE"];
        } else if (email.includes("admin")) {
          roles = ["SUPER_ADMIN"];
        } else if (email.includes("event")) {
          roles = ["EVENT_MANAGER"];
        } else {
          roles = ["CUSTOMER"];
        }

        console.log(
          `Session endpoint: Creating profile for ${email} with roles ${roles.join(
            ", "
          )}`
        );

        try {
          const profile = await ensureUserProfile({
            userId: session.user.id,
            email: session.user.email!,
            role: roles[0] // Use the first role as primary
          });

          if (profile) {
            console.log(
              `Session endpoint: Successfully created profile for ${email}`
            );

            // Return session with the new roles
            const response = NextResponse.json(
              {
                session: {
                  ...session,
                  user: {
                    ...session.user,
                    roles: profile.roles,
                  },
                },
              },
              { status: 200 }
            );

            // Copy all auth-related cookies
            const authCookies = [
              "sb-access-token",
              "sb-refresh-token",
              "sb-127-auth-token",
            ];
            allCookies
              .filter((cookie) => authCookies.includes(cookie.name))
              .forEach((cookie) => {
                response.headers.append("Set-Cookie", cookie.value);
              });

            const endTime = Date.now();
            console.log(
              `Session endpoint: Completed in ${endTime - startTime}ms`
            );
            return response;
          } else {
            console.error(
              "Session endpoint: Failed to create profile"
            );
            // Continue with empty roles
          }
        } catch (error) {
          console.error(
            "Session endpoint: Error or timeout creating profile:",
            error
          );
          // Continue with empty roles
        }
      }

      // For other profile errors, return session without roles
      const response = NextResponse.json(
        {
          session: {
            ...session,
            user: {
              ...session.user,
              roles: [],
            },
          },
        },
        { status: 200 }
      );

      const endTime = Date.now();
      console.log(
        `Session endpoint: Completed with errors in ${endTime - startTime}ms`
      );
      return response;
    }

    let roles = profile?.roles || [];

    // If no roles found, assign based on email
    if (!roles || roles.length === 0) {
      const email = session.user.email?.toLowerCase() || "";

      if (email.includes("affiliate")) {
        roles = ["AFFILIATE"];
      } else if (email.includes("admin")) {
        roles = ["SUPER_ADMIN"];
      } else if (email.includes("event") || email.includes("eventmanager")) {
        roles = ["EVENT_MANAGER"];
      } else {
        roles = ["CUSTOMER"];
      }

      console.log(
        `Session endpoint: No roles found, assigning ${roles.join(
          ", "
        )} based on email`
      );

      // Update profile with role
      try {
        const { error: updateError } = await withTimeout<{ error: any }>(
          supabase.from("profiles").update({ roles }).eq("id", session.user.id),
          3000, // 3 second timeout
          "updateProfile"
        );

        if (updateError) {
          console.error(
            "Session endpoint: Error updating profile:",
            updateError
          );
        } else {
          console.log("Session endpoint: Updated profile with roles:", roles);
        }
      } catch (error) {
        console.error(
          "Session endpoint: Error or timeout updating profile:",
          error
        );
        // Continue with the roles we've determined
      }
    }

    // Create response with session data
    const response = NextResponse.json(
      {
        session: {
          ...session,
          user: {
            ...session.user,
            roles,
          },
        },
      },
      { status: 200 }
    );

    // Copy all auth-related cookies from the cookie store to the response
    const authCookies = [
      "sb-access-token",
      "sb-refresh-token",
      "sb-127-auth-token",
    ];
    allCookies
      .filter((cookie) => authCookies.includes(cookie.name))
      .forEach((cookie) => {
        response.headers.append("Set-Cookie", cookie.value);
      });

    const endTime = Date.now();
    console.log(
      `Session endpoint: Completed successfully in ${endTime - startTime}ms`
    );
    return response;
  } catch (error) {
    console.error("Session endpoint: Unexpected error:", error);
    const endTime = Date.now();
    console.log(
      `Session endpoint: Failed with error in ${endTime - startTime}ms`
    );
    return NextResponse.json(
      {
        session: null,
        error: "Unexpected error",
        errorDetails: error instanceof Error ? error.message : String(error),
      },
      { status: 200 }
    );
  }
}

export async function POST(request: Request) {
  console.log("Session endpoint POST: Starting sign-out request");
  const startTime = Date.now();

  const cookieStore = cookies();
  const response = NextResponse.json({ success: true });

  // Clear all auth-related cookies from both store and response
  const authCookies = [
    "sb-access-token",
    "sb-refresh-token",
    "sb-127-auth-token",
    "wwms-auth-session",
    "affiliate-session",
    "last_refresh_time",
    "refresh_attempts",
    "refresh_count",
  ];

  authCookies.forEach((cookieName) => {
    try {
      // Clear from store
      cookieStore.delete(cookieName);

      // Clear from response
      response.cookies.delete(cookieName);
      console.log(`Session endpoint POST: Deleted cookie ${cookieName}`);
    } catch (e) {
      console.warn(
        `Session endpoint POST: Failed to delete cookie ${cookieName}:`,
        e
      );
    }
  });

  try {
    console.log("Session endpoint POST: Creating Supabase client");
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value, ...options });
            } catch (error) {
              // The `set` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
          remove(name: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value: "", ...options });
            } catch (error) {
              // The `delete` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    // Sign out with timeout
    console.log("Session endpoint POST: Signing out from Supabase");
    try {
      await withTimeout(
        supabase.auth.signOut(),
        5000, // 5 second timeout
        "signOut"
      );
      console.log(
        "Session endpoint POST: Successfully signed out from Supabase"
      );
    } catch (error) {
      console.error(
        "Session endpoint POST: Error or timeout during sign-out:",
        error
      );
      // Continue anyway since we've cleared cookies
    }

    const endTime = Date.now();
    console.log(`Session endpoint POST: Completed in ${endTime - startTime}ms`);
    return response;
  } catch (error) {
    console.error("Session endpoint POST: Unexpected error:", error);
    const endTime = Date.now();
    console.log(
      `Session endpoint POST: Failed with error in ${endTime - startTime}ms`
    );

    // Still return success since we've cleared cookies
    return response;
  }
}
