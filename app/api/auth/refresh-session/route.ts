import { createServerClient, type CookieOptions } from '@supabase/ssr'

export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET() {
  console.log('GET /api/auth/refresh-session: Starting request')
  
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set({ name, value, ...options })
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set({ name, value: '', ...options })
        },
      },
    }
  )

  try {
    // Get the current session first
    const { data: { session: currentSession }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.error('GET /api/auth/refresh-session: Error getting session:', sessionError)
      return NextResponse.json({ success: false, error: 'Failed to get session', details: sessionError.message }, { status: 401 })
    }
    
    if (!currentSession) {
      console.error('GET /api/auth/refresh-session: No current session found')
      return NextResponse.json({ success: false, error: 'No session to refresh' }, { status: 401 })
    }
    
    console.log(`GET /api/auth/refresh-session: Current session found for user ${currentSession.user.id}`)
    console.log(`GET /api/auth/refresh-session: Session expires at: ${new Date(currentSession.expires_at! * 1000).toISOString()}`)
    
    // Now refresh the session
    console.log('GET /api/auth/refresh-session: Attempting to refresh session')
    const { data, error } = await supabase.auth.refreshSession()
    
    if (error) {
      console.error('GET /api/auth/refresh-session: Error refreshing session:', error)
      return NextResponse.json({ success: false, error: 'Failed to refresh session', details: error.message }, { status: 401 })
    }
    
    if (data && data.session) {
      console.log(`GET /api/auth/refresh-session: Session refreshed successfully`)
      console.log(`GET /api/auth/refresh-session: New session expires at: ${new Date(data.session.expires_at! * 1000).toISOString()}`)
      
      // Also fetch the user's profile to ensure the roles are attached
      try {
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('id, email, roles')
          .eq('id', data.session.user.id)
          .single()
          
        if (profileError) {
          console.error('GET /api/auth/refresh-session: Error fetching user profile:', profileError)
        } else if (profile) {
          console.log(`GET /api/auth/refresh-session: User profile found with roles:`, profile.roles)
          
          // Update user metadata with roles if necessary
          if (profile.roles && Array.isArray(profile.roles) && profile.roles.length > 0) {
            const currentRoles = data.session.user.user_metadata?.roles || []
            if (JSON.stringify(currentRoles.sort()) !== JSON.stringify(profile.roles.sort())) {
              console.log('GET /api/auth/refresh-session: Updating user metadata with roles from profile')
              await supabase.auth.updateUser({
                data: { roles: profile.roles }
              })
            }
          }
        }
      } catch (profileError) {
        console.error('GET /api/auth/refresh-session: Exception during profile check:', profileError)
      }
      
      // Track refresh in cookies to help detect issues
      const refreshCount = parseInt(cookieStore.get('refresh_count')?.value || '0') + 1
      cookieStore.set('refresh_count', refreshCount.toString(), {
        path: '/',
        maxAge: 60 * 60 * 24, // 24 hours
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
      })
      
      cookieStore.set('last_refresh_time', Date.now().toString(), {
        path: '/',
        maxAge: 60 * 60 * 24, // 24 hours
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
      })
      
      return NextResponse.json({ 
        success: true, 
        session: {
          userId: data.session.user.id,
          expiresAt: data.session.expires_at,
          roles: data.session.user.user_metadata?.roles || []
        }
      })
    } else {
      console.error('GET /api/auth/refresh-session: No session returned after refresh')
      return NextResponse.json({ success: false, error: 'No session after refresh' }, { status: 401 })
    }
  } catch (error) {
    console.error('GET /api/auth/refresh-session: Unexpected error:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Unexpected error during session refresh',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 