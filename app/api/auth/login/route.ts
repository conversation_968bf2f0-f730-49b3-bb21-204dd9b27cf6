import { NextResponse } from "next/server";

export const runtime = 'nodejs'
import { cookies } from "next/headers";
import { createServerClient, type CookieOptions } from "@supabase/ssr";

export async function POST(request: Request) {
  console.log("Starting login process...");
  const cookieStore = cookies();

  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      console.error("Missing credentials");
      return NextResponse.json(
        { error: "Missing credentials" },
        { status: 400 }
      );
    }

    console.log(`Attempting login for email: ${email}`);
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value, ...options });
            } catch (error) {
              console.warn(`Failed to set cookie ${name}`, error);
            }
          },
          remove(name: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value: "", ...options });
            } catch (error) {
              console.warn(`Failed to remove cookie ${name}`, error);
            }
          },
        },
      }
    );

    // Sign in with Supabase
    console.log("Calling supabase.auth.signInWithPassword...");
    const {
      data: { session, user },
      error: signInError,
    } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (signInError) {
      console.error("Sign in error:", signInError);
      console.error("Sign in error details:", {
        status: signInError.status,
        message: signInError.message,
        name: signInError.name,
        code: signInError.code,
      });
      return NextResponse.json({ error: signInError.message }, { status: 401 });
    }

    if (!session?.user) {
      console.error("No session or user after sign in");
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 }
      );
    }

    console.log("Sign in successful, user ID:", user.id);
    console.log("User metadata:", user.app_metadata);
    console.log("User email:", user.email);

    // Get profile with retries
    let profile = null;
    let retryCount = 0;
    const MAX_RETRIES = 3;

    while (retryCount < MAX_RETRIES) {
      try {
        console.log(
          `Fetching profile for user ${user.id}, attempt ${retryCount + 1}`
        );
        const { data, error } = await supabase
          .from("profiles")
          .select("roles, full_name, avatar_url, first_name, last_name")
          .eq("id", user.id)
          .single();

        if (!error && data) {
          profile = data;
          console.log("Profile fetch successful:", profile);
          console.log("Profile roles from DB:", data.roles);
          console.log("Profile roles type:", typeof data.roles, Array.isArray(data.roles));
          break;
        }

        console.warn(`Profile fetch attempt ${retryCount + 1} failed:`, error);
        console.warn("Error details:", error?.message, error?.code);
        retryCount++;
        if (retryCount < MAX_RETRIES) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.warn(
          `Profile fetch attempt ${retryCount + 1} failed with exception:`,
          error
        );
        retryCount++;
        if (retryCount < MAX_RETRIES) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
    }

    // Use default values if profile fetch failed
    if (!profile) {
      console.warn("Using default profile values for user:", user.email);
      
      // Check if this is a known super admin user
      if (user.email === '<EMAIL>' || user.email === '<EMAIL>' || user.email === '<EMAIL>') {
        console.log("Detected super admin user, setting SUPER_ADMIN role");
        profile = {
          roles: ["SUPER_ADMIN"],
          full_name: user.email?.split("@")[0],
          avatar_url: null,
        };
      } else {
        profile = {
          roles: ["CLIENT"],
          full_name: user.email?.split("@")[0],
          avatar_url: null,
        };
      }
    }

    // Ensure roles is an array and handle null values
    if (!profile.roles || profile.roles.length === 0 || profile.roles[0] === null) {
      console.warn("Profile has no valid roles for user:", user.email);
      
      // Check if this is a known super admin user
      if (user.email === '<EMAIL>' || user.email === '<EMAIL>' || user.email === '<EMAIL>') {
        console.log("Setting SUPER_ADMIN role for known admin user");
        profile.roles = ["SUPER_ADMIN"];
      } else {
        console.warn("Defaulting to CLIENT role");
        profile.roles = ["CLIENT"];
      }
    }

    console.log("Login successful:", {
      user: user.email,
      roles: profile.roles,
    });

    // Create response with user data
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        roles: profile.roles,
        name: profile.full_name || user.email?.split("@")[0],
        image: profile.avatar_url,
      },
    });
  } catch (error) {
    console.error("Unexpected error during login:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
