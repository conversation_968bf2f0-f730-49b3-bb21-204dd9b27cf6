import { createServerClient, type CookieOptions } from '@supabase/ssr'

export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import type { Database } from '@/lib/database.types'

const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

export async function GET(request: Request) {
  try {
    console.log('GET /api/auth/fix-session - Starting request')
    const url = new URL(request.url)
    const redirectTo = url.searchParams.get('redirectTo') || '/'
    
    console.log('GET /api/auth/fix-session - Redirect URL:', redirectTo)
    console.log('GET /api/auth/fix-session - Full request URL:', request.url)
    
    // Check if we've fixed recently (within the last 5 seconds)
    const cookieStore = cookies()
    const lastFix = cookieStore.get('last_fix_time')
    const now = Date.now()
    
    if (lastFix) {
      console.log('GET /api/auth/fix-session - Found last_fix_time cookie:', lastFix.value)
      const lastFixTime = parseInt(lastFix.value, 10)
      const timeSinceLastFix = now - lastFixTime
      
      console.log(`GET /api/auth/fix-session - Time since last fix: ${timeSinceLastFix}ms`)
      
      // If we've fixed within the last 5 seconds, just redirect without fixing again
      if (timeSinceLastFix < 5000) { // 5 seconds
        console.log(`GET /api/auth/fix-session - Skipping fix, last fix was ${timeSinceLastFix}ms ago`)
        console.log(`GET /api/auth/fix-session - Redirecting to: ${redirectTo}`)
        return NextResponse.redirect(new URL(redirectTo, request.url))
      }
    } else {
      console.log('GET /api/auth/fix-session - No last_fix_time cookie found')
    }
    
    console.log('GET /api/auth/fix-session - Creating Supabase client')
    const supabase = createSupabaseClient()
    
    // Get the user session
    console.log('GET /api/auth/fix-session - Getting session')
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.error('GET /api/auth/fix-session - Error getting session:', sessionError)
    }
    
    // Check if there's a client session ID passed as a parameter
    const clientSessionId = url.searchParams.get('clientSessionId')
    console.log('GET /api/auth/fix-session - Client session ID from params:', clientSessionId)
    console.log('GET /api/auth/fix-session - Server session:', session ? `exists (${session.user.id})` : 'null')
    
    // Case 1: Both have sessions but they don't match
    if (session && clientSessionId && session.user.id !== clientSessionId) {
      console.log('GET /api/auth/fix-session - Session mismatch detected')
      console.log(`GET /api/auth/fix-session - Server: ${session.user.id}, Client: ${clientSessionId}`)
      
      // Sign out to clear the session
      console.log('GET /api/auth/fix-session - Signing out to clear mismatched session')
      await supabase.auth.signOut()
      
      // Redirect to login with the original redirect
      console.log(`GET /api/auth/fix-session - Redirecting to login with redirectTo=${redirectTo}`)
      return NextResponse.redirect(new URL(`/login?redirectTo=${encodeURIComponent(redirectTo)}`, request.url))
    }
    
    // Case 2: Server has session but client doesn't
    if (session && !clientSessionId) {
      console.log('GET /api/auth/fix-session - Server has session but client does not')
      
      // Refresh the session to ensure cookies are set properly
      console.log('GET /api/auth/fix-session - Refreshing session')
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('GET /api/auth/fix-session - Error refreshing session:', error)
        return NextResponse.redirect(new URL('/login?error=refresh_failed', request.url))
      }
      
      console.log('GET /api/auth/fix-session - Session refreshed successfully')
      
      // Set a cookie to track when we last fixed
      const response = NextResponse.redirect(new URL(redirectTo, request.url))
      response.cookies.set('last_fix_time', now.toString(), {
        path: '/',
        maxAge: 60 * 60, // 1 hour
        httpOnly: true,
        sameSite: 'lax'
      })
      
      console.log(`GET /api/auth/fix-session - Redirecting to: ${redirectTo}`)
      return response
    }
    
    // Case 3: Client has session but server doesn't
    if (!session && clientSessionId) {
      console.log('GET /api/auth/fix-session - Client has session but server does not')
      
      // Redirect to login with the original redirect
      console.log(`GET /api/auth/fix-session - Redirecting to login with redirectTo=${redirectTo}`)
      return NextResponse.redirect(new URL(`/login?redirectTo=${encodeURIComponent(redirectTo)}`, request.url))
    }
    
    // Case 4: Neither has session
    if (!session && !clientSessionId) {
      console.log('GET /api/auth/fix-session - Neither client nor server has session')
      
      // Redirect to login with the original redirect
      console.log(`GET /api/auth/fix-session - Redirecting to login with redirectTo=${redirectTo}`)
      return NextResponse.redirect(new URL(`/login?redirectTo=${encodeURIComponent(redirectTo)}`, request.url))
    }
    
    // Case 5: Both have matching sessions
    console.log('GET /api/auth/fix-session - Sessions match, no fix needed')
    
    // Set a cookie to track when we last fixed
    const response = NextResponse.redirect(new URL(redirectTo, request.url))
    response.cookies.set('last_fix_time', now.toString(), {
      path: '/',
      maxAge: 60 * 60, // 1 hour
      httpOnly: true,
      sameSite: 'lax'
    })
    
    console.log(`GET /api/auth/fix-session - Redirecting to: ${redirectTo}`)
    return response
  } catch (error) {
    console.error('Error in fix-session route:', error)
    const url = new URL(request.url)
    const redirectTo = url.searchParams.get('redirectTo') || '/'
    console.log(`GET /api/auth/fix-session - Error occurred, redirecting to login with redirectTo=${redirectTo}`)
    return NextResponse.redirect(new URL(`/login?error=fix_error&redirectTo=${encodeURIComponent(redirectTo)}`, request.url))
  }
} 