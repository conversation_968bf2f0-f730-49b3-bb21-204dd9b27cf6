import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST(req: NextRequest) {
  console.log('POST /api/auth/create-profile - Starting request')
  
  try {
    // Create a Supabase client
    console.log('POST /api/auth/create-profile - Creating Supabase client')
    const cookieStore = cookies()
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set({ name, value, ...options })
          },
          remove(name: string, options: any) {
            cookieStore.set({ name, value: '', ...options })
          },
        },
      }
    )
    
    // Get the current user
    console.log('POST /api/auth/create-profile - Getting user')
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError) {
      console.error('POST /api/auth/create-profile - Error getting user:', userError)
      return NextResponse.json({ error: 'Authentication error' }, { status: 401 })
    }
    
    if (!user) {
      console.log('POST /api/auth/create-profile - No user found')
      return NextResponse.json({ error: 'No authenticated user' }, { status: 401 })
    }
    
    console.log(`POST /api/auth/create-profile - User found: ${user.id}`)
    
    // Check if profile exists
    console.log('POST /api/auth/create-profile - Checking if profile exists')
    const { data: existingProfile, error: profileError } = await supabase
      .from('profiles')
      .select('id, roles')
      .eq('id', user.id)
      .single()
    
    if (profileError && profileError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('POST /api/auth/create-profile - Error checking profile:', profileError)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }
    
    // If profile exists, check if roles need updating
    if (existingProfile) {
      console.log('POST /api/auth/create-profile - Profile exists, checking roles')
      const email = user.email?.toLowerCase() || ''
      let roles = existingProfile.roles || []
      
      // Only update roles if they're empty
      if (!roles || roles.length === 0) {
        if (email.includes('affiliate')) {
          roles = ['AFFILIATE']
        } else if (email.includes('admin')) {
          roles = ['ADMIN']
        } else if (email.includes('event')) {
          roles = ['EVENT_MANAGER']
        } else {
          roles = ['CUSTOMER']
        }
        
        // Update profile with new roles
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ roles })
          .eq('id', user.id)
        
        if (updateError) {
          console.error('POST /api/auth/create-profile - Error updating roles:', updateError)
          return NextResponse.json({ error: 'Failed to update roles' }, { status: 500 })
        }
        
        console.log('POST /api/auth/create-profile - Updated roles:', roles)
      }
      
      return NextResponse.json({ profile: { id: user.id, roles } })
    }
    
    // Create new profile if it doesn't exist
    console.log('POST /api/auth/create-profile - Creating new profile')
    const email = user.email?.toLowerCase() || ''
    let roles = []
    
    if (email.includes('affiliate')) {
      roles = ['AFFILIATE']
    } else if (email.includes('admin')) {
      roles = ['ADMIN']
    } else if (email.includes('event')) {
      roles = ['EVENT_MANAGER']
    } else {
      roles = ['CUSTOMER']
    }
    
    const { error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: user.id,
        email: user.email,
        roles
      })
    
    if (insertError) {
      console.error('POST /api/auth/create-profile - Error creating profile:', insertError)
      return NextResponse.json({ error: 'Failed to create profile' }, { status: 500 })
    }
    
    console.log('POST /api/auth/create-profile - Created new profile with roles:', roles)
    return NextResponse.json({ profile: { id: user.id, roles } })
    
  } catch (error) {
    console.error('POST /api/auth/create-profile - Unexpected error:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
} 