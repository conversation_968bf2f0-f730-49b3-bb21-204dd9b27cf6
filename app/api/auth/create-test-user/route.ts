import { createClient } from '@supabase/supabase-js'

export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    console.log('Creating admin user...')
    
    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log('Service role key:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Present' : 'Missing')

    // Create user with admin API
    console.log('Attempting to create admin user with admin API...')
    const { data: userData, error: userError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'password123',
      email_confirm: true,
      user_metadata: { role: 'ADMIN' }
    })

    if (userError) {
      console.error('User creation error:', {
        message: userError.message,
        status: userError.status,
        name: userError.name
      })
      return NextResponse.json({ error: userError.message }, { status: 400 })
    }

    console.log('Admin user created successfully:', {
      id: userData.user.id,
      email: userData.user.email
    })

    // Create profile
    console.log('Creating admin profile...')
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: userData.user.id,
        email: '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User',
        role: 'ADMIN'
      })
      .select()
      .single()

    if (profileError) {
      console.error('Profile creation error:', profileError)
      return NextResponse.json({ error: profileError.message }, { status: 400 })
    }

    console.log('Admin profile created successfully:', profileData)

    return NextResponse.json({
      message: 'Admin user created successfully',
      user: userData.user,
      profile: profileData
    })

  } catch (error) {
    console.error('Error creating admin user:', error)
    return NextResponse.json(
      { 
        error: 'Failed to create admin user',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
} 