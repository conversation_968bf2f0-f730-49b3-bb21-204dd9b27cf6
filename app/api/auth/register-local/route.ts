import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'pg';

// Use direct PostgreSQL connection for reliable user creation
async function createDatabaseConnection() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@127.0.0.1:54322/postgres'
  });
  await client.connect();
  return client;
}

export async function POST(request: NextRequest) {
  let client;
  try {
    const { email, password, role = 'CLIENT' } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Create user directly using SQL (this method we know works)
    client = await createDatabaseConnection();
    
    const result = await client.query(`
      INSERT INTO auth.users (
        id, instance_id, email, encrypted_password, email_confirmed_at, 
        created_at, updated_at, aud, role, raw_user_meta_data
      ) VALUES (
        gen_random_uuid(),
        '00000000-0000-0000-0000-000000000000'::uuid,
        $1,
        crypt($2, gen_salt('bf', 6)),
        NOW(),
        NOW(),
        NOW(),
        'authenticated',
        'authenticated',
        $3
      ) RETURNING id, email, created_at;
    `, [
      email,
      password,
      JSON.stringify({
        full_name: email.split('@')[0],
        role: role
      })
    ]);

    const userData = result.rows[0];
    
    return NextResponse.json({
      success: true,
      user: {
        id: userData.id,
        email: userData.email,
        created_at: userData.created_at
      }
    });

  } catch (error) {
    console.error('Registration exception:', error);
    return NextResponse.json(
      { error: 'Registration failed' },
      { status: 500 }
    );
  } finally {
    if (client) {
      await client.end();
    }
  }
}