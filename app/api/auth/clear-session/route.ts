import { NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST() {
  try {
    const cookieStore = await cookies()
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            try {
              cookieStore.set({ name, value, ...options })
            } catch (error) {
              // Ignore errors from Server Components
            }
          },
          remove(name: string, options: any) {
            try {
              cookieStore.set({ name, value: '', ...options })
            } catch (error) {
              // Ignore errors from Server Components
            }
          },
        },
      }
    )

    // Sign out to clear the session
    await supabase.auth.signOut()

    return NextResponse.json({ 
      success: true, 
      message: 'Session cleared successfully' 
    })
  } catch (error) {
    console.error('Error clearing session:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to clear session' 
    }, { status: 500 })
  }
}
