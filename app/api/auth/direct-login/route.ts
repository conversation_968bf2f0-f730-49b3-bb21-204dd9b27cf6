import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'

export async function POST(req: NextRequest) {
  console.log('POST /api/auth/direct-login - Starting request')
  
  try {
    // Parse request body
    const body = await req.json()
    const { email, password, role } = body
    
    if (!email || !password) {
      console.error('POST /api/auth/direct-login - Missing email or password')
      return NextResponse.json({ error: 'Missing email or password' }, { status: 400 })
    }
    
    console.log(`POST /api/auth/direct-login - Attempting login for ${email}`)
    
    // Create a Supabase client
    const supabase = createClient()
    
    // Sign in with email and password
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) {
      console.error('POST /api/auth/direct-login - Login error:', error)
      return NextResponse.json({ error: error.message }, { status: 401 })
    }
    
    console.log('POST /api/auth/direct-login - Login successful')
    
    // Set a cookie to track the last refresh time
    cookies().set('last_refresh_time', Date.now().toString(), {
      maxAge: 60 * 60 * 24, // 24 hours
      path: '/',
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
    })
    
    // Create or update profile
    let userRole = role || 'CUSTOMER'
    
    // Special <NAME_EMAIL>
    if (email === '<EMAIL>') {
      userRole = 'CUSTOMER'
    } else if (email.includes('admin')) {
      userRole = 'ADMIN'
    } else if (email.includes('affiliate')) {
      userRole = 'AFFILIATE'
    } else if (email.includes('event')) {
      userRole = 'EVENT_MANAGER'
    }
    
    console.log(`POST /api/auth/direct-login - Setting role to ${userRole}`)
    
    // Check if profile exists
    const { data: existingProfile, error: profileError } = await supabase
      .from('profiles')
      .select('id, roles')
      .eq('id', data.user.id)
      .single()
    
    if (profileError && profileError.code !== 'PGRST116') {
      console.error('POST /api/auth/direct-login - Error checking profile:', profileError)
      // Continue anyway
    }
    
    if (existingProfile) {
      console.log('POST /api/auth/direct-login - Profile exists, updating roles if needed')
      
      // Check if roles need to be updated
      const currentRoles = existingProfile.roles || []
      const roles = [userRole]
      const needsUpdate = !currentRoles.includes(userRole)
      
      if (needsUpdate) {
        console.log(`POST /api/auth/direct-login - Updating roles from ${JSON.stringify(currentRoles)} to ${JSON.stringify(roles)}`)
        
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ roles })
          .eq('id', data.user.id)
        
        if (updateError) {
          console.error('POST /api/auth/direct-login - Error updating profile:', updateError)
          // Continue anyway
        } else {
          console.log('POST /api/auth/direct-login - Profile updated successfully')
        }
      } else {
        console.log('POST /api/auth/direct-login - No role update needed')
      }
    } else {
      // Create new profile
      console.log('POST /api/auth/direct-login - Creating new profile with role:', userRole)
      
      const { error: createError } = await supabase
        .from('profiles')
        .insert({
          id: data.user.id,
          email: data.user.email,
          roles: [userRole]
        })
      
      if (createError) {
        console.error('POST /api/auth/direct-login - Error creating profile:', createError)
        // Continue anyway
      } else {
        console.log('POST /api/auth/direct-login - Profile created successfully')
      }
    }
    
    // Determine redirect URL based on role
    let redirectUrl = '/customer/dashboard'
    
    if (userRole === 'ADMIN') {
      redirectUrl = '/admin/dashboard'
    } else if (userRole === 'AFFILIATE') {
      redirectUrl = '/affiliate/dashboard'
    } else if (userRole === 'EVENT_MANAGER') {
      redirectUrl = '/event-manager/dashboard'
    }
    
    console.log(`POST /api/auth/direct-login - Redirecting to ${redirectUrl}`)
    
    return NextResponse.json({ 
      success: true, 
      user: data.user,
      redirectUrl
    })
  } catch (error) {
    console.error('POST /api/auth/direct-login - Unexpected error:', error)
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 })
  }
} 