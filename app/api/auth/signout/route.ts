import { cookies } from 'next/headers'

export const runtime = 'nodejs'
import { NextResponse } from 'next/server'

// This file is maintained for backward compatibility
// New code should use the camelCase endpoint at /api/auth/signOut

export async function POST() {
  try {
    const cookieStore = cookies()
    
    // Clear all cookies
    const allCookies = cookieStore.getAll()
    allCookies.forEach(cookie => {
      cookieStore.delete(cookie.name)
    })
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error signing out:', error)
    return NextResponse.json({ error: 'Failed to sign out' }, { status: 500 })
  }
}

// Redirect GET requests to login
export async function GET(request: Request) {
  try {
    // Redirect to login
    const redirectUrl = new URL('/login', request.url)
    return NextResponse.redirect(redirectUrl)
  } catch (error) {
    console.error('Error handling sign-out request:', error)
    // Fallback to direct login redirect
    const redirectUrl = new URL('/login', request.url)
    return NextResponse.redirect(redirectUrl)
  }
}
