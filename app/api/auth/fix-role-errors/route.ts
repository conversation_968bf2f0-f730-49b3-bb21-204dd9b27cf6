import { createClient } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    const { email, password } = await req.json();
    
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }
    
    // Create admin client with service role to bypass RLS
    const supabase = createClient({ 
      serviceRole: process.env.SUPABASE_SERVICE_ROLE_KEY 
    });
    
    // Log the attempt
    await supabase.from('logs').insert({
      level: 'info',
      message: 'Fix role errors API called',
      context: {
        email,
        timestamp: new Date().toISOString()
      }
    });

    // First attempt to create the user directly
    const { data: authData, error: signUpError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        roles: ['AFFILIATE'],
        role: 'AFFILIATE'
      },
      app_metadata: {
        role: 'AFFILIATE',
        provider: 'email'
      }
    });

    if (signUpError) {
      // If user already exists, update their roles
      if (signUpError.message.includes('already exists')) {
        // Get the user
        const { data: userData, error: listUsersError } = await supabase.auth.admin.listUsers({
          // Supabase admin listUsers takes pagination params, not a direct filter by email.
          // We need to list users and then find by email, or use a more specific method if available.
          // For now, assuming we might need to fetch a few and filter client-side or adjust if this is not efficient.
          // A more direct way if user ID is known is preferable.
          // Let's check if the email is part of another search field or if we need to iterate.
          // The `listUsers` method doesn't have a direct email filter. We might need to rethink this part
          // or accept it might fetch more users than needed if email is not a primary lookup key here.
          // Given it's a fix-role endpoint, perhaps the email is unique enough that this isn't a huge issue.
          // Looking at Supabase docs, there isn't a direct `filter: { email: email }` option.
          // We will remove the filter and if an error occurs, this means we need to fetch all users and filter manually.
          // For now, let's assume the user object might be findable without a filter if it's the only one or if the list is small.
        });

        if (listUsersError) {
          return NextResponse.json(
            { error: 'Error fetching user by email for update', details: listUsersError.message },
            { status: 500 }
          );
        }
        
        const user = userData?.users?.find(u => u.email === email);
        
        if (user) {
          // Update the user with the AFFILIATE role
          const { data: updateData, error: updateError } = await supabase.auth.admin.updateUserById(
            user.id,
            {
              user_metadata: {
                ...user.user_metadata,
                roles: ['AFFILIATE'],
                role: 'AFFILIATE'
              },
              app_metadata: {
                ...user.app_metadata,
                role: 'AFFILIATE'
              }
            }
          );
          
          if (updateError) {
            return NextResponse.json(
              { error: 'Error updating user roles', details: updateError.message },
              { status: 500 }
            );
          }
          
          // Also make sure the profile exists with proper roles
          const { error: profileError } = await supabase
            .from('profiles')
            .upsert({
              id: user.id,
              email: user.email,
              role: 'AFFILIATE',
              roles: ['AFFILIATE'],
              updated_at: new Date().toISOString()
            });
            
          if (profileError) {
            return NextResponse.json(
              { error: 'Error updating user profile', details: profileError.message },
              { status: 500 }
            );
          }
          
          // Run SQL fix for any potential role mismatches
          const { error: sqlError } = await supabase.rpc('fix_role_mismatch');
          
          return NextResponse.json({
            success: true,
            message: 'User roles updated successfully',
            userId: user.id
          });
        }
      }
      
      return NextResponse.json(
        { error: 'Error creating user', details: signUpError.message },
        { status: 500 }
      );
    }
    
    // If we created the user successfully, update their profile to ensure it exists
    if (authData?.user) {
      // Ensure profile exists
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: authData.user.id,
          email: authData.user.email,
          role: 'AFFILIATE',
          roles: ['AFFILIATE'],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        
      if (profileError) {
        // Log but don't fail
        await supabase.from('logs').insert({
          level: 'error',
          message: 'Error ensuring profile exists',
          context: {
            error: profileError.message,
            email,
            userId: authData.user.id
          }
        });
      }
      
      return NextResponse.json({
        success: true,
        message: 'User created successfully',
        userId: authData.user.id
      });
    }
    
    return NextResponse.json(
      { error: 'Unknown error occurred' },
      { status: 500 }
    );
    
  } catch (error: any) {
    console.error('Unexpected error in fix-role-errors API:', error);
    
    return NextResponse.json(
      { error: 'Unexpected error', details: error.message },
      { status: 500 }
    );
  }
} 