import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'

export async function GET(request: NextRequest) {
  console.log('Clear cookies GET endpoint called');

  // Create a redirect response to login page
  const response = NextResponse.redirect(new URL('/login?cleared=true', request.url));

  // List of all possible auth cookie names to clear
  const cookiesToClear = [
    'sb-127-auth-token',
    'sb-localhost-auth-token',
    'sb-127-auth-token-code-verifier',
    'supabase-auth-token',
    'supabase.auth.token',
    'sb-auth-token',
    'wwms-auth-session',
    'session_id',
    'refresh_attempts',
    'last_refresh_time',
    'refresh_count'
  ];

  // Clear each cookie
  cookiesToClear.forEach(cookieName => {
    response.cookies.set({
      name: cookieName,
      value: '',
      path: '/',
      expires: new Date(0),
      httpOnly: false, // Allow client-side clearing
      secure: false,   // Allow on localhost
      sameSite: 'lax'
    });
    console.log(`Cleared cookie: ${cookieName}`);
  });

  return response;
}

export async function POST(request: NextRequest) {
  try {
    const response = NextResponse.json({ success: true, message: 'Cookies cleared' });
    
    // List of all possible auth cookies to clear
    const cookiesToClear = [
      'sb-127-auth-token',
      'sb-localhost-auth-token', 
      'sb-127-auth-token-code-verifier',
      'supabase-auth-token',
      'supabase.auth.token',
      'sb-auth-token',
      'wwms-auth-session',
      'session_id',
      'refresh_attempts',
      'last_refresh_time',
      'refresh_count'
    ];
    
    // Clear each cookie
    cookiesToClear.forEach(cookieName => {
      response.cookies.set({
        name: cookieName,
        value: '',
        path: '/',
        maxAge: 0,
        httpOnly: false, // Allow client-side clearing
        secure: false,   // Allow on localhost
        sameSite: 'lax'
      });
    });
    
    console.log('Cleared auth cookies:', cookiesToClear);
    
    return response;
  } catch (error) {
    console.error('Error clearing cookies:', error);
    return NextResponse.json({ success: false, error: 'Failed to clear cookies' }, { status: 500 });
  }
}
