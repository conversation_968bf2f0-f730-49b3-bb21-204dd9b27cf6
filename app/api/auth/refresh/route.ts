import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

import { cookies } from 'next/headers'
import { createClient } from '@/lib/supabase/server'

// Clear all auth cookies
function clearAuthCookies() {
  const cookiesToClear = [
    'sb-access-token',
    'sb-refresh-token',
    'last_refresh_time',
    'refresh_attempts',
    'refresh_count',
    'sb-127-auth-token',
    'supabase.auth.token',
    'wwms-auth-session',
    'session_id'
  ];
  
  cookiesToClear.forEach(name => {
    try {
      cookies().delete(name);
      console.log(`Deleted cookie: ${name}`);
    } catch (error) {
      console.error(`Error deleting cookie ${name}:`, error);
    }
  });
}

export async function GET(req: NextRequest) {
  console.log('GET /api/auth/refresh - Starting request')
  
  try {
    // Get the redirectTo parameter
    const redirectTo = req.nextUrl.searchParams.get('redirectTo') || '/'
    
    // Special case: if redirectTo is /login or contains error=refresh_loop, redirect to root
    if (redirectTo === '/login' || 
        redirectTo.startsWith('/login?') || 
        redirectTo.includes('error=refresh_loop')) {
      console.log('GET /api/auth/refresh - Detected potential redirect loop to /login, redirecting to / instead')
      return NextResponse.redirect(new URL('/', req.url))
    }
    
    // Check if we've refreshed recently using the cookie
    const lastRefreshTime = cookies().get('last_refresh_time')?.value
    
    if (lastRefreshTime) {
      const timeSinceLastRefresh = Date.now() - parseInt(lastRefreshTime)
      console.log(`GET /api/auth/refresh - Found last_refresh_time cookie: ${lastRefreshTime}`)
      console.log(`GET /api/auth/refresh - Time since last refresh: ${timeSinceLastRefresh}ms`)
      
      // If we've refreshed in the last 3 seconds, skip this refresh
      const MIN_REFRESH_INTERVAL = 3000 // 3 seconds
      
      if (timeSinceLastRefresh < MIN_REFRESH_INTERVAL) {
        console.log(`GET /api/auth/refresh - Skipping refresh, last refresh was ${timeSinceLastRefresh}ms ago`)
        
        // Set a cookie to track refresh attempts
        const refreshAttempts = parseInt(cookies().get('refresh_attempts')?.value || '0') + 1
        
        // If we've had too many refresh attempts in a short time, clear auth to break the loop
        if (refreshAttempts > 2) { // Reduced from 3 to 2
          console.log(`GET /api/auth/refresh - Too many refresh attempts (${refreshAttempts}), clearing auth cookies`)
          
          // Clear auth cookies to break the loop
          clearAuthCookies();
          
          // Redirect to login
          return NextResponse.redirect(new URL('/login?error=refresh_loop', req.url))
        }
        
        // Set the refresh attempts cookie
        cookies().set('refresh_attempts', refreshAttempts.toString(), {
          maxAge: 60, // 1 minute
          path: '/',
          sameSite: 'lax',
          secure: process.env.NODE_ENV === 'production',
        })
        
        // Redirect to the original URL
        return NextResponse.redirect(new URL(redirectTo, req.url))
      }
      
      // Reset refresh attempts if we're proceeding with a refresh
      cookies().set('refresh_attempts', '0', {
        maxAge: 60, // 1 minute
        path: '/',
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
      })
      
      // Reset refresh count
      cookies().set('refresh_count', '0', {
        maxAge: 60, // 1 minute
        path: '/',
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
      })
    }
    
    try {
      // Create a Supabase client
      console.log('GET /api/auth/refresh - Creating Supabase client');
      const supabase = createClient();
      
      // Get the current session
      console.log('GET /api/auth/refresh - Getting session');
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('GET /api/auth/refresh - Error getting session:', sessionError);
        clearAuthCookies();
        return NextResponse.redirect(new URL('/login?error=session', req.url));
      }
      
      if (session) {
        console.log(`GET /api/auth/refresh - Session found for user: ${session.user.id}`);
        console.log(`GET /api/auth/refresh - Session expires at: ${new Date(session.expires_at! * 1000).toISOString()}`);
        
        try {
          // Refresh the session
          console.log('GET /api/auth/refresh - Refreshing session');
          const { data, error } = await supabase.auth.refreshSession();
          
          if (error) {
            console.error('GET /api/auth/refresh - Error refreshing session:', error);
            clearAuthCookies();
            return NextResponse.redirect(new URL('/login?error=refresh', req.url));
          }
          
          if (data.session) {
            console.log(`GET /api/auth/refresh - Session refreshed successfully`);
            console.log(`GET /api/auth/refresh - New session expires at: ${new Date(data.session.expires_at! * 1000).toISOString()}`);
            
            // Set a cookie to track the last refresh time
            cookies().set('last_refresh_time', Date.now().toString(), {
              maxAge: 60 * 60 * 24, // 24 hours
              path: '/',
              sameSite: 'lax',
              secure: process.env.NODE_ENV === 'production',
            });
            
            console.log(`GET /api/auth/refresh - Set last_refresh_time cookie to: ${Date.now()}`);
          } else {
            console.log('GET /api/auth/refresh - No session data returned after refresh');
          }
        } catch (error) {
          console.error('GET /api/auth/refresh - Unexpected error refreshing session:', error);
          clearAuthCookies();
          return NextResponse.redirect(new URL('/login?error=refresh_exception', req.url));
        }
      } else {
        console.log('GET /api/auth/refresh - No session found, redirecting to login');
        clearAuthCookies();
        return NextResponse.redirect(new URL('/login', req.url));
      }
      
      console.log(`GET /api/auth/refresh - Redirecting to: ${redirectTo}`);
      return NextResponse.redirect(new URL(redirectTo, req.url));
    } catch (error) {
      console.error('GET /api/auth/refresh - Error creating Supabase client or getting session:', error);
      clearAuthCookies();
      return NextResponse.redirect(new URL('/login?error=supabase', req.url));
    }
  } catch (error) {
    console.error('GET /api/auth/refresh - Critical error:', error);
    clearAuthCookies();
    return NextResponse.redirect(new URL('/login?error=critical', req.url));
  }
} 