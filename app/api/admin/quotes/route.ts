import { createServerClient, type CookieOptions } from "@supabase/ssr";

export const runtime = 'nodejs'
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
/**
 * GET /api/admin/quotes
 * Admin endpoint to fetch all quotes with comprehensive data
 */
export async function GET(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, "", options);
        },
      },
    }
  );

  try {
    // Get request parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get("status");
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");

    // Auth check - verify user is authenticated
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json(
        { error: "User not authenticated" },
        { status: 401 }
      );
    }

    // Build query for quotes
    let query = supabase
      .from("quotes")
      .select(
        `
        *,
        quote_affiliate_offers(
          id,
          company_id,
          rate_amount,
          currency,
          status,
          notes,
          expires_at,
          created_at,
          updated_at,
          affiliate_companies(
            id,
            name
          )
        )
        `
      )
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply status filter if provided
    if (status) {
      query = query.eq("status", status);
    }

    // Execute query
    const { data: quotes, error: quotesError, count } = await query;

    if (quotesError) {
      console.error("Error fetching quotes:", quotesError);
      return NextResponse.json(
        { error: "Failed to fetch quotes" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      quotes: quotes || [],
      count: count || 0,
      hasMore: (count || 0) > offset + limit,
    });
  } catch (error) {
    console.error("Error in GET /api/admin/quotes:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}
