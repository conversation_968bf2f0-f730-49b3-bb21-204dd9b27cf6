import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    // Create Supabase client with service role key for admin operations
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookies().get(name)?.value
          },
        },
      }
    )

    console.log('Running RLS policy fix for quote_affiliate_offers...')

    // Execute SQL commands directly
    try {
      // Drop restrictive policy
      await supabase.rpc('exec_sql', {
        sql: `DROP POLICY IF EXISTS "Users can submit counter-offers for their company" ON public.quote_affiliate_offers;`
      })

      // Create permissive policy for authenticated users
      const { error: policyError } = await supabase.rpc('exec_sql', {
        sql: `
          CREATE POLICY "Authenticated users can create quote offers"
          ON public.quote_affiliate_offers
          FOR INSERT
          WITH CHECK (auth.uid() IS NOT NULL);
        `
      })

      if (policyError) {
        console.error('Error creating insert policy:', policyError)
      }

      // Create policy for viewing offers
      const { error: selectPolicyError } = await supabase.rpc('exec_sql', {
        sql: `
          CREATE POLICY "Users can view offers for their quotes"
          ON public.quote_affiliate_offers
          FOR SELECT
          USING (
            EXISTS (
              SELECT 1 FROM quotes
              WHERE quotes.id = quote_affiliate_offers.quote_id
              AND quotes.customer_id = auth.uid()
            )
          );
        `
      })

      if (selectPolicyError) {
        console.error('Error creating select policy:', selectPolicyError)
      }

    } catch (sqlError) {
      console.error('Error executing SQL:', sqlError)
      return NextResponse.json({ error: 'Failed to execute SQL', details: sqlError }, { status: 500 })
    }

    console.log('RLS policies updated successfully')

    return NextResponse.json({
      success: true,
      message: 'RLS policies for quote_affiliate_offers updated successfully'
    })

  } catch (error) {
    console.error('Error in migration:', error)
    return NextResponse.json(
      { error: 'Migration failed', details: error },
      { status: 500 }
    )
  }
}
