import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * GET /api/admin/messages/[threadId]
 * Get messages for a specific thread
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { threadId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { threadId } = params;

    // Get thread details with messages
    const { data: thread, error: threadError } = await supabase
      .from('message_threads')
      .select(`
        id,
        title,
        type,
        priority,
        status,
        created_by,
        assigned_to,
        related_entity_type,
        related_entity_id,
        organization_id,
        tags,
        metadata,
        created_at,
        updated_at,
        creator:profiles!message_threads_created_by_fkey(first_name, last_name, email),
        assignee:profiles!message_threads_assigned_to_fkey(first_name, last_name, email)
      `)
      .eq('id', threadId)
      .single();

    if (threadError) {
      console.error('Error fetching thread:', threadError);
      return NextResponse.json({ error: 'Thread not found' }, { status: 404 });
    }

    // Get messages for the thread
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select(`
        id,
        content,
        message_type,
        attachments,
        metadata,
        is_internal,
        read_by,
        created_at,
        updated_at,
        sender:profiles!messages_sender_id_fkey(first_name, last_name, email)
      `)
      .eq('thread_id', threadId)
      .order('created_at', { ascending: true });

    if (messagesError) {
      console.error('Error fetching messages:', messagesError);
      return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 });
    }

    // Get participants
    const { data: participants, error: participantsError } = await supabase
      .from('message_participants')
      .select(`
        id,
        role,
        joined_at,
        last_read_at,
        notifications_enabled,
        user:profiles!message_participants_user_id_fkey(first_name, last_name, email)
      `)
      .eq('thread_id', threadId);

    if (participantsError) {
      console.error('Error fetching participants:', participantsError);
    }

    // Mark messages as read for current user
    const unreadMessages = messages?.filter(msg => 
      !msg.read_by || !msg.read_by[session.user.id]
    ) || [];

    if (unreadMessages.length > 0) {
      const updates = unreadMessages.map(msg => ({
        id: msg.id,
        read_by: {
          ...msg.read_by,
          [session.user.id]: new Date().toISOString()
        }
      }));

      await supabase
        .from('messages')
        .upsert(updates);

      // Update participant's last read timestamp
      await supabase
        .from('message_participants')
        .update({ last_read_at: new Date().toISOString() })
        .eq('thread_id', threadId)
        .eq('user_id', session.user.id);
    }

    return NextResponse.json({
      success: true,
      data: {
        thread,
        messages: messages || [],
        participants: participants || []
      }
    });

  } catch (error) {
    console.error('Get thread messages error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch thread messages' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/messages/[threadId]
 * Add a new message to the thread
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { threadId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { threadId } = params;
    const body = await request.json();
    const { content, messageType = 'text', attachments = [], isInternal = false } = body;

    if (!content) {
      return NextResponse.json({ error: 'Message content is required' }, { status: 400 });
    }

    // Verify user has access to this thread
    const { data: thread, error: threadError } = await supabase
      .from('message_threads')
      .select('id, created_by, assigned_to')
      .eq('id', threadId)
      .single();

    if (threadError) {
      return NextResponse.json({ error: 'Thread not found' }, { status: 404 });
    }

    // Check if user is participant or admin
    const { data: participant } = await supabase
      .from('message_participants')
      .select('id')
      .eq('thread_id', threadId)
      .eq('user_id', session.user.id)
      .single();

    const isThreadOwner = thread.created_by === session.user.id || thread.assigned_to === session.user.id;
    
    if (!participant && !isThreadOwner) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Create message
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert({
        thread_id: threadId,
        sender_id: session.user.id,
        content,
        message_type: messageType,
        attachments,
        is_internal: isInternal,
        read_by: {
          [session.user.id]: new Date().toISOString()
        }
      })
      .select(`
        id,
        content,
        message_type,
        attachments,
        is_internal,
        created_at,
        sender:profiles!messages_sender_id_fkey(first_name, last_name, email)
      `)
      .single();

    if (messageError) {
      console.error('Error creating message:', messageError);
      return NextResponse.json({ error: 'Failed to create message' }, { status: 500 });
    }

    // Update thread's updated_at timestamp
    await supabase
      .from('message_threads')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', threadId);

    return NextResponse.json({
      success: true,
      data: { message }
    });

  } catch (error) {
    console.error('Create message error:', error);
    return NextResponse.json(
      { error: 'Failed to create message' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/messages/[threadId]
 * Update thread properties (status, assignment, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { threadId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { threadId } = params;
    const body = await request.json();
    const { status, assignedTo, priority, tags } = body;

    // Check admin privileges
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];
    const isAdmin = userRoles.includes('SUPER_ADMIN') || userRoles.includes('TNC_ADMIN');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Build update object
    const updates: any = {};
    if (status) updates.status = status;
    if (assignedTo !== undefined) updates.assigned_to = assignedTo;
    if (priority) updates.priority = priority;
    if (tags) updates.tags = tags;

    const { data: thread, error: updateError } = await supabase
      .from('message_threads')
      .update(updates)
      .eq('id', threadId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating thread:', updateError);
      return NextResponse.json({ error: 'Failed to update thread' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: { thread }
    });

  } catch (error) {
    console.error('Update thread error:', error);
    return NextResponse.json(
      { error: 'Failed to update thread' },
      { status: 500 }
    );
  }
}
