import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * GET /api/admin/messages
 * Get message threads for admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin privileges
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];
    const isAdmin = userRoles.includes('SUPER_ADMIN') || userRoles.includes('TNC_ADMIN');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const priority = searchParams.get('priority');
    const assignedTo = searchParams.get('assignedTo');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query
    let query = supabase
      .from('message_threads')
      .select(`
        id,
        title,
        type,
        priority,
        status,
        created_by,
        assigned_to,
        related_entity_type,
        related_entity_id,
        organization_id,
        tags,
        created_at,
        updated_at,
        creator:profiles!message_threads_created_by_fkey(first_name, last_name, email),
        assignee:profiles!message_threads_assigned_to_fkey(first_name, last_name, email),
        messages(
          id,
          content,
          message_type,
          is_internal,
          created_at,
          sender:profiles!messages_sender_id_fkey(first_name, last_name, email)
        )
      `)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (status) query = query.eq('status', status);
    if (type) query = query.eq('type', type);
    if (priority) query = query.eq('priority', priority);
    if (assignedTo) query = query.eq('assigned_to', assignedTo);

    const { data: threads, error: threadsError } = await query;

    if (threadsError) {
      console.error('Error fetching message threads:', threadsError);
      return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 });
    }

    // Get unread counts
    const { data: unreadCounts } = await supabase
      .rpc('get_unread_message_counts', {
        p_user_id: session.user.id
      });

    return NextResponse.json({
      success: true,
      data: {
        threads: threads || [],
        unreadCounts: unreadCounts || { total: 0, byType: {}, byPriority: {} },
        pagination: {
          limit,
          offset,
          hasMore: (threads?.length || 0) === limit
        }
      }
    });

  } catch (error) {
    console.error('Admin messages error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch admin messages' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/messages
 * Create a new message thread
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin privileges
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];
    const isAdmin = userRoles.includes('SUPER_ADMIN') || userRoles.includes('TNC_ADMIN');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const {
      title,
      type,
      priority = 'normal',
      assignedTo,
      relatedEntityType,
      relatedEntityId,
      organizationId,
      tags = [],
      initialMessage,
      participants = []
    } = body;

    // Validate required fields
    if (!title || !type || !initialMessage) {
      return NextResponse.json(
        { error: 'Missing required fields: title, type, initialMessage' },
        { status: 400 }
      );
    }

    // Create thread
    const { data: thread, error: threadError } = await supabase
      .from('message_threads')
      .insert({
        title,
        type,
        priority,
        created_by: session.user.id,
        assigned_to: assignedTo,
        related_entity_type: relatedEntityType,
        related_entity_id: relatedEntityId,
        organization_id: organizationId,
        tags
      })
      .select()
      .single();

    if (threadError) {
      console.error('Error creating message thread:', threadError);
      return NextResponse.json({ error: 'Failed to create thread' }, { status: 500 });
    }

    // Add initial message
    const { error: messageError } = await supabase
      .from('messages')
      .insert({
        thread_id: thread.id,
        sender_id: session.user.id,
        content: initialMessage,
        message_type: 'text'
      });

    if (messageError) {
      console.error('Error creating initial message:', messageError);
      return NextResponse.json({ error: 'Failed to create initial message' }, { status: 500 });
    }

    // Add participants
    const participantInserts = [
      // Creator is always a participant
      {
        thread_id: thread.id,
        user_id: session.user.id,
        role: 'admin'
      },
      // Add assignee if different from creator
      ...(assignedTo && assignedTo !== session.user.id ? [{
        thread_id: thread.id,
        user_id: assignedTo,
        role: 'admin'
      }] : []),
      // Add additional participants
      ...participants.map((participantId: string) => ({
        thread_id: thread.id,
        user_id: participantId,
        role: 'participant'
      }))
    ];

    const { error: participantsError } = await supabase
      .from('message_participants')
      .insert(participantInserts);

    if (participantsError) {
      console.error('Error adding participants:', participantsError);
      // Don't fail the request for this, just log the error
    }

    return NextResponse.json({
      success: true,
      data: { thread }
    });

  } catch (error) {
    console.error('Create message thread error:', error);
    return NextResponse.json(
      { error: 'Failed to create message thread' },
      { status: 500 }
    );
  }
}
