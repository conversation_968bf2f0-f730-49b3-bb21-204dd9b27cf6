import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * GET /api/admin/email-templates
 * Get email templates for admin management
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin privileges
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];
    const isAdmin = userRoles.includes('SUPER_ADMIN') || userRoles.includes('TNC_ADMIN');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const type = searchParams.get('type');
    const isActive = searchParams.get('is_active');

    // Build query
    let query = supabase
      .from('email_templates')
      .select('*')
      .order('updated_at', { ascending: false });

    // Apply filters
    if (category && category !== 'all') query = query.eq('category', category);
    if (type) query = query.eq('type', type);
    if (isActive !== null) query = query.eq('is_active', isActive === 'true');

    const { data: templates, error: templatesError } = await query;

    if (templatesError) {
      console.error('Error fetching email templates:', templatesError);
      return NextResponse.json({ error: 'Failed to fetch templates' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: {
        templates: templates || []
      }
    });

  } catch (error) {
    console.error('Email templates error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email templates' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/email-templates
 * Create a new email template
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin privileges
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];
    const isAdmin = userRoles.includes('SUPER_ADMIN') || userRoles.includes('TNC_ADMIN');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { name, subject, content, type, category, is_active = true } = body;

    // Validate required fields
    if (!name || !subject || !content || !type || !category) {
      return NextResponse.json(
        { error: 'Missing required fields: name, subject, content, type, category' },
        { status: 400 }
      );
    }

    // Extract variables from content
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const variables: string[] = [];
    let match;
    while ((match = variableRegex.exec(content)) !== null) {
      if (!variables.includes(match[1])) {
        variables.push(match[1]);
      }
    }

    // Create template
    const { data: template, error: createError } = await supabase
      .from('email_templates')
      .insert({
        name,
        subject,
        content,
        type,
        category,
        variables,
        is_active,
        created_by: session.user.id
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating email template:', createError);
      return NextResponse.json({ error: 'Failed to create template' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: { template }
    });

  } catch (error) {
    console.error('Create email template error:', error);
    return NextResponse.json(
      { error: 'Failed to create email template' },
      { status: 500 }
    );
  }
}
