import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'

interface JWTPayload {
  userId: string;
  email: string;
  type: string;
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Authorization token required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    let decoded: JWTPayload

    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as JWTPayload
    } catch (jwtError) {
      return NextResponse.json(
        { success: false, message: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const bookingData = await request.json()

    // Validate required fields
    const requiredFields = ['serviceType', 'pickupLocation', 'date', 'time', 'passengers']
    for (const field of requiredFields) {
      if (!bookingData[field]) {
        return NextResponse.json(
          { success: false, message: `${field} is required` },
          { status: 400 }
        )
      }
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Generate a unique reference number
    const referenceNumber = `PUB-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`

    // Create the booking record
    const { data: booking, error: bookingError } = await supabase
      .from('quotes')
      .insert({
        reference_number: referenceNumber,
        customer_id: decoded.userId,
        service_type: bookingData.serviceType,
        vehicle_type: bookingData.vehicleType || 'Standard',
        pickup_location: bookingData.pickupLocation,
        pickup_date: bookingData.date,
        pickup_time: bookingData.time,
        dropoff_location: bookingData.dropoffLocation || '',
        passenger_count: parseInt(bookingData.passengers) || 1,
        special_requests: bookingData.specialRequests || '',
        status: 'pending_quote',
        priority: 'medium',
        notes: `Public booking - Contact: ${bookingData.contactPhone || 'Not provided'}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (bookingError) {
      console.error('Booking creation error:', bookingError)
      return NextResponse.json(
        { success: false, message: 'Failed to create booking' },
        { status: 500 }
      )
    }

    // Store additional contact information if provided
    if (bookingData.contactPhone || bookingData.emergencyContact) {
      const { error: contactError } = await supabase
        .from('quote_contacts')
        .insert({
          quote_id: booking.id,
          contact_phone: bookingData.contactPhone,
          emergency_contact: bookingData.emergencyContact,
          emergency_phone: bookingData.emergencyPhone,
          created_at: new Date().toISOString()
        })

      if (contactError) {
        console.error('Contact info creation error:', contactError)
        // Don't fail the booking if contact info fails
      }
    }

    // TODO: Send booking confirmation email
    // TODO: Notify affiliate partners about new booking request

    return NextResponse.json({
      success: true,
      booking: {
        id: booking.id,
        referenceNumber: booking.reference_number,
        status: booking.status,
        serviceType: booking.service_type,
        pickupLocation: booking.pickup_location,
        dropoffLocation: booking.dropoff_location,
        date: booking.pickup_date,
        time: booking.pickup_time,
        passengers: booking.passenger_count
      },
      message: 'Booking request submitted successfully'
    })

  } catch (error) {
    console.error('Public booking error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
