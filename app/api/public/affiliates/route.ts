import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const { pickupLocation, serviceType, date } = await request.json()

    if (!pickupLocation) {
      return NextResponse.json(
        { success: false, message: 'Pickup location is required' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Extract city/state from pickup location for matching
    // This is a simplified approach - in production, you'd use geocoding
    const locationParts = pickupLocation.split(',').map((part: string) => part.trim())
    const city = locationParts[0]
    const state = locationParts[1] || locationParts[0]

    // Fetch active affiliate companies that serve the area
    const { data: affiliates, error: affiliatesError } = await supabase
      .from('affiliate_companies')
      .select(`
        id,
        name,
        dba,
        email,
        phone,
        city,
        state,
        status,
        airports_served,
        languages_spoken,
        dispatch_software,
        offer_meet_greet,
        road_shows,
        direct_billing,
        live_phone_support
      `)
      .eq('status', 'active')
      .or(`city.ilike.%${city}%,state.ilike.%${state}%`)
      .limit(10)

    if (affiliatesError) {
      console.error('Error fetching affiliates:', affiliatesError)
      return NextResponse.json(
        { success: false, message: 'Failed to fetch available partners' },
        { status: 500 }
      )
    }

    // Enhance affiliate data with additional information
    const enhancedAffiliates = await Promise.all(
      (affiliates || []).map(async (affiliate) => {
        // Get vehicle count for this affiliate
        const { count: vehicleCount } = await supabase
          .from('vehicles')
          .select('*', { count: 'exact', head: true })
          .eq('company_id', affiliate.id)
          .eq('status', 'active')

        // Get recent quotes/ratings (simplified - you might have a reviews table)
        const { data: recentQuotes } = await supabase
          .from('quotes')
          .select('total_amount, status, created_at')
          .eq('company_id', affiliate.id)
          .eq('status', 'completed')
          .order('created_at', { ascending: false })
          .limit(5)

        // Calculate average pricing and response metrics
        const avgPrice = recentQuotes && recentQuotes.length > 0
          ? recentQuotes.reduce((sum, quote) => sum + (parseFloat(quote.total_amount) || 0), 0) / recentQuotes.length
          : null

        // Generate estimated price based on service type and historical data
        let estimatedPrice = null
        if (avgPrice) {
          // Adjust based on service type
          const multiplier = serviceType === 'airport' ? 1.2 : serviceType === 'hourly' ? 0.8 : 1.0
          estimatedPrice = Math.round(avgPrice * multiplier)
        }

        return {
          id: affiliate.id,
          name: affiliate.dba || affiliate.name,
          description: `Professional transportation services in ${affiliate.city}, ${affiliate.state}`,
          city: affiliate.city,
          state: affiliate.state,
          email: affiliate.email,
          phone: affiliate.phone,
          vehicleCount: vehicleCount || 0,
          estimatedPrice,
          rating: 4.5 + Math.random() * 0.5, // Placeholder - replace with actual ratings
          reviewCount: Math.floor(Math.random() * 50) + 10, // Placeholder
          responseTime: 'Within 2 hours',
          features: {
            meetGreet: affiliate.offer_meet_greet,
            roadShows: affiliate.road_shows,
            directBilling: affiliate.direct_billing,
            liveSupport: affiliate.live_phone_support,
            languages: affiliate.languages_spoken || [],
            airportsServed: affiliate.airports_served || []
          },
          availability: {
            available: true, // You'd check actual availability here
            nextAvailable: date
          }
        }
      })
    )

    // Sort by rating and availability
    const sortedAffiliates = enhancedAffiliates.sort((a, b) => {
      if (a.availability.available && !b.availability.available) return -1
      if (!a.availability.available && b.availability.available) return 1
      return b.rating - a.rating
    })

    return NextResponse.json({
      success: true,
      affiliates: sortedAffiliates,
      searchCriteria: {
        location: pickupLocation,
        serviceType,
        date,
        resultsCount: sortedAffiliates.length
      }
    })

  } catch (error) {
    console.error('Public affiliates fetch error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
