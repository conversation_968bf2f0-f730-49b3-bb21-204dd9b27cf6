import { NextResponse } from 'next/server'
import { createAuthenticatedSupabaseClient, requireAuth } from '@/lib/auth/server'

export async function POST(req: Request) {
  try {
    // Authenticate user
    const user = await requireAuth()
    const supabase = await createAuthenticatedSupabaseClient()

    const body = await req.json()
    const { quote_id, affiliate_company_ids } = body

    // Validate required fields
    if (!quote_id) {
      return NextResponse.json({
        error: "Missing required fields",
        details: "quote_id is required"
      }, { status: 400 })
    }

    // Get the quote details
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quote_id)
      .single()

    if (quoteError || !quote) {
      console.error('Error fetching quote:', quoteError)
      return NextResponse.json({ error: "Quote not found" }, { status: 404 })
    }

    // If specific affiliate IDs are provided, send to those
    let targetAffiliates = []
    if (affiliate_company_ids && affiliate_company_ids.length > 0) {
      const { data: affiliates, error: affiliatesError } = await supabase
        .from('affiliate_companies')
        .select('id, name')
        .in('id', affiliate_company_ids)
        .eq('status', 'active')

      if (affiliatesError) {
        console.error('Error fetching specific affiliates:', affiliatesError)
        return NextResponse.json({ error: "Failed to fetch affiliates" }, { status: 500 })
      }

      targetAffiliates = affiliates || []
    } else {
      // Find matching affiliates using the enhanced matching logic
      if (!quote.pickup_latitude || !quote.pickup_longitude) {
        return NextResponse.json({
          error: "Quote coordinates required",
          details: "Quote must have pickup coordinates for affiliate matching"
        }, { status: 400 })
      }

      const { data: matchingAffiliates, error: matchingError } = await supabase
        .rpc('find_matching_affiliates_for_quote', {
          p_organization_id: quote.organization_id,
          p_pickup_lat: quote.pickup_latitude,
          p_pickup_lng: quote.pickup_longitude,
          p_pickup_city: quote.city,
          p_vehicle_type: quote.vehicle_type,
          p_pickup_date: quote.date,
          p_service_type: quote.service_type
        })

      if (matchingError) {
        console.error('Error finding matching affiliates:', matchingError)
        return NextResponse.json({
          error: "Failed to find matching affiliates",
          details: matchingError.message
        }, { status: 500 })
      }

      targetAffiliates = (matchingAffiliates || []).map((a: any) => ({
        id: a.affiliate_company_id,
        name: a.company_name
      }))
    }

    if (targetAffiliates.length === 0) {
      return NextResponse.json({
        error: "No affiliates found",
        details: "No matching affiliates found for this quote"
      }, { status: 404 })
    }

    // Create offers for each affiliate
    const offers = []
    const errors = []

    for (const affiliate of targetAffiliates) {
      try {
        // Get applicable rates for this affiliate
        const { data: applicableRates, error: ratesError } = await supabase
          .rpc('get_applicable_rates_for_quote', {
            p_pickup_lat: quote.pickup_latitude,
            p_pickup_lng: quote.pickup_longitude,
            p_pickup_city: quote.city,
            p_vehicle_type: quote.vehicle_type,
            p_pickup_date: quote.date,
            p_service_type: quote.service_type
          })

        let suggestedRate = null // No default rate - let affiliates set their own
        if (!ratesError && applicableRates && applicableRates.length > 0) {
          const affiliateRate = applicableRates.find((r: any) => r.affiliate_company_id === affiliate.id)
          if (affiliateRate) {
            suggestedRate = affiliateRate.applicable_rate
          }
        }

        // Create the offer (only if there's a suggested rate from rate cards)
        if (suggestedRate) {
          const { data: offer, error: offerError } = await supabase
            .from('quote_affiliate_offers')
            .insert({
              quote_id: quote_id,
              company_id: affiliate.id,
              rate_amount: suggestedRate,
              rate_currency: 'USD',
              status: 'PENDING',
              expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
              created_by: user.id,
              updated_by: user.id,
              notes: `Auto-generated offer based on configured rates`
            })
            .select('id, company_id, rate_amount, status')
            .single()

          if (offerError) {
            console.error(`Error creating offer for affiliate ${affiliate.id}:`, offerError)
            errors.push({
              affiliate_company_id: affiliate.id,
              affiliate_name: affiliate.name,
              error: offerError.message
            })
          } else {
            offers.push({
              ...offer,
              affiliate_name: affiliate.name
            })
          }
        } else {
          // No rate card available - affiliate will need to submit their own rate
          console.log(`No rate card for affiliate ${affiliate.id} - they will need to submit their own rate`)
        }
      } catch (error) {
        console.error(`Unexpected error creating offer for affiliate ${affiliate.id}:`, error)
        errors.push({
          affiliate_company_id: affiliate.id,
          affiliate_name: affiliate.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Update quote status to indicate it has been sent to affiliates
    if (offers.length > 0) {
      const { error: updateError } = await supabase
        .from('quotes')
        .update({
          status: 'sent_to_affiliates',
          updated_at: new Date().toISOString()
        })
        .eq('id', quote_id)

      if (updateError) {
        console.error('Error updating quote status:', updateError)
      }
    }

    console.log(`Quote ${quote_id} sent to ${offers.length} affiliates with ${errors.length} errors`)

    return NextResponse.json({
      success: true,
      quote_id,
      offers_created: offers.length,
      offers,
      errors: errors.length > 0 ? errors : undefined,
      message: `Quote sent to ${offers.length} affiliate${offers.length !== 1 ? 's' : ''}`
    }, { status: 200 })

  } catch (error) {
    console.error('Error in send-to-affiliates API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(req: Request) {
  try {
    // Authenticate user
    const user = await requireAuth()
    const supabase = await createAuthenticatedSupabaseClient()

    const url = new URL(req.url)
    const quote_id = url.searchParams.get('quote_id')

    if (!quote_id) {
      return NextResponse.json({
        error: "Missing required parameter",
        details: "quote_id is required"
      }, { status: 400 })
    }

    // Get quote details
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quote_id)
      .single()

    if (quoteError || !quote) {
      return NextResponse.json({ error: "Quote not found" }, { status: 404 })
    }

    // Find matching affiliates for preview
    if (!quote.pickup_latitude || !quote.pickup_longitude) {
      return NextResponse.json({
        error: "Quote coordinates required",
        details: "Quote must have pickup coordinates for affiliate matching"
      }, { status: 400 })
    }

    const { data: matchingAffiliates, error: matchingError } = await supabase
      .rpc('find_matching_affiliates_for_quote', {
        p_organization_id: quote.organization_id,
        p_pickup_lat: quote.pickup_latitude,
        p_pickup_lng: quote.pickup_longitude,
        p_pickup_city: quote.city,
        p_vehicle_type: quote.vehicle_type,
        p_pickup_date: quote.date,
        p_service_type: quote.service_type
      })

    if (matchingError) {
      console.error('Error finding matching affiliates:', matchingError)
      return NextResponse.json({
        error: "Failed to find matching affiliates",
        details: matchingError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      quote_id,
      matching_affiliates: matchingAffiliates || [],
      total_matches: matchingAffiliates?.length || 0
    })

  } catch (error) {
    console.error('Error in send-to-affiliates GET API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}