// API endpoint for getting quote offer history

export const runtime = 'nodejs'
// This provides the complete offer and counter-offer history for a quote
// as specified in the enhanced affiliate matching requirements

import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server'

export async function GET(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const url = new URL(req.url)
    const quote_id = url.searchParams.get('quote_id')

    // Validate required parameters
    if (!quote_id) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "quote_id is required"
      }, { status: 400 })
    }

    // Check if user has permission to view this quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select(`
        id,
        customer_id,
        organization_id,
        status
      `)
      .eq('id', quote_id)
      .single()

    if (quoteError || !quote) {
      return NextResponse.json({
        error: "Quote not found",
        details: "The specified quote does not exist or you don't have access to it"
      }, { status: 404 })
    }

    // Get offer history using the database function
    const { data: offerHistory, error: historyError } = await supabase
      .rpc('get_quote_offer_history', {
        p_quote_id: quote_id
      })

    if (historyError) {
      console.error('Error getting offer history:', historyError)
      return NextResponse.json({
        error: "Failed to get offer history",
        details: historyError.message
      }, { status: 500 })
    }

    // Group offers by company and organize counter-offers
    const organizedHistory = (offerHistory || []).reduce((acc: any, offer: any) => {
      const companyId = offer.company_id
      
      if (!acc[companyId]) {
        acc[companyId] = {
          company_id: companyId,
          company_name: offer.company_name,
          offers: []
        }
      }
      
      acc[companyId].offers.push({
        offer_id: offer.offer_id,
        rate_amount: offer.rate_amount,
        counter_offer_amount: offer.counter_offer_amount,
        counter_offer_note: offer.counter_offer_note,
        is_counter_offer: offer.is_counter_offer,
        original_offer_id: offer.original_offer_id,
        status: offer.status,
        notes: offer.notes,
        created_at: offer.created_at,
        created_by_email: offer.created_by_email
      })
      
      return acc
    }, {})

    // Convert to array and sort by latest activity
    const formattedHistory = Object.values(organizedHistory).map((company: any) => {
      // Sort offers within each company by creation date
      company.offers.sort((a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
      
      // Get the latest offer for sorting companies
      const latestOffer = company.offers[company.offers.length - 1]
      company.latest_activity = latestOffer.created_at
      company.current_status = latestOffer.status
      
      return company
    })

    // Sort companies by latest activity
    formattedHistory.sort((a: any, b: any) => 
      new Date(b.latest_activity).getTime() - new Date(a.latest_activity).getTime()
    )

    // Calculate summary statistics
    const summary = {
      total_companies: formattedHistory.length,
      total_offers: offerHistory?.length || 0,
      counter_offers_count: offerHistory?.filter((o: any) => o.is_counter_offer).length || 0,
      accepted_offers: offerHistory?.filter((o: any) => o.status === 'ACCEPTED').length || 0,
      pending_offers: offerHistory?.filter((o: any) => o.status === 'PENDING').length || 0,
      rejected_offers: offerHistory?.filter((o: any) => o.status === 'REJECTED').length || 0
    }

    return NextResponse.json({
      success: true,
      data: {
        quote_id: quote_id,
        quote_status: quote.status,
        offer_history: formattedHistory,
        summary: summary,
        raw_history: offerHistory || []
      }
    })

  } catch (error) {
    console.error('Error in offer history API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const { quote_id } = body

    // Validate required parameters
    if (!quote_id) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "quote_id is required"
      }, { status: 400 })
    }

    // Check if user has permission to view this quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select(`
        id,
        customer_id,
        organization_id,
        status
      `)
      .eq('id', quote_id)
      .single()

    if (quoteError || !quote) {
      return NextResponse.json({
        error: "Quote not found",
        details: "The specified quote does not exist or you don't have access to it"
      }, { status: 404 })
    }

    // Get offer history using the database function
    const { data: offerHistory, error: historyError } = await supabase
      .rpc('get_quote_offer_history', {
        p_quote_id: quote_id
      })

    if (historyError) {
      console.error('Error getting offer history:', historyError)
      return NextResponse.json({
        error: "Failed to get offer history",
        details: historyError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        quote_id: quote_id,
        quote_status: quote.status,
        offer_history: offerHistory || []
      }
    })

  } catch (error) {
    console.error('Error in offer history API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}