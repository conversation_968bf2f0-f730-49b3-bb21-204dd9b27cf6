import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get current user and verify admin access
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin privileges
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    if (profileError || !profile?.roles?.some(role => ['SUPER_ADMIN', 'ADMIN', 'EVENT_MANAGER'].includes(role))) {
      return NextResponse.json({ error: 'Access denied. Admin role required.' }, { status: 403 });
    }

    const { quote_ids, cleanup_type = 'expired' } = await request.json();

    let quotesToCleanup: string[] = [];

    if (cleanup_type === 'expired') {
      // Find expired quotes automatically
      const { data: expiredQuotes, error: expiredError } = await supabase
        .from('quotes')
        .select('id')
        .eq('status', 'pending')
        .lt('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // 24 hours ago
        .or(`
          pickup_date.lt.${new Date().toISOString()},
          pickup_date.is.null
        `);

      if (expiredError) {
        console.error('Error finding expired quotes:', expiredError);
        return NextResponse.json({ error: 'Failed to find expired quotes' }, { status: 500 });
      }

      quotesToCleanup = expiredQuotes?.map(q => q.id) || [];
    } else if (cleanup_type === 'specific' && quote_ids) {
      quotesToCleanup = quote_ids;
    } else {
      return NextResponse.json({ error: 'Invalid cleanup type or missing quote IDs' }, { status: 400 });
    }

    if (quotesToCleanup.length === 0) {
      return NextResponse.json({ 
        message: 'No quotes found for cleanup',
        cleaned_count: 0
      });
    }

    // Update quotes to expired status
    const { data: updatedQuotes, error: updateError } = await supabase
      .from('quotes')
      .update({ 
        status: 'expired',
        updated_at: new Date().toISOString()
      })
      .in('id', quotesToCleanup)
      .select('id, reference_number');

    if (updateError) {
      console.error('Error updating quotes to expired:', updateError);
      return NextResponse.json({ error: 'Failed to update quotes' }, { status: 500 });
    }

    // Log cleanup events for each quote
    const timelineEntries = updatedQuotes?.map(quote => ({
      quote_id: quote.id,
      event_type: 'quote_expired',
      status: 'expired',
      title: 'Quote Expired',
      description: cleanup_type === 'expired' 
        ? 'Quote automatically expired due to timeout'
        : 'Quote manually expired by administrator',
      actor_id: user.id,
      actor_type: 'system',
      event_data: {
        cleanup_type,
        expired_at: new Date().toISOString(),
        actor_id: user.id
      },
      visible_to_customer: true,
      visible_to_affiliate: true
    })) || [];

    if (timelineEntries.length > 0) {
      const { error: timelineError } = await supabase
        .from('quote_timeline')
        .insert(timelineEntries);

      if (timelineError) {
        console.error('Error logging to timeline:', timelineError);
        // Don't fail the request for timeline errors
      }
    }

    // Clean up related affiliate offers for expired quotes
    const { error: offersError } = await supabase
      .from('quote_affiliate_offers')
      .update({ status: 'expired' })
      .in('quote_id', quotesToCleanup)
      .eq('status', 'pending');

    if (offersError) {
      console.error('Error expiring affiliate offers:', offersError);
      // Don't fail the request for offers cleanup errors
    }

    return NextResponse.json({
      success: true,
      message: `Successfully cleaned up ${updatedQuotes?.length || 0} quotes`,
      cleaned_count: updatedQuotes?.length || 0,
      cleaned_quotes: updatedQuotes?.map(q => ({
        id: q.id,
        reference_number: q.reference_number
      })) || []
    });

  } catch (error) {
    console.error('Error in quote cleanup:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to check for quotes that need cleanup
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get current user and verify admin access
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin privileges
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    if (profileError || !profile?.roles?.some(role => ['SUPER_ADMIN', 'ADMIN', 'EVENT_MANAGER'].includes(role))) {
      return NextResponse.json({ error: 'Access denied. Admin role required.' }, { status: 403 });
    }

    // Find quotes that need cleanup
    const { data: pendingQuotes, error: pendingError } = await supabase
      .from('quotes')
      .select('id, reference_number, created_at, pickup_date, customer_name')
      .eq('status', 'pending')
      .lt('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

    if (pendingError) {
      console.error('Error finding pending quotes:', pendingError);
      return NextResponse.json({ error: 'Failed to fetch pending quotes' }, { status: 500 });
    }

    // Find quotes with past pickup dates
    const { data: pastDateQuotes, error: pastDateError } = await supabase
      .from('quotes')
      .select('id, reference_number, created_at, pickup_date, customer_name')
      .eq('status', 'pending')
      .lt('pickup_date', new Date().toISOString());

    if (pastDateError) {
      console.error('Error finding past date quotes:', pastDateError);
      return NextResponse.json({ error: 'Failed to fetch past date quotes' }, { status: 500 });
    }

    // Combine and deduplicate
    const allExpiredQuotes = [...(pendingQuotes || []), ...(pastDateQuotes || [])]
      .filter((quote, index, self) => 
        index === self.findIndex(q => q.id === quote.id)
      );

    return NextResponse.json({
      expired_quotes: allExpiredQuotes,
      count: allExpiredQuotes.length,
      summary: {
        timeout_expired: pendingQuotes?.length || 0,
        past_date_expired: pastDateQuotes?.length || 0,
        total_expired: allExpiredQuotes.length
      }
    });

  } catch (error) {
    console.error('Error in quote cleanup check:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}