import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { authenticateWithPermissions } from "@/lib/auth/api-authentication";
import { createClient } from "@supabase/supabase-js";
import { extractCityFromQuoteRequest } from "@/app/utils/geocoding";
import { createSuccessResponse, createErrorResponse, ErrorResponses } from "@/app/lib/utils/api-responses";

export const runtime = 'nodejs';

// Input validation schemas
const createQuoteSchema = z.object({
  pickup_location: z.string().min(1, 'Pickup location is required'),
  dropoff_location: z.string().optional(),
  pickup_latitude: z.number().optional(),
  pickup_longitude: z.number().optional(),
  dropoff_latitude: z.number().optional(),
  dropoff_longitude: z.number().optional(),
  date: z.string().min(1, 'Pickup date is required'),
  time: z.string().min(1, 'Pickup time is required'),
  service_type: z.string().default('point'),
  vehicle_type: z.string().default('sedan'),
  passenger_count: z.number().min(1).max(50).default(1),
  luggage_count: z.number().min(0).default(0),
  city: z.string().optional(),
  dropoff_city: z.string().optional(),
  special_requests: z.string().optional(),
  special_instructions: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  duration_hours: z.number().optional(),
  is_multi_day: z.boolean().default(false),
  flight_number: z.string().optional(),
  is_return_trip: z.boolean().default(false),
  return_date: z.string().optional(),
  return_time: z.string().optional(),
  return_flight_number: z.string().optional(),
  car_seats_needed: z.boolean().default(false),
  infant_seats: z.number().min(0).default(0),
  toddler_seats: z.number().min(0).default(0),
  booster_seats: z.number().min(0).default(0),
  intermediate_stops: z.array(z.object({
    location: z.string(),
    order_number: z.number(),
    latitude: z.number().optional(),
    longitude: z.number().optional()
  })).optional(),
  selectedAffiliates: z.array(z.object({
    id: z.string(),
    order: z.number().optional()
  })).optional()
});

const updateQuoteSchema = z.object({
  pickup_location: z.string().optional(),
  dropoff_location: z.string().optional(),
  pickup_latitude: z.number().optional(),
  pickup_longitude: z.number().optional(),
  dropoff_latitude: z.number().optional(),
  dropoff_longitude: z.number().optional(),
  date: z.string().optional(),
  time: z.string().optional(),
  service_type: z.string().optional(),
  vehicle_type: z.string().optional(),
  passenger_count: z.number().min(1).max(50).optional(),
  luggage_count: z.number().min(0).optional(),
  city: z.string().optional(),
  dropoff_city: z.string().optional(),
  special_requests: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']).optional(),
  status: z.string().optional(),
  intermediate_stops: z.array(z.object({
    location: z.string(),
    order_number: z.number(),
    latitude: z.number().optional(),
    longitude: z.number().optional()
  })).optional()
});

// Business logic validation
function validateQuoteBusinessRules(input: z.infer<typeof createQuoteSchema>) {
  // Pickup time must be in the future
  const pickupDateTime = new Date(`${input.date} ${input.time}`);
  if (pickupDateTime <= new Date()) {
    throw new Error('Pickup time must be in the future');
  }

  // Pickup time cannot be more than 1 year in advance
  const oneYearFromNow = new Date();
  oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
  if (pickupDateTime > oneYearFromNow) {
    throw new Error('Pickup time cannot be more than 1 year in advance');
  }

  // Validate service type specific requirements
  if (input.service_type !== 'hourly' && !input.dropoff_location) {
    throw new Error('Drop-off location is required for non-hourly services');
  }

  // Validate pickup and dropoff are different
  if (input.pickup_location === input.dropoff_location) {
    throw new Error('Pickup and dropoff locations cannot be the same');
  }
}

// Error handling utility
function handleApiError(error: any): NextResponse {
  console.error('API Error:', error);
  
  if (error.message?.includes('Pickup time must be in the future')) {
    return NextResponse.json({
      success: false,
      error: {
        code: 'INVALID_PICKUP_TIME',
        message: error.message
      }
    }, { status: 400 });
  }
  
  if (error.message?.includes('required')) {
    return NextResponse.json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: error.message
      }
    }, { status: 400 });
  }
  
  if (error.message?.includes('Unauthorized') || error.message?.includes('Authentication')) {
    return NextResponse.json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'Authentication required'
      }
    }, { status: 401 });
  }
  
  if (error.message?.includes('Forbidden') || error.message?.includes('permissions')) {
    return NextResponse.json({
      success: false,
      error: {
        code: 'FORBIDDEN',
        message: 'Insufficient permissions'
      }
    }, { status: 403 });
  }
  
  return NextResponse.json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }
  }, { status: 500 });
}

// Strict TypeScript interfaces
interface Quote {
  id: string;
  organization_id: string;
  customer_id: string;
  reference_number: string;
  service_type: string;
  vehicle_type: string;
  pickup_location: string;
  pickup_latitude?: number;
  pickup_longitude?: number;
  dropoff_location?: string;
  dropoff_latitude?: number;
  dropoff_longitude?: number;
  pickup_city: string;
  dropoff_city?: string;
  date: string;
  time: string;
  passenger_count: number;
  luggage_count: number;
  special_requests?: string[] | null;
  status: string;
  priority: string;
  notes?: string | null;
  total_amount?: number | null;
  base_fare?: number | null;
  gratuity?: number | null;
  duration: string;
  distance: string;
  intermediate_stops?: Array<{
    location: string;
    order_number: number;
    latitude?: number;
    longitude?: number;
  }>;
  created_at: string;
  updated_at: string;
  customer?: Customer;
}

interface Customer {
  id: string;
  email: string;
  full_name?: string;
}

interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  message?: string;
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
    };
    timestamp: string;
  };
}

// Request deduplication cache
interface RequestCache {
  data: any;
  timestamp: number;
  status: number;
}

// Cache for deduplicating requests (in-memory, will reset on server restart)
const requestCache = new Map<string, RequestCache>();
const CACHE_TTL = 30000; // 30 seconds cache TTL (increased from 5 seconds)

// Generate a cache key from the request
function getCacheKey(req: Request, userId: string): string {
  const url = new URL(req.url);
  return `${userId}:${url.pathname}${url.search}`;
}

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Generate a request ID for tracking
    const requestId = Math.random().toString(36).substring(2, 10);
    console.log(`GET /api/quotes [${requestId}] - Starting request`);

    // Authenticate user with proper role and permission validation
    const authResult = await authenticateWithPermissions(request, {
      allowedRoles: ['CLIENT', 'CLIENT_COORDINATOR', 'SUPER_ADMIN'],
      requiredPermissions: ['quotes.create'], // Users need at least quote creation permission to view quotes
      requireOrganization: true
    });

    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: authResult.error || 'Authentication failed'
        }
      }, { status: authResult.statusCode || 401 });
    }

    const { user, organization } = authResult;
    console.log(`GET /api/quotes [${requestId}] - User authenticated:`, user!.id);

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    console.log("GET /api/quotes - User email:", user!.email);

    // Check cache for this request
    const cacheKey = getCacheKey(request, user!.id);
    const now = Date.now();
    const cachedResponse = requestCache.get(cacheKey);

    if (cachedResponse && now - cachedResponse.timestamp < CACHE_TTL) {
      console.log(
        `GET /api/quotes [${requestId}] - Returning cached response for`,
        cacheKey
      );
      return NextResponse.json({
        success: true,
        data: cachedResponse.data,
        message: "Quotes retrieved successfully (cached)"
      }, { status: cachedResponse.status });
    }

    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get("status");
    const limit = Math.min(parseInt(url.searchParams.get("limit") || "50"), 100); // Cap at 100
    const id = url.searchParams.get("id");

    const isAdmin = user!.is_super_admin;
    console.log("GET /api/quotes - User is admin:", isAdmin);
    console.log("GET /api/quotes - User role:", user!.role);

    // If a specific quote ID is requested
    if (id) {
      console.log("GET /api/quotes - Fetching specific quote:", id);

      let query = supabase
        .from("quotes")
        .select(`
          *,
          customer:profiles!quotes_customer_id_fkey(
            id, email, full_name
          )
        `)
        .eq("id", id);

      // Apply organization-based filtering for multi-tenant isolation
      if (!isAdmin) {
        if (organization) {
          query = query.eq("organization_id", organization.id);
        }
        query = query.eq("customer_id", user!.id);
      }

      const { data: quote, error: quoteError } = await query.single();

      if (quoteError) {
        console.error("GET /api/quotes - Error fetching quote:", quoteError);
        return NextResponse.json({
          success: false,
          error: {
            code: 'QUOTE_NOT_FOUND',
            message: 'Quote not found or access denied'
          }
        }, { status: 404 });
      }

      // Ensure all required fields are present
      const processedQuote = ensureQuoteFields(quote);
      console.log("GET /api/quotes - Successfully fetched quote:", id);

      // Cache the response
      requestCache.set(cacheKey, {
        data: processedQuote,
        timestamp: now,
        status: 200,
      });

      return NextResponse.json({
        success: true,
        data: processedQuote,
        message: "Quote retrieved successfully"
      }, { status: 200 });
    }

    // Build the query with proper joins and organization filtering
    console.log("GET /api/quotes - Building query for quotes list");
    let query = supabase
      .from("quotes")
      .select(`
        *,
        customer:profiles!quotes_customer_id_fkey(
          id, email, full_name
        )
      `)
      .order("created_at", { ascending: false })
      .limit(limit);

    // Apply organization-based filtering for multi-tenant isolation
    if (!isAdmin) {
      // Non-admin users can only see quotes from their organization
      console.log("GET /api/quotes - Filtering quotes for non-admin user");
      if (organization) {
        query = query.eq("organization_id", organization.id);
      }
      query = query.eq("customer_id", user!.id);
    } else {
      console.log("GET /api/quotes - User is admin, fetching all quotes");
    }

    // Add status filter if provided
    if (status && status !== "all") {
      console.log("GET /api/quotes - Filtering by status:", status);
      query = query.eq("status", status);
    }

    // Execute the query
    console.log("GET /api/quotes - Executing query");
    const { data: quotes, error } = await query;

    if (error) {
      console.error("GET /api/quotes - Error fetching quotes:", error);
      return handleApiError(error);
    }

    console.log("GET /api/quotes - Found", quotes?.length || 0, "quotes");

    // Process quotes to ensure all required fields are present
    const processedQuotes = quotes ? quotes.map(ensureQuoteFields) : [];

    console.log(
      "GET /api/quotes - Successfully returning",
      processedQuotes.length,
      "quotes"
    );

    // Cache the response
    requestCache.set(cacheKey, {
      data: processedQuotes,
      timestamp: now,
      status: 200,
    });

    return NextResponse.json({
      success: true,
      data: processedQuotes,
      message: "Quotes retrieved successfully",
      meta: {
        pagination: {
          page: 1,
          limit: limit,
          total: processedQuotes.length
        },
        timestamp: new Date().toISOString()
      }
    }, { status: 200 });
  } catch (error: any) {
    console.error("GET /api/quotes - Unhandled error:", error);
    return handleApiError(error);
  }
}

// Helper function to ensure all required fields are present in a quote
function ensureQuoteFields(quote: any): Quote {
  if (!quote) return quote;

  // Log the original quote for debugging
  console.log("Processing quote:", quote.id);

  // Ensure all required fields have at least default values
  const processedQuote = {
    ...quote,
    id: quote.id || `generated-${Date.now()}`,
    customer_id: quote.customer_id || "unknown",
    reference_number:
      quote.reference_number || `Q${Date.now().toString().slice(-6)}`,
    service_type: quote.service_type || "standard",
    vehicle_type: quote.vehicle_type || "sedan",
    pickup_location: quote.pickup_location || "Unknown",
    pickup_latitude: quote.pickup_latitude || null,
    pickup_longitude: quote.pickup_longitude || null,
    dropoff_location: quote.dropoff_location || "Unknown",
    dropoff_latitude: quote.dropoff_latitude || null,
    dropoff_longitude: quote.dropoff_longitude || null,
    pickup_date: quote.pickup_date || new Date().toISOString().split("T")[0],
    pickup_time: quote.pickup_time || "12:00",
    dropoff_date: quote.dropoff_date || null,
    dropoff_time: quote.dropoff_time || null,
    passenger_count: quote.passenger_count || 1,
    luggage_count: quote.luggage_count || 0,
    special_requests: quote.special_requests || null,
    status: quote.status || "pending",
    priority: quote.priority || "medium",
    notes: quote.notes || null,
    total_amount: quote.total_amount || null,
    base_fare: quote.base_fare || null,
    gratuity: quote.gratuity || null,
    intermediate_stops:
      quote.intermediate_stops?.map((stop: any) => ({
        location: stop.location || "Unknown",
        order_number: stop.order_number || 0,
        latitude: stop.latitude || null,
        longitude: stop.longitude || null,
      })) || [],
  };

  return processedQuote;
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    console.log("POST /api/quotes - Starting quote creation request");

    // Authenticate user with proper role and permission validation
    const authResult = await authenticateWithPermissions(request, {
      allowedRoles: ['CLIENT', 'CLIENT_COORDINATOR'],
      requiredPermissions: ['quotes.create'],
      requiredSubscription: 'free_trial', // Minimum subscription required
      requireOrganization: true
    });

    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: authResult.error || 'Authentication failed'
        }
      }, { status: authResult.statusCode || 401 });
    }

    const { user, organization } = authResult;
    console.log("POST /api/quotes - User authenticated:", user!.id);

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get and validate the request body
    const body = await request.json();
    
    // Validate input with Zod schema
    const validatedInput = createQuoteSchema.parse(body);
    
    // Apply business logic validation
    validateQuoteBusinessRules(validatedInput);

    // Extract city using proper geocoding from coordinates
    console.log(
      "POST /api/quotes - Extracting city from quote request using geocoding..."
    );
    const city = await extractCityFromQuoteRequest(validatedInput);
    console.log("POST /api/quotes - City extracted:", city);

    // Map frontend service types to database values
    const service_type = validatedInput.service_type === "point-to-point" ? "point" : validatedInput.service_type;

    // Log city information
    console.log("POST /api/quotes - City information:", {
      raw_city: validatedInput.city,
      trimmed_city: validatedInput.city ? validatedInput.city.trim() : "N/A",
      city_present: !!validatedInput.city && validatedInput.city.trim() !== "",
    });

    // Create the quote with organization-based isolation
    const quoteData = {
      customer_id: user!.id,
      organization_id: organization?.id || user!.organization_id,
      reference_number: `TF${Date.now().toString().slice(-8)}`,
      service_type: service_type,
      vehicle_type: validatedInput.vehicle_type,
      pickup_location: validatedInput.pickup_location,
      pickup_latitude: validatedInput.pickup_latitude || null,
      pickup_longitude: validatedInput.pickup_longitude || null,
      dropoff_location: validatedInput.dropoff_location || null,
      dropoff_latitude: validatedInput.dropoff_latitude || null,
      dropoff_longitude: validatedInput.dropoff_longitude || null,
      pickup_city: (validatedInput.city || city || "Unknown").trim(),
      dropoff_city: (validatedInput.dropoff_city || validatedInput.city || city || "Unknown").trim(),
      date: validatedInput.date,
      time: validatedInput.time,
      passenger_count: validatedInput.passenger_count,
      luggage_count: validatedInput.luggage_count,
      special_requests: validatedInput.special_requests || validatedInput.special_instructions
        ? [validatedInput.special_requests || validatedInput.special_instructions]
        : null,
      status: "pending",
      priority: validatedInput.priority,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      // Optional fields based on service type
      duration_hours: service_type === "hourly" ? validatedInput.duration_hours || 4 : null,
      is_multi_day: validatedInput.is_multi_day || service_type === "multi-day",
      flight_number: service_type === "airport" ? validatedInput.flight_number : null,
      is_return_trip: validatedInput.is_return_trip,
      return_date: validatedInput.return_date || null,
      return_time: validatedInput.return_time || null,
      return_flight_number: validatedInput.return_flight_number || null,
      car_seats_needed: validatedInput.car_seats_needed,
      infant_seats: validatedInput.infant_seats,
      toddler_seats: validatedInput.toddler_seats,
      booster_seats: validatedInput.booster_seats,
      intermediate_stops: validatedInput.intermediate_stops || null,
      duration: "TBD", // NOT NULL constraint - provide default value
      distance: "TBD", // NOT NULL constraint - provide default value
      total_amount: null,
    };

    // If intermediate stops are provided, create them in a transaction
    if (validatedInput.intermediate_stops && validatedInput.intermediate_stops.length > 0) {
      const { data: quote, error: quoteError } = await supabase.rpc(
        "create_quote_with_stops",
        {
          quote_data: quoteData,
          stops_data: validatedInput.intermediate_stops.map((stop) => ({
            location: stop.location,
            order_number: stop.order_number,
            latitude: stop.latitude || null,
            longitude: stop.longitude || null,
          })),
        }
      );

      if (quoteError) {
        console.error("Error creating quote with stops:", quoteError);
        return handleApiError(quoteError);
      }

      return NextResponse.json({
        success: true,
        data: quote,
        message: "Quote created successfully with intermediate stops"
      }, { status: 201 });
    }

    // If no intermediate stops, just create the quote
    const { data: quote, error: quoteError } = await supabase
      .from("quotes")
      .insert([quoteData])
      .select()
      .single();

    if (quoteError) {
      console.error("Error creating quote:", quoteError);
      return handleApiError(quoteError);
    }

    console.log("POST /api/quotes - Quote created successfully:", {
      id: quote.id,
      reference_number: quote.reference_number,
      organization_id: quote.organization_id,
    });

    // Store client's affiliate selections if provided
    if (validatedInput.selectedAffiliates && validatedInput.selectedAffiliates.length > 0) {
      console.log("POST /api/quotes - Storing client affiliate selections:", validatedInput.selectedAffiliates);

      try {
        // Create affiliate offers for client-selected affiliates
        const affiliateOffers = validatedInput.selectedAffiliates.map((affiliate, index) => ({
          quote_id: quote.id,
          company_id: affiliate.id,
          rate_amount: 0, // Placeholder - affiliates must provide their own rate
          currency: "USD",
          status: "PENDING",
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
          notes: `Client-selected affiliate - Priority ${affiliate.order || index + 1}`,
          is_counter_offer: false,
          submission_order: affiliate.order || index + 1,
          sent_at: new Date().toISOString(),
          created_by: user!.id,
          updated_by: user!.id,
        }));

        const { error: offersError } = await supabase
          .from('quote_affiliate_offers')
          .insert(affiliateOffers);

        if (offersError) {
          console.error('Error storing affiliate selections:', offersError);
          // Don't fail the quote creation, just log the error
        } else {
          console.log("POST /api/quotes - Client affiliate selections stored successfully");
        }
      } catch (error) {
        console.error('Error processing affiliate selections:', error);
        // Don't fail the quote creation, just log the error
      }
    }

    // After creating the quote, find matching affiliates and determine processing approach
    try {
      if (validatedInput.pickup_latitude && validatedInput.pickup_longitude && validatedInput.city) {
        console.log(
          "POST /api/quotes - Finding matching affiliates for quote:",
          quote.id
        );

        // Map service type for database function
        const mappedServiceType = service_type === "point" ? "point_to_point" : service_type;
        const cleanCity = validatedInput.city.split(",")[0].trim();

        console.log(
          "POST /api/quotes - Service type mapping:",
          validatedInput.service_type,
          "->",
          mappedServiceType
        );
        console.log(
          "POST /api/quotes - City cleaning:",
          validatedInput.city,
          "->",
          cleanCity
        );

        // Find matching affiliates using the restored function
        const { data: matchingAffiliates, error: matchingError } =
          await supabase.rpc("find_matching_affiliates_for_quote", {
            p_organization_id: organization?.id || user!.organization_id,
            p_pickup_lat: validatedInput.pickup_latitude,
            p_pickup_lng: validatedInput.pickup_longitude,
            p_pickup_city: cleanCity,
            p_vehicle_type: validatedInput.vehicle_type,
            p_pickup_date: validatedInput.date,
            p_service_type: mappedServiceType
          });

        if (matchingError) {
          console.error(
            "POST /api/quotes - Error finding matching affiliates:",
            matchingError
          );
        } else {
          console.log("POST /api/quotes - Found matching affiliates:", {
            count: matchingAffiliates?.length || 0,
            affiliates: matchingAffiliates?.map((a: any) => ({
              id: a.affiliate_company_id,
              name: a.company_name,
              score: a.overall_score,
              price: a.estimated_price,
            })),
          });
        }

        // Update quote with matching affiliates count
        if (matchingAffiliates && matchingAffiliates.length > 0) {
          const { error: updateError } = await supabase
            .from("quotes")
            .update({
              matching_affiliates_count: matchingAffiliates.length,
              updated_at: new Date().toISOString(),
            })
            .eq("id", quote.id);

          if (updateError) {
            console.error(
              "POST /api/quotes - Error updating quote with affiliate count:",
              updateError
            );
          }
        }

        // Auto-create offers for matched affiliates
        if (matchingAffiliates && matchingAffiliates.length > 0) {
          console.log("POST /api/quotes - Auto-creating offers for matched affiliates");

          const affiliateOffers = matchingAffiliates.slice(0, 5).map((affiliate: any, index: number) => ({
            quote_id: quote.id,
            company_id: affiliate.affiliate_id,
            rate_amount: affiliate.rate_info?.base_rate || 0,
            currency: "USD",
            status: "pending",
            expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
            notes: `Auto-matched affiliate - Score: ${affiliate.matching_score}`,
            submission_order: index + 1,
            created_by: user.id,
            updated_by: user.id
          }));

          const { error: offersError } = await supabase
            .from('quote_affiliate_offers')
            .insert(affiliateOffers);

          if (offersError) {
            console.error("POST /api/quotes - Error creating affiliate offers:", offersError);
          } else {
            console.log(`POST /api/quotes - Created ${affiliateOffers.length} affiliate offers`);

            // Update quote status to indicate it has been sent to affiliates
            await supabase
              .from("quotes")
              .update({
                status: 'pending_affiliate_responses',
                updated_at: new Date().toISOString(),
              })
              .eq("id", quote.id);
          }
        }
      } else {
        console.log(
          "POST /api/quotes - Missing coordinate data, skipping affiliate matching"
        );
      }
    } catch (matchingError) {
      console.error(
        "POST /api/quotes - Error in affiliate matching workflow:",
        matchingError
      );
      // Don't fail the quote creation if affiliate matching fails
    }

    return NextResponse.json({
      success: true,
      data: quote,
      message: "Quote created successfully"
    }, { status: 201 });
  } catch (error: any) {
    console.error("Unhandled error in POST /api/quotes:", error);
    return handleApiError(error);
  }
}

export async function PATCH(request: NextRequest): Promise<NextResponse> {
  try {
    // Authenticate user with proper role and permission validation
    const authResult = await authenticateWithPermissions(request, {
      allowedRoles: ['CLIENT', 'CLIENT_COORDINATOR', 'SUPER_ADMIN'],
      requiredPermissions: ['quotes.edit'], // Specific permission for editing quotes
      requireOrganization: true
    });

    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: authResult.error || 'Authentication failed'
        }
      }, { status: authResult.statusCode || 401 });
    }

    const { user, organization } = authResult;

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get request body and URL
    const body = await request.json();
    const url = new URL(request.url);
    const id = url.searchParams.get("id");

    if (!id) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_QUOTE_ID',
          message: 'Quote ID is required'
        }
      }, { status: 400 });
    }

    // Validate input with Zod schema
    const validatedInput = updateQuoteSchema.parse(body);

    const isAdmin = user!.is_super_admin;

    // Check if the user has permission to update this quote with organization filtering
    let quoteQuery = supabase
      .from("quotes")
      .select("customer_id, organization_id")
      .eq("id", id);

    // Apply organization-based filtering for multi-tenant isolation
    if (!isAdmin) {
      if (organization) {
        quoteQuery = quoteQuery.eq("organization_id", organization.id);
      }
      quoteQuery = quoteQuery.eq("customer_id", user!.id);
    }

    const { data: quote, error: quoteError } = await quoteQuery.single();

    if (quoteError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'QUOTE_NOT_FOUND',
          message: 'Quote not found or access denied'
        }
      }, { status: 404 });
    }

    // Extract intermediate stops if present
    const { intermediate_stops, ...quoteData } = validatedInput;

    // Start a transaction if we have intermediate stops
    if (intermediate_stops) {
      const { data: result, error: updateError } = await supabase.rpc(
        "update_quote_with_stops",
        {
          quote_id: id,
          quote_data: quoteData,
          stops_data: intermediate_stops.map((stop) => ({
            location: stop.location,
            order_number: stop.order_number,
            latitude: stop.latitude || null,
            longitude: stop.longitude || null,
          })),
        }
      );

      if (updateError) {
        console.error("Error updating quote with stops:", updateError);
        return handleApiError(updateError);
      }

      return NextResponse.json({
        success: true,
        data: result,
        message: "Quote updated successfully with intermediate stops"
      }, { status: 200 });
    }

    // If no intermediate stops, just update the quote
    // Map city field to pickup_city if provided
    const updateData = { ...quoteData };
    if (updateData.city) {
      updateData.pickup_city = updateData.city;
      updateData.dropoff_city = updateData.dropoff_city || updateData.city;
      delete updateData.city; // Remove the non-existent field
    }

    const { data: updatedQuote, error: updateError } = await supabase
      .from("quotes")
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating quote:", updateError);
      return handleApiError(updateError);
    }

    return NextResponse.json({
      success: true,
      data: updatedQuote,
      message: "Quote updated successfully"
    }, { status: 200 });
  } catch (error: any) {
    console.error("Unhandled error in PATCH /api/quotes:", error);
    return handleApiError(error);
  }
}

export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    // Authenticate user with proper role and permission validation
    const authResult = await authenticateWithPermissions(request, {
      allowedRoles: ['CLIENT', 'CLIENT_COORDINATOR', 'SUPER_ADMIN'],
      requiredPermissions: ['quotes.delete'], // Specific permission for deleting quotes
      requireOrganization: true
    });

    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: authResult.error || 'Authentication failed'
        }
      }, { status: authResult.statusCode || 401 });
    }

    const { user, organization } = authResult;

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_QUOTE_ID',
          message: 'Quote ID is required'
        }
      }, { status: 400 });
    }

    const isAdmin = user!.is_super_admin;

    // Build the query with organization-based filtering
    let query = supabase
      .from("quotes")
      .update({
        status: "cancelled",
        updated_at: new Date().toISOString(),
      })
      .eq("id", id);

    // Apply organization-based filtering for multi-tenant isolation
    if (!isAdmin) {
      if (organization) {
        query = query.eq("organization_id", organization.id);
      }
      query = query.eq("customer_id", user!.id);
    }

    const { data: quote, error } = await query.select().single();

    if (error) {
      console.error("Error cancelling quote:", error);
      return NextResponse.json({
        success: false,
        error: {
          code: 'QUOTE_CANCELLATION_FAILED',
          message: 'Failed to cancel quote or quote not found'
        }
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: quote,
      message: "Quote cancelled successfully"
    }, { status: 200 });
  } catch (error: any) {
    console.error("Error in DELETE /api/quotes:", error);
    return handleApiError(error);
  }
}
