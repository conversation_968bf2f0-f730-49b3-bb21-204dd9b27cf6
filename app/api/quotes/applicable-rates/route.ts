// API endpoint for getting applicable rates for quotes

export const runtime = 'nodejs'
// This displays applicable rates for active vehicles that match quote requirements
// as specified in the enhanced affiliate matching requirements

import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const {
      pickup_lat,
      pickup_lng,
      pickup_city,
      vehicle_type,
      pickup_date,
      service_type = 'point_to_point'
    } = body

    // Validate required parameters
    if (!pickup_lat || !pickup_lng || !vehicle_type) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "pickup_lat, pickup_lng, and vehicle_type are required"
      }, { status: 400 })
    }

    // Get applicable rates using the new logic
    const { data: applicableRates, error: ratesError } = await supabase
      .rpc('get_applicable_rates_for_quote', {
        p_pickup_lat: pickup_lat,
        p_pickup_lng: pickup_lng,
        p_pickup_city: pickup_city,
        p_vehicle_type: vehicle_type,
        p_pickup_date: pickup_date || new Date().toISOString().split('T')[0],
        p_service_type: service_type
      })

    if (ratesError) {
      console.error('Error getting applicable rates:', ratesError)
      return NextResponse.json({
        error: "Failed to get applicable rates",
        details: ratesError.message
      }, { status: 500 })
    }

    // Process and format the rates data
    const formattedRates = (applicableRates || []).map((rate: any) => ({
      affiliate_company_id: rate.affiliate_company_id,
      company_name: rate.company_name,
      rate_card_id: rate.rate_card_id,
      base_rate: rate.base_rate,
      applicable_rate: rate.applicable_rate,
      pricing_model: rate.pricing_model,
      rate_details: rate.rate_details,
      distance_miles: rate.distance_miles,
      in_service_area: rate.in_service_area,
      date_block_multiplier: rate.date_block_multiplier,
      has_special_pricing: rate.date_block_multiplier > 1.0
    }))

    return NextResponse.json({
      success: true,
      data: {
        applicable_rates: formattedRates,
        total_count: formattedRates.length,
        service_type: service_type,
        pickup_date: pickup_date || new Date().toISOString().split('T')[0]
      }
    })

  } catch (error) {
    console.error('Error in applicable rates API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const url = new URL(req.url)
    const pickup_lat = parseFloat(url.searchParams.get('pickup_lat') || '0')
    const pickup_lng = parseFloat(url.searchParams.get('pickup_lng') || '0')
    const pickup_city = url.searchParams.get('pickup_city')
    const vehicle_type = url.searchParams.get('vehicle_type')
    const pickup_date = url.searchParams.get('pickup_date')
    const service_type = url.searchParams.get('service_type') || 'point_to_point'

    // Validate required parameters
    if (!pickup_lat || !pickup_lng || !vehicle_type) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "pickup_lat, pickup_lng, and vehicle_type are required"
      }, { status: 400 })
    }

    // Get applicable rates using the new logic
    const { data: applicableRates, error: ratesError } = await supabase
      .rpc('get_applicable_rates_for_quote', {
        p_pickup_lat: pickup_lat,
        p_pickup_lng: pickup_lng,
        p_pickup_city: pickup_city,
        p_vehicle_type: vehicle_type,
        p_pickup_date: pickup_date || new Date().toISOString().split('T')[0],
        p_service_type: service_type
      })

    if (ratesError) {
      console.error('Error getting applicable rates:', ratesError)
      return NextResponse.json({
        error: "Failed to get applicable rates",
        details: ratesError.message
      }, { status: 500 })
    }

    // Process and format the rates data
    const formattedRates = (applicableRates || []).map((rate: any) => ({
      affiliate_company_id: rate.affiliate_company_id,
      company_name: rate.company_name,
      rate_card_id: rate.rate_card_id,
      base_rate: rate.base_rate,
      applicable_rate: rate.applicable_rate,
      pricing_model: rate.pricing_model,
      rate_details: rate.rate_details,
      distance_miles: rate.distance_miles,
      in_service_area: rate.in_service_area,
      date_block_multiplier: rate.date_block_multiplier,
      has_special_pricing: rate.date_block_multiplier > 1.0
    }))

    return NextResponse.json({
      success: true,
      data: {
        applicable_rates: formattedRates,
        total_count: formattedRates.length,
        service_type: service_type,
        pickup_date: pickup_date || new Date().toISOString().split('T')[0]
      }
    })

  } catch (error) {
    console.error('Error in applicable rates API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}