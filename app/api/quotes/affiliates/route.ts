import { NextRequest, NextResponse } from "next/server";
import { getAffiliatesForQuote } from "@/lib/api/affiliates";
import { createClient } from "@/lib/supabase/server";

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const city = url.searchParams.get("city");
    const ids = url.searchParams.get("ids");

    // Support fetching by specific IDs
    if (ids) {
      console.log(`[API] Getting affiliates by IDs: "${ids}"`);

      const affiliateIds = ids.split(",").filter((id) => id.trim());
      const supabase = createClient();

      const { data: affiliates, error } = await supabase
        .from("affiliate_companies")
        .select("*")
        .in("id", affiliateIds);

      if (error) {
        console.error("[API] Error fetching affiliates by IDs:", error);
        return NextResponse.json(
          {
            success: false,
            error: "Failed to fetch affiliates",
          },
          { status: 500 }
        );
      }

      console.log(`[API] Found ${affiliates?.length || 0} affiliates by IDs`);

      return NextResponse.json({
        success: true,
        data: affiliates || [],
        count: affiliates?.length || 0,
      });
    }

    // Original city-based search
    if (!city) {
      return NextResponse.json(
        {
          success: false,
          error: "City or ids parameter is required",
        },
        { status: 400 }
      );
    }

    console.log(`[API] Getting affiliates for city: "${city}"`);

    // Call the function on the server side
    const affiliates = await getAffiliatesForQuote(city);

    console.log(
      `[API] Found ${affiliates.length} affiliates for city: "${city}"`
    );

    return NextResponse.json({
      success: true,
      data: affiliates,
      count: affiliates.length,
    });
  } catch (error) {
    console.error("[API] Error getting affiliates:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
