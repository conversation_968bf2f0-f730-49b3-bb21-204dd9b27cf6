import { createServerClient, type CookieOptions } from '@supabase/ssr'

export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import type { Database } from '@/lib/database.types'

const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

interface AffiliateCompany {
  id: string
  name: string
  email: string
  phone: string
}

interface QuoteOffer {
  id: string
  quote_id: string
  company_id: string
  status: string
  rate_amount: number
  rate_currency: string
  timeout_at: string
  created_at: string
  updated_at: string
  affiliate_companies: AffiliateCompany
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createSupabaseClient()
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      return NextResponse.json({ error: "Authentication error" }, { status: 500 })
    }
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get the quote ID from the URL params
    const url = new URL(request.url)
    const quoteId = url.searchParams.get('id')

    if (!quoteId) {
      return NextResponse.json({ error: "Quote ID is required" }, { status: 400 })
    }

    // Get the quote offers with company details
    const { data: offers, error: offersError } = await supabase
      .from('quote_offers')
      .select(`
        id,
        quote_id,
        company_id,
        status,
        rate_amount,
        rate_currency,
        timeout_at,
        created_at,
        updated_at,
        affiliate_companies!company_id (
          id,
          name,
          email,
          phone
        )
      `)
      .eq('quote_id', quoteId)
      .order('created_at', { ascending: false })

    if (offersError) {
      console.error('Error fetching quote offers:', offersError)
      return NextResponse.json({ error: "Failed to fetch quote offers" }, { status: 500 })
    }

    // Transform the offers to include company details
    const transformedOffers = (offers || []).map((offer: any) => {
      const company = offer.affiliate_companies as AffiliateCompany || {
        id: '',
        name: 'Unknown Company',
        email: '',
        phone: ''
      };
      
      return {
        id: offer.id,
        quote_id: offer.quote_id,
        company_id: offer.company_id,
        company_name: company.name,
        company_email: company.email,
        company_phone: company.phone,
        status: offer.status || 'pending',
        rate_amount: offer.rate_amount || 150.00,
        rate_currency: offer.rate_currency || 'USD',
        timeout_at: offer.timeout_at,
        created_at: offer.created_at,
        updated_at: offer.updated_at
      }
    })

    return NextResponse.json(transformedOffers)
  } catch (error) {
    console.error('Error in quote offers API:', error)
    return NextResponse.json({ error: "Unexpected error occurred" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const supabase = createSupabaseClient()
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      return NextResponse.json({ error: "Authentication error" }, { status: 500 })
    }
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      quote_id,
      company_id,
      rate_amount,
      rate_currency = 'USD',
      notes,
      is_counter_offer = false,
      original_offer_id,
      counter_offer_amount,
      counter_offer_note
    } = body

    // Validate required fields
    if (!quote_id || !company_id || (!rate_amount && !counter_offer_amount)) {
      return NextResponse.json({
        error: "Missing required fields",
        details: "quote_id, company_id, and rate_amount (or counter_offer_amount) are required"
      }, { status: 400 })
    }

    // Check if user has permission to create offers for this company
    const { data: userCompanies, error: userCompaniesError } = await supabase
      .from('affiliate_users')
      .select('affiliate_company_id, role, status')
      .eq('user_id', session.user.id)
      .eq('affiliate_company_id', company_id)
      .eq('status', 'ACTIVE')

    if (userCompaniesError) {
      console.error('Error checking user company permissions:', userCompaniesError)
      return NextResponse.json({ error: "Failed to verify permissions" }, { status: 500 })
    }

    if (!userCompanies || userCompanies.length === 0) {
      return NextResponse.json({ 
        error: "Forbidden", 
        details: "You don't have permission to create offers for this company" 
      }, { status: 403 })
    }

    const userRole = userCompanies[0].role
    if (!['OWNER', 'DISPATCHER'].includes(userRole)) {
      return NextResponse.json({ 
        error: "Forbidden", 
        details: "Only owners and dispatchers can create offers" 
      }, { status: 403 })
    }

    // If this is a counter-offer, use the counter-offer submission function
    if (is_counter_offer && original_offer_id) {
      // Counter offer functionality not implemented yet - database function doesn't exist
      return NextResponse.json({
        error: 'Counter offer functionality not implemented yet'
      }, { status: 501 })
    }

    // Regular offer creation - table doesn't exist yet
    return NextResponse.json({
      error: 'Quote offers functionality not implemented yet'
    }, { status: 501 })

  } catch (error) {
    console.error('Error in POST quote offers API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}