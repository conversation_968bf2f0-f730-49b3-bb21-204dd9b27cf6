import { NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import type { Database } from '@/lib/database.types';

const createSupabaseClient = () => {
  const cookieStore = cookies();
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options);
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  );
};

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  const supabase = createSupabaseClient();

  // Check user session
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return NextResponse.json(
      { message: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    const offerId = params.id;
    if (!offerId) {
      return NextResponse.json(
        { message: 'Offer ID is required' }, 
        { status: 400 }
      );
    }
    
    // Get the offer data to make sure it belongs to the current affiliate
    const { data: offer, error: offerError } = await supabase
      .from('quote_offers')
      .select('*, quotes(id, status)')
      .eq('id', offerId)
      .single();
    
    if (offerError) {
      console.error('Error fetching offer:', offerError);
      return NextResponse.json(
        { message: 'Offer not found' }, 
        { status: 404 }
      );
    }
    
    // Verify the offer belongs to the current affiliate. offer.company_id should match affiliateCompany.id
    const { data: affiliateCompany, error: affiliateError } = await supabase
      .from('affiliate_companies')
      .select('id')
      .eq('user_id', session.user.id)
      .single();
    
    if (affiliateError) {
      console.error('Error fetching affiliate company:', affiliateError);
      return NextResponse.json(
        { message: 'Failed to verify affiliate' }, 
        { status: 500 }
      );
    }
    
    if (offer.company_id !== affiliateCompany.id) {
      return NextResponse.json(
        { message: 'You do not have permission to reject this offer' }, 
        { status: 403 }
      );
    }
    
    // Update the offer status to rejected
    const { error: updateError } = await supabase
      .from('quote_offers')
      .update({ status: 'rejected' })
      .eq('id', offerId);
    
    if (updateError) {
      console.error('Error updating offer status:', updateError);
      return NextResponse.json(
        { message: 'Failed to reject offer' }, 
        { status: 500 }
      );
    }
    
    // Add a timeline entry
    if (!offer.quote_id) {
      console.error('Error creating timeline entry: offer is missing quote_id', offer);
      // Optionally, you could still proceed with offer rejection but skip timeline, 
      // or return an error. For now, log and continue.
    } else {
      const { error: timelineError } = await supabase
        .from('quote_timeline')
        .insert({
          quote_id: offer.quote_id,
          action: 'offer_rejected',
          user_id: session.user.id,
          company_id: affiliateCompany.id,
          details: 'Offer rejected by affiliate'
        });
      
      if (timelineError) {
        console.error('Error creating timeline entry:', timelineError);
        // Don't fail the request for timeline errors
      }
    }
    
    return NextResponse.json({
      message: 'Offer rejected successfully'
    });
    
  } catch (error) {
    console.error('Error rejecting offer:', error);
    return NextResponse.json(
      { message: 'Internal server error' }, 
      { status: 500 }
    );
  }
} 