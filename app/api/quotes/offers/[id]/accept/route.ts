import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';
import { transitionQuoteStatus } from '@/lib/utils/quote-status-transitions';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const offerId = params.id;
    
    if (!offerId) {
      return NextResponse.json(
        { error: 'Offer ID is required' },
        { status: 400 }
      );
    }
    
    // Create a Supabase client
    const supabase = createRouteHandlerClient({ cookies });
    
    // Check if the user is authenticated
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the offer details
    const { data: offer, error: offerError } = await supabase
      .from('quote_offers')
      .select('id, quote_id, company_id, rate_amount, rate_currency, status')
      .eq('id', offerId)
      .single();
    
    if (offerError || !offer) {
      return NextResponse.json(
        { error: offerError?.message || 'Offer not found' },
        { status: 404 }
      );
    }
    
    // Check if the offer is already accepted or rejected
    if (offer.status !== 'pending') {
      return NextResponse.json(
        { error: `Offer is already ${offer.status}` },
        { status: 400 }
      );
    }
    
    // Begin a transaction manually
    // 1. Update the offer status to 'accepted'
    const { error: updateOfferError } = await supabase
      .from('quote_offers')
      .update({ 
        status: 'accepted',
        updated_at: new Date().toISOString()
      })
      .eq('id', offerId);
    
    if (updateOfferError) {
      return NextResponse.json(
        { error: updateOfferError.message },
        { status: 500 }
      );
    }
    
    // 2. Reject all other offers for this quote
    const { error: rejectOffersError } = await supabase
      .from('quote_offers')
      .update({ 
        status: 'rejected',
        updated_at: new Date().toISOString()
      })
      .eq('quote_id', offer.quote_id)
      .neq('id', offerId);
    
    if (rejectOffersError) {
      console.error('Error rejecting other offers:', rejectOffersError);
      // Continue anyway, this is not critical
    }
    
    // 3. Update the quote status using the transition utility
    const { success, error: transitionError } = await transitionQuoteStatus(
      offer.quote_id,
      'accepted',
      {
        company_id: offer.company_id,
        total_amount: offer.rate_amount,
        rate_currency: offer.rate_currency,
        accepted_at: new Date().toISOString(),
        accepted_by: session.user.id
      }
    );
    
    if (!success || transitionError) {
      return NextResponse.json(
        { error: transitionError?.message || 'Failed to update quote status' },
        { status: 500 }
      );
    }
    
    // 4. Add a timeline entry
    const { error: timelineError } = await supabase
      .from('quote_timeline')
      .insert({
        quote_id: offer.quote_id,
        user_id: session.user.id,
        action: 'accepted',
        details: `Quote offer accepted with rate ${offer.rate_amount} ${offer.rate_currency}`
      });
    
    if (timelineError) {
      console.error('Error adding timeline entry:', timelineError);
      // Continue anyway, this is not critical
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error accepting offer:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 