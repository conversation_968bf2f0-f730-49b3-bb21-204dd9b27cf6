// API endpoint for counter-offer functionality

export const runtime = 'nodejs'
// This allows affiliates to submit counter-offers with explanatory notes
// as specified in the enhanced affiliate matching requirements

import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { triggerQuoteUpdateBroadcast } from "@/lib/websocket/quote-broadcaster";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function POST(req: NextRequest) {
  try {
    const supabase = createClient();

    // Get the current user session
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error("[Counter Offer API] Authentication failed - Error:", authError);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json()
    const {
      original_offer_id,
      counter_amount,
      counter_note
    } = body

    // Validate required parameters
    if (!original_offer_id || !counter_amount || !counter_note) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "original_offer_id, counter_amount, and counter_note are required"
      }, { status: 400 })
    }

    // Validate counter amount
    if (isNaN(counter_amount) || counter_amount <= 0) {
      return NextResponse.json({
        error: "Invalid counter amount",
        details: "counter_amount must be a valid number greater than 0"
      }, { status: 400 })
    }

    // Get the user's affiliate company
    const { data: userCompany, error: companyError } = await supabase
      .from("affiliate_users")
      .select(`
        affiliate_company_id,
        role,
        status,
        company:affiliate_companies!inner(
          id,
          name,
          status
        )
      `)
      .eq("user_id", user.id)
      .single();

    if (companyError || !userCompany) {
      console.error("[Counter Offer API] User company not found:", companyError);

      return NextResponse.json(
        { error: "Affiliate company not found" },
        { status: 404 }
      );
    }

    // Verify the original offer belongs to this affiliate
    const { data: originalOffer, error: offerError } = await supabase
      .from("quote_affiliate_offers")
      .select(`
        *,
        quotes!inner(
          id,
          reference_number,
          status
        )
      `)
      .eq("id", original_offer_id)
      .eq("company_id", userCompany.affiliate_company_id)
      .single();

    if (offerError || !originalOffer) {
      console.error("[Counter Offer API] Original offer not found or unauthorized:", offerError);
      return NextResponse.json(
        { error: "Original offer not found or unauthorized" },
        { status: 404 }
      );
    }

    // Create counter-offer directly instead of using RPC function
    const { data: counterOffer, error: createError } = await supabase
      .from("quote_counter_offers")
      .insert({
        original_offer_id: original_offer_id,
        quote_id: originalOffer.quote_id,
        company_id: userCompany.affiliate_company_id,
        counter_amount: parseFloat(counter_amount),
        counter_note: counter_note,
        status: "pending",
        created_by: user.id,
        updated_by: user.id
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating counter-offer:', createError)
      return NextResponse.json({
        error: "Failed to create counter-offer",
        details: createError.message
      }, { status: 500 })
    }

    // Counter-offer created successfully

    // Add timeline entry for counter-offer submission
    try {
      await supabase.from("quote_timeline").insert({
        quote_id: originalOffer.quote_id,
        action: "COUNTER_OFFER_SUBMITTED",
        status: "counter_offer_submitted",
        created_by: user.id,
        details: JSON.stringify({
          original_offer_id: original_offer_id,
          counter_offer_id: counterOffer.id,
          counter_amount: counter_amount,
          counter_note: counter_note,
          affiliate_company_id: userCompany.affiliate_company_id,
          affiliate_name: userCompany.company.name
        })
      });
    } catch (timelineError) {
      console.error('Error creating timeline entry:', timelineError);
      // Don't fail the request for timeline errors
    }

    // Create audit log entry
    try {
      await supabase.from('audit_logs').insert({
        table_name: 'quote_counter_offers',
        record_id: counterOffer.id,
        action: 'create',
        old_values: null,
        new_values: counterOffer,
        user_id: user.id,
        metadata: {
          quote_id: originalOffer.quote_id,
          original_offer_id: original_offer_id,
          counter_amount: counter_amount,
          action_type: 'counter_offer_submitted',
          affiliate_name: userCompany.affiliate_companies.name
        }
      });
    } catch (auditError) {
      console.error('Error creating audit log for counter-offer:', auditError);
      // Don't fail the request for audit errors
    }

    // Broadcast real-time update
    try {
      await triggerQuoteUpdateBroadcast(
        originalOffer.quote_id,
        "counter_offer_submitted",
        user.id,
        {
          action: "counter_offer_submitted",
          counter_offer_id: counterOffer.id,
          affiliate_name: userCompany.affiliate_companies.name,
          counter_amount: counter_amount
        }
      );
    } catch (broadcastError) {
      console.error('Error broadcasting counter-offer update:', broadcastError);
      // Don't fail the request for broadcast errors
    }

    return NextResponse.json({
      success: true,
      data: {
        counter_offer: counterOffer,
        message: "Counter-offer submitted successfully"
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Error in counter-offer submission API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function PUT(req: Request) {
  try {
    const supabase = createClient()
    
    // Get the user session
    const { data: { user }, error: sessionError } = await supabase.auth.getUser()

    if (sessionError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const {
      counter_offer_id,
      action // 'accept' or 'reject'
    } = body

    // Validate required parameters
    if (!counter_offer_id || !action) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "counter_offer_id and action are required"
      }, { status: 400 })
    }

    // Validate action
    if (!['accept', 'reject'].includes(action)) {
      return NextResponse.json({
        error: "Invalid action",
        details: "action must be either 'accept' or 'reject'"
      }, { status: 400 })
    }

    if (action === 'accept') {
      // Accept the counter-offer using the database function
      const { data: result, error: acceptError } = await supabase
        .rpc('accept_counter_offer', {
          p_counter_offer_id: counter_offer_id
        })

      if (acceptError) {
        console.error('Error accepting counter-offer:', acceptError)
        return NextResponse.json({
          error: "Failed to accept counter-offer",
          details: acceptError.message
        }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
        data: {
          message: "Counter-offer accepted successfully",
          result: result
        }
      })
    } else {
      // Reject the counter-offer
      const { data: updatedOffer, error: rejectError } = await supabase
        .from('quote_affiliate_offers')
        .update({
          status: 'REJECTED',
          updated_at: new Date().toISOString(),
          updated_by: user.id
        })
        .eq('id', counter_offer_id)
        .eq('is_counter_offer', true)
        .select(`
          id,
          quote_id,
          company_id,
          rate_amount,
          counter_offer_amount,
          counter_offer_note,
          status,
          updated_at
        `)
        .single()

      if (rejectError) {
        console.error('Error rejecting counter-offer:', rejectError)
        return NextResponse.json({
          error: "Failed to reject counter-offer",
          details: rejectError.message
        }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
        data: {
          counter_offer: updatedOffer,
          message: "Counter-offer rejected successfully"
        }
      })
    }

  } catch (error) {
    console.error('Error in counter-offer action API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}