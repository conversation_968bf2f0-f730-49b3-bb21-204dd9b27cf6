import { createServerClient, type CookieOptions } from '@supabase/ssr'

export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import type { Database } from '@/lib/database.types'

const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

export async function GET(
  request: Request,
  { params }: { params: { quoteId: string } }
) {
  try {
    const supabase = createSupabaseClient()
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      return NextResponse.json({ error: "Authentication error" }, { status: 500 })
    }
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const quoteId = params.quoteId

    // Get user's roles from profiles table
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single()

    const isAdmin = profile?.roles?.includes('ADMIN')

    // Verify the user has access to this quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('customer_id')
      .eq('id', quoteId)
      .single()

    if (quoteError) {
      return NextResponse.json({ error: "Quote not found" }, { status: 404 })
    }

    if (!isAdmin && quote.customer_id !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized to view this quote" }, { status: 403 })
    }

    // Get the timeline entries
    const { data: timeline, error: timelineError } = await supabase
      .from('quote_timeline')
      .select(`
        *,
        profiles (
          id,
          first_name,
          last_name,
          avatar_url
        )
      `)
      .eq('quote_id', quoteId)
      .order('created_at', { ascending: false })

    if (timelineError) {
      console.error('Error fetching timeline:', timelineError)
      return NextResponse.json({ error: "Failed to fetch timeline" }, { status: 500 })
    }

    return NextResponse.json(timeline)
  } catch (error) {
    console.error('Error in timeline route:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: Request,
  { params }: { params: { quoteId: string } }
) {
  try {
    const supabase = createSupabaseClient()
    
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const quoteId = params.quoteId
    const { action, details } = await request.json()

    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 })
    }

    // First verify the user has access to this quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quoteId)
      .eq('customer_id', session.user.id)
      .single()

    if (quoteError || !quote) {
      console.error('Error fetching quote:', quoteError)
      return NextResponse.json({ error: 'Quote not found or access denied' }, { status: 404 })
    }

    // Add the timeline entry
    const { data: timeline, error } = await supabase
      .from('quote_timeline')
      .insert({
        quote_id: quoteId,
        user_id: session.user.id,
        action,
        details
      })
      .select('*, profiles(first_name, last_name, avatar_url)')
      .single()

    if (error) {
      console.error('Error adding timeline entry:', error)
      return NextResponse.json({ error: 'Failed to add timeline entry' }, { status: 500 })
    }

    return NextResponse.json({ timeline })
  } catch (error) {
    console.error('Error in timeline API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 