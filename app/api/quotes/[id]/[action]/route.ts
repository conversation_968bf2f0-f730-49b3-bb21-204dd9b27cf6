import { NextResponse } from "next/server"

export const runtime = 'nodejs'
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/lib/database.types'
import { updateQuoteStatusWithBroadcast } from '@/lib/websocket/quote-integration'

const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

export async function POST(
  req: Request,
  { params }: { params: { id: string; action: string } }
) {
  try {
    const supabase = createSupabaseClient()
    
    // Get the user session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }
    
    const { id, action } = params
    
    // Validate action
    const validActions = ['accept', 'reject', 'change']
    if (!validActions.includes(action)) {
      return NextResponse.json({ message: "Invalid action" }, { status: 400 })
    }
    
    // Get the quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', id)
      .eq('customer_id', session.user.id)
      .single()
    
    if (quoteError) {
      console.error('Error fetching quote:', quoteError)
      return NextResponse.json({ message: "Quote not found or access denied" }, { status: 404 })
    }
    
    // Check if the quote is already in the requested state
    if ((action === 'accept' && quote.status === 'accepted') ||
        (action === 'reject' && quote.status === 'rejected') ||
        (action === 'change' && quote.status === 'change_requested')) {
      return NextResponse.json({ 
        message: `Quote is already ${quote.status}`,
        quote
      }, { status: 200 })
    }
    
    // Determine the new status based on the action
    let newStatus = ''
    switch (action) {
      case 'accept':
        newStatus = 'accepted'
        break
      case 'reject':
        newStatus = 'rejected'
        break
      case 'change':
        newStatus = 'change_requested'
        break
    }
    
    // Get request body for additional data
    const body = await req.json().catch(() => ({}))
    
    // Update the quote status with WebSocket broadcasting
    const updateResult = await updateQuoteStatusWithBroadcast(
      id,
      newStatus,
      session.user.id,
      action === 'change' && body.changeDetails ? {
        change_details: body.changeDetails
      } : {}
    )

    if (!updateResult.success) {
      console.error('Error updating quote:', updateResult.error)
      return NextResponse.json({ message: updateResult.error }, { status: 500 })
    }

    const updatedQuote = updateResult.data
    
    // If the quote is accepted, create a trip
    let trip = null
    if (action === 'accept') {
      // Check if a trip already exists for this quote
      const { data: existingTrip, error: existingTripError } = await supabase
        .from('trips')
        .select('*')
        .eq('quote_id', id)
        .single()
      
      if (!existingTripError && existingTrip) {
        // Trip already exists, use it
        trip = existingTrip
      } else {
        // Create a new trip
        const { data: newTrip, error: tripError } = await supabase
          .from('trips')
          .insert({
            quote_id: id,
            customer_id: session.user.id,
            pickup_location: quote.pickup_location,
            dropoff_location: quote.dropoff_location,
            date: quote.date,
            time: quote.time,
            vehicle_type: quote.vehicle_type,
            status: 'confirmed',
            passenger_count: quote.passenger_count || 1,
            luggage_count: quote.luggage_count || 0,
            special_requests: quote.special_requests,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single()
        
        if (tripError) {
          console.error('Error creating trip:', tripError)
          // Don't return an error, just log it
        } else {
          trip = newTrip
        }
      }
    }
    
    // Log the action
    await supabase
      .from('audit_logs')
      .insert({
        user_id: session.user.id,
        action: `quote_${action}`,
        resource_type: 'quote',
        resource_id: id,
        details: JSON.stringify({ 
          status: newStatus,
          ...(trip ? { trip_id: trip.id } : {}),
          ...(body.changeDetails ? { change_details: body.changeDetails } : {})
        })
      })
    
    return NextResponse.json({ 
      message: `Quote ${action}ed successfully`,
      quote: updatedQuote,
      ...(trip ? { trip } : {})
    }, { status: 200 })
  } catch (error: any) {
    console.error(`Error in quote ${params.action} route:`, error)
    return NextResponse.json(
      { message: error.message || `Error processing quote ${params.action} action` },
      { status: 500 }
    )
  }
} 