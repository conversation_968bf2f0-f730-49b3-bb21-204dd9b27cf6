import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { hasRole } from '@/app/lib/auth';

/**
 * POST /api/quotes/[quoteId]/counter-offer-response
 * Handle client response to affiliate counter-offers
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { counterOfferId, clientResponse, clientNotes } = body;

    if (!counterOfferId || !clientResponse) {
      return NextResponse.json(
        { error: 'counterOfferId and clientResponse are required' },
        { status: 400 }
      );
    }

    if (!['ACCEPTED', 'REJECTED'].includes(clientResponse)) {
      return NextResponse.json(
        { error: 'clientResponse must be ACCEPTED or REJECTED' },
        { status: 400 }
      );
    }

    // Get user profile to check roles
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];

    // Allow CLIENT and quote owner to respond to counter-offers
    if (!hasRole(userRoles, ['CLIENT'])) {
      // Check if user is the quote owner
      const { data: quote } = await supabase
        .from('quotes')
        .select('customer_id')
        .eq('id', params.id)
        .single();

      if (!quote || quote.customer_id !== session.user.id) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
    }

    // Use the database function to handle the counter-offer response
    const { data: result, error: functionError } = await supabase
      .rpc('handle_client_counter_offer_response', {
        p_counter_offer_id: counterOfferId,
        p_client_response: clientResponse,
        p_client_notes: clientNotes || null
      });

    if (functionError) {
      console.error('Error handling counter-offer response:', functionError);
      return NextResponse.json(
        { error: 'Failed to process counter-offer response' },
        { status: 500 }
      );
    }

    if (!result?.success) {
      return NextResponse.json(
        { error: result?.error || 'Failed to process response' },
        { status: 400 }
      );
    }

    // Broadcast real-time update via WebSocket
    try {
      const { broadcastQuoteStatusUpdate } = await import('@/lib/websocket/quote-integration');
      
      if (clientResponse === 'ACCEPTED') {
        await broadcastQuoteStatusUpdate(params.id, {
          status: 'accepted',
          action: 'counter_offer_accepted',
          updatedBy: session.user.id,
          timestamp: new Date().toISOString()
        });
      } else {
        await broadcastQuoteStatusUpdate(params.id, {
          status: result.next_affiliate_company_id ? 'sent_to_next_affiliate' : 'no_affiliates_available',
          action: 'counter_offer_rejected',
          nextAffiliateId: result.next_affiliate_company_id,
          nextAffiliateName: result.next_affiliate_name,
          updatedBy: session.user.id,
          timestamp: new Date().toISOString()
        });
      }
    } catch (wsError) {
      console.error('WebSocket broadcast error:', wsError);
      // Don't fail the request if WebSocket fails
    }

    return NextResponse.json({
      success: true,
      result,
      message: result.message
    });

  } catch (error) {
    console.error('Error in counter-offer response:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
