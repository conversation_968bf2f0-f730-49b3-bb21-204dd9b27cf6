import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`[Quote Offers API] Fetching offers for quote: ${params.id}`);

    const supabase = createClient();

    // Fetch quote offers for this quote
    const { data: offers, error } = await supabase
      .from("quote_affiliate_offers")
      .select(`
        *,
        affiliate_companies!inner(
          id,
          name,
          city,
          state,
          email,
          phone
        )
      `)
      .eq("quote_id", params.id)
      .order("submission_order", { ascending: true, nullsLast: true });

    if (error) {
      console.error("[Quote Offers API] Database error:", error);
      return NextResponse.json(
        { success: false, error: "Failed to fetch quote offers" },
        { status: 500 }
      );
    }

    console.log(`[Quote Offers API] Found ${offers?.length || 0} offers`);

    return NextResponse.json({
      success: true,
      data: offers || [],
    });
  } catch (error) {
    console.error("[Quote Offers API] Unexpected error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
