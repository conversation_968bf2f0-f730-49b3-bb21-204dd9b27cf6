import { createServerClient, type CookieOptions } from '@supabase/ssr';

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { Database } from '@/lib/database.types';

const createSupabaseClient = () => {
  const cookieStore = cookies();
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing user sessions.
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options);
          } catch (error) {
            // The `delete` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing user sessions.
          }
        },
      },
    }
  );
};

export async function POST(
  request: Request,
  { params }: { params: { quoteId: string } }
) {
  const quoteId = params.quoteId;
  
  try {
    // Initialize Supabase client
    const supabase = createSupabaseClient();
    
    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get customer ID from user metadata
    const customerId = session.user.user_metadata.customer_id;
    if (!customerId) {
      return NextResponse.json(
        { error: 'Customer ID not found' },
        { status: 400 }
      );
    }
    
    // Verify the quote belongs to the customer
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*, affiliate_companies(id, name, email)')
      .eq('id', quoteId)
      .eq('customer_id', customerId)
      .single();
    
    if (quoteError || !quote) {
      return NextResponse.json(
        { error: 'Quote not found or not authorized' },
        { status: 404 }
      );
    }
    
    // Check if the quote is in a state that can be accepted
    if (quote.status !== 'quote_assigned' && 
        quote.status !== 'fixed_offer' && 
        quote.status !== 'quote_ready' && 
        quote.status !== 'pending_quote') {
      return NextResponse.json(
        { error: 'Quote cannot be accepted in its current state' },
        { status: 400 }
      );
    }
    
    // Update the quote status to accepted
    const { error: updateError } = await supabase
      .from('quotes')
      .update({ status: 'accepted' })
      .eq('id', quoteId);
    
    if (updateError) {
      console.error('Error updating quote:', updateError);
      return NextResponse.json(
        { error: 'Failed to accept quote' },
        { status: 500 }
      );
    }
    
    // Create a trip based on the quote
    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .insert({
        quote_id: quoteId,
        company_id: quote.company_id,
        driver_id: quote.driver_id, // This might be null
        status: 'scheduled'
      })
      .select()
      .single();
    
    if (tripError) {
      console.error('Error creating trip:', tripError);
      // Revert the quote status if trip creation fails
      await supabase
        .from('quotes')
        .update({ status: 'quote_assigned' })
        .eq('id', quoteId);
        
      return NextResponse.json(
        { error: 'Failed to create trip from quote' },
        { status: 500 }
      );
    }
    
    // Add a timeline entry for the acceptance
    const { error: timelineError } = await supabase
      .from('quote_timeline')
      .insert({
        quote_id: quoteId,
        action: 'accepted',
        user_id: session.user.id,
        details: 'Quote accepted by customer'
      });
    
    if (timelineError) {
      console.error('Error adding timeline entry:', timelineError);
      // Continue despite timeline error
    }
    
    // Return success response with trip details
    return NextResponse.json(
      { 
        message: 'Quote accepted successfully',
        trip: trip
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error accepting quote:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 