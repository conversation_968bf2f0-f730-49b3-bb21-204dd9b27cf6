import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function POST(
  request: Request,
  { params }: { params: { quoteId: string } }
) {
  const quoteId = params.quoteId;
  
  try {
    // Initialize Supabase client
    const supabase = createRouteHandlerClient({ cookies });
    
    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get customer ID from user metadata
    const customerId = session.user.user_metadata.customer_id;
    if (!customerId) {
      return NextResponse.json(
        { error: 'Customer ID not found' },
        { status: 400 }
      );
    }
    
    // Verify the quote belongs to the customer
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quoteId)
      .eq('customer_id', customerId)
      .single();
    
    if (quoteError || !quote) {
      return NextResponse.json(
        { error: 'Quote not found or not authorized' },
        { status: 404 }
      );
    }
    
    // Check if the quote is in a state that can be rejected
    if (quote.status !== 'quote_assigned') {
      return NextResponse.json(
        { error: 'Quote cannot be rejected in its current state' },
        { status: 400 }
      );
    }
    
    // Update the quote status to rejected
    const { error: updateError } = await supabase
      .from('quotes')
      .update({ status: 'rejected' })
      .eq('id', quoteId);
    
    if (updateError) {
      console.error('Error updating quote:', updateError);
      return NextResponse.json(
        { error: 'Failed to reject quote' },
        { status: 500 }
      );
    }
    
    // Add a timeline entry for the rejection
    const { error: timelineError } = await supabase
      .from('quote_timeline')
      .insert({
        quote_id: quoteId,
        action: 'rejected',
        user_id: session.user.id,
        details: 'Quote rejected by customer'
      });
    
    if (timelineError) {
      console.error('Error adding timeline entry:', timelineError);
      // Continue despite timeline error
    }
    
    // Return success response
    return NextResponse.json(
      { message: 'Quote rejected successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error rejecting quote:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 