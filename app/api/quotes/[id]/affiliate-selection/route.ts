import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, withAuth, createSupabaseClient } from '@/lib/auth/unified-auth';

/**
 * GET /api/quotes/[quoteId]/affiliate-selection
 * Get the client's affiliate selection order for a quote
 */
export const GET = withAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    // Authenticate user
    const user = await requireAuth(request);
    console.log("GET /api/quotes/[id]/affiliate-selection - User authenticated:", user.id);

    const supabase = createSupabaseClient();

    // Get user profile to check roles
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    const userRoles = profile?.roles || [];

    // Allow SUPER_ADMIN, CLIENT, and the quote owner to view selection order
    const hasAccess = userRoles.includes('SUPER_ADMIN') ||
                     userRoles.includes('CLIENT') ||
                     userRoles.includes('SERVICE_ACCOUNT');

    if (!hasAccess) {
      // Check if user is the quote owner
      const { data: quote } = await supabase
        .from('quotes')
        .select('customer_id')
        .eq('id', params.id)
        .single();

      if (!quote || quote.customer_id !== user.id) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
    }

    // Get the affiliate selection order from quote_affiliate_offers
    const { data: affiliateSelections, error } = await supabase
      .from('quote_affiliate_offers')
      .select(`
        id,
        company_id,
        submission_order,
        sent_at,
        status,
        affiliate_companies!inner(
          id,
          name,
          city,
          state,
          tier
        )
      `)
      .eq('quote_id', params.id)
      .order('submission_order', { ascending: true });

    if (error) {
      console.error('Error fetching affiliate selections:', error);
      return NextResponse.json(
        { error: 'Failed to fetch affiliate selections' },
        { status: 500 }
      );
    }

    // Transform the data for client consumption
    const selectionOrder = affiliateSelections?.map((selection, index) => ({
      order: selection.submission_order || index + 1,
      affiliateId: selection.company_id,
      affiliateName: selection.affiliate_companies.name,
      location: `${selection.affiliate_companies.city || ''}, ${selection.affiliate_companies.state || ''}`.trim().replace(/^,\s*|,\s*$/g, ''),
      tier: selection.affiliate_companies.tier || 'Standard',
      status: selection.status,
      sentAt: selection.sent_at,
      offerId: selection.id
    })) || [];

    return NextResponse.json({
      quoteId: params.id,
      selectionOrder,
      totalSelected: selectionOrder.length
    });

  } catch (error) {
    console.error('Error in affiliate selection GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/quotes/[quoteId]/affiliate-selection
 * Store the client's affiliate selection order for a quote
 */
export const POST = withAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    // Authenticate user
    const user = await requireAuth(request);
    console.log("POST /api/quotes/[id]/affiliate-selection - User authenticated:", user.id);

    const supabase = createSupabaseClient();

    const body = await request.json();
    const { selectedAffiliates } = body; // Array of { id: string, order: number }

    if (!Array.isArray(selectedAffiliates)) {
      return NextResponse.json(
        { error: 'selectedAffiliates must be an array' },
        { status: 400 }
      );
    }

    // Get user profile to check roles
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    const userRoles = profile?.roles || [];

    // Allow CLIENT and quote owner to set selection order
    const hasAccess = userRoles.includes('CLIENT') ||
                     userRoles.includes('SERVICE_ACCOUNT');

    if (!hasAccess) {
      // Check if user is the quote owner
      const { data: quote } = await supabase
        .from('quotes')
        .select('customer_id')
        .eq('id', params.id)
        .single();

      if (!quote || quote.customer_id !== user.id) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
    }

    // Fetch real affiliate names before storing
    const affiliateIds = selectedAffiliates.map((a: any) => a.id).filter(Boolean);
    const { data: affiliateCompanies, error: affiliateError } = await supabase
      .from('affiliate_companies')
      .select('id, name, city, state, tier')
      .in('id', affiliateIds);

    if (affiliateError) {
      console.error('Error fetching affiliate companies:', affiliateError);
      return NextResponse.json(
        { error: 'Failed to fetch affiliate details' },
        { status: 500 }
      );
    }

    // Map real affiliate names to the selection order
    const enrichedSelectionOrder = selectedAffiliates.map((affiliate: any) => {
      const realAffiliate = affiliateCompanies?.find((ac: any) => ac.id === affiliate.id);
      return {
        id: affiliate.id,
        name: realAffiliate?.name || affiliate.name || `Affiliate ${affiliate.id}`,
        order: affiliate.order,
        city: realAffiliate?.city || affiliate.city,
        state: realAffiliate?.state || affiliate.state,
        tier: realAffiliate?.tier || affiliate.tier || 'Standard',
        location: realAffiliate ? `${realAffiliate.city || ''}, ${realAffiliate.state || ''}`.trim().replace(/^,\s*|,\s*$/g, '') : affiliate.location
      };
    });

    // Store the enriched selection order in the quotes table
    const { error: updateError } = await supabase
      .from('quotes')
      .update({
        affiliate_selection_order: enrichedSelectionOrder,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id);

    if (updateError) {
      console.error('Error storing affiliate selection order:', updateError);
      return NextResponse.json(
        { error: 'Failed to store selection order' },
        { status: 500 }
      );
    }

    // Add timeline entry
    await supabase.from('quote_timeline').insert({
      quote_id: params.id,
      action: 'AFFILIATE_SELECTION_UPDATED',
      status: 'affiliate_selection_set',
      created_by: user.id,
      details: JSON.stringify({
        selectedAffiliates: enrichedSelectionOrder.length,
        selectionOrder: enrichedSelectionOrder
      })
    });

    return NextResponse.json({
      success: true,
      message: 'Affiliate selection order stored successfully',
      selectionOrder: enrichedSelectionOrder
    });

  } catch (error) {
    console.error('Error in affiliate selection POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
