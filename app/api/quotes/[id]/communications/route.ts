import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient()
    
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const quoteId = params.id

    // First verify the user has access to this quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quoteId)
      .eq('customer_id', session.user.id)
      .single()

    if (quoteError || !quote) {
      console.error('Error fetching quote:', quoteError)
      return NextResponse.json({ error: 'Quote not found or access denied' }, { status: 404 })
    }

    // Get the communications
    const { data: communications, error } = await supabase
      .from('quote_communications')
      .select('*, profiles(first_name, last_name, avatar_url)')
      .eq('quote_id', quoteId)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('Error fetching communications:', error)
      return NextResponse.json({ error: 'Failed to fetch communications' }, { status: 500 })
    }

    return NextResponse.json({ communications })
  } catch (error) {
    console.error('Error in communications API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient()
    
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const quoteId = params.id
    const { message } = await request.json()

    if (!message) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 })
    }

    // First verify the user has access to this quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quoteId)
      .eq('customer_id', session.user.id)
      .single()

    if (quoteError || !quote) {
      console.error('Error fetching quote:', quoteError)
      return NextResponse.json({ error: 'Quote not found or access denied' }, { status: 404 })
    }

    // Add the communication
    const { data: communication, error } = await supabase
      .from('quote_communications')
      .insert({
        quote_id: quoteId,
        sender_id: session.user.id,
        message
      })
      .select('*, profiles(first_name, last_name, avatar_url)')
      .single()

    if (error) {
      console.error('Error adding communication:', error)
      return NextResponse.json({ error: 'Failed to add communication' }, { status: 500 })
    }

    // Add a timeline entry for the communication
    await supabase
      .from('quote_timeline')
      .insert({
        quote_id: quoteId,
        user_id: session.user.id,
        action: 'message_sent',
        details: 'Message sent by customer'
      })

    return NextResponse.json({ communication })
  } catch (error) {
    console.error('Error in communications API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient()
    
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const quoteId = params.id
    const { messageId } = await request.json()

    if (!messageId) {
      return NextResponse.json({ error: 'Message ID is required' }, { status: 400 })
    }

    // First verify the user has access to this quote
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quoteId)
      .eq('customer_id', session.user.id)
      .single()

    if (quoteError || !quote) {
      console.error('Error fetching quote:', quoteError)
      return NextResponse.json({ error: 'Quote not found or access denied' }, { status: 404 })
    }

    // Mark the message as read
    const { data: communication, error } = await supabase
      .from('quote_communications')
      .update({ is_read: true })
      .eq('id', messageId)
      .eq('quote_id', quoteId)
      .select('*')
      .single()

    if (error) {
      console.error('Error updating communication:', error)
      return NextResponse.json({ error: 'Failed to update communication' }, { status: 500 })
    }

    return NextResponse.json({ communication })
  } catch (error) {
    console.error('Error in communications API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 