import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { hasRole } from '@/app/lib/auth';

/**
 * POST /api/quotes/[quoteId]/manual-assignment
 * Handle manual affiliate assignment and external sourcing
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      type, // 'manual_assignment' | 'external_sourcing'
      affiliateEmail,
      affiliateName,
      message,
      rateAmount,
      expiryHours = 24
    } = body;

    if (!type || !['manual_assignment', 'external_sourcing'].includes(type)) {
      return NextResponse.json(
        { error: 'type must be manual_assignment or external_sourcing' },
        { status: 400 }
      );
    }

    // Get user profile to check roles
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];

    // Allow SUPER_ADMIN and TNC_ADMIN to perform manual assignments
    if (!hasRole(userRoles, ['SUPER_ADMIN', 'TNC_ADMIN'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get quote details
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', params.id)
      .single();

    if (quoteError || !quote) {
      return NextResponse.json({ error: 'Quote not found' }, { status: 404 });
    }

    if (type === 'external_sourcing') {
      if (!affiliateEmail || !affiliateName) {
        return NextResponse.json(
          { error: 'affiliateEmail and affiliateName are required for external sourcing' },
          { status: 400 }
        );
      }

      // Create external affiliate recruitment email
      const emailContent = `
Dear ${affiliateName},

We have a quote request that matches your service area and would like to invite you to submit a rate.

Quote Details:
- Service: ${quote.service_type}
- Pickup: ${quote.pickup_location}
- Dropoff: ${quote.dropoff_location}
- Date: ${quote.date}
- Time: ${quote.time}
- Vehicle Type: ${quote.vehicle_type}
- Passengers: ${quote.passenger_count || 'Not specified'}

${message ? `Additional Notes: ${message}` : ''}

Please respond within ${expiryHours} hours if you can service this request.

To join our affiliate network permanently, please visit: [Registration Link]

Best regards,
TransFlow Network Team
      `.trim();

      // Log the external sourcing attempt
      await supabase.from('quote_timeline').insert({
        quote_id: params.id,
        action: 'EXTERNAL_SOURCING_INITIATED',
        status: 'external_sourcing',
        created_by: session.user.id,
        details: JSON.stringify({
          affiliateEmail,
          affiliateName,
          message,
          rateAmount,
          expiryHours,
          emailContent
        })
      });

      // In a real implementation, you would send the email here
      // For now, we'll just log it and return the email content
      console.log('External sourcing email would be sent to:', affiliateEmail);
      console.log('Email content:', emailContent);

      return NextResponse.json({
        success: true,
        type: 'external_sourcing',
        message: 'External sourcing email prepared successfully',
        emailContent,
        recipient: {
          email: affiliateEmail,
          name: affiliateName
        }
      });

    } else if (type === 'manual_assignment') {
      if (!affiliateEmail) {
        return NextResponse.json(
          { error: 'affiliateEmail is required for manual assignment' },
          { status: 400 }
        );
      }

      // Check if affiliate exists in the system
      const { data: existingAffiliate, error: affiliateError } = await supabase
        .from('affiliate_companies')
        .select('id, name, contact_email')
        .eq('contact_email', affiliateEmail)
        .single();

      if (affiliateError || !existingAffiliate) {
        return NextResponse.json(
          { error: 'Affiliate not found in system. Use external_sourcing instead.' },
          { status: 404 }
        );
      }

      // Create manual assignment offer
      const expiryDate = new Date();
      expiryDate.setHours(expiryDate.getHours() + expiryHours);

      const { data: offer, error: offerError } = await supabase
        .from('quote_affiliate_offers')
        .insert({
          quote_id: params.id,
          company_id: existingAffiliate.id,
          rate_amount: rateAmount || 0,
          currency: 'USD',
          status: 'PENDING',
          expires_at: expiryDate.toISOString(),
          notes: `Manual assignment by ${userRoles.includes('SUPER_ADMIN') ? 'Super Admin' : 'TNC Admin'}: ${message || 'No additional notes'}`,
          created_by: session.user.id,
          updated_by: session.user.id,
          submission_order: 999 // High number to indicate manual assignment
        })
        .select()
        .single();

      if (offerError) {
        console.error('Error creating manual assignment offer:', offerError);
        return NextResponse.json(
          { error: 'Failed to create manual assignment' },
          { status: 500 }
        );
      }

      // Update quote status
      await supabase
        .from('quotes')
        .update({
          status: 'manual_assignment',
          updated_at: new Date().toISOString()
        })
        .eq('id', params.id);

      // Log the manual assignment
      await supabase.from('quote_timeline').insert({
        quote_id: params.id,
        action: 'MANUAL_ASSIGNMENT_CREATED',
        status: 'manual_assignment',
        created_by: session.user.id,
        details: JSON.stringify({
          affiliateId: existingAffiliate.id,
          affiliateName: existingAffiliate.name,
          affiliateEmail,
          rateAmount,
          expiryHours,
          message,
          offerId: offer.id
        })
      });

      return NextResponse.json({
        success: true,
        type: 'manual_assignment',
        message: 'Manual assignment created successfully',
        offer: {
          id: offer.id,
          affiliateId: existingAffiliate.id,
          affiliateName: existingAffiliate.name,
          rateAmount: offer.rate_amount,
          expiresAt: offer.expires_at
        }
      });
    }

  } catch (error) {
    console.error('Error in manual assignment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/quotes/[quoteId]/manual-assignment
 * Get manual assignment history for a quote
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get manual assignment history from timeline
    const { data: timeline, error: timelineError } = await supabase
      .from('quote_timeline')
      .select('*')
      .eq('quote_id', params.id)
      .in('action', ['MANUAL_ASSIGNMENT_CREATED', 'EXTERNAL_SOURCING_INITIATED'])
      .order('created_at', { ascending: false });

    if (timelineError) {
      console.error('Error fetching manual assignment history:', timelineError);
      return NextResponse.json(
        { error: 'Failed to fetch assignment history' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      quoteId: params.id,
      assignmentHistory: timeline || []
    });

  } catch (error) {
    console.error('Error fetching manual assignment history:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
