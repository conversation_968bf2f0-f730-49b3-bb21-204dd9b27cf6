import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequest } from '@/lib/auth/api-authentication';

export async function POST(request: NextRequest) {
  try {
    // Authenticate and get user/org context
    const authResult = await authenticateApiRequest(request, ['CLIENT', 'CLIENT_COORDINATOR', 'SUPER_ADMIN']);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: authResult.statusCode || 401 });
    }
    const organizationId = authResult.organization?.id || authResult.user.organization_id;
    if (!organizationId) {
      return NextResponse.json({ error: 'Organization context required' }, { status: 400 });
    }

    const body = await request.json();
    const { city, vehicleType, serviceType = 'point_to_point', date } = body;

    console.log('Matching affiliates for:', { city, vehicleType, serviceType, date, passengers });

    if (!city || !vehicleType) {
      return NextResponse.json(
        { error: 'City and vehicle type are required' },
        { status: 400 }
      );
    }

    const params = {
      p_organization_id: organizationId,
      p_pickup_lat: 25.7617, // Default Miami coordinates
      p_pickup_lng: -80.1918,
      p_pickup_city: city,
      p_vehicle_type: vehicleType,
      p_pickup_date: date || new Date().toISOString().split('T')[0],
      p_service_type: serviceType
    };

    console.log('Database function parameters:', params);

    // Use org-scoped function
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      { auth: { autoRefreshToken: false, persistSession: false } }
    );

    const { data: matchingAffiliates, error: matchingError } = await supabase
      .rpc('find_matching_affiliates_for_quote', params);

    if (matchingError) {
      console.error('Database function error:', matchingError);
      return NextResponse.json(
        { error: 'Failed to fetch affiliates', details: matchingError.message },
        { status: 500 }
      );
    }

    console.log(`Found ${matchingAffiliates?.length || 0} matching affiliates`);
    console.log('Raw affiliate data:', matchingAffiliates);

    // Transform the data to match the expected frontend format
    const affiliates = (matchingAffiliates || []).map((affiliate: any) => ({
      id: affiliate.affiliate_id,
      company_name: affiliate.company_name,
      tier: affiliate.matching_score >= 80 ? 'premium' : 
            affiliate.matching_score >= 60 ? 'standard' : 'basic',
      distance_miles: affiliate.distance_miles,
      matching_score: affiliate.matching_score,
      has_rates: affiliate.has_rates,
      has_active_vehicles: affiliate.has_active_vehicles,
      in_service_area: affiliate.in_service_area,
      rate_info: affiliate.rate_info,
      estimated_price: affiliate.rate_info?.base_rate || null,
      onboarding_status: {
        has_fleet: affiliate.has_active_vehicles,
        has_rates: affiliate.has_rates,
        approval_status: 'approved' // Since our function only returns approved affiliates
      }
    }));

    return NextResponse.json({
      success: true,
      affiliates,
      total: affiliates.length,
      city,
      vehicleType,
      debug: {
        function_called: 'find_matching_affiliates_for_quote',
        parameters: params,
        raw_results: matchingAffiliates?.length || 0
      }
    });

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}