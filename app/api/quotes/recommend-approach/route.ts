// API endpoint for quote processing approach recommendations

export const runtime = 'nodejs'
// This determines whether to use fixed rates or competitive bidding
// based on affiliate configuration and availability

import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const {
      pickup_lat,
      pickup_lng,
      pickup_city,
      vehicle_type,
      pickup_date
    } = body

    // Validate required parameters
    if (!pickup_lat || !pickup_lng || !vehicle_type) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "pickup_lat, pickup_lng, and vehicle_type are required"
      }, { status: 400 })
    }

    // Get processing approach recommendation
    const { data: recommendation, error: recommendationError } = await supabase
      .rpc('recommend_quote_processing_approach', {
        p_pickup_lat: pickup_lat,
        p_pickup_lng: pickup_lng,
        p_pickup_city: pickup_city,
        p_vehicle_type: vehicle_type,
        p_pickup_date: pickup_date || new Date().toISOString().split('T')[0]
      })

    if (recommendationError) {
      console.error('Error getting processing recommendation:', recommendationError)
      return NextResponse.json({
        error: "Failed to get processing recommendation",
        details: recommendationError.message
      }, { status: 500 })
    }

    const result = recommendation?.[0]
    if (!result) {
      return NextResponse.json({
        error: "No recommendation available",
        details: "Unable to determine processing approach"
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: {
        recommended_approach: result.recommended_approach,
        reason: result.reason,
        statistics: {
          matching_affiliates_count: result.matching_affiliates_count,
          in_service_area_count: result.in_service_area_count,
          has_date_blocks_count: result.has_date_blocks_count
        },
        confidence_score: result.confidence_score
      }
    })

  } catch (error) {
    console.error('Error in quote processing recommendation API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const url = new URL(req.url)
    const pickup_lat = parseFloat(url.searchParams.get('pickup_lat') || '0')
    const pickup_lng = parseFloat(url.searchParams.get('pickup_lng') || '0')
    const pickup_city = url.searchParams.get('pickup_city')
    const vehicle_type = url.searchParams.get('vehicle_type')
    const pickup_date = url.searchParams.get('pickup_date')

    // Validate required parameters
    if (!pickup_lat || !pickup_lng || !vehicle_type) {
      return NextResponse.json({
        error: "Missing required parameters",
        details: "pickup_lat, pickup_lng, and vehicle_type are required"
      }, { status: 400 })
    }

    // Get processing approach recommendation
    const { data: recommendation, error: recommendationError } = await supabase
      .rpc('recommend_quote_processing_approach', {
        p_pickup_lat: pickup_lat,
        p_pickup_lng: pickup_lng,
        p_pickup_city: pickup_city,
        p_vehicle_type: vehicle_type,
        p_pickup_date: pickup_date || new Date().toISOString().split('T')[0]
      })

    if (recommendationError) {
      console.error('Error getting processing recommendation:', recommendationError)
      return NextResponse.json({
        error: "Failed to get processing recommendation",
        details: recommendationError.message
      }, { status: 500 })
    }

    const result = recommendation?.[0]
    if (!result) {
      return NextResponse.json({
        error: "No recommendation available",
        details: "Unable to determine processing approach"
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: {
        recommended_approach: result.recommended_approach,
        reason: result.reason,
        statistics: {
          matching_affiliates_count: result.matching_affiliates_count,
          in_service_area_count: result.in_service_area_count,
          has_date_blocks_count: result.has_date_blocks_count
        },
        confidence_score: result.confidence_score
      }
    })

  } catch (error) {
    console.error('Error in quote processing recommendation API:', error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}