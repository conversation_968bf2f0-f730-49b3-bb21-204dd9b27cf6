import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';
import { authenticateBasicRequest, canAccessOrganization } from '@/lib/auth/api-authentication';
import { z } from 'zod';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Input validation schema
const querySchema = z.object({
  include_inactive: z.boolean().optional(),
  network_type: z.enum(['platform', 'tnc_managed']).optional(),
  organization_id: z.string().uuid().optional()
});

// Standardized API response interface
interface NetworksResponse {
  success: boolean;
  data?: {
    networks: any[];
    currentNetwork: any;
  };
  error?: {
    code: string;
    message: string;
    details?: string;
  };
  meta?: {
    total: number;
    timestamp: string;
  };
}

// GET handler to fetch available networks for the network switcher
export async function GET(request: NextRequest): Promise<NextResponse<NetworksResponse>> {
  try {
    console.log("Networks API GET - Starting request with authentication");
    
    // Use standardized authentication
    const authResult = await authenticateBasicRequest(request);
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
          details: 'Valid JWT token or session required'
        }
      }, { status: authResult.statusCode || 401 });
    }

    const { user, organization } = authResult;
    const isSuperAdmin = user.is_super_admin;

    // Validate query parameters
    const url = new URL(request.url);
    const queryParams = {
      include_inactive: url.searchParams.get('include_inactive') === 'true',
      network_type: url.searchParams.get('network_type') as 'platform' | 'tnc_managed' | undefined,
      organization_id: url.searchParams.get('organization_id') || undefined
    };

    const validatedQuery = querySchema.parse(queryParams);

    // Validate organization access if organization_id provided
    if (validatedQuery.organization_id && !canAccessOrganization(user, validatedQuery.organization_id)) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Organization access denied',
          details: 'User cannot access specified organization'
        }
      }, { status: 403 });
    }

    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Build available networks based on user role and organization
    let availableNetworks = [];

    if (isSuperAdmin) {
      // SUPER_ADMIN can see all networks - query from database
      let query = supabase
        .from('affiliate_networks')
        .select('id, name, slug, network_type, description, is_active, is_public')
        .order('name');

      // Apply filters based on query parameters
      if (!validatedQuery.include_inactive) {
        query = query.eq('is_active', true);
      }
      
      if (validatedQuery.network_type) {
        query = query.eq('network_type', validatedQuery.network_type);
      }

      const { data: allNetworks, error: networksError } = await query;

      if (networksError) {
        console.error('Error fetching networks:', networksError);
        return NextResponse.json({
          success: false,
          error: {
            code: 'DATABASE_ERROR',
            message: 'Failed to fetch networks',
            details: networksError.message
          }
        }, { status: 500 });
      }

      // Transform database results to match expected format
      availableNetworks = (allNetworks || []).map(network => ({
        id: network.id,
        name: network.name,
        slug: network.slug,
        account_type: network.network_type === 'platform' ? 'platform' : 'tnc_managed',
        organization_type: network.network_type === 'platform' ? 'shared' : 'segregated',
        status: network.is_active ? 'active' : 'inactive',
        description: network.description || '',
        is_default: network.id === '********-1111-1111-1111-************', // TransFlow Shared Network
        is_public: network.is_public
      }));

      console.log(`SUPER_ADMIN: Found ${availableNetworks.length} networks from database`);
    } else {
      // Regular users see networks based on their organization context
      // For now, default to TransFlow Shared Network
      // TODO: Implement organization-specific network access logic
      availableNetworks.push({
        id: '********-1111-1111-1111-************',
        name: 'TransFlow Shared Network',
        slug: 'transflow-shared',
        account_type: 'platform',
        organization_type: 'shared',
        status: 'active',
        description: 'Platform-wide shared affiliate network',
        is_default: true,
        is_public: true
      });

      // Add organization-specific networks if user has access
      if (organization && organization.organization_type === 'segregated') {
        // TODO: Query organization-specific networks
        console.log(`Regular user in segregated organization: ${organization.id}`);
      }
    }

    // Determine current network (default to first available or shared)
    const currentNetwork = availableNetworks.find(n => n.is_default) || availableNetworks[0];

    console.log(`Found ${availableNetworks.length} networks for user ${user.id} (${user.role})`);
    
    return NextResponse.json({
      success: true,
      data: {
        networks: availableNetworks,
        currentNetwork: currentNetwork
      },
      meta: {
        total: availableNetworks.length,
        timestamp: new Date().toISOString(),
        user_context: {
          role: user.role,
          organization_id: user.organization_id,
          is_super_admin: user.is_super_admin
        }
      }
    });

  } catch (error) {
    console.error('Error in GET /api/networks:', error);
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid query parameters',
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
        }
      }, { status: 400 });
    }
    
    // Handle other errors
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 });
  }
}