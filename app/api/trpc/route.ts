import { fetchRe<PERSON>Hand<PERSON> } from '@trpc/server/adapters/fetch';
import { appRouter } from '@/server/routers';
import { createContext } from '@/server/trpc';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
const handler = (req: Request) =>
  fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: () => createContext({ req, resHeaders: new Headers() }),
  });

export { handler as GET, handler as POST }; 