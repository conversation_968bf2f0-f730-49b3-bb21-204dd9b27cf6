import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(request: NextRequest) {
  try {
    // Create Supabase client with proper auth
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('Event Manager Events API - Fetching events for user:', user.id)

    // Fetch events for the authenticated user with passenger information
    const { data: events, error: eventsError } = await supabase
      .from('events')
      .select(`
        id,
        name,
        description,
        start_date,
        end_date,
        location,
        status,
        total_passengers,
        created_at,
        event_passengers (
          id,
          passengers (
            id,
            first_name,
            last_name,
            email,
            passenger_type
          )
        )
      `)
      .eq('created_by', user.id)
      .order('start_date', { ascending: true })

    if (eventsError) {
      console.error('Error fetching events:', eventsError)
      return NextResponse.json({ error: 'Failed to fetch events' }, { status: 500 })
    }

    // Transform the data to match the expected format with passenger information
    const transformedEvents = (events || []).map((event: any) => ({
      id: event.id,
      name: event.name,
      description: event.description,
      start_date: event.start_date,
      end_date: event.end_date,
      location: event.location,
      status: event.status,
      total_passengers: event.total_passengers,
      created_at: event.created_at,
      passenger_count: event.event_passengers?.length || 0,
      passengers: event.event_passengers?.map((ep: any) => ep.passengers) || []
    }))

    console.log(`Event Manager Events API - Returning ${transformedEvents.length} events`)

    return NextResponse.json({
      success: true,
      events: transformedEvents
    })

  } catch (error) {
    console.error('Error in event manager events API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      description,
      start_date,
      end_date,
      location,
      total_passengers,
      type,
      vehicle_types,
      additional_notes,
      passenger_ids = []
    } = body

    // Create Supabase client with proper auth
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Validate required fields
    if (!name || !start_date || !location) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Create the event (organization_id will be handled by RLS)
    const { data: event, error: createError } = await supabase
      .from('events')
      .insert({
        name,
        description,
        start_date,
        end_date,
        location,
        total_passengers: total_passengers || 0,
        type: type || 'general',
        vehicle_types: vehicle_types || [],
        additional_notes,
        status: 'planning',
        customer_id: user.id, // Set customer_id to the authenticated user
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (createError) {
      console.error('Error creating event:', createError)
      return NextResponse.json({ error: 'Failed to create event' }, { status: 500 })
    }

    // Link passengers to event if provided
    if (passenger_ids.length > 0) {
      const eventPassengerLinks = passenger_ids.map((passengerId: string) => ({
        event_id: event.id,
        passenger_id: passengerId
      }))

      const { error: linkError } = await supabase
        .from('event_passengers')
        .insert(eventPassengerLinks)

      if (linkError) {
        console.error('Error linking passengers to event:', linkError)
        // Don't fail the entire request, just log the error
      }
    }

    // Fetch the complete event with passengers
    const { data: completeEvent, error: fetchError } = await supabase
      .from('events')
      .select(`
        id,
        name,
        description,
        start_date,
        end_date,
        location,
        status,
        total_passengers,
        created_at,
        event_passengers (
          id,
          passengers (
            id,
            first_name,
            last_name,
            email,
            passenger_type
          )
        )
      `)
      .eq('id', event.id)
      .single()

    if (fetchError) {
      console.error('Error fetching complete event:', fetchError)
      return NextResponse.json({ success: true, event }, { status: 201 })
    }

    const eventWithPassengers = {
      ...completeEvent,
      passenger_count: completeEvent.event_passengers?.length || 0,
      passengers: completeEvent.event_passengers?.map((ep: any) => ep.passengers) || []
    }

    return NextResponse.json({
      success: true,
      event: eventWithPassengers
    })

  } catch (error) {
    console.error('Error in event manager events POST API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
