import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const eventId = params.id

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch event details with related data
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select(`
        *,
        quotes (
          id,
          reference_number,
          service_type,
          vehicle_type,
          pickup_location,
          dropoff_location,
          pickup_date,
          pickup_time,
          passenger_count,
          luggage_count,
          special_requests,
          status,
          priority,
          total_amount,
          base_fare,
          additional_services,
          gratuity,
          admin_fee,
          notes,
          created_at
        )
      `)
      .eq('id', eventId)
      .single()

    if (eventError) {
      console.error('Error fetching event:', eventError)
      return NextResponse.json({ error: 'Event not found' }, { status: 404 })
    }

    // Fetch live trips for this event
    const { data: trips, error: tripsError } = await supabase
      .from('trips')
      .select(`
        *,
        drivers (
          first_name,
          last_name,
          phone
        ),
        vehicles (
          make,
          model,
          license_plate,
          vehicle_type
        ),
        quotes (
          passenger_count,
          pickup_location,
          dropoff_location
        )
      `)
      .eq('quote_id', event.quotes?.map((q: any) => q.id))
      .in('status', ['scheduled', 'on_the_way', 'waiting_for_passenger', 'passenger_on_board', 'in_progress'])

    if (tripsError) {
      console.error('Error fetching trips:', tripsError)
    }

    // Transform the data to match the expected format
    const transformedEvent = {
      id: event.id,
      title: event.name,
      description: event.description,
      startDate: new Date(event.start_date),
      endDate: new Date(event.end_date),
      location: event.location,
      status: event.status,
      totalPassengers: event.total_passengers,
      quotes: event.quotes?.map((quote: any) => ({
        id: quote.id,
        vehicle: quote.vehicle_type,
        passengerCount: quote.passenger_count,
        pickupLocation: quote.pickup_location,
        dropoffLocation: quote.dropoff_location,
        date: quote.pickup_date,
        time: quote.pickup_time,
        status: quote.status,
        price: {
          baseRate: quote.base_fare || 0,
          gratuity: quote.gratuity || 0,
          adminFee: quote.admin_fee || 0,
          total: quote.total_amount || 0
        },
        notes: quote.notes,
        guestGroup: 'General Attendees', // Default for now
        eventActivity: 'Event Transportation', // Default for now
        eventRelation: 'Event Transport' // Default for now
      })) || [],
      trips: trips?.map((trip: any) => ({
        id: trip.id,
        vehicle: trip.vehicles ? `${trip.vehicles.make} ${trip.vehicles.model}` : 'Unknown Vehicle',
        passengerCount: trip.passengers || trip.quotes?.passenger_count || 1,
        passengers: [
          {
            id: 'p1',
            name: 'Event Passenger',
            group: 'Attendees'
          }
        ],
        pickupLocation: trip.pickup_location || trip.quotes?.pickup_location || 'Unknown',
        dropoffLocation: trip.dropoff_location || trip.quotes?.dropoff_location || 'Unknown',
        time: trip.pickup_time ? new Date(trip.pickup_time).toLocaleTimeString() : 'TBD',
        status: trip.status,
        stage: trip.status === 'in_progress' ? 'passenger_on_board' : 
               trip.status === 'on_the_way' ? 'on_the_way' :
               trip.status === 'waiting_for_passenger' ? 'waiting_for_passenger' : 'scheduled',
        type: 'transfer',
        notes: trip.notes,
        driver: trip.drivers ? {
          name: `${trip.drivers.first_name} ${trip.drivers.last_name}`,
          phone: trip.drivers.phone
        } : undefined,
        estimatedDuration: 15 // Default estimate
      })) || []
    }

    return NextResponse.json({
      success: true,
      event: transformedEvent
    })

  } catch (error) {
    console.error('Error in event details API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const eventId = params.id
    const body = await request.json()

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Update the event
    const { data: event, error: updateError } = await supabase
      .from('events')
      .update({
        name: body.name,
        description: body.description,
        start_date: body.start_date,
        end_date: body.end_date,
        location: body.location,
        total_passengers: body.total_passengers,
        status: body.status,
        updated_at: new Date().toISOString()
      })
      .eq('id', eventId)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating event:', updateError)
      return NextResponse.json({ error: 'Failed to update event' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      event: event
    })

  } catch (error) {
    console.error('Error in event update API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
