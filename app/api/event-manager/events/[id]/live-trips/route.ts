import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const eventId = params.id

    // Create Supabase client
    const supabase = createClient()

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch live trips for this event
    const { data: trips, error: tripsError } = await supabase
      .from('trips')
      .select(`
        *,
        drivers (
          id,
          first_name,
          last_name,
          phone,
          rating
        ),
        vehicles (
          id,
          make,
          model,
          license_plate,
          vehicle_type,
          capacity
        ),
        quotes (
          id,
          passenger_count,
          pickup_location,
          dropoff_location,
          special_requests,
          event_id
        ),
        affiliate_companies (
          id,
          name,
          phone
        )
      `)
      .eq('quotes.event_id', eventId)
      .in('status', ['scheduled', 'on_the_way', 'waiting_for_passenger', 'passenger_on_board', 'in_progress', 'completed'])
      .order('pickup_time', { ascending: true })

    if (tripsError) {
      console.error('Error fetching live trips:', tripsError)
      return NextResponse.json({ error: 'Failed to fetch live trips' }, { status: 500 })
    }

    // Also fetch trips that might be related to the event but not through quotes
    const { data: directTrips, error: directTripsError } = await supabase
      .from('trips')
      .select(`
        *,
        drivers (
          id,
          first_name,
          last_name,
          phone,
          rating
        ),
        vehicles (
          id,
          make,
          model,
          license_plate,
          vehicle_type,
          capacity
        ),
        affiliate_companies (
          id,
          name,
          phone
        )
      `)
      .is('quote_id', null)
      .in('status', ['scheduled', 'on_the_way', 'waiting_for_passenger', 'passenger_on_board', 'in_progress'])
      .gte('pickup_time', new Date().toISOString())
      .order('pickup_time', { ascending: true })
      .limit(10) // Limit to recent direct bookings

    if (directTripsError) {
      console.error('Error fetching direct trips:', directTripsError)
    }

    // Combine and transform the trips data
    const allTrips = [...(trips || []), ...(directTrips || [])]
    
    const transformedTrips = allTrips.map((trip: any) => {
      // Determine trip stage based on status and timing
      let stage = 'scheduled'
      const now = new Date()
      const pickupTime = trip.pickup_time ? new Date(trip.pickup_time) : null
      const actualStartTime = trip.actual_start_time ? new Date(trip.actual_start_time) : null

      if (trip.status === 'completed') {
        stage = 'dropped_off'
      } else if (trip.status === 'in_progress' || (actualStartTime && actualStartTime <= now)) {
        stage = 'passenger_on_board'
      } else if (trip.status === 'waiting_for_passenger') {
        stage = 'waiting_for_passenger'
      } else if (trip.status === 'on_the_way' || (pickupTime && pickupTime <= now)) {
        stage = 'on_the_way'
      }

      // Generate mock passengers based on passenger count
      const passengers = []
      const passengerCount = trip.passengers || trip.quotes?.passenger_count || 1
      for (let i = 0; i < passengerCount; i++) {
        passengers.push({
          id: `p${i + 1}`,
          name: `Passenger ${i + 1}`,
          group: trip.quotes?.special_requests?.includes('VIP') ? 'VIP' : 'Attendees',
          isVIP: trip.quotes?.special_requests?.includes('VIP') || false
        })
      }

      return {
        id: trip.id,
        vehicle: trip.vehicles ? `${trip.vehicles.make} ${trip.vehicles.model}` : 'Unknown Vehicle',
        vehicleType: trip.vehicles?.vehicle_type || 'sedan',
        passengerCount: passengerCount,
        passengers: passengers,
        pickupLocation: trip.pickup_location || trip.quotes?.pickup_location || 'Unknown Location',
        dropoffLocation: trip.dropoff_location || trip.quotes?.dropoff_location || 'Unknown Destination',
        time: trip.pickup_time ? new Date(trip.pickup_time).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        }) : 'TBD',
        status: trip.status,
        stage: stage,
        type: trip.pickup_location?.includes('Airport') || trip.dropoff_location?.includes('Airport') ? 'arrival' : 'transfer',
        notes: trip.notes || '',
        driver: trip.drivers ? {
          name: `${trip.drivers.first_name} ${trip.drivers.last_name}`,
          phone: trip.drivers.phone,
          rating: trip.drivers.rating
        } : undefined,
        affiliate: trip.affiliate_companies ? {
          name: trip.affiliate_companies.name,
          phone: trip.affiliate_companies.phone
        } : undefined,
        estimatedDuration: 30, // Default estimate in minutes
        actualStartTime: trip.actual_start_time,
        scheduledPickupTime: trip.pickup_time,
        scheduledDropoffTime: trip.dropoff_time,
        // Mock coordinates for Austin area
        currentLocation: stage === 'passenger_on_board' ? {
          lat: 30.2672 + (Math.random() - 0.5) * 0.1,
          lng: -97.7431 + (Math.random() - 0.5) * 0.1
        } : null
      }
    })

    // Calculate event performance metrics
    const totalTrips = transformedTrips.length
    const completedTrips = transformedTrips.filter(t => t.stage === 'dropped_off').length
    const onTimeTrips = transformedTrips.filter(t => {
      // Mock on-time calculation
      return Math.random() > 0.1 // 90% on-time rate
    }).length
    const delayedTrips = transformedTrips.filter(t => {
      return t.stage !== 'dropped_off' && Math.random() > 0.8 // 20% delayed
    }).length
    const vipTrips = transformedTrips.filter(t => 
      t.passengers.some(p => p.isVIP)
    ).length

    const performanceMetrics = {
      totalTrips,
      completedTrips,
      onTimeRate: totalTrips > 0 ? Math.round((onTimeTrips / totalTrips) * 100) : 0,
      delayedTrips,
      vipTrips,
      guestCoverage: {
        covered: Math.min(transformedTrips.reduce((sum, t) => sum + t.passengerCount, 0), 140),
        total: 140,
        percentage: Math.min(Math.round((transformedTrips.reduce((sum, t) => sum + t.passengerCount, 0) / 140) * 100), 100)
      }
    }

    return NextResponse.json({
      success: true,
      trips: transformedTrips,
      metrics: performanceMetrics
    })

  } catch (error) {
    console.error('Error in live trips API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
