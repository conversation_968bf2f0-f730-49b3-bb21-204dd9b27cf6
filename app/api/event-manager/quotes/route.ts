import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(request: NextRequest) {
  try {
    console.log('Event Manager Quotes API - Starting request')
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')
    console.log('Event Manager Quotes API - Params:', { status, limit, offset })

    // Create Supabase client with proper auth
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('Event Manager Quotes API - Fetching quotes for user:', user.id)

    // Build the query - use correct column names and make joins optional
    let query = supabase
      .from('quotes')
      .select(`
        id,
        reference_number,
        pickup_location,
        dropoff_location,
        city,
        date,
        time,
        passenger_count,
        vehicle_type,
        service_type,
        status,
        created_at,
        total_amount,
        customer_id,
        contact_name,
        contact_email,
        quote_affiliate_offers (
          id,
          affiliate_company_id,
          status,
          offer_price,
          affiliate_companies (
            name
          )
        )
      `)
      .eq('customer_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Filter by status if provided (normalize to lowercase for database)
    if (status && status !== 'all') {
      query = query.eq('status', status.toLowerCase())
    }

    console.log('Event Manager Quotes API - Executing database query')
    const { data: quotes, error: quotesError } = await query

    if (quotesError) {
      console.error('Event Manager Quotes API - Database error:', quotesError)
      return NextResponse.json({ error: 'Failed to fetch quotes' }, { status: 500 })
    }

    console.log('Event Manager Quotes API - Query successful, got', quotes?.length || 0, 'quotes')

    // Get user profile for client name
    const { data: profile } = await supabase
      .from('profiles')
      .select('first_name, last_name')
      .eq('id', user.id)
      .single()

    const clientName = profile ? `${profile.first_name} ${profile.last_name}`.trim() : 'Unknown Customer'

    // Transform the data to match the expected format
    const transformedQuotes = (quotes || []).map((quote: any) => {
      // Group affiliate responses
      const affiliateResponses = quote.quote_affiliate_offers?.map((offer: any) => ({
        affiliate_name: offer.affiliate_companies?.name || 'Unknown Affiliate',
        status: offer.status || 'pending',
        price: offer.offer_price || 0,
        tier: 'Standard' // Default tier since we don't have this field
      })) || []

      return {
        id: quote.id,
        reference_number: quote.reference_number,
        pickup_location: quote.pickup_location,
        dropoff_location: quote.dropoff_location,
        city: quote.city,
        pickup_date: quote.date,
        pickup_time: quote.time,
        passenger_count: quote.passenger_count,
        vehicle_type_preference: quote.vehicle_type,
        service_type: quote.service_type,
        status: quote.status,
        created_at: quote.created_at,
        client_name: quote.contact_name || clientName,
        total_price: quote.total_amount,
        affiliate_responses: affiliateResponses
      }
    })

    // Calculate summary stats
    const stats = {
      total: transformedQuotes.length,
      pending: transformedQuotes.filter(q => q.status === 'pending' || q.status === 'pending_affiliate_response').length,
      accepted: transformedQuotes.filter(q => q.status === 'accepted').length,
      rejected: transformedQuotes.filter(q => q.status === 'rejected').length,
      completed: transformedQuotes.filter(q => q.status === 'completed').length,
      totalValue: transformedQuotes.reduce((sum, q) => sum + (q.total_price || 0), 0)
    }

    console.log(`Event Manager Quotes API - Returning ${transformedQuotes.length} quotes`)

    return NextResponse.json({
      success: true,
      quotes: transformedQuotes,
      stats: stats,
      pagination: {
        limit,
        offset,
        total: transformedQuotes.length
      }
    })

  } catch (error) {
    console.error('Error in event manager quotes API:', error)
    console.error('Error details:', error instanceof Error ? error.message : error)
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return NextResponse.json(
      { error: 'Failed to fetch quotes', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, quoteId, ...data } = body

    // Create Supabase client with proper auth
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    switch (action) {
      case 'cancel_quote':
        const { error: cancelError } = await supabase
          .from('quotes')
          .update({ 
            status: 'cancelled',
            updated_at: new Date().toISOString()
          })
          .eq('id', quoteId)
          .eq('customer_id', user.id)

        if (cancelError) {
          return NextResponse.json({ error: 'Failed to cancel quote' }, { status: 500 })
        }

        return NextResponse.json({ success: true, message: 'Quote cancelled successfully' })

      case 'accept_offer':
        const { affiliateOfferId } = data
        
        // Update the specific affiliate offer to accepted
        const { error: acceptError } = await supabase
          .from('quote_affiliate_offers')
          .update({ 
            status: 'accepted',
            updated_at: new Date().toISOString()
          })
          .eq('id', affiliateOfferId)

        if (acceptError) {
          return NextResponse.json({ error: 'Failed to accept offer' }, { status: 500 })
        }

        // Update the main quote status
        const { error: quoteUpdateError } = await supabase
          .from('quotes')
          .update({ 
            status: 'accepted',
            updated_at: new Date().toISOString()
          })
          .eq('id', quoteId)
          .eq('customer_id', user.id)

        if (quoteUpdateError) {
          return NextResponse.json({ error: 'Failed to update quote status' }, { status: 500 })
        }

        return NextResponse.json({ success: true, message: 'Offer accepted successfully' })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error in event manager quotes POST API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
