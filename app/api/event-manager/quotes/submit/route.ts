import { NextRequest, NextResponse } from "next/server";

export const runtime = 'nodejs'
import { createServerClient } from "@supabase/ssr";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { extractCityFromQuoteRequest } from "@/app/utils/geocoding";
import { triggerQuoteUpdateBroadcast } from "@/lib/websocket/quote-broadcaster";

export async function POST(request: NextRequest) {
  console.log("=== QUOTE SUBMIT API CALLED ===");
  try {
    const body = await request.json();
    const { quoteRequest, selectedAffiliates } = body;
    console.log("Request body received:", { quoteRequest, selectedAffiliates });

    if (
      !quoteRequest ||
      !selectedAffiliates ||
      selectedAffiliates.length === 0
    ) {
      return NextResponse.json(
        { error: "Quote request and selected affiliates are required" },
        { status: 400 }
      );
    }

    // Create Supabase client with proper auth for user verification
    const cookieStore = cookies();
    const authSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Verify user is authenticated - get token from request
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '') || 
                  request.cookies.get('supabase-auth-token')?.value;
    
    if (token) {
      await authSupabase.auth.setSession({ access_token: token, refresh_token: '' });
    }
    
    const {
      data: { user },
      error: authError,
    } = await authSupabase.auth.getUser();
    console.log("Auth check result:", {
      user: !!user,
      authError,
      userEmail: user?.email,
    });
    if (authError || !user) {
      console.error("Authentication failed:", authError);
      console.error("Auth error details:", JSON.stringify(authError, null, 2));
      return NextResponse.json(
        {
          error: "Unauthorized",
          details: authError,
          message: "User authentication failed. Please try logging in again.",
        },
        { status: 401 }
      );
    }

    console.log("User authenticated:", user.id);

    // Create service role client for database operations (bypasses RLS)
    console.log(
      "Using service role key:",
      !!process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Use createClient directly for service role to bypass RLS properly
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    console.log("Event Manager Quote Submit API - Starting request");
    console.log("Quote Request:", JSON.stringify(quoteRequest, null, 2));
    console.log(
      "Selected Affiliates:",
      JSON.stringify(selectedAffiliates, null, 2)
    );
    console.log("User ID:", user.id);

    // Generate quote reference number
    const quoteReference = `Q${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    // Map frontend service types to database values (must match check constraint)
    const rawServiceType = quoteRequest.service_type || "point-to-point";
    const service_type =
      rawServiceType === "point-to-point" ? "point_to_point" : rawServiceType;

    // Extract city using proper geocoding from coordinates
    console.log("Extracting city from quote request using geocoding...");
    const city = await extractCityFromQuoteRequest(quoteRequest);
    console.log("City extracted:", city);

    // Create the main quote record - using correct field names from form
    const { data: quote, error: quoteError } = await supabase
      .from("quotes")
      .insert({
        reference_number: quoteReference,
        customer_id: user.id,
        organization_id: (await supabase
          .from('profiles')
          .select('organization_id')
          .eq('id', user.id)
          .single()
        ).data?.organization_id || '734b53df-4c3e-48ca-a822-cfccbabb68b7',
        pickup_location: quoteRequest.pickup_location || "Unknown Location",
        dropoff_location: quoteRequest.dropoff_location || "Unknown Location",
        city: city,
        date: quoteRequest.date || new Date().toISOString().split("T")[0],
        time: quoteRequest.time || "12:00",
        duration: quoteRequest.duration_hours
          ? `${quoteRequest.duration_hours} hours`
          : "1 hour",
        distance: "25 miles", // Default distance - could be calculated
        passenger_count: parseInt(quoteRequest.passenger_count) || 1,
        vehicle_type: quoteRequest.vehicle_type || "sedan",
        service_type: service_type,
        special_requests: quoteRequest.special_requests
          ? [quoteRequest.special_requests]
          : null,
        event_id:
          quoteRequest.event_id === "none" ? null : quoteRequest.event_id,
        status: "pending",
        // Add contact information
        contact_name: quoteRequest.contact_name || user.email,
        contact_email: quoteRequest.contact_email || user.email,
        contact_phone: quoteRequest.contact_phone || null,
        // Add flight information for airport transfers
        flight_number: quoteRequest.flight_number || null,
        is_return_trip: quoteRequest.is_return_trip || false,
        return_date: quoteRequest.return_date || null,
        return_time: quoteRequest.return_time || null,
        return_flight_number: quoteRequest.return_flight_number || null,
        // Add hourly service information
        is_multi_day: quoteRequest.is_multi_day || false,
        // Store client's affiliate selection order for admin visibility
        affiliate_selection_criteria: selectedAffiliates.map((affiliate, index) => ({
          id: affiliate.id,
          order: affiliate.order || index + 1,
          name: affiliate.name || `Affiliate ${index + 1}`
        })),
      })
      .select()
      .single();

    if (quoteError) {
      console.error("Error creating quote:", quoteError);
      console.error(
        "Full quote error details:",
        JSON.stringify(quoteError, null, 2)
      );
      console.error(
        "Quote request data received:",
        JSON.stringify(quoteRequest, null, 2)
      );
      console.error("Quote data that failed:", {
        reference_number: quoteReference,
        customer_id: user.id,
        pickup_location: quoteRequest.pickup_location,
        dropoff_location: quoteRequest.dropoff_location,
        pickup_city: quoteRequest.city,
        date: quoteRequest.date,
        time: quoteRequest.time,
        passenger_count: quoteRequest.passenger_count,
        vehicle_type: quoteRequest.vehicle_type,
        service_type: quoteRequest.service_type,
        event_id: quoteRequest.event_id,
        special_requests: quoteRequest.special_requests,
      });
      return NextResponse.json(
        {
          error: "Failed to create quote",
          details: quoteError,
          errorCode: quoteError?.code,
          errorMessage: quoteError?.message,
          hint: quoteError?.hint,
        },
        { status: 500 }
      );
    }

    console.log("Quote created:", quote);

    // Get affiliate information (using service role to bypass RLS)
    console.log("Fetching affiliate information for IDs:", selectedAffiliates.map((a: any) => a.id));
    const { data: affiliateData, error: affiliateError } = await supabase
      .from("affiliate_companies")
      .select("id, name, average_rating")
      .in(
        "id",
        selectedAffiliates.map((a: any) => a.id)
      );
    
    console.log("Affiliate data fetched:", affiliateData);
    console.log("Affiliate error:", affiliateError);

    if (affiliateError) {
      console.error("Error fetching affiliate data:", affiliateError);
      return NextResponse.json(
        { error: "Failed to fetch affiliate information" },
        { status: 500 }
      );
    }

    // Create affiliate offers one by one to handle unique constraints
    const now = new Date();
    const offers = [];

    for (let i = 0; i < selectedAffiliates.length; i++) {
      const affiliate = selectedAffiliates[i];
      const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000);

      // Check if offer already exists for this quote + company combination
      const { data: existingOffer } = await supabase
        .from("quote_affiliate_offers")
        .select("id")
        .eq("quote_id", quote.id)
        .eq("affiliate_company_id", affiliate.id)
        .single();

      if (existingOffer) {
        console.log(
          `Offer already exists for quote ${quote.id} and company ${affiliate.id}`
        );
        offers.push(existingOffer);
        continue;
      }

      const offerData = {
        quote_id: quote.id,
        affiliate_company_id: affiliate.id,
        offer_price: 0.00, // Required field - affiliates will update with their actual price
        status: "pending",
        submission_order: affiliate.order, // Add the client's selection order
      };

      console.log(`Creating offer ${i + 1}:`, offerData);

      const { data: newOffer, error: offerError } = await supabase
        .from("quote_affiliate_offers")
        .insert(offerData)
        .select()
        .single();

      if (offerError) {
        console.error(
          `Error creating offer for affiliate ${affiliate.id}:`,
          offerError
        );
        console.error("Offer data that failed:", offerData);
        console.error(
          "Full error details:",
          JSON.stringify(offerError, null, 2)
        );
        return NextResponse.json(
          {
            error: `Failed to create offer for affiliate ${affiliate.id}`,
            details: offerError,
            offerData: offerData,
            errorCode: offerError?.code,
            errorMessage: offerError?.message,
          },
          { status: 500 }
        );
      }

      offers.push(newOffer);
    }

    console.log("All affiliate offers created successfully:", offers);

    // Create timeline entry
    const { error: timelineError } = await supabase
      .from("quote_timeline")
      .insert({
        quote_id: quote.id,
        event_type: "quote_submitted",
        event_data: {
          submitted_by: user.id,
          affiliate_count: selectedAffiliates.length,
          submission_order: selectedAffiliates.map((a: any) => ({
            id: a.id,
            order: a.order,
          })),
          quote_details: {
            pickup: quoteRequest.pickup_location,
            dropoff: quoteRequest.dropoff_location,
            date: quoteRequest.date,
            time: quoteRequest.time,
            passengers: quoteRequest.passenger_count,
            service_type: quoteRequest.service_type,
          },
        },
        created_at: new Date().toISOString(),
      });

    if (timelineError) {
      console.error("Error creating timeline entry:", timelineError);
      // Don't fail the operation for timeline errors
    }

    // Create audit log entry
    const { error: auditError } = await supabase.from("audit_logs").insert({
      table_name: "quotes",
      record_id: quote.id,
      action: "create",
      old_values: null,
      new_values: quote,
      user_id: user.id,
      metadata: {
        quote_reference: quoteReference,
        affiliate_count: selectedAffiliates.length,
        submission_type: "client_request",
        selected_affiliates: selectedAffiliates,
      },
    });

    if (auditError) {
      console.error("Error creating audit log:", auditError);
      // Don't fail the operation for audit log errors
    }

    // Send notifications to selected affiliates (in order)
    try {
      for (const affiliate of selectedAffiliates) {
        await fetch(
          `${process.env.NEXT_PUBLIC_APP_URL}/api/notifications/affiliate-quote-request`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              affiliateId: affiliate.id,
              quoteId: quote.id,
              quoteReference: quoteReference,
              submissionOrder: affiliate.order,
              quoteDetails: quoteRequest,
            }),
          }
        );
      }
    } catch (notificationError) {
      console.error(
        "Error sending affiliate notifications:",
        notificationError
      );
      // Don't fail the operation for notification errors
    }

    // Send confirmation notification to client
    try {
      await fetch(
        `${process.env.NEXT_PUBLIC_APP_URL}/api/notifications/quote-submitted`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            clientId: user.id,
            quoteId: quote.id,
            quoteReference: quoteReference,
            affiliateCount: selectedAffiliates.length,
          }),
        }
      );
    } catch (notificationError) {
      console.error("Error sending client notification:", notificationError);
      // Don't fail the operation for notification errors
    }

    console.log(
      `Event Manager Quote Submit API - Quote ${quoteReference} submitted successfully`
    );

    // Broadcast real-time update for new quote submission
    try {
      await triggerQuoteUpdateBroadcast(
        quote.id,
        "pending",
        user.id,
        {
          action: "quote_submitted",
          quoteReference: quoteReference,
          affiliateCount: selectedAffiliates.length,
          submissionType: "client_request"
        }
      );
      console.log(`WebSocket broadcast sent for quote ${quoteReference}`);
    } catch (broadcastError) {
      console.error("Error broadcasting quote update:", broadcastError);
      // Don't fail the operation for broadcast errors
    }

    return NextResponse.json({
      success: true,
      quoteId: quote.id,
      quoteReference: quoteReference,
      status: "pending",
      affiliateCount: selectedAffiliates.length,
      offersCreated: offers.length,
      message: `Quote request ${quoteReference} submitted to ${selectedAffiliates.length} affiliate${selectedAffiliates.length !== 1 ? "s" : ""}`,
    });
  } catch (error) {
    console.error("Error in event manager quote submit API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
