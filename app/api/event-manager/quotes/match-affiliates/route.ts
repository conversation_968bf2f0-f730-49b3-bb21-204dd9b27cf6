import { NextRequest, NextResponse } from "next/server";

export const runtime = 'nodejs'
import { createServerClient } from "@supabase/ssr";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { city, vehicleType, serviceType, date, time, passengers } = body;

    if (!city) {
      return NextResponse.json(
        { error: "City is required for affiliate matching" },
        { status: 400 }
      );
    }

    // Create Supabase client with proper auth for user verification
    const cookieStore = cookies();
    const supabaseAuth = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Verify user is authenticated
    const {
      data: { user },
      error: authError,
    } = await supabaseAuth.auth.getUser();
    console.log("Affiliate Matching API - Auth check:", {
      hasUser: !!user,
      userId: user?.id,
      userEmail: user?.email,
      userRoles: user?.user_metadata?.roles,
      authError: authError?.message,
    });

    if (authError || !user) {
      console.log(
        "Affiliate Matching API - Authentication failed:",
        authError?.message || "No user"
      );
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Create service role client for affiliate queries (bypasses RLS)
    // This is needed because CLIENT users need to see active/approved affiliates for quote matching
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    console.log("Affiliate Matching API - Starting request");
    console.log("Search criteria:", {
      city,
      vehicleType,
      serviceType,
      date,
      time,
      passengers,
    });

    // Map service types to match database expectations
    let mappedServiceType = serviceType;
    if (serviceType === "point" || serviceType === "point-to-point") {
      mappedServiceType = "point_to_point";
    }
    console.log("Mapped service type:", serviceType, "->", mappedServiceType);

    // Clean city name to remove state suffix
    const cleanCity = city.split(",")[0].trim();
    console.log("Cleaned city:", city, "->", cleanCity);

    // Query to find matching affiliates - we'll join vehicles and rate cards separately
    // First, let's see all affiliates in the city regardless of status
    const { data: allAffiliates, error: allAffiliatesError } = await supabase
      .from("affiliate_companies")
      .select("id, name, city, status, approval_status")
      .ilike("city", `%${cleanCity}%`);

    console.log(
      `All affiliates in ${cleanCity}:`,
      allAffiliates?.map((a) => ({
        id: a.id,
        name: a.name,
        city: a.city,
        status: a.status,
        approval_status: a.approval_status,
      }))
    );

    // Now filter for active ones
    const { data: affiliates, error: affiliatesError } = await supabase
      .from("affiliate_companies")
      .select("id, name, city, status, approval_status")
      .eq("status", "active")
      .eq("approval_status", "approved")
      .ilike("city", `%${cleanCity}%`);

    if (affiliatesError) {
      console.error("Error fetching affiliates:", affiliatesError);
      return NextResponse.json(
        { error: "Failed to fetch affiliates" },
        { status: 500 }
      );
    }

    console.log(
      `Found ${affiliates?.length || 0} active affiliates out of ${allAffiliates?.length || 0} total`
    );

    if (!affiliates || affiliates.length === 0) {
      return NextResponse.json({
        success: true,
        affiliates: [],
        allowManualSubmission: true, // Allow client to submit quote even without matches
        message:
          "No affiliates found in this area. You can still submit your quote and we will manually find suitable partners.",
        searchCriteria: {
          city: cleanCity,
          vehicleType,
          serviceType: mappedServiceType,
          originalCity: city,
          originalServiceType: serviceType,
          date,
          time,
          passengers,
        },
      });
    }

    // Now fetch vehicles and rate cards for these affiliates
    const affiliateIds = affiliates.map((a) => a.id);

    // Get vehicles for these affiliates
    let vehicleQuery = supabase
      .from("vehicles")
      .select("id, affiliate_company_id, vehicle_type, make, model, passenger_capacity, status")
      .in("affiliate_company_id", affiliateIds)
      .eq("status", "active");

    // Note: We'll do vehicle type filtering after fetching to handle case-insensitive matching
    if (passengers) {
      vehicleQuery = vehicleQuery.gte("passenger_capacity", passengers);
    }

    const { data: vehicles, error: vehiclesError } = await vehicleQuery;

    if (vehiclesError) {
      console.error("Error fetching vehicles:", vehiclesError);
      return NextResponse.json(
        { error: "Failed to fetch vehicles" },
        { status: 500 }
      );
    }

    // Get rate cards for these affiliates - use safe columns and extract from special_rates
    let rateCardQuery = supabase
      .from("rate_cards")
      .select(
        `
        id, affiliate_company_id, vehicle_type, status,
        base_rate, per_mile_rate, per_hour_rate, minimum_hours,
        special_rates
      `
      )
      .in("affiliate_company_id", affiliateIds)
      .eq("status", "approved");

    // Note: We'll do vehicle type filtering after fetching to handle case-insensitive matching

    console.log(
      "Rate card filtering for service type:",
      mappedServiceType,
      "allows pricing models:",
      ["P2P", "AIRPORT_TRANSFER", "D+T"]
    );

    const { data: rawRateCards, error: rateCardsError } = await rateCardQuery;

    if (rateCardsError) {
      console.error("Error fetching rate cards:", rateCardsError);
      return NextResponse.json(
        { error: "Failed to fetch rate cards" },
        { status: 500 }
      );
    }

    // Process rate cards to extract data from special_rates JSONB
    const rateCards = (rawRateCards || [])
      .map((card) => {
        const specialRates = card.special_rates || {};
        return {
          ...card,
          // Extract fields from special_rates
          pricing_model_type: specialRates.pricing_model_type || "P2P",
          p2p_point_to_point_rate:
            specialRates.p2p_point_to_point_rate || card.base_rate,
          p2p_extra_hour_rate: specialRates.p2p_extra_hour_rate,
          dt_base_fee: specialRates.dt_base_fee,
          dt_per_mile_rate: specialRates.dt_per_mile_rate || card.per_mile_rate,
          dt_per_hour_rate: specialRates.dt_per_hour_rate || card.per_hour_rate,
          dt_min_miles: specialRates.dt_min_miles,
          dt_min_hours: specialRates.dt_min_hours,
          airport_transfer_flat_rate: specialRates.airport_transfer_flat_rate,
          charter_hourly_rate:
            specialRates.charter_hourly_rate || card.per_hour_rate,
          charter_min_hours:
            specialRates.charter_min_hours || card.minimum_hours,
        };
      })
      .filter((card) => {
        // Filter by service type compatibility
        const pricingModel = card.pricing_model_type;
        if (mappedServiceType === "point_to_point") {
          return (
            ["P2P", "AIRPORT_TRANSFER", "D+T"].includes(pricingModel) &&
            card.p2p_point_to_point_rate
          );
        } else if (mappedServiceType === "airport") {
          return (
            ["AIRPORT_TRANSFER", "P2P"].includes(pricingModel) &&
            (card.airport_transfer_flat_rate || card.p2p_point_to_point_rate)
          );
        } else if (mappedServiceType === "hourly") {
          return pricingModel === "CHARTER" && card.charter_hourly_rate;
        }
        return card.p2p_point_to_point_rate; // Default fallback
      });

    console.log(
      `Found ${vehicles?.length || 0} vehicles and ${rateCards?.length || 0} rate cards`
    );

    // Get deactivated vehicles for affiliates (minimal friction onboarding)
    // Affiliates can deactivate vehicle types they don't want to receive offers for
    const { data: deactivatedVehicles, error: deactivatedError } =
      await supabase
        .from("vehicles")
        .select("affiliate_company_id, vehicle_type")
        .in("affiliate_company_id", affiliateIds)
        .eq("status", "inactive");

    if (deactivatedError) {
      console.error("Error fetching deactivated vehicles:", deactivatedError);
    }

    console.log(
      `Found ${deactivatedVehicles?.length || 0} deactivated vehicle types`
    );

    // Helper function to normalize vehicle types for matching
    const normalizeVehicleType = (type: string): string => {
      return type.toLowerCase().replace(/[^a-z]/g, "");
    };

    // MINIMAL FRICTION ONBOARDING: Include all active affiliates regardless of fleet/rate status
    // This allows new affiliates to receive offers even before completing their setup
    const validAffiliates = affiliates.filter((affiliate) => {
      const affiliateVehicles =
        vehicles?.filter((v) => v.affiliate_company_id === affiliate.id) || [];
      const affiliateRateCards =
        rateCards?.filter((rc) => rc.affiliate_company_id === affiliate.id) || [];

      // Check if affiliate has deactivated this vehicle type
      if (vehicleType && deactivatedVehicles) {
        const normalizedRequestedType = normalizeVehicleType(vehicleType);
        const hasDeactivatedVehicleType = deactivatedVehicles.some(
          (vehicle) =>
            vehicle.affiliate_company_id === affiliate.id &&
            normalizeVehicleType(vehicle.vehicle_type) === normalizedRequestedType
        );

        if (hasDeactivatedVehicleType) {
          console.log(
            `Affiliate ${affiliate.name} has deactivated ${vehicleType} - skipping`
          );
          return false;
        }
      }

      console.log(`Affiliate ${affiliate.name} onboarding status:`, {
        hasVehicles: affiliateVehicles.length > 0,
        hasRateCards: affiliateRateCards.length > 0,
        approvalStatus: affiliate.approval_status,
        status: affiliate.status,
        excluded: false,
      });

      // For minimal friction onboarding, include all active affiliates
      // They can decline offers they can't fulfill, which helps with recruitment
      return true; // Include all active affiliates (except those who opted out)
    });

    console.log(
      `${validAffiliates.length} affiliates included for minimal friction onboarding`
    );

    // If no valid affiliates found, return empty with manual submission option
    if (validAffiliates.length === 0) {
      return NextResponse.json({
        success: true,
        affiliates: [],
        allowManualSubmission: true,
        message:
          "No affiliates found in this area. You can still submit your quote and we will manually find suitable partners.",
        searchCriteria: {
          city: cleanCity,
          vehicleType,
          serviceType: mappedServiceType,
          originalCity: city,
          originalServiceType: serviceType,
          date,
          time,
          passengers,
        },
      });
    }

    // Transform and enhance affiliate data
    const enhancedAffiliates = validAffiliates.map((affiliate: any) => {
      const affiliateVehicles =
        vehicles?.filter((v) => v.affiliate_company_id === affiliate.id) || [];
      const affiliateRateCards =
        rateCards?.filter((rc) => rc.affiliate_company_id === affiliate.id) || [];

      // Filter for matching vehicle types if specified
      let matchingVehicles = affiliateVehicles;
      let matchingRateCards = affiliateRateCards;

      if (vehicleType) {
        const normalizedRequestedType = normalizeVehicleType(vehicleType);
        matchingVehicles = affiliateVehicles.filter(
          (v) => normalizeVehicleType(v.vehicle_type) === normalizedRequestedType
        );
        matchingRateCards = affiliateRateCards.filter(
          (rc) =>
            normalizeVehicleType(rc.vehicle_type) === normalizedRequestedType
        );
      }

      // MINIMAL FRICTION ONBOARDING: Handle missing vehicle/rate data gracefully
      const vehicle = matchingVehicles[0] || affiliateVehicles[0]; // Take first matching or any vehicle
      const rateCard = matchingRateCards[0] || affiliateRateCards[0]; // Take first matching or any rate card

      // For affiliates without fleet data, create placeholder vehicle info
      const vehicleInfo = vehicle
        ? {
            type: vehicle.vehicle_type,
            make: vehicle.make,
            model: vehicle.model,
            capacity: vehicle.passenger_capacity,
          }
        : {
            type: vehicleType || "Various",
            make: "Fleet",
            model: "Available",
            capacity: passengers || 4,
          };

      // Calculate estimated pricing based on rate card pricing model and service type
      let estimatedPrice = null; // No default rate - let affiliates set their own rates
      let pricingStatus = "pending"; // 'confirmed' if rate card exists, 'pending' if no rate set

      if (rateCard && rateCard.special_rates) {
        pricingStatus = "confirmed";

        if (mappedServiceType === "airport") {
          // For airport service, prefer airport rates, then P2P rates
          estimatedPrice =
            rateCard.special_rates.airport_transfer_flat_rate ||
            rateCard.special_rates.p2p_point_to_point_rate ||
            100;
        } else if (mappedServiceType === "hourly") {
          // For hourly service, use charter rates
          estimatedPrice =
            rateCard.special_rates.charter_hourly_rate ||
            rateCard.special_rates.dt_per_hour_rate ||
            80;
        } else if (mappedServiceType === "point_to_point") {
          // For point-to-point, use the best available rate based on pricing model
          if (rateCard.special_rates.pricing_model_type === "P2P") {
            estimatedPrice =
              rateCard.special_rates.p2p_point_to_point_rate || 100;
          } else if (
            rateCard.special_rates.pricing_model_type === "AIRPORT_TRANSFER"
          ) {
            // Airport transfer rates can be used for point-to-point
            estimatedPrice =
              rateCard.special_rates.airport_transfer_flat_rate ||
              rateCard.special_rates.p2p_point_to_point_rate ||
              100;
          } else if (rateCard.special_rates.pricing_model_type === "D+T") {
            // Distance + Time pricing (estimate 10 miles, 1 hour)
            estimatedPrice =
              (rateCard.special_rates.dt_per_mile_rate || 2) * 10 +
              (rateCard.special_rates.dt_per_hour_rate || 50);
          } else {
            // Fallback to any available rate
            estimatedPrice =
              rateCard.special_rates.p2p_point_to_point_rate ||
              rateCard.special_rates.airport_transfer_flat_rate ||
              100;
          }
        }
      } else {
        // NO RATE CARD: Let affiliates set their own rates
        pricingStatus = "pending";
        estimatedPrice = null; // No default rate - affiliates must provide their own rate
      }

      console.log("Pricing calculation:", {
        serviceType: mappedServiceType,
        pricingModel:
          rateCard?.special_rates?.pricing_model_type || "estimated",
        estimatedPrice,
        pricingStatus,
        hasRateCard: !!rateCard,
        availableRates: rateCard?.special_rates
          ? {
              p2p: rateCard.special_rates.p2p_point_to_point_rate,
              airport: rateCard.special_rates.airport_transfer_flat_rate,
              charter: rateCard.special_rates.charter_hourly_rate,
            }
          : "none",
      });

      // No minimum fare check needed for new structure

      // Determine tier based on default logic (columns don't exist in DB)
      let tier = "Standard";
      const rating = 4.0; // Default rating since column doesn't exist
      if (rating >= 4.8) {
        tier = "Elite";
      } else if (rating >= 4.5) {
        tier = "Premium";
      }

      // Calculate response time priority based on tier
      let avgResponseTime = "< 15 min";
      if (tier === "Elite") {
        avgResponseTime = "< 5 min";
      } else if (tier === "Premium") {
        avgResponseTime = "< 10 min";
      }

      return {
        id: affiliate.id,
        company_name: affiliate.name,
        city: affiliate.city,
        tier: tier,
        rating: rating,
        avg_response_time: avgResponseTime,
        vehicle_type: vehicleInfo.type,
        vehicle_model: `${vehicleInfo.make} ${vehicleInfo.model}`,
        capacity: vehicleInfo.capacity,
        base_rate:
          rateCard?.special_rates?.p2p_point_to_point_rate ||
          rateCard?.special_rates?.airport_transfer_flat_rate ||
          rateCard?.special_rates?.charter_hourly_rate ||
          null, // No default rate
        total_price: estimatedPrice ? Math.round(estimatedPrice) : null,
        pricing_status: pricingStatus, // 'confirmed', 'pending', or 'estimated'
        features: getVehicleFeatures(vehicleInfo.type, tier),
        availability:
          pricingStatus === "confirmed" ? "confirmed" : "pending_confirmation",
        estimated_arrival: getEstimatedArrival(tier),
        notes:
          pricingStatus === "estimated"
            ? "Pricing estimated - affiliate will provide final quote"
            : tier === "Elite"
              ? "Premium service with professional chauffeur"
              : null,
        onboarding_status: {
          has_fleet: affiliateVehicles.length > 0,
          has_rates: affiliateRateCards.length > 0,
          approval_status: affiliate.approval_status,
        },
      };
    });

    // Sort by tier priority (Elite first, then Premium, then Standard)
    const sortedAffiliates = enhancedAffiliates.sort((a, b) => {
      const tierOrder = { Elite: 0, Premium: 1, Standard: 2 };
      const aTierOrder = tierOrder[a.tier as keyof typeof tierOrder] || 2;
      const bTierOrder = tierOrder[b.tier as keyof typeof tierOrder] || 2;

      if (aTierOrder !== bTierOrder) {
        return aTierOrder - bTierOrder;
      }

      // If same tier, sort by rating
      return (b.rating || 0) - (a.rating || 0);
    });

    console.log(
      `Affiliate Matching API - Returning ${sortedAffiliates.length} sorted affiliates`
    );

    return NextResponse.json({
      success: true,
      affiliates: sortedAffiliates,
      searchCriteria: {
        city: cleanCity,
        vehicleType,
        serviceType: mappedServiceType,
        originalCity: city,
        originalServiceType: serviceType,
        date,
        time,
        passengers,
      },
    });
  } catch (error) {
    console.error("Error in affiliate matching API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to get vehicle features based on type and tier
function getVehicleFeatures(vehicleType: string, tier: string): string[] {
  const baseFeatures = ["Professional Driver", "Clean Vehicle"];

  if (tier === "Elite") {
    return [
      ...baseFeatures,
      "WiFi",
      "Refreshments",
      "Premium Sound System",
      "Climate Control",
    ];
  } else if (tier === "Premium") {
    return [...baseFeatures, "WiFi", "Climate Control", "Phone Charging"];
  } else {
    return [...baseFeatures, "Phone Charging"];
  }
}

// Helper function to get estimated arrival based on tier
function getEstimatedArrival(tier: string): string {
  if (tier === "Elite") {
    return "10-15 minutes";
  } else if (tier === "Premium") {
    return "15-20 minutes";
  } else {
    return "20-25 minutes";
  }
}
