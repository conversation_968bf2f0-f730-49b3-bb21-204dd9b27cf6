import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      first_name,
      last_name,
      email,
      phone_number,
      role = 'EVENT_COORDINATOR'
    } = body

    // Create Supabase client with proper auth
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Validate required fields
    if (!first_name || !last_name || !email) {
      return NextResponse.json({ error: 'First name, last name, and email are required' }, { status: 400 })
    }

    // Check if user already exists with this email
    const { data: existingProfile, error: profileCheckError } = await supabase
      .from('profiles')
      .select('id, email, first_name, last_name')
      .eq('email', email)
      .single()

    if (profileCheckError && profileCheckError.code !== 'PGRST116') {
      console.error('Error checking existing profile:', profileCheckError)
      return NextResponse.json({ error: 'Failed to check existing user' }, { status: 500 })
    }

    if (existingProfile) {
      // User already exists, just return their info
      return NextResponse.json({
        success: true,
        coordinator: {
          id: existingProfile.id,
          first_name: existingProfile.first_name,
          last_name: existingProfile.last_name,
          email: existingProfile.email,
          phone_number: phone_number,
          role: role,
          existing: true
        }
      })
    }

    // Create a new user profile (this will be a coordinator without auth.users entry)
    // In a real system, you might want to send an invitation email instead
    const { data: coordinator, error: createError } = await supabase
      .from('profiles')
      .insert({
        first_name,
        last_name,
        email,
        phone_number,
        role: role,
        roles: [role],
        full_name: `${first_name} ${last_name}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (createError) {
      console.error('Error creating coordinator profile:', createError)
      return NextResponse.json({ error: 'Failed to create coordinator' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      coordinator: {
        id: coordinator.id,
        first_name: coordinator.first_name,
        last_name: coordinator.last_name,
        email: coordinator.email,
        phone_number: coordinator.phone_number,
        role: coordinator.role,
        existing: false
      }
    })

  } catch (error) {
    console.error('Error in POST /api/event-manager/coordinators:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    // Create Supabase client with proper auth
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch coordinators (users with EVENT_COORDINATOR role)
    const { data: coordinators, error: fetchError } = await supabase
      .from('profiles')
      .select('id, first_name, last_name, email, phone_number, role, roles')
      .or('role.eq.EVENT_COORDINATOR,roles.cs.{EVENT_COORDINATOR}')
      .order('created_at', { ascending: false })

    if (fetchError) {
      console.error('Error fetching coordinators:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch coordinators' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      coordinators: coordinators || []
    })

  } catch (error) {
    console.error('Error in GET /api/event-manager/coordinators:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
