import { NextResponse } from "next/server";

export const runtime = 'nodejs'
import { Resend } from "resend";
import { getTenantEmailTemplate } from "@/app/lib/tenant-email-templates";
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
const resend = new Resend(process.env.RESEND_API_KEY || 'dummy-key-for-build');

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { to, from, subject, html, type, templateVariables, organizationId } = body;

    if (!to || !from || !subject || (!html && !type)) {
      return NextResponse.json({ error: "Missing required email fields (to, from, subject, html/type)" }, { status: 400 });
    }

    let finalHtml = html;

    // If a template type is provided, attempt to fetch and render it
    if (type) {
      const template = await getTenantEmailTemplate(organizationId, type);
      if (template) {
        finalHtml = template.content_template;
        // Simple templating logic - replace {{variable}} with actual values
        for (const key in templateVariables) {
          if (Object.prototype.hasOwnProperty.call(templateVariables, key)) {
            const placeholder = new RegExp(`\\{\\{${key}\\\}\\}`.replace(/\\/g, ""), "g");
            finalHtml = finalHtml.replace(placeholder, templateVariables[key]);
          }
        }

        // Handle conditional blocks (e.g., {{#if variable}}...{{/if}})
        finalHtml = finalHtml.replace(/\{\{#if\s+([^\}]+)\}\}([\s\S]*?)\{\{\/if\}\}/g, (match: string, conditionKey: string, content: string) => {
          if (templateVariables && templateVariables[conditionKey]) {
            return content;
          } else {
            return "";
          }
        });

        // Handle list iteration (e.g., {{#each items}}<li>{{.}}</li>{{/each}})
        finalHtml = finalHtml.replace(/\{\{#each\s+([^\}]+)\}\}([\s\S]*?)\{\{\/each\}\}/g, (match: string, arrayKey: string, itemTemplate: string) => {
          if (templateVariables && Array.isArray(templateVariables[arrayKey])) {
            return templateVariables[arrayKey].map((item: string) => itemTemplate.replace(/\{\{\.\}\}|\{\{this\}\}/g, item)).join("");
          } else {
            return "";
          }
        });

      } else if (!html) {
        // If a type was specified but no template found AND no fallback HTML provided
        return NextResponse.json({ error: `No email template found for type: ${type}` }, { status: 404 });
      }
    }

    if (!process.env.RESEND_API_KEY) {
      console.warn("RESEND_API_KEY is not set. Email will not be sent.");
      return NextResponse.json({ success: true, message: "Email sending skipped (RESEND_API_KEY not set)" });
    }

    const data = await resend.emails.send({
      from: from, // e.g., 'Acme <<EMAIL>>' or '<EMAIL>'
      to: to, // array of emails or single email string
      subject: subject,
      html: finalHtml,
    });

    if (data.error) {
      console.error("Error sending email with Resend:", data.error);
      return NextResponse.json({ error: data.error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error("Error in /api/emails POST:", error);
    return NextResponse.json({ error: (error as Error).message }, { status: 500 });
  }
} 