import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();

    // Get the current user session
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Fetch all email templates
    const { data: templates, error } = await supabase
      .from("email_templates")
      .select("*")
      .order("category", { ascending: true })
      .order("name", { ascending: true });

    if (error) {
      console.error("Error fetching email templates:", error);
      return NextResponse.json(
        { error: "Failed to fetch email templates" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      templates: templates || []
    });

  } catch (error) {
    console.error("Email templates API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();

    // Get the current user session
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, name, subject, content, category, variables, is_active } = body;

    if (!type || !name || !subject || !content) {
      return NextResponse.json(
        { error: "Missing required fields: type, name, subject, content" },
        { status: 400 }
      );
    }

    // Create new email template
    const { data: template, error } = await supabase
      .from("email_templates")
      .insert({
        type,
        name,
        subject,
        content,
        category: category || 'quote',
        variables: variables || [],
        is_active: is_active !== undefined ? is_active : true,
        created_by: user.id,
        updated_by: user.id
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating email template:", error);
      return NextResponse.json(
        { error: "Failed to create email template" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      template
    });

  } catch (error) {
    console.error("Email templates POST API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Get the current user session
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { id, type, name, subject, content, category, variables, is_active } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Template ID is required for updates" },
        { status: 400 }
      );
    }

    // Update email template
    const { data: template, error } = await supabase
      .from("email_templates")
      .update({
        type,
        name,
        subject,
        content,
        category,
        variables,
        is_active,
        updated_by: user.id,
        updated_at: new Date().toISOString()
      })
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating email template:", error);
      return NextResponse.json(
        { error: "Failed to update email template" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      template
    });

  } catch (error) {
    console.error("Email templates PUT API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
