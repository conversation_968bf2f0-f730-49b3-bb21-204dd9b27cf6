import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';

/**
 * GET /api/tenant-admin/permissions
 * Get granular permissions for users in tenant
 */
export async function GET(request: NextRequest) {
  try {
    // Require TENANT_ADMIN, TNC_ADMIN, or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TENANT_ADMIN', 'TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const organizationId = searchParams.get('organizationId');

    if (userId) {
      // Get permissions for specific user
      const { data: permissions, error } = await supabase.rpc('get_user_effective_permissions', {
        user_uuid: userId,
        organization_uuid: organizationId
      });

      if (error) {
        console.error('Error fetching user permissions:', error);
        return NextResponse.json({ error: 'Failed to fetch user permissions' }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        permissions: permissions || []
      });
    } else {
      // Get all granular permissions for tenant
      let query = supabase
        .from('user_granular_permissions')
        .select(`
          *,
          profiles:user_id (
            id,
            email,
            first_name,
            last_name,
            roles
          )
        `)
        .order('created_at', { ascending: false });

      if (organizationId) {
        query = query.eq('organization_id', organizationId);
      }

      const { data: permissions, error } = await query;

      if (error) {
        console.error('Error fetching tenant permissions:', error);
        return NextResponse.json({ error: 'Failed to fetch tenant permissions' }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        permissions: permissions || []
      });
    }

  } catch (error: any) {
    console.error('Tenant Admin permissions API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * POST /api/tenant-admin/permissions
 * Grant granular permission to a user
 */
export async function POST(request: NextRequest) {
  try {
    // Require TENANT_ADMIN, TNC_ADMIN, or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TENANT_ADMIN', 'TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const body = await request.json();
    
    const { 
      user_id,
      organization_id,
      permission_category,
      permission_key,
      granted = true,
      expires_at
    } = body;

    if (!user_id || !permission_category || !permission_key) {
      return NextResponse.json(
        { error: 'user_id, permission_category, and permission_key are required' },
        { status: 400 }
      );
    }

    // Get the granting user
    const { data: { user: grantingUser } } = await supabase.auth.getUser();
    if (!grantingUser) {
      return NextResponse.json({ error: 'Granting user not found' }, { status: 401 });
    }

    const { data: permission, error } = await supabase
      .from('user_granular_permissions')
      .upsert({
        user_id,
        organization_id,
        permission_category,
        permission_key,
        granted,
        granted_by: grantingUser.id,
        granted_at: new Date().toISOString(),
        expires_at,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,organization_id,permission_category,permission_key'
      })
      .select()
      .single();

    if (error) {
      console.error('Error granting permission:', error);
      return NextResponse.json({ error: 'Failed to grant permission' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      permission
    });

  } catch (error: any) {
    console.error('Tenant Admin grant permission API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * PUT /api/tenant-admin/permissions
 * Bulk update permissions for a user
 */
export async function PUT(request: NextRequest) {
  try {
    // Require TENANT_ADMIN, TNC_ADMIN, or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TENANT_ADMIN', 'TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const body = await request.json();
    
    const { user_id, organization_id, permissions } = body;

    if (!user_id || !Array.isArray(permissions)) {
      return NextResponse.json(
        { error: 'user_id and permissions array are required' },
        { status: 400 }
      );
    }

    // Get the granting user
    const { data: { user: grantingUser } } = await supabase.auth.getUser();
    if (!grantingUser) {
      return NextResponse.json({ error: 'Granting user not found' }, { status: 401 });
    }

    const results = [];
    for (const perm of permissions) {
      const { data, error } = await supabase
        .from('user_granular_permissions')
        .upsert({
          user_id,
          organization_id,
          permission_category: perm.permission_category,
          permission_key: perm.permission_key,
          granted: perm.granted ?? true,
          granted_by: grantingUser.id,
          granted_at: new Date().toISOString(),
          expires_at: perm.expires_at,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,organization_id,permission_category,permission_key'
        })
        .select()
        .single();

      if (error) {
        console.error(`Error updating permission ${perm.permission_key}:`, error);
        results.push({ 
          permission_key: perm.permission_key, 
          success: false, 
          error: error.message 
        });
      } else {
        results.push({ 
          permission_key: perm.permission_key, 
          success: true, 
          permission: data 
        });
      }
    }

    return NextResponse.json({
      success: true,
      results
    });

  } catch (error: any) {
    console.error('Tenant Admin bulk update permissions API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * DELETE /api/tenant-admin/permissions
 * Revoke a granular permission
 */
export async function DELETE(request: NextRequest) {
  try {
    // Require TENANT_ADMIN, TNC_ADMIN, or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TENANT_ADMIN', 'TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const { searchParams } = new URL(request.url);
    const permissionId = searchParams.get('id');
    const userId = searchParams.get('userId');
    const organizationId = searchParams.get('organizationId');
    const permissionCategory = searchParams.get('permissionCategory');
    const permissionKey = searchParams.get('permissionKey');

    if (permissionId) {
      // Delete by permission ID
      const { error } = await supabase
        .from('user_granular_permissions')
        .delete()
        .eq('id', permissionId);

      if (error) {
        console.error('Error deleting permission:', error);
        return NextResponse.json({ error: 'Failed to delete permission' }, { status: 500 });
      }
    } else if (userId && permissionCategory && permissionKey) {
      // Delete by user and permission details
      let query = supabase
        .from('user_granular_permissions')
        .delete()
        .eq('user_id', userId)
        .eq('permission_category', permissionCategory)
        .eq('permission_key', permissionKey);

      if (organizationId) {
        query = query.eq('organization_id', organizationId);
      }

      const { error } = await query;

      if (error) {
        console.error('Error deleting permission:', error);
        return NextResponse.json({ error: 'Failed to delete permission' }, { status: 500 });
      }
    } else {
      return NextResponse.json(
        { error: 'Either permission ID or user details with permission info required' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Permission revoked successfully'
    });

  } catch (error: any) {
    console.error('Tenant Admin delete permission API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}