import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

/**
 * GET /api/tenant-admin/embeddable-forms
 * Get embeddable forms for tenant admins
 */
export async function GET(request: NextRequest) {
  try {
    // Require TENANT_ADMIN, TNC_ADMIN, or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TENANT_ADMIN', 'TENANT_MANAGER', 'TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    // Get user's tenant context if not provided
    let targetTenantId = organizationId;
    if (!targetTenantId) {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 401 });
      }

      // Get user's tenant from their organization
      const { data: userOrg } = await supabase
        .from('user_companies')
        .select(`
          organizations (
            organization_id
          )
        `)
        .eq('user_id', user.id)
        .single();

      targetTenantId = userOrg?.organizations?.organization_id;
    }

    if (!targetTenantId) {
      return NextResponse.json({ error: 'Tenant context required' }, { status: 400 });
    }

    const { data: forms, error } = await supabase
      .from('tenant_embeddable_forms')
      .select('*')
      .eq('organization_id', targetTenantId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching embeddable forms:', error);
      return NextResponse.json({ error: 'Failed to fetch embeddable forms' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      forms: forms || []
    });

  } catch (error: any) {
    console.error('Tenant Admin embeddable forms API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * POST /api/tenant-admin/embeddable-forms
 * Create a new embeddable form
 */
export async function POST(request: NextRequest) {
  try {
    // Require TENANT_ADMIN, TNC_ADMIN, or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TENANT_ADMIN', 'TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const body = await request.json();
    
    const { 
      organization_id,
      form_name, 
      form_type, 
      configuration, 
      styling = {},
      enabled = true 
    } = body;

    if (!organization_id || !form_name || !form_type) {
      return NextResponse.json(
        { error: 'organization_id, form_name, and form_type are required' },
        { status: 400 }
      );
    }

    // Generate embed code
    const embedCode = generateEmbedCode(organization_id, form_name, form_type);

    const { data: form, error } = await supabase
      .from('tenant_embeddable_forms')
      .insert({
        organization_id,
        form_name,
        form_type,
        configuration: configuration || {},
        styling,
        enabled,
        embed_code: embedCode
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating embeddable form:', error);
      return NextResponse.json({ error: 'Failed to create embeddable form' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      form
    });

  } catch (error: any) {
    console.error('Tenant Admin create embeddable form API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * PUT /api/tenant-admin/embeddable-forms
 * Update an embeddable form
 */
export async function PUT(request: NextRequest) {
  try {
    // Require TENANT_ADMIN, TNC_ADMIN, or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TENANT_ADMIN', 'TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const body = await request.json();
    
    const { 
      id,
      form_name, 
      configuration, 
      styling,
      enabled 
    } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Form ID is required' },
        { status: 400 }
      );
    }

    const updateData: any = { updated_at: new Date().toISOString() };
    
    if (form_name !== undefined) updateData.form_name = form_name;
    if (configuration !== undefined) updateData.configuration = configuration;
    if (styling !== undefined) updateData.styling = styling;
    if (enabled !== undefined) updateData.enabled = enabled;

    const { data: form, error } = await supabase
      .from('tenant_embeddable_forms')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating embeddable form:', error);
      return NextResponse.json({ error: 'Failed to update embeddable form' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      form
    });

  } catch (error: any) {
    console.error('Tenant Admin update embeddable form API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * DELETE /api/tenant-admin/embeddable-forms
 * Delete an embeddable form
 */
export async function DELETE(request: NextRequest) {
  try {
    // Require TENANT_ADMIN, TNC_ADMIN, or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TENANT_ADMIN', 'TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Form ID is required' },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from('tenant_embeddable_forms')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting embeddable form:', error);
      return NextResponse.json({ error: 'Failed to delete embeddable form' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Form deleted successfully'
    });

  } catch (error: any) {
    console.error('Tenant Admin delete embeddable form API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * Generate embed code for a form
 */
function generateEmbedCode(organizationId: string, formName: string, formType: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.transflow.com';
  const embedUrl = `${baseUrl}/embed/${organizationId}/${formType}/${encodeURIComponent(formName)}`;
  
  return `<iframe 
  src="${embedUrl}" 
  width="100%" 
  height="600" 
  frameborder="0" 
  style="border: none; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);"
  title="TransFlow ${formType} Form">
</iframe>

<script>
  // Auto-resize iframe based on content
  window.addEventListener('message', function(event) {
    if (event.origin !== '${baseUrl}') return;
    if (event.data.type === 'resize') {
      const iframe = document.querySelector('iframe[src*="${organizationId}"]');
      if (iframe) {
        iframe.style.height = event.data.height + 'px';
      }
    }
  });
</script>`;
}