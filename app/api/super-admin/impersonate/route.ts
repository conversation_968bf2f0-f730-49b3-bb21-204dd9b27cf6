import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { cookies } from 'next/headers';

/**
 * POST /api/super-admin/impersonate
 * Allow super admin to impersonate other users for testing
 */
export async function POST(request: NextRequest) {
  try {
    // Only super admin can impersonate
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const body = await request.json();
    
    const { user_id, target_role } = body;

    if (!user_id) {
      return NextResponse.json(
        { error: 'user_id is required' },
        { status: 400 }
      );
    }

    // Get the target user
    const { data: targetUser, error: userError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user_id)
      .single();

    if (userError || !targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get auth user data
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(user_id);
    
    if (authError || !authUser.user) {
      return NextResponse.json({ error: 'Auth user not found' }, { status: 404 });
    }

    // Create impersonation session
    const { data: session, error: sessionError } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: targetUser.email,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/super-admin`
      }
    });

    if (sessionError) {
      console.error('Error generating impersonation session:', sessionError);
      return NextResponse.json({ error: 'Failed to create impersonation session' }, { status: 500 });
    }

    // Set impersonation cookies
    const cookieStore = cookies();
    
    // Set a flag that this is an impersonation session
    cookieStore.set('impersonation_active', 'true', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 2 // 2 hours
    });
    
    cookieStore.set('impersonated_user_id', user_id, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 2 // 2 hours
    });

    cookieStore.set('original_admin_id', (await supabase.auth.getUser()).data.user?.id || '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 2 // 2 hours
    });

    return NextResponse.json({
      success: true,
      message: 'Impersonation session created',
      impersonated_user: {
        id: targetUser.id,
        email: targetUser.email,
        roles: targetUser.roles,
        name: `${targetUser.first_name} ${targetUser.last_name}`
      },
      redirect_url: session.properties?.action_link || `${process.env.NEXT_PUBLIC_APP_URL}/super-admin`
    });

  } catch (error: any) {
    console.error('Impersonation API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * DELETE /api/super-admin/impersonate
 * Stop impersonation and return to original admin session
 */
export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = cookies();
    
    // Clear impersonation cookies
    cookieStore.delete('impersonation_active');
    cookieStore.delete('impersonated_user_id');
    cookieStore.delete('original_admin_id');

    return NextResponse.json({
      success: true,
      message: 'Impersonation session ended'
    });

  } catch (error: any) {
    console.error('End impersonation API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}