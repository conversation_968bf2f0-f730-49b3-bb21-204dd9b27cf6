import { getSession } from '@/app/lib/auth';

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/super-admin/subscription-plans
 * Get all subscription plans for management
 */
export async function GET(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Use service role client for comprehensive data access
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Fetch all subscription plans (consolidated table)
    const { data: plans, error } = await supabase
      .from('subscription_plans')
      .select('*')
      .order('price', { ascending: true });

    if (error) {
      console.error('Error fetching subscription plans:', error);
      return NextResponse.json({ error: 'Failed to fetch subscription plans' }, { status: 500 });
    }

    // Get usage statistics for each plan
    const plansWithStats = await Promise.all(
      (plans || []).map(async (plan) => {
        const { data: subscriptions, error: subError } = await supabase
          .from('tenant_subscriptions')
          .select('organization_id, status')
          .eq('plan_id', plan.id);

        const activeSubscriptions = subscriptions?.filter(sub => sub.status === 'active').length || 0;
        const totalSubscriptions = subscriptions?.length || 0;

        return {
          ...plan,
          stats: {
            active_subscriptions: activeSubscriptions,
            total_subscriptions: totalSubscriptions,
            revenue_monthly: activeSubscriptions * plan.price
          }
        };
      })
    );

    return NextResponse.json({
      success: true,
      plans: plansWithStats
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/subscription-plans:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/super-admin/subscription-plans
 * Create a new subscription plan
 */
export async function POST(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const body = await request.json();
    const {
      name,
      description,
      price,
      features
    } = body;

    // Validate required fields
    if (!name || price === undefined) {
      return NextResponse.json(
        { error: 'Name and price are required' },
        { status: 400 }
      );
    }

    // Create new subscription plan (consolidated table)
    const { data: plan, error } = await supabase
      .from('subscription_plans')
      .insert({
        name,
        description,
        price,
        features: features || {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating subscription plan:', error);
      return NextResponse.json({ error: 'Failed to create subscription plan' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      plan
    });

  } catch (error) {
    console.error('Error in POST /api/super-admin/subscription-plans:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/super-admin/subscription-plans/[planId]
 * Update a subscription plan
 */
export async function PUT(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const url = new URL(request.url);
    const planId = url.searchParams.get('planId');
    
    if (!planId) {
      return NextResponse.json({ error: 'Plan ID is required' }, { status: 400 });
    }

    const body = await request.json();
    const {
      name,
      description,
      price,
      features
    } = body;

    // Update subscription plan (consolidated table)
    const { data: plan, error } = await supabase
      .from('subscription_plans')
      .update({
        name,
        description,
        price,
        features,
        updated_at: new Date().toISOString()
      })
      .eq('id', planId)
      .select()
      .single();

    if (error) {
      console.error('Error updating subscription plan:', error);
      return NextResponse.json({ error: 'Failed to update subscription plan' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      plan
    });

  } catch (error) {
    console.error('Error in PUT /api/super-admin/subscription-plans:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/super-admin/subscription-plans/[planId]
 * Delete a subscription plan (only if no active subscriptions)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const url = new URL(request.url);
    const planId = url.searchParams.get('planId');
    
    if (!planId) {
      return NextResponse.json({ error: 'Plan ID is required' }, { status: 400 });
    }

    // Check if plan has active subscriptions
    const { data: activeSubscriptions, error: checkError } = await supabase
      .from('tenant_subscriptions')
      .select('id')
      .eq('plan_id', planId)
      .eq('status', 'active');

    if (checkError) {
      console.error('Error checking active subscriptions:', checkError);
      return NextResponse.json({ error: 'Failed to check active subscriptions' }, { status: 500 });
    }

    if (activeSubscriptions && activeSubscriptions.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete plan with active subscriptions' },
        { status: 400 }
      );
    }

    // Delete subscription plan (consolidated table)
    const { error } = await supabase
      .from('subscription_plans')
      .delete()
      .eq('id', planId);

    if (error) {
      console.error('Error deleting subscription plan:', error);
      return NextResponse.json({ error: 'Failed to delete subscription plan' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Subscription plan deleted successfully'
    });

  } catch (error) {
    console.error('Error in DELETE /api/super-admin/subscription-plans:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
