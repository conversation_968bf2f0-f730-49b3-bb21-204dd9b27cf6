import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    console.log("Super-admin users-simple GET API called");

    // Create service role client to bypass RLS
    const supabase = await createClient({ serviceRole: true });

    // Simple query to get users
    const { data: users, error } = await supabase
      .from("profiles")
      .select(`
        id,
        email,
        full_name,
        phone,
        roles,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error("Error fetching users:", error);
      return NextResponse.json({ 
        error: "Failed to fetch users", 
        details: error.message 
      }, { status: 500 });
    }

    console.log(`Successfully fetched ${users?.length || 0} users`);

    return NextResponse.json({
      success: true,
      users: users || [],
      total: users?.length || 0
    });

  } catch (error) {
    console.error("Error in super-admin users-simple API:", error);
    return NextResponse.json({ 
      error: "Internal Server Error", 
      details: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}