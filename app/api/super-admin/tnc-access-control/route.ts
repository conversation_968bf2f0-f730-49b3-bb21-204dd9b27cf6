import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// POST: Validate TNC customer access and start session
export async function POST(request: NextRequest) {
  try {
    console.log("TNC Access Control API - Starting POST request");
    
    const body = await request.json();
    const { 
      customer_org_id, 
      user_id, 
      requested_route,
      access_type = 'event_manager',
      ip_address,
      user_agent 
    } = body;
    
    // Validate required fields
    if (!customer_org_id || !user_id) {
      return NextResponse.json(
        { error: 'Missing required fields: customer_org_id, user_id' },
        { status: 400 }
      );
    }
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Validate access using the database function
    const { data: accessValidation, error: validationError } = await supabase
      .rpc('validate_tnc_customer_access', {
        customer_org_id,
        user_id,
        requested_route
      });

    if (validationError) {
      console.error('Error validating access:', validationError);
      return NextResponse.json(
        { error: 'Failed to validate access' },
        { status: 500 }
      );
    }

    if (!accessValidation.access_granted) {
      return NextResponse.json(
        { 
          error: 'Access denied',
          reason: accessValidation.error,
          access_granted: false
        },
        { status: 403 }
      );
    }

    // Start session if access is granted
    const { data: sessionId, error: sessionError } = await supabase
      .rpc('start_tnc_customer_session', {
        customer_org_id,
        user_id,
        access_type,
        ip_address,
        user_agent
      });

    if (sessionError) {
      console.error('Error starting session:', sessionError);
      return NextResponse.json(
        { error: 'Failed to start session' },
        { status: 500 }
      );
    }

    // Get portal context
    const { data: portalContext, error: contextError } = await supabase
      .rpc('get_tnc_customer_portal_context', {
        customer_org_id
      });

    if (contextError) {
      console.warn('Warning: Failed to get portal context:', contextError);
    }

    console.log(`Access granted and session started for customer org: ${customer_org_id}`);
    
    return NextResponse.json({
      success: true,
      access_granted: true,
      session_id: sessionId,
      access_validation: accessValidation,
      portal_context: portalContext || null
    });

  } catch (error) {
    console.error('Error in POST /api/super-admin/tnc-access-control:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET: Get TNC customer portal context and session info
export async function GET(request: NextRequest) {
  try {
    console.log("TNC Access Control API - Starting GET request");
    
    const { searchParams } = new URL(request.url);
    const customer_org_id = searchParams.get('customer_org_id');
    const user_id = searchParams.get('user_id');
    
    if (!customer_org_id) {
      return NextResponse.json(
        { error: 'customer_org_id parameter is required' },
        { status: 400 }
      );
    }
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get portal context
    const { data: portalContext, error: contextError } = await supabase
      .rpc('get_tnc_customer_portal_context', {
        customer_org_id
      });

    if (contextError) {
      console.error('Error getting portal context:', contextError);
      return NextResponse.json(
        { error: 'Failed to get portal context' },
        { status: 500 }
      );
    }

    // Get active sessions if user_id provided
    let activeSessions = null;
    if (user_id) {
      const { data: sessions, error: sessionsError } = await supabase
        .from('tnc_customer_sessions')
        .select('id, access_type, started_at, last_activity, expires_at, status')
        .eq('tnc_customer_org_id', customer_org_id)
        .eq('user_id', user_id)
        .eq('status', 'active')
        .order('started_at', { ascending: false });

      if (!sessionsError) {
        activeSessions = sessions;
      }
    }

    // Get available routes
    const { data: routes, error: routesError } = await supabase
      .from('tnc_customer_portal_routes')
      .select('route_path, route_name, description, is_enabled, is_public, sort_order')
      .eq('tnc_customer_org_id', customer_org_id)
      .eq('is_enabled', true)
      .order('sort_order');

    if (routesError) {
      console.warn('Warning: Failed to get routes:', routesError);
    }

    return NextResponse.json({
      success: true,
      portal_context: portalContext,
      active_sessions: activeSessions,
      available_routes: routes || []
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/tnc-access-control:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}