import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';

// PRODUCTION AFFILIATES API - Enterprise-grade authentication
export async function GET(request: NextRequest) {
  try {
    console.log("Production super-admin affiliates API - Starting request");
    
    // Authenticate request with super admin access
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { user } = authResult;
    console.log(`Authenticated user: ${user!.email} (${user!.role})`);
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get organization filter from query params
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('org_id');
    
    console.log('Filtering by organization ID:', orgId);
    
    // Organization access validation (simplified for super admin)
    if (orgId && orgId !== 'all') {
      console.log(`Filtering by organization: ${orgId}`);
    }

    // Get affiliate companies with correct column names from database
    let query = supabase
      .from('affiliate_companies')
      .select(`
        id,
        name,
        legal_name,
        primary_contact_name,
        primary_contact_email,
        primary_contact_phone,
        status,
        service_areas,
        fleet_size,
        average_rating,
        created_at,
        updated_at,
        organization_id
      `)
      .order('created_at', { ascending: false });

    // Apply organization filter if provided and not "all"
    if (orgId && orgId !== 'all') {
      query = query.eq('organization_id', orgId);
    } else if (user!.role !== 'SUPER_ADMIN') {
      // Non-super admin users can only see their organization's data
      query = query.eq('organization_id', user!.organization_id);
    }

    const { data: affiliates, error: affiliatesError } = await query;

    if (affiliatesError) {
      console.error('Error fetching affiliates:', affiliatesError);
      return NextResponse.json(
        { error: 'Failed to fetch affiliates' },
        { status: 500 }
      );
    }

    console.log(`Found ${affiliates?.length || 0} affiliates`);
    
    return NextResponse.json({
      success: true,
      affiliates: affiliates || [],
      total: affiliates?.length || 0,
      filtered_by_org: orgId && orgId !== 'all' ? orgId : null,
      user_context: {
        role: user!.role,
        userId: user!.id,
        email: user!.email
      }
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/affiliates-simple:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}