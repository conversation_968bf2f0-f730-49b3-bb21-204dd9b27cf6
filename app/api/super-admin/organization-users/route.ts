import { createClient } from '@supabase/supabase-js';
import { type NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import {
  requireRole,
} from "@/lib/auth/server";

// Schema for user_organizations table (updated for organization_id only)
// id (uuid, pk) - auto-generated
// organization_id (uuid, fk to organizations.id)
// user_id (uuid, fk to auth.users.id or public.profiles.id)
// role (text)
// created_at (timestamptz) - auto-generated
// updated_at (timestamptz) - auto-generated

const UUID_REGEX = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

/**
 * @swagger
 * /api/super-admin/organization-users:
 *   post:
 *     summary: Associate a user with an organization and assign a role.
 *     tags:
 *       - Super Admin - Organization Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - organization_id
 *               - role
 *             properties:
 *               user_id:
 *                 type: string
 *                 format: uuid
 *                 description: The ID of the user to associate.
 *               organization_id:
 *                 type: string
 *                 format: uuid
 *                 description: The ID of the organization.
 *               role:
 *                 type: string
 *                 description: The role to assign to the user.
 *     responses:
 *       201:
 *         description: User successfully associated with organization.
 *       400:
 *         description: Bad request (e.g., missing required fields, invalid UUIDs).
 *       409:
 *         description: Conflict (e.g., user already associated with organization).
 *       500:
 *         description: Internal server error.
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    await requireRole(supabase, ['SUPER_ADMIN']);

    const body = await request.json();
    const { user_id, organization_id, role } = body;

    if (!user_id || !organization_id || !role) {
      return NextResponse.json(
        { error: 'user_id, organization_id, and role are required fields.' },
        { status: 400 }
      );
    }

    // Validate UUIDs
    if (!UUID_REGEX.test(user_id)) {
      return NextResponse.json({ error: 'Invalid user_id format.' }, { status: 400 });
    }
    if (!UUID_REGEX.test(organization_id)) {
        return NextResponse.json({ error: 'Invalid organization_id format.' }, { status: 400 });
    }

    // Insert the association
    const { data, error } = await supabase
      .from('user_organizations')
      .insert([{ user_id, organization_id, role }])
      .select();

    if (error) {
      console.error('Error inserting user-organization association:', error);
      
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { error: 'User is already associated with this organization.' },
          { status: 409 }
        );
      }
      if (error.code === '23503') { // Foreign key constraint violation
        return NextResponse.json(
          { error: 'Invalid user_id or organization_id. Ensure they exist.' },
          { status: 400 }
        );
      }
      
      return NextResponse.json({ error: 'Failed to associate user with organization.' }, { status: 500 });
    }

    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    console.error('Unexpected error in POST /api/super-admin/organization-users:', error);
    return NextResponse.json({ error: 'Internal server error.' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/super-admin/organization-users:
 *   get:
 *     summary: Retrieve user-organization associations.
 *     tags:
 *       - Super Admin - Organization Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by organization ID.
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by user ID.
 *     responses:
 *       200:
 *         description: List of user-organization associations.
 *       400:
 *         description: Bad request (e.g., must provide organization_id or user_id).
 *       500:
 *         description: Internal server error.
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    await requireRole(supabase, ['SUPER_ADMIN']);

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id');
    const userId = searchParams.get('user_id');

    if (!organizationId && !userId) {
      return NextResponse.json(
        { error: 'Either organization_id or user_id query parameter must be provided.' },
        { status: 400 }
      );
    }

    let query = supabase
      .from('user_organizations')
      .select(`
        *,
        organizations:organization_id(name, slug),
        profiles:user_id(email, first_name, last_name)
      `);

    if (organizationId) {
      if (!UUID_REGEX.test(organizationId)) {
        return NextResponse.json({ error: 'Invalid organization_id format.' }, { status: 400 });
      }
      query = query.eq('organization_id', organizationId);
    }

    if (userId) {
      if (!UUID_REGEX.test(userId)) {
        return NextResponse.json({ error: 'Invalid user_id format.' }, { status: 400 });
      }
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching user-organization associations:', error);
      return NextResponse.json({ error: 'Failed to fetch associations.' }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Unexpected error in GET /api/super-admin/organization-users:', error);
    return NextResponse.json({ error: 'Internal server error.' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/super-admin/organization-users:
 *   put:
 *     summary: Update a user's role in an organization.
 *     tags:
 *       - Super Admin - Organization Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - organization_id
 *               - role
 *             properties:
 *               user_id:
 *                 type: string
 *                 format: uuid
 *               organization_id:
 *                 type: string
 *                 format: uuid
 *               role:
 *                 type: string
 *     responses:
 *       200:
 *         description: User role updated successfully.
 *       400:
 *         description: Bad request.
 *       404:
 *         description: Association not found.
 *       500:
 *         description: Internal server error.
 */
export async function PUT(request: NextRequest) {
  try {
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    await requireRole(supabase, ['SUPER_ADMIN']);

    const body = await request.json();
    const { user_id, organization_id, role } = body;

    if (!user_id || !organization_id || !role) {
      return NextResponse.json(
        { error: 'user_id, organization_id, and role are required.' },
        { status: 400 }
      );
    }

    // Validate UUIDs
    if (!UUID_REGEX.test(user_id) || !UUID_REGEX.test(organization_id)) {
      return NextResponse.json({ error: 'Invalid organization_id format.' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('user_organizations')
      .update({ role })
      .eq('user_id', user_id)
      .eq('organization_id', organization_id)
      .select();

    if (error) {
      console.error('Error updating user role:', error);
      return NextResponse.json({ error: 'Failed to update user role.' }, { status: 500 });
    }

    if (!data || data.length === 0) {
      return NextResponse.json({ error: 'Association not found.' }, { status: 404 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Unexpected error in PUT /api/super-admin/organization-users:', error);
    return NextResponse.json({ error: 'Internal server error.' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/super-admin/organization-users:
 *   delete:
 *     summary: Remove a user from an organization.
 *     tags:
 *       - Super Admin - Organization Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - organization_id
 *             properties:
 *               user_id:
 *                 type: string
 *                 format: uuid
 *               organization_id:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: User removed from organization successfully.
 *       400:
 *         description: Bad request.
 *       404:
 *         description: Association not found.
 *       500:
 *         description: Internal server error.
 */
export async function DELETE(request: NextRequest) {
  try {
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    await requireRole(supabase, ['SUPER_ADMIN']);

    const body = await request.json();
    const { user_id, organization_id } = body;

    if (!user_id || !organization_id) {
      return NextResponse.json(
        { error: 'user_id and organization_id are required.' },
        { status: 400 }
      );
    }

    // Validate UUIDs
    if (!UUID_REGEX.test(user_id) || !UUID_REGEX.test(organization_id)) {
      return NextResponse.json({ error: 'Invalid organization_id format.' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('user_organizations')
      .delete()
      .eq('user_id', user_id)
      .eq('organization_id', organization_id);

    if (error) {
      console.error('Error removing user from organization:', error);
      return NextResponse.json({ error: 'Failed to remove user from organization.' }, { status: 500 });
    }

    // Also clean up any permissions for this user in this organization
    await supabase
      .from('user_granular_permissions')
      .delete()
      .eq('user_id', user_id)
      .eq('organization_id', organization_id);

    return NextResponse.json({ message: 'User removed from organization successfully.' });
  } catch (error) {
    console.error('Unexpected error in DELETE /api/super-admin/organization-users:', error);
    return NextResponse.json({ error: 'Internal server error.' }, { status: 500 });
  }
}