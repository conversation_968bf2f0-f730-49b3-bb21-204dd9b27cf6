import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    console.log("Super-admin organizations-simple GET API called");

    // Create service role client to bypass RLS
    const supabase = await createClient({ serviceRole: true });

    // Simple query to get organizations
    const { data: organizations, error } = await supabase
      .from("organizations")
      .select(`
        id,
        name,
        slug,
        organization_type,
        subscription_plan,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error("Error fetching organizations:", error);
      return NextResponse.json({ 
        error: "Failed to fetch organizations", 
        details: error.message 
      }, { status: 500 });
    }

    console.log(`Successfully fetched ${organizations?.length || 0} organizations`);

    return NextResponse.json({
      success: true,
      organizations: organizations || [],
      total: organizations?.length || 0
    });

  } catch (error) {
    console.error("Error in super-admin organizations-simple API:", error);
    return NextResponse.json({ 
      error: "Internal Server Error", 
      details: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}