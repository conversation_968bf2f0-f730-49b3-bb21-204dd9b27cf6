import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';

// PRODUCTION APPLICATIONS API - Enterprise-grade authentication
export async function GET(request: NextRequest) {
  try {
    console.log("Production Applications API - Starting request");
    
    // Authenticate request with super admin access
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { context } = authResult;
    console.log(`Authenticated user: ${context!.email} (${context!.roles.join(', ')})`);
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    console.log('Fetching applications with filters:', { status, type, limit, offset });

    // For now, return affiliate applications since that's the most common use case
    let query = supabase
      .from('affiliate_companies')
      .select(`
        id,
        company_name,
        contact_email,
        contact_phone,
        status,
        application_date,
        approval_date,
        rejection_reason,
        created_at,
        updated_at,
        business_license,
        insurance_info,
        fleet_size,
        service_areas
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    const { data: applications, error, count } = await query;

    if (error) {
      console.error('Error fetching applications:', error);
      return NextResponse.json(
        { error: 'Failed to fetch applications' },
        { status: 500 }
      );
    }

    // Transform data to match expected application format
    const transformedApplications = applications?.map(app => ({
      id: app.id,
      applicantName: app.company_name,
      applicantEmail: app.contact_email,
      applicantPhone: app.contact_phone,
      applicationType: 'affiliate',
      status: app.status,
      submittedAt: app.application_date || app.created_at,
      reviewedAt: app.approval_date,
      rejectionReason: app.rejection_reason,
      businessLicense: app.business_license,
      insuranceInfo: app.insurance_info,
      fleetSize: app.fleet_size,
      serviceAreas: app.service_areas,
      createdAt: app.created_at,
      updatedAt: app.updated_at
    })) || [];

    console.log(`Successfully fetched ${transformedApplications.length} applications`);

    return NextResponse.json({
      success: true,
      applications: transformedApplications,
      total: count || transformedApplications.length,
      pagination: {
        limit,
        offset,
        total: count || transformedApplications.length
      },
      user_context: {
        roles: context!.roles,
        userId: context!.userId,
        email: context!.email
      }
    });

  } catch (error) {
    console.error('Applications API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}