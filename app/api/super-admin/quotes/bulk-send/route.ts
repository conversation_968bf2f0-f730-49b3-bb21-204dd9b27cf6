import { getSession } from '@/app/lib/auth';

export const runtime = 'nodejs'
import { toUserRoles } from '@/app/lib/auth/roles';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  // Require session and SUPER_ADMIN
  const session = await getSession(request);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
  const sessionUserRoles = toUserRoles(sessionRoles);
  if (!sessionUserRoles.includes('SUPER_ADMIN')) {
    return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: sessionUserRoles }, { status: 403 });
  }
  // Use authenticated Supabase client
  const supabase = await getSupabaseClient(request);
  if (!supabase) {
    return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
  }
  try {
    const body = await request.json()
    const { quoteIds, affiliateIds, deadline, notes } = body

    if (!quoteIds || !Array.isArray(quoteIds) || quoteIds.length === 0) {
      return NextResponse.json({ error: 'Quote IDs are required' }, { status: 400 })
    }

    if (!affiliateIds || !Array.isArray(affiliateIds) || affiliateIds.length === 0) {
      return NextResponse.json({ error: 'Affiliate IDs are required' }, { status: 400 })
    }

    // Parse deadline to hours
    const parseDeadlineToHours = (deadline: string): number => {
      const value = parseInt(deadline.replace(/[^0-9]/g, ''))
      const unit = deadline.replace(/[0-9]/g, '')
      
      if (unit === 'h') return value
      if (unit === 'd') return value * 24
      return 24 // Default to 24 hours
    }

    const deadlineHours = parseDeadlineToHours(deadline || '24h')
    const expiryDate = new Date(Date.now() + deadlineHours * 60 * 60 * 1000)

    // Get affiliate companies
    let affiliateFilter = affiliateIds
    
    // Handle special affiliate selections
    if (affiliateIds.includes('all')) {
      const { data: allAffiliates, error: affiliatesError } = await supabase
        .from('affiliate_companies')
        .select('id')
        .eq('application_status', 'approved')
      
      if (affiliatesError) {
        return NextResponse.json({ error: 'Failed to fetch affiliates' }, { status: 500 })
      }
      
      affiliateFilter = allAffiliates?.map(a => a.id) || []
    } else if (affiliateIds.includes('elite')) {
      const { data: eliteAffiliates, error: affiliatesError } = await supabase
        .from('affiliate_companies')
        .select('id, affiliate_performance_metrics(current_tier)')
        .eq('application_status', 'approved')
      
      if (affiliatesError) {
        return NextResponse.json({ error: 'Failed to fetch elite affiliates' }, { status: 500 })
      }
      
      affiliateFilter = eliteAffiliates?.filter(a => 
        a.affiliate_performance_metrics?.[0]?.current_tier === 'Elite'
      ).map(a => a.id) || []
    } else if (affiliateIds.includes('premium')) {
      const { data: premiumAffiliates, error: affiliatesError } = await supabase
        .from('affiliate_companies')
        .select('id, affiliate_performance_metrics(current_tier)')
        .eq('application_status', 'approved')
      
      if (affiliatesError) {
        return NextResponse.json({ error: 'Failed to fetch premium affiliates' }, { status: 500 })
      }
      
      affiliateFilter = premiumAffiliates?.filter(a => 
        ['Elite', 'Premium'].includes(a.affiliate_performance_metrics?.[0]?.current_tier || 'Standard')
      ).map(a => a.id) || []
    }

    // Get quotes to validate they can be sent
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select('id, status, reference_number, service_type, vehicle_type, pickup_location, dropoff_location')
      .in('id', quoteIds)

    if (quotesError) {
      console.error('Error fetching quotes:', quotesError)
      return NextResponse.json({ error: 'Failed to fetch quotes' }, { status: 500 })
    }

    // Filter quotes that can be sent
    const sendableQuotes = quotes?.filter(quote => 
      ['pending', 'pending_quote'].includes(quote.status)
    ) || []

    if (sendableQuotes.length === 0) {
      return NextResponse.json({ 
        error: 'No quotes can be sent. Quotes must be in pending status.' 
      }, { status: 400 })
    }

    const sendableQuoteIds = sendableQuotes.map(q => q.id)

    // Create quote offers for each quote-affiliate combination
    const quoteOffers = []
    for (const quoteId of sendableQuoteIds) {
      for (const affiliateId of affiliateFilter) {
        quoteOffers.push({
          quote_id: quoteId,
          company_id: affiliateId,
          status: 'pending',
          offer_type: 'rate_request',
          expires_at: expiryDate.toISOString(),
          notes: notes || null,
          created_at: new Date().toISOString()
        })
      }
    }

    // Insert quote offers
    const { data: createdOffers, error: offersError } = await supabase
      .from('quote_affiliate_offers')
      .insert(quoteOffers)
      .select()

    if (offersError) {
      console.error('Error creating quote offers:', offersError)
      return NextResponse.json({ error: 'Failed to create quote offers' }, { status: 500 })
    }

    // Update quote statuses
    const { data: updatedQuotes, error: updateError } = await supabase
      .from('quotes')
      .update({
        status: 'sent_to_affiliates',
        updated_at: new Date().toISOString()
      })
      .in('id', sendableQuoteIds)
      .select()

    if (updateError) {
      console.error('Error updating quotes:', updateError)
      return NextResponse.json({ error: 'Failed to update quote statuses' }, { status: 500 })
    }

    // Create audit log entries
    const auditEntries = sendableQuoteIds.map(quoteId => ({
      table_name: 'quotes',
      record_id: quoteId,
      action: 'bulk_send',
      old_values: {},
      new_values: { status: 'sent_to_affiliates' },
      user_id: session.id,
      metadata: {
        bulk_operation: true,
        affiliate_count: affiliateFilter.length,
        deadline_hours: deadlineHours,
        notes: notes || null,
        sent_count: sendableQuoteIds.length
      }
    }))

    const { error: auditError } = await supabase
      .from('audit_logs')
      .insert(auditEntries)

    if (auditError) {
      console.error('Error creating audit logs:', auditError)
    }

    // Create timeline entries
    const timelineEntries = sendableQuoteIds.map(quoteId => ({
      quote_id: quoteId,
      event_type: 'bulk_sent_to_affiliates',
      event_data: {
        affiliate_count: affiliateFilter.length,
        deadline_hours: deadlineHours,
        expires_at: expiryDate.toISOString(),
        sent_by: session.id,
        notes: notes || null,
        bulk_operation: true
      },
      created_at: new Date().toISOString()
    }))

    const { error: timelineError } = await supabase
      .from('quote_timeline')
      .insert(timelineEntries)

    if (timelineError) {
      console.error('Error creating timeline entries:', timelineError)
    }

    // Send notifications to affiliates (optional)
    try {
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/notifications/bulk-quote-offers`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          affiliateIds: affiliateFilter,
          quoteIds: sendableQuoteIds,
          deadline: expiryDate.toISOString(),
          sentBy: session.id,
          notes
        })
      })
    } catch (notificationError) {
      console.error('Error sending bulk notifications:', notificationError)
    }

    return NextResponse.json({
      success: true,
      sentCount: sendableQuoteIds.length,
      skippedCount: quoteIds.length - sendableQuoteIds.length,
      affiliateCount: affiliateFilter.length,
      offersCreated: createdOffers?.length || 0,
      updatedQuotes,
      deadline: expiryDate.toISOString(),
      message: `Successfully sent ${sendableQuoteIds.length} quotes to ${affiliateFilter.length} affiliates`
    })

  } catch (error) {
    console.error('Error in bulk send API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};
