import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { quoteIds, affiliateId, notes } = body

    if (!quoteIds || !Array.isArray(quoteIds) || quoteIds.length === 0) {
      return NextResponse.json({ error: 'Quote IDs are required' }, { status: 400 })
    }

    if (!affiliateId) {
      return NextResponse.json({ error: 'Affiliate ID is required' }, { status: 400 })
    }

    // Create Supabase client with proper auth
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated and has super admin access
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get affiliate company details
    const { data: affiliate, error: affiliateError } = await supabase
      .from('affiliate_companies')
      .select('id, name, application_status')
      .eq('id', affiliateId)
      .single()

    if (affiliateError || !affiliate) {
      return NextResponse.json({ error: 'Affiliate not found' }, { status: 404 })
    }

    if (affiliate.application_status !== 'approved') {
      return NextResponse.json({ error: 'Affiliate is not approved' }, { status: 400 })
    }

    // Get quotes to validate they can be assigned
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select('id, status, reference_number')
      .in('id', quoteIds)

    if (quotesError) {
      console.error('Error fetching quotes:', quotesError)
      return NextResponse.json({ error: 'Failed to fetch quotes' }, { status: 500 })
    }

    // Filter quotes that can be assigned
    const assignableQuotes = quotes?.filter(quote => 
      ['pending', 'rate_requested', 'sent_to_affiliates'].includes(quote.status)
    ) || []

    if (assignableQuotes.length === 0) {
      return NextResponse.json({ 
        error: 'No quotes can be assigned. Quotes must be in pending, rate_requested, or sent_to_affiliates status.' 
      }, { status: 400 })
    }

    const assignableQuoteIds = assignableQuotes.map(q => q.id)

    // Perform bulk assignment
    const { data: updatedQuotes, error: updateError } = await supabase
      .from('quotes')
      .update({
        affiliate_company_id: affiliateId,
        status: 'assigned',
        updated_at: new Date().toISOString()
      })
      .in('id', assignableQuoteIds)
      .select()

    if (updateError) {
      console.error('Error updating quotes:', updateError)
      return NextResponse.json({ error: 'Failed to assign quotes' }, { status: 500 })
    }

    // Create audit log entries for each assignment
    const auditEntries = assignableQuoteIds.map(quoteId => ({
      table_name: 'quotes',
      record_id: quoteId,
      action: 'bulk_assign',
      old_values: {},
      new_values: { affiliate_company_id: affiliateId, status: 'assigned' },
      user_id: user.id,
      metadata: {
        bulk_operation: true,
        affiliate_name: affiliate.name,
        notes: notes || null,
        assigned_count: assignableQuoteIds.length
      }
    }))

    const { error: auditError } = await supabase
      .from('audit_logs')
      .insert(auditEntries)

    if (auditError) {
      console.error('Error creating audit logs:', auditError)
      // Don't fail the operation for audit log errors
    }

    // Create timeline entries for each quote
    const timelineEntries = assignableQuoteIds.map(quoteId => ({
      quote_id: quoteId,
      event_type: 'bulk_assigned',
      event_data: {
        affiliate_company_id: affiliateId,
        affiliate_name: affiliate.name,
        assigned_by: user.id,
        notes: notes || null,
        bulk_operation: true
      },
      created_at: new Date().toISOString()
    }))

    const { error: timelineError } = await supabase
      .from('quote_timeline')
      .insert(timelineEntries)

    if (timelineError) {
      console.error('Error creating timeline entries:', timelineError)
      // Don't fail the operation for timeline errors
    }

    // Send notifications to affiliate (optional)
    try {
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/notifications/affiliate-assignment`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          affiliateId,
          quoteIds: assignableQuoteIds,
          assignedBy: user.id,
          notes
        })
      })
    } catch (notificationError) {
      console.error('Error sending assignment notification:', notificationError)
      // Don't fail the operation for notification errors
    }

    return NextResponse.json({
      success: true,
      assignedCount: assignableQuoteIds.length,
      skippedCount: quoteIds.length - assignableQuoteIds.length,
      assignedQuotes: updatedQuotes,
      affiliate: {
        id: affiliate.id,
        name: affiliate.name
      },
      message: `Successfully assigned ${assignableQuoteIds.length} quotes to ${affiliate.name}`
    })

  } catch (error) {
    console.error('Error in bulk assign API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
