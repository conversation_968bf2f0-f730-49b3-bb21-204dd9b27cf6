import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, with<PERSON>uth, createSupabaseClient } from '@/lib/auth/unified-auth';

/**
 * GET /api/super-admin/quotes/[id]
 * Get details for a specific quote including affiliate selection order
 */
export const GET = withAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    // Authenticate user
    const user = await requireAuth(request);
    console.log("GET /api/super-admin/quotes/[id] - User authenticated:", user.id);

    const supabase = createSupabaseClient();

    // Get user profile to check super admin permissions
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    if (profileError || !profile?.roles?.includes('SUPER_ADMIN')) {
      return NextResponse.json(
        { error: 'Unauthorized: Super admin access required' },
        { status: 403 }
      );
    }

    const quoteId = params.id;
    if (!quoteId) {
      return NextResponse.json(
        { error: 'Quote ID is required' },
        { status: 400 }
      );
    }

    // Fetch quote details including affiliate selection order
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select(`
        *,
        affiliate_selection_order
      `)
      .eq('id', quoteId)
      .single();

    if (quoteError) {
      console.error('Error fetching quote:', quoteError);
      return NextResponse.json(
        { error: 'Quote not found' },
        { status: 404 }
      );
    }

    // Fetch related affiliate offers
    const { data: affiliateOffers, error: offersError } = await supabase
      .from('quote_affiliate_offers')
      .select(`
        id,
        company_id,
        rate_amount,
        status,
        notes,
        submission_order,
        sent_at,
        created_at,
        updated_at,
        expires_at,
        counter_offer_amount,
        counter_offer_note,
        is_counter_offer
      `)
      .eq('quote_id', quoteId)
      .order('submission_order', { ascending: true });

    if (offersError) {
      console.error('Error fetching affiliate offers:', offersError);
    }

    // Fetch affiliate company details for the offers
    let affiliateDetails = [];
    if (affiliateOffers && affiliateOffers.length > 0) {
      const companyIds = affiliateOffers.map(offer => offer.company_id);
      const { data: companies, error: companiesError } = await supabase
        .from('affiliate_companies')
        .select(`
          id,
          name,
          city,
          state,
          status,
          tier,
          rating,
          phone,
          email
        `)
        .in('id', companyIds);

      if (companiesError) {
        console.error('Error fetching affiliate companies:', companiesError);
      } else {
        affiliateDetails = companies || [];
      }
    }

    // Combine offers with affiliate details
    const enrichedOffers = affiliateOffers?.map(offer => {
      const affiliate = affiliateDetails.find(a => a.id === offer.company_id);
      return {
        ...offer,
        affiliate: affiliate || null
      };
    }) || [];

    // Parse affiliate selection order if it exists
    let selectedAffiliates = [];
    if (quote.affiliate_selection_order) {
      try {
        const selectionOrder = Array.isArray(quote.affiliate_selection_order) 
          ? quote.affiliate_selection_order 
          : JSON.parse(quote.affiliate_selection_order);
        
        selectedAffiliates = selectionOrder.map((selection: any) => {
          const affiliate = affiliateDetails.find(a => a.id === selection.id);
          return {
            id: selection.id,
            order: selection.order,
            name: selection.name || affiliate?.name || 'Unknown Affiliate',
            affiliate: affiliate || null
          };
        }).sort((a: any, b: any) => a.order - b.order);
      } catch (parseError) {
        console.error('Error parsing affiliate selection order:', parseError);
      }
    }

    const response = {
      ...quote,
      affiliate_offers: enrichedOffers,
      selected_affiliates: selectedAffiliates,
      success: true
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in GET /api/super-admin/quotes/[id]:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
});

/**
 * PATCH /api/super-admin/quotes/[id]
 * Update a specific quote
 */
export const PATCH = withAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    // Authenticate user
    const user = await requireAuth(request);
    console.log("PATCH /api/super-admin/quotes/[id] - User authenticated:", user.id);

    const supabase = createSupabaseClient();

    // Get user profile to check super admin permissions
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    if (profileError || !profile?.roles?.includes('SUPER_ADMIN')) {
      return NextResponse.json(
        { error: 'Unauthorized: Super admin access required' },
        { status: 403 }
      );
    }

    const quoteId = params.id;
    const body = await request.json();

    // Update the quote
    const { data: updatedQuote, error: updateError } = await supabase
      .from('quotes')
      .update({
        ...body,
        updated_at: new Date().toISOString()
      })
      .eq('id', quoteId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating quote:', updateError);
      return NextResponse.json(
        { error: 'Failed to update quote' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      quote: updatedQuote,
      success: true
    });

  } catch (error) {
    console.error('Error in PATCH /api/super-admin/quotes/[id]:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/super-admin/quotes/[id]
 * Delete a specific quote
 */
export const DELETE = withAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    // Authenticate user
    const user = await requireAuth(request);
    console.log("DELETE /api/super-admin/quotes/[id] - User authenticated:", user.id);

    const supabase = createSupabaseClient();

    // Get user profile to check super admin permissions
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    if (profileError || !profile?.roles?.includes('SUPER_ADMIN')) {
      return NextResponse.json(
        { error: 'Unauthorized: Super admin access required' },
        { status: 403 }
      );
    }

    const quoteId = params.id;

    // Delete the quote (cascade will handle related records)
    const { error: deleteError } = await supabase
      .from('quotes')
      .delete()
      .eq('id', quoteId);

    if (deleteError) {
      console.error('Error deleting quote:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete quote' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Quote deleted successfully',
      success: true
    });

  } catch (error) {
    console.error('Error in DELETE /api/super-admin/quotes/[id]:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
});
