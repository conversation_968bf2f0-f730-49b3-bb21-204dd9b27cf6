import { NextResponse } from "next/server"

export const runtime = 'nodejs'
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { Database } from '@/lib/types/supabase' // Use global Database type
import { transitionQuoteStatus } from '@/lib/utils/quote-status-transitions'
import { QuoteStatus } from '@/lib/utils/quote-status-mapping' // Assuming this is the correct source for QuoteStatus
import { hasRole } from '@/app/lib/auth';
import { UserRole } from '@/app/lib/auth/roles';

// API endpoint to assign an affiliate to a quote by a Super Admin
// POST /api/super-admin/quotes/assign
// Body: { quoteId: string, affiliateId: string }

export async function POST(req: Request) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })
    
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    if (sessionError) {
      console.error('Session error in super-admin/quotes/assign:', sessionError);
      return NextResponse.json({ error: "Authentication error", code: "auth_error" }, { status: 500 });
    }
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required", code: "auth_required" }, 
        { status: 401 }
      )
    }

    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles') // Only select roles
      .eq('id', session.user.id)
      .single()
    
    if (profileError) {
      console.error('Error fetching super-admin profile for assign quote:', profileError);
      return NextResponse.json({ error: "Failed to verify user role", code: "profile_fetch_error" }, { status: 500 });
    }

    if (!profile?.roles || !hasRole(profile.roles as UserRole[], 'SUPER_ADMIN' as UserRole)) {
      return NextResponse.json(
        { error: "Forbidden: SUPER_ADMIN access required", code: "access_denied" }, 
        { status: 403 }
      )
    }

    const body = await req.json()
    const { quoteId, affiliateId } = body
    
    if (!quoteId || !affiliateId) {
      return NextResponse.json(
        { error: "Missing required fields: quoteId and affiliateId", code: "invalid_request" },
        { status: 400 }
      )
    }
    
    const { data: quote, error: quoteFetchError } = await supabase
      .from('quotes')
      .select('id, status') // Select only needed fields for verification and current status
      .eq('id', quoteId)
      .single()
    
    if (quoteFetchError) {
      if (quoteFetchError.code === 'PGRST116') {
        return NextResponse.json({ error: "Quote not found", code: "quote_not_found" }, { status: 404 });
      }
      console.error('Error fetching quote for assignment by super-admin:', quoteFetchError);
      return NextResponse.json({ error: "Failed to fetch quote details", code: "quote_fetch_error" }, { status: 500 });
    }
    
    const { data: affiliate, error: affiliateFetchError } = await supabase
      .from('affiliate_companies') // Assuming this is the correct table name
      .select('id') // Only need ID for verification
      .eq('id', affiliateId)
      .single()
    
    if (affiliateFetchError) {
      if (affiliateFetchError.code === 'PGRST116') {
        return NextResponse.json({ error: "Affiliate not found", code: "affiliate_not_found" }, { status: 404 });
      }
      console.error('Error fetching affiliate for assignment by super-admin:', affiliateFetchError);
      return NextResponse.json({ error: "Failed to fetch affiliate details", code: "affiliate_fetch_error" }, { status: 500 });
    }
    
    // Data for updating the quote
    const updatePayload = {
      company_id: affiliateId, // company_id and affiliate_company_id seem to be used interchangeably here
      affiliate_company_id: affiliateId,
      assigned_by: session.user.id,
      updated_at: new Date().toISOString()
    };

    // Use transitionQuoteStatus utility
    // Note: Review transitionQuoteStatus if it has admin-specific logic that needs adaptation for SUPER_ADMIN
    const { success, error: transitionError } = await transitionQuoteStatus(
      quoteId,
      'quote_assigned' as QuoteStatus, // Ensure QuoteStatus type is correctly imported and used
      updatePayload
    );
    
    let finalUpdatedQuote: any = null; // Initialize to null

    if (!success || transitionError) {
      console.warn('transitionQuoteStatus failed for super-admin assignment, attempting direct update. Error:', transitionError);
      
      const { data: directUpdateData, error: directUpdateError } = await supabase
        .from('quotes')
        .update({
          status: 'quote_assigned' as QuoteStatus,
          ...updatePayload
        })
        .eq('id', quoteId)
        .select('*') // Fetch the full updated quote
        .single();

      if (directUpdateError) {
        console.error('Direct quote update failed for super-admin assignment:', directUpdateError);
        return NextResponse.json(
          { error: "Failed to assign affiliate to quote after transition error", code: "assignment_failed_fallback" },
          { status: 500 }
        );
      }
      finalUpdatedQuote = directUpdateData;
      console.log('Direct quote update successful for super-admin assignment.');
    } else {
      // If transitionQuoteStatus was successful, fetch the updated quote
      const { data: fetchedQuote, error: fetchError } = await supabase
        .from('quotes')
        .select('*')
        .eq('id', quoteId)
        .single();
      if (fetchError) {
        console.error('Failed to fetch quote after successful transition by super-admin:', fetchError);
        // Non-fatal, but the response won't have the latest quote data
      }
      finalUpdatedQuote = fetchedQuote;
    }

    if (!finalUpdatedQuote) {
        console.error('Failed to get updated quote data after assignment by super-admin.');
        return NextResponse.json(
            { error: "Affiliate assigned, but failed to retrieve updated quote data.", code: "updated_quote_fetch_failed" }, 
            { status: 500 } // Or 207 Multi-Status if part succeeded
        );
    }

    // Add timeline entry
    try {
      await supabase.from('quote_timeline').insert({
        quote_id: quoteId,
        user_id: session.user.id,
        action: 'ASSIGNED_TO_AFFILIATE_BY_SUPER_ADMIN',
        details: `Quote assigned to affiliate ${affiliateId} by super-admin ${session.user.email}`,
        // previous_status: quote.status, // if needed for timeline
        // new_status: 'quote_assigned'
      });
    } catch (timelineInsertError) {
      console.warn('Failed to create timeline entry for super-admin quote assignment:', timelineInsertError);
      // Non-critical error, proceed with response
    }
    
    return NextResponse.json({
      message: "Affiliate assigned to quote successfully by super-admin",
      quote: finalUpdatedQuote,
    });

  } catch (error: any) {
    console.error('Error in super-admin assign quote API:', error);
    const errorMessage = error.message || "Internal server error assigning quote";
    return NextResponse.json(
      { error: errorMessage, code: error.code || "unknown_error" },
      { status: 500 }
    );
  }
} 