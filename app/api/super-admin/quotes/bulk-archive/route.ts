import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { quoteIds } = body

    if (!quoteIds || !Array.isArray(quoteIds) || quoteIds.length === 0) {
      return NextResponse.json({ error: 'Quote IDs are required' }, { status: 400 })
    }

    // Create Supabase client with proper auth
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated and has super admin access
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get quotes to validate they can be archived
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select('id, status, reference_number')
      .in('id', quoteIds)

    if (quotesError) {
      console.error('Error fetching quotes:', quotesError)
      return NextResponse.json({ error: 'Failed to fetch quotes' }, { status: 500 })
    }

    // Filter quotes that can be archived (exclude active/in-progress quotes)
    const archivableQuotes = quotes?.filter(quote => 
      !['assigned', 'in_progress', 'confirmed'].includes(quote.status)
    ) || []

    if (archivableQuotes.length === 0) {
      return NextResponse.json({ 
        error: 'No quotes can be archived. Cannot archive assigned, in-progress, or confirmed quotes.' 
      }, { status: 400 })
    }

    const archivableQuoteIds = archivableQuotes.map(q => q.id)

    // Update quotes to archived status
    const { data: updatedQuotes, error: updateError } = await supabase
      .from('quotes')
      .update({
        status: 'archived',
        archived_at: new Date().toISOString(),
        archived_by: user.id,
        updated_at: new Date().toISOString()
      })
      .in('id', archivableQuoteIds)
      .select()

    if (updateError) {
      console.error('Error archiving quotes:', updateError)
      return NextResponse.json({ error: 'Failed to archive quotes' }, { status: 500 })
    }

    // Archive related quote offers
    const { error: offersArchiveError } = await supabase
      .from('quote_affiliate_offers')
      .update({
        status: 'archived',
        archived_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .in('quote_id', archivableQuoteIds)

    if (offersArchiveError) {
      console.error('Error archiving quote offers:', offersArchiveError)
      // Don't fail the operation for this
    }

    // Create audit log entries for each archived quote
    const auditEntries = archivableQuoteIds.map(quoteId => {
      const originalQuote = quotes?.find(q => q.id === quoteId)
      return {
        table_name: 'quotes',
        record_id: quoteId,
        action: 'bulk_archive',
        old_values: { status: originalQuote?.status },
        new_values: { 
          status: 'archived',
          archived_at: new Date().toISOString(),
          archived_by: user.id
        },
        user_id: user.id,
        metadata: {
          bulk_operation: true,
          archived_count: archivableQuoteIds.length,
          original_status: originalQuote?.status,
          reference_number: originalQuote?.reference_number
        }
      }
    })

    const { error: auditError } = await supabase
      .from('audit_logs')
      .insert(auditEntries)

    if (auditError) {
      console.error('Error creating audit logs:', auditError)
      // Don't fail the operation for audit log errors
    }

    // Create timeline entries for each quote
    const timelineEntries = archivableQuoteIds.map(quoteId => {
      const originalQuote = quotes?.find(q => q.id === quoteId)
      return {
        quote_id: quoteId,
        event_type: 'bulk_archived',
        event_data: {
          archived_by: user.id,
          original_status: originalQuote?.status,
          archived_at: new Date().toISOString(),
          bulk_operation: true,
          reason: 'Bulk archive operation'
        },
        created_at: new Date().toISOString()
      }
    })

    const { error: timelineError } = await supabase
      .from('quote_timeline')
      .insert(timelineEntries)

    if (timelineError) {
      console.error('Error creating timeline entries:', timelineError)
      // Don't fail the operation for timeline errors
    }

    // Send notification to relevant stakeholders (optional)
    try {
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/notifications/bulk-archive`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          quoteIds: archivableQuoteIds,
          archivedBy: user.id,
          archivedCount: archivableQuoteIds.length
        })
      })
    } catch (notificationError) {
      console.error('Error sending archive notification:', notificationError)
      // Don't fail the operation for notification errors
    }

    return NextResponse.json({
      success: true,
      archivedCount: archivableQuoteIds.length,
      skippedCount: quoteIds.length - archivableQuoteIds.length,
      archivedQuotes: updatedQuotes,
      message: `Successfully archived ${archivableQuoteIds.length} quotes`,
      skippedQuotes: quotes?.filter(q => !archivableQuoteIds.includes(q.id)).map(q => ({
        id: q.id,
        reference_number: q.reference_number,
        status: q.status,
        reason: 'Cannot archive quotes with status: ' + q.status
      })) || []
    })

  } catch (error) {
    console.error('Error in bulk archive API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
