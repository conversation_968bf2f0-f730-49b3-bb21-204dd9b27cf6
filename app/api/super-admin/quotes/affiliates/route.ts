import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { requireAuth, withAuth, createSupabaseClient } from '@/lib/auth/unified-auth';

// API endpoint to fetch affiliates for quote assignment by Super Admin
// GET /api/super-admin/quotes/affiliates?city=Boston

export const GET = withAuth(async (request: NextRequest) => {
  console.log('[API SA] GET /api/super-admin/quotes/affiliates - Request received');
  try {
    // Authenticate user and check super admin permissions
    const user = await requireAuth(request);
    console.log("Super-admin quotes affiliates API - User authenticated:", user.id);

    const supabase = createSupabaseClient();

    // Get user profile to check super admin permissions
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();
    
    if (profileError || !profile) {
      console.error('[API SA] Error fetching super-admin profile:', profileError);
      return NextResponse.json({ error: "Failed to verify user role", code: "profile_fetch_error" }, { status: 500 });
    }

    // Check if user has super admin permissions
    const roles = Array.isArray(profile.roles) ? profile.roles : [];
    if (!roles.includes('super_admin')) {
      console.warn(`[API SA] User ${user.id} not super_admin. Roles: ${roles.join(', ')}`);
      return NextResponse.json({ error: "Forbidden: Super admin access required", code: "access_denied" }, { status: 403 });
    }

    const url = new URL(request.url);
    const city = url.searchParams.get('city');
    
    let query = supabase
      .from('affiliate_companies')
      .select('*') // Consider selecting specific fields if not all are needed by client
      .eq('status', 'active'); // Always fetch active affiliates

    if (city) {
      const searchCity = city.trim();
      console.log(`[API SA] Filtering active affiliates by city (ILIKE): ${searchCity}`);
      // Using .or() for flexible city matching: exact, starts with, contains
      query = query.or(`city.ilike.${searchCity},city.ilike.${searchCity}%,city.ilike.%${searchCity}%`);
    } else {
      console.log('[API SA] No city filter, fetching all active affiliates.');
    }
    
    const { data: affiliates, error } = await query;

    if (error) {
      console.error('[API SA] Error fetching affiliate_companies:', error.message);
      return NextResponse.json({ error: "Failed to fetch affiliates", details: error.message, code: "fetch_error" }, { status: 500 });
    }

    let sortedAffiliates = affiliates || [];

    if (city && sortedAffiliates.length > 0) {
      const searchCityLower = city.trim().toLowerCase();
      const getMatchType = (affiliateCity: string | null) => {
        if (!affiliateCity) return 3; // No city ranks last
        const lowerAffiliateCity = affiliateCity.trim().toLowerCase();
        if (lowerAffiliateCity === searchCityLower) return 0; // Exact match
        if (lowerAffiliateCity.startsWith(searchCityLower)) return 1; // Starts with
        if (lowerAffiliateCity.includes(searchCityLower)) return 2; // Contains
        return 3; // No match or partial match not covered, ranks last
      };

      sortedAffiliates.sort((a, b) => {
        const matchA = getMatchType(a.city);
        const matchB = getMatchType(b.city);
        if (matchA !== matchB) {
          return matchA - matchB;
        }
        // Optional: Secondary sort by name if match type is the same
        return (a.name || '').localeCompare(b.name || '');
      });
      console.log(`[API SA] Fetched and sorted ${sortedAffiliates.length} affiliates for city: ${city}`);
    } else if (sortedAffiliates.length > 0) {
      console.log(`[API SA] Fetched ${sortedAffiliates.length} active affiliates (no city filter or no matches for city).`);
    }

    return NextResponse.json({ affiliates: sortedAffiliates });

  } catch (error: any) {
    console.error('[API SA] Unexpected error in /super-admin/quotes/affiliates route:', error.message);
    return NextResponse.json(
      { error: "Internal server error fetching affiliates", details: error.message, code: "unknown_error" },
      { status: 500 }
    );
  }
});