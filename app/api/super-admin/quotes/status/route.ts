import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { Database } from '@/lib/types/supabase'; // Assuming Database types are correctly defined
import { hasRole } from '@/app/lib/auth';
import { UserRole } from '@/app/lib/auth/roles';

export async function POST(req: Request) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })
    
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    if (sessionError) {
      console.error('Session error in super-admin/quotes/status:', sessionError)
      return NextResponse.json({ message: "Authentication error" }, { status: 500 })
    }
    if (!session) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }
    
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles') // Only select roles
      .eq('id', session.user.id)
      .single()
    
    if (profileError) {
      console.error('Error fetching super-admin profile:', profileError)
      return NextResponse.json({ message: "Error fetching user profile" }, { status: 500 })
    }

    if (!profile?.roles || !hasRole(profile.roles as UserRole[], 'SUPER_ADMIN' as UserRole)) { // Changed to SUPER_ADMIN
      return NextResponse.json(
        { message: "Not authorized to update quote status (requires SUPER_ADMIN)" },
        { status: 403 }
      )
    }
    
    const { quoteId, status } = await req.json()
    
    if (!quoteId || !status) {
      return NextResponse.json(
        { message: "Missing required fields: quoteId and status" },
        { status: 400 }
      )
    }
    
    const { data: currentQuote, error: fetchError } = await supabase
      .from('quotes')
      .select('status')
      .eq('id', quoteId)
      .single()
    
    if (fetchError) {
      // Differentiate between not found and other errors
      if (fetchError.code === 'PGRST116') { // PostgREST error for no rows found
        return NextResponse.json({ message: "Quote not found" }, { status: 404 })
      }
      console.error('Error fetching current quote for status update:', fetchError)
      return NextResponse.json({ message: "Error fetching quote details", error: fetchError.message }, { status: 500 })
    }
    
    const { data: updatedQuote, error: updateError } = await supabase
      .from('quotes')
      .update({
        status: status,
        updated_at: new Date().toISOString()
      })
      .eq('id', quoteId)
      .select('*') // Select all fields of the updated quote
      .single()
    
    if (updateError) {
      console.error('Error updating quote status by super-admin:', updateError)
      return NextResponse.json(
        { message: "Failed to update quote status", error: updateError.message },
        { status: 500 }
      )
    }
    
    const { error: historyError } = await supabase
      .from('quote_status_history')
      .insert({
        quote_id: quoteId,
        status: status,
        previous_status: currentQuote.status,
        user_id: session.user.id,
        user_email: session.user.email,
        metadata: {
          manual_update_by: 'SUPER_ADMIN', // Indicate super-admin action
          updated_at: new Date().toISOString()
        }
      })
    
    if (historyError) {
      // Log as a warning, as the primary operation (status update) succeeded
      console.warn('Warning: Failed to record status history for super-admin update:', historyError)
    }
    
    return NextResponse.json({
      message: "Quote status updated successfully by super-admin",
      quote: updatedQuote
    })
    
  } catch (error: any) {
    console.error('Error in super-admin/quotes/status POST endpoint:', error)
    // Avoid exposing too much detail in the error response for non-JSON errors
    const errorMessage = error.message || "Unknown error updating quote status";
    return NextResponse.json(
      { message: errorMessage, error: error.code || undefined }, // Include error code if available
      { status: 500 }
    )
  }
} 