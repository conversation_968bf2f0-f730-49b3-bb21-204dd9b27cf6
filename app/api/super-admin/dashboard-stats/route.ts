import { NextRequest, NextResponse } from 'next/server';
import { authenticateSuperAdminRequest } from '@/lib/auth/api-authentication';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    console.log('Super-admin dashboard stats API - Starting request');

    // Use correct auth pattern with request parameter
    const authResult = await authenticateSuperAdminRequest(request);
    
    if (!authResult.success) {
      console.log('Authentication failed:', authResult.error);
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }
    
    console.log('Authentication successful for user:', authResult.user.id);
    
    // Use service role client for comprehensive data access
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Fetch organization statistics (using consolidated organizations table)
    const { data: organizations, error: orgsError } = await supabase
      .from('organizations')
      .select('id, created_at, subscription_plan')
      .order('created_at', { ascending: false });

    if (orgsError) {
      console.error('Error fetching organizations:', orgsError);
      // Don't throw error, just log and continue with empty data
      console.log('Continuing with empty organization data');
    }
    
    // Fetch user statistics (profiles table doesn't have status column)
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id, created_at, roles')
      .order('created_at', { ascending: false });

    if (usersError) {
      console.error('Error fetching users:', usersError);
      // Don't throw error, just log and continue with empty data
      console.log('Continuing with empty user data');
    }
    
    // Fetch quote statistics for revenue calculation
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select('id, created_at, status, organization_id')
      .order('created_at', { ascending: false });

    if (quotesError) {
      console.error('Error fetching quotes:', quotesError);
      // Don't throw error, just log and continue with empty data
      console.log('Continuing with empty quote data');
    }
    
    // Calculate growth metrics
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, now.getDate());
    
    // Calculate organization growth
    const currentMonthOrgs = organizations?.filter(o => new Date(o.created_at) >= lastMonth).length || 0;
    const previousMonthOrgs = organizations?.filter(o =>
      new Date(o.created_at) >= twoMonthsAgo && new Date(o.created_at) < lastMonth
    ).length || 0;
    const orgGrowth = previousMonthOrgs > 0
      ? Math.round(((currentMonthOrgs - previousMonthOrgs) / previousMonthOrgs) * 100)
      : currentMonthOrgs > 0 ? 100 : 0;
    
    // Calculate user growth
    const currentMonthUsers = users?.filter(u => new Date(u.created_at) >= lastMonth).length || 0;
    const previousMonthUsers = users?.filter(u => 
      new Date(u.created_at) >= twoMonthsAgo && new Date(u.created_at) < lastMonth
    ).length || 0;
    const userGrowth = previousMonthUsers > 0 
      ? Math.round(((currentMonthUsers - previousMonthUsers) / previousMonthUsers) * 100)
      : currentMonthUsers > 0 ? 100 : 0;
    
    // Calculate revenue from completed quotes (placeholder - no total_amount column yet)
    const completedQuotes = quotes?.filter(q => q.status === 'accepted' || q.status === 'completed') || [];
    const currentMonthRevenue = 0; // Placeholder until total_amount column is added
    const previousMonthRevenue = 0; // Placeholder until total_amount column is added
    
    const revenueGrowth = previousMonthRevenue > 0 
      ? Math.round(((currentMonthRevenue - previousMonthRevenue) / previousMonthRevenue) * 100)
      : currentMonthRevenue > 0 ? 100 : 0;
    
    // Calculate active organizations (organizations with recent activity)
    const activeOrganizations = organizations?.filter(o => {
      // Consider an organization active if they have quotes in the last 30 days
      const orgQuotes = quotes?.filter(q => q.created_at >= lastMonth.toISOString()) || [];
      return orgQuotes.length > 0;
    }).length || 0;

    const previousActiveOrganizations = organizations?.filter(o => {
      const orgQuotes = quotes?.filter(q =>
        new Date(q.created_at) >= twoMonthsAgo && new Date(q.created_at) < lastMonth
      ) || [];
      return orgQuotes.length > 0;
    }).length || 0;
    
    const organizationActivityGrowth = previousActiveOrganizations > 0
      ? Math.round(((activeOrganizations - previousActiveOrganizations) / previousActiveOrganizations) * 100)
      : activeOrganizations > 0 ? 100 : 0;

    const stats = {
      organizations: {
        total: organizations?.length || 0,
        growth: orgGrowth
      },
      users: {
        total: users?.length || 0,
        growth: userGrowth
      },
      revenue: {
        total: Math.round(currentMonthRevenue),
        growth: revenueGrowth
      },
      activeOrganizations: {
        total: activeOrganizations,
        growth: organizationActivityGrowth
      }
    };
    
    console.log('Super-admin dashboard stats calculated:', stats);
    
    return NextResponse.json({
      success: true,
      stats,
      meta: {
        calculatedAt: new Date().toISOString(),
        dataPoints: {
          totalOrganizations: organizations?.length || 0,
          totalUsers: users?.length || 0,
          totalQuotes: quotes?.length || 0,
          completedQuotes: completedQuotes.length
        }
      }
    });
    
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Error in super-admin dashboard stats API:', error);
    return NextResponse.json(
      { 
        success: false,
        error: `Failed to fetch dashboard stats: ${errorMessage}` 
      },
      { status: 500 }
    );
  }
}
