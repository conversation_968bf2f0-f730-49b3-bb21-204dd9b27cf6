import { NextRequest, NextResponse } from "next/server";

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    console.log("Organizations test API - Starting request");
    
    return NextResponse.json({
      success: true,
      message: "Organizations test API is working",
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in organizations test API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}