import { createClient } from '@supabase/supabase-js';
import { NextResponse } from "next/server";
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export async function GET(req: Request) {
  try {
    console.log("Super-admin passengers API - Starting request");

    // Require super admin access
    const context = await authenticateApiRequestWithRoles(["SUPER_ADMIN"]);
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Parse query parameters
    const url = new URL(req.url);
    const orgFilter = url.searchParams.get('org');

    console.log(`Super-admin passengers API - Params: { orgFilter: ${orgFilter} }`);

    // Fetch real passenger data from the passengers table with simplified query
    let query = supabase
      .from('passengers')
      .select(`
        id,
        first_name,
        last_name,
        email,
        phone,
        special_requirements,
        created_at,
        updated_at,
        event_id,
        events(
          id,
          name,
          customer_id
        )
      `)
      .order('created_at', { ascending: false });

    const { data: passengers, error } = await query.limit(100);

    if (error) {
      console.error('Error fetching passenger data:', error);
      return NextResponse.json({ error: 'Failed to fetch passenger data' }, { status: 500 });
    }
    // Transform the real passenger data for response
    const transformedPassengers = passengers?.map((passenger: any) => ({
      id: passenger.id,
      first_name: passenger.first_name,
      last_name: passenger.last_name,
      email: passenger.email,
      phone: passenger.phone,
      special_requirements: passenger.special_requirements,
      created_at: passenger.created_at,
      updated_at: passenger.updated_at,
      event_id: passenger.events?.id || passenger.event_id,
      event_name: passenger.events?.name || 'Unknown Event',
      customer: {
        id: passenger.events?.customer_id,
        email: passenger.email,
        full_name: `${passenger.first_name} ${passenger.last_name}`
      },
      organization: {
        name: 'Organization' // Simplified for now
      }
    })) || [];

    console.log(`Super-admin passengers API - Found ${transformedPassengers?.length || 0} passengers`);

    return NextResponse.json({
      passengers: transformedPassengers,
      total: transformedPassengers?.length || 0,
      success: true
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Error in super-admin passengers API:', error);
    return NextResponse.json(
      { error: `Failed to fetch passengers: ${errorMessage}` },
      { status: 500 }
    );
  }
}
