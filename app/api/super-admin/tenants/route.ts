import { cookies } from 'next/headers';

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js';
import { toUserRoles } from '@/app/lib/auth/roles';
import { NextRequest, NextResponse } from 'next/server';
import { getSession, hasRole } from '@/app/lib/auth';

/**
 * GET /api/super-admin/tenants
 * CONSOLIDATED: Now serves as unified tenant/organization management endpoint
 * Post GUG-119 consolidation - handles both tenant and organization concepts
 */
export async function GET(request: NextRequest) {
  try {
    console.log('[GUG-119] Consolidated tenants API - GET request');
    
    // Require session and super admin role
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
    const sessionUserRoles = toUserRoles(sessionRoles);
    
    if (!sessionUserRoles.includes('SUPER_ADMIN')) {
      return NextResponse.json({ 
        error: 'Forbidden: SUPER_ADMIN role required', 
        requiredRole: 'SUPER_ADMIN', 
        userRoles: sessionUserRoles 
      }, { status: 403 });
    }

    // Use service role client for super admin access
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get query parameters for filtering and pagination
    const url = new URL(request.url);
    const organizationType = url.searchParams.get('type');
    const status = url.searchParams.get('status');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // Build query for consolidated organizations table
    let query = supabase
      .from('organizations')
      .select(`
        id,
        name,
        slug,
        description,
        organization_type,
        status,
        domain,
        branding,
        settings,
        parent_organization_id,
        created_at,
        updated_at
      `);

    // Apply filters
    if (organizationType) {
      query = query.eq('organization_type', organizationType);
    }
    if (status) {
      query = query.eq('status', status);
    }

    // Apply pagination and ordering
    query = query
      .order('name')
      .range(offset, offset + limit - 1);

    const { data: organizations, error, count } = await query;

    if (error) {
      console.error('Error fetching consolidated organizations:', error);
      return NextResponse.json({ error: 'Failed to fetch organizations' }, { status: 500 });
    }

    // Enhance organizations with user counts and additional metadata
    const enhancedOrganizations = await Promise.all(
      (organizations || []).map(async (org) => {
        // Get user count for this organization
        const { count: userCount } = await supabase
          .from('user_organizations')
          .select('*', { count: 'exact', head: true })
          .eq('organization_id', org.id);

        // Get permission template info if available
        const { data: permissions } = await supabase
          .from('organization_permissions')
          .select('permission_key, enabled')
          .eq('organization_id', org.id)
          .limit(5);

        return {
          ...org,
          users_count: userCount || 0,
          permissions_count: permissions?.length || 0,
          sample_permissions: permissions || [],
          // Add tenant-specific metadata for backward compatibility
          tenant_type: org.organization_type, // Map organization_type to tenant_type
          is_white_label: org.organization_type === 'white_label',
          has_custom_domain: !!org.domain
        };
      })
    );

    console.log(`[GUG-119] Consolidated tenants API - Found ${enhancedOrganizations?.length || 0} organizations`);

    return NextResponse.json({
      organizations: enhancedOrganizations || [],
      tenants: enhancedOrganizations || [], // Backward compatibility alias
      total_count: count,
      success: true,
      consolidation_status: 'GUG-119_COMPLETE'
    });

  } catch (error) {
    console.error('Error in consolidated GET /api/super-admin/tenants:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/super-admin/tenants
 * CONSOLIDATED: Creates new tenant/organization in unified system
 * Post GUG-119 consolidation - creates organizations with tenant capabilities
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[GUG-119] Consolidated tenants API - POST request');
    
    // Require session and super admin role
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
    const sessionUserRoles = toUserRoles(sessionRoles);
    
    if (!sessionUserRoles.includes('SUPER_ADMIN')) {
      return NextResponse.json({ 
        error: 'Forbidden: SUPER_ADMIN role required', 
        requiredRole: 'SUPER_ADMIN', 
        userRoles: sessionUserRoles 
      }, { status: 403 });
    }

    // Use service role client for tenant creation
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const body = await request.json();
    console.log('[GUG-119] Creating consolidated tenant/organization:', JSON.stringify(body, null, 2));

    const {
      name,
      slug,
      organization_type = 'segregated', // Default for TNC clients
      domain,
      branding = {},
      settings = {},
      admin_user
    } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Organization name is required' },
        { status: 400 }
      );
    }

    // Generate unique slug
    const baseSlug = slug || name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '') || 'org';
    let finalSlug = baseSlug;
    let counter = 1;

    // Ensure slug uniqueness
    while (true) {
      const { data: existingOrg } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', finalSlug)
        .maybeSingle();

      if (!existingOrg) break;

      finalSlug = `${baseSlug}-${counter}`;
      counter++;

      if (counter > 100) {
        finalSlug = `${baseSlug}-${Date.now()}`;
        break;
      }
    }

    // Create the consolidated organization/tenant
    const organizationData = {
      name,
      slug: finalSlug,
      organization_type,
      domain,
      branding: {
        ...branding,
        created_via: 'super_admin_tenant_creation'
      },
      settings: {
        ...settings,
        tenant_capabilities: true,
        white_label_enabled: organization_type === 'white_label',
        created_via: 'gug_119_consolidation'
      },
      status: 'active'
    };

    const { data: organization, error } = await supabase
      .from('organizations')
      .insert(organizationData)
      .select()
      .single();

    if (error) {
      console.error('Error creating consolidated organization:', error);
      return NextResponse.json({
        error: `Failed to create organization: ${error.message}`,
        details: error
      }, { status: 500 });
    }

    console.log('[GUG-119] Consolidated organization created:', organization.id);

    // Create admin user if provided
    let adminUserResult = null;
    if (admin_user && admin_user.email) {
      try {
        const userPassword = admin_user.password || generateSecurePassword();

        const { data: authUser, error: userError } = await supabase.auth.admin.createUser({
          email: admin_user.email.toLowerCase(),
          password: userPassword,
          email_confirm: true,
          user_metadata: {
            full_name: `${admin_user.first_name || ''} ${admin_user.last_name || ''}`.trim(),
            first_name: admin_user.first_name,
            last_name: admin_user.last_name,
            role: admin_user.role || 'TNC_ADMIN',
            organization_id: organization.id,
            created_by_admin: true,
            tenant_type: organization_type
          }
        });

        if (userError) {
          console.error('Error creating admin user:', userError);
          adminUserResult = { error: userError.message };
        } else {
          // Create profile
          await supabase
            .from('profiles')
            .insert({
              id: authUser.user.id,
              email: admin_user.email.toLowerCase(),
              role: admin_user.role || 'TNC_ADMIN',
              roles: [admin_user.role || 'TNC_ADMIN'],
              status: 'ACTIVE'
            });

          // Create organization association
          await supabase
            .from('user_organizations')
            .insert({
              user_id: authUser.user.id,
              organization_id: organization.id,
              role: admin_user.role || 'TNC_ADMIN'
            });

          adminUserResult = {
            id: authUser.user.id,
            email: admin_user.email,
            role: admin_user.role || 'TNC_ADMIN',
            password: userPassword,
            organization_id: organization.id
          };
        }
      } catch (userCreationError) {
        console.error('Error in admin user creation:', userCreationError);
        adminUserResult = { error: userCreationError instanceof Error ? userCreationError.message : 'Unknown error' };
      }
    }

    return NextResponse.json({
      success: true,
      organization,
      tenant: organization, // Backward compatibility alias
      admin_user: adminUserResult,
      consolidation_status: 'GUG-119_COMPLETE',
      message: 'Tenant/Organization created successfully in consolidated system'
    });

  } catch (error) {
    console.error('Error in consolidated POST /api/super-admin/tenants:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

// Utility function to generate secure password
function generateSecurePassword(): string {
  const length = 12;
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
  let password = "";
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};
