import { getSession, hasRole } from '@/app/lib/auth';

export const runtime = 'nodejs'
import { toUserRoles, UserRole } from '@/app/lib/auth/roles';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  // Require session and SUPER_ADMIN
  const session = await getSession(request);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
  const sessionUserRoles = toUserRoles(sessionRoles);
  if (!hasRole(sessionUserRoles, 'SUPER_ADMIN' as UserRole)) {
    return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: sessionUserRoles }, { status: 403 });
  }
  // Use authenticated Supabase client
  const supabase = await getSupabaseClient(request);
  if (!supabase) {
    return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
  }
  try {
    const organizationId = params.id;
    if (!organizationId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }
    const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    if (!uuidRegex.test(organizationId)) {
      return NextResponse.json({ error: 'Invalid Tenant ID format' }, { status: 400 });
    }
    const { data: tenantData, error: rpcError } = await supabase
      .rpc('get_tenant_by_id_rpc', { p_organization_id: organizationId })
      .single();
    if (rpcError) {
      console.error(`Error calling get_tenant_by_id_rpc for ID ${organizationId}:`, rpcError);
      if (rpcError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Tenant not found' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Failed to fetch tenant.', details: rpcError.message }, { status: 500 });
    }
    if (!tenantData) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 404 });
    }
    return NextResponse.json({ tenant: tenantData, success: true });
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
    const sessionUserRoles = toUserRoles(sessionRoles);
    if (!hasRole(sessionUserRoles, 'SUPER_ADMIN' as UserRole)) {
      return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: sessionUserRoles }, { status: 403 });
    }

    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
    }

    const organizationId = params.id;

    if (!organizationId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }
    const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    if (!uuidRegex.test(organizationId)) {
        return NextResponse.json({ error: 'Invalid Tenant ID format' }, { status: 400 });
    }

    let updates;
    try {
      updates = await request.json();
    } catch (e) {
      return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    if (Object.keys(updates).length === 0) {
      return NextResponse.json({ error: 'Request body cannot be empty' }, { status: 400 });
    }
    
    const { data: updatedTenantData, error: rpcError } = await supabase
      .rpc('update_tenant_rpc', {
        p_organization_id: organizationId,
        p_updates: updates,
      })
      .single(); 

    if (rpcError) {
      console.error(`Error calling update_tenant_rpc for ID ${organizationId}:`, rpcError);
      if (rpcError.code === 'P0002' || (rpcError.message && rpcError.message.includes('Tenant with ID') && rpcError.message.includes('not found'))) {
         return NextResponse.json({ error: 'Tenant not found' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Failed to update tenant.', details: rpcError.message }, { status: 500 });
    }
    
    if (!updatedTenantData) {
        return NextResponse.json({ error: 'Tenant not found or update failed' }, { status: 404 });
    }

    return NextResponse.json({ tenant: updatedTenantData, success: true });

  } catch (error: any) {
    console.error(`Error in PUT /api/super-admin/tenants/[id] for ID ${params.id}:`, error);
    // Handle cases where requireRole might throw an error not caught by the inner try-catch
    if (error.message && error.message.startsWith('Forbidden:')) {
        return NextResponse.json({ error: error.message }, { status: 403 });
    }
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
    const sessionUserRoles = toUserRoles(sessionRoles);
    if (!hasRole(sessionUserRoles, 'SUPER_ADMIN' as UserRole)) {
      return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: sessionUserRoles }, { status: 403 });
    }

    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
    }

    const organizationId = params.id;

    if (!organizationId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });    
    }

    const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    if (!uuidRegex.test(organizationId)) {
      return NextResponse.json({ error: 'Invalid Tenant ID format' }, { status: 400 });
    }

    // Call the soft delete RPC function
    const { error: rpcError } = await supabase
      .rpc('soft_delete_tenant_rpc', { p_organization_id: organizationId });

    if (rpcError) {
      console.error(`Error calling soft_delete_tenant_rpc for ID ${organizationId}:`, rpcError);
      
      // Check for specific error codes
      if (rpcError.code === 'P0002' || 
          (rpcError.message && rpcError.message.includes('not found'))) {
        return NextResponse.json({ error: 'Tenant not found' }, { status: 404 });
      }
      
      return NextResponse.json(
        { error: 'Failed to delete tenant', details: rpcError.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: true, message: 'Tenant deleted successfully' },
      { status: 200 }
    );

  } catch (error: any) {
    console.error(`Error in DELETE /api/super-admin/tenants/[id] for ID ${params.id}:`, error);
    // Handle cases where requireRole might throw an error not caught by the inner try-catch
    if (error.message && error.message.startsWith('Forbidden:')) {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
