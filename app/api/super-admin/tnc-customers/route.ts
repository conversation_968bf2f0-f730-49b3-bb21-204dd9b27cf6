import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// GET: Fetch TNC customer organizations for a specific TNC
export async function GET(request: NextRequest) {
  try {
    console.log("TNC customers API - Starting GET request");
    
    const { searchParams } = new URL(request.url);
    const tncId = searchParams.get('tnc_id');
    
    if (!tncId) {
      return NextResponse.json(
        { error: 'TNC ID is required' },
        { status: 400 }
      );
    }
    
    // Check environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify the TNC organization exists and is valid
    const { data: tncOrg, error: tncError } = await supabase
      .from('organizations')
      .select('id, name, account_type')
      .eq('id', tncId)
      .eq('account_type', 'tnc_account')
      .single();

    if (tncError || !tncOrg) {
      return NextResponse.json(
        { error: 'Invalid TNC organization' },
        { status: 404 }
      );
    }

    // Fetch TNC customer organizations
    const { data: customers, error: customersError } = await supabase
      .from('organizations')
      .select(`
        id,
        name,
        slug,
        account_type,
        parent_tnc_id,
        managed_by,
        status,
        business_type,
        industry,
        primary_contact_name,
        primary_contact_email,
        created_at,
        updated_at
      `)
      .eq('parent_tnc_id', tncId)
      .eq('account_type', 'tnc_customer')
      .order('created_at', { ascending: false });

    if (customersError) {
      console.error('Error fetching TNC customers:', customersError);
      return NextResponse.json(
        { error: 'Failed to fetch TNC customers' },
        { status: 500 }
      );
    }

    console.log(`Found ${customers?.length || 0} TNC customers for ${tncOrg.name}`);
    
    return NextResponse.json({
      success: true,
      tnc_organization: tncOrg,
      customers: customers || [],
      total: customers?.length || 0
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/tnc-customers:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST: Create a new TNC customer organization
export async function POST(request: NextRequest) {
  try {
    console.log("TNC customers API - Starting POST request");
    
    const body = await request.json();
    const { 
      name, 
      parent_tnc_id, 
      primary_contact_name, 
      primary_contact_email,
      business_type,
      industry 
    } = body;
    
    // Validate required fields
    if (!name || !parent_tnc_id || !primary_contact_name || !primary_contact_email) {
      return NextResponse.json(
        { error: 'Missing required fields: name, parent_tnc_id, primary_contact_name, primary_contact_email' },
        { status: 400 }
      );
    }
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify parent TNC exists
    const { data: parentTnc, error: parentError } = await supabase
      .from('organizations')
      .select('id, name, account_type')
      .eq('id', parent_tnc_id)
      .eq('account_type', 'tnc_account')
      .single();

    if (parentError || !parentTnc) {
      return NextResponse.json(
        { error: 'Invalid parent TNC organization' },
        { status: 400 }
      );
    }

    // Generate slug from name
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Create TNC customer organization
    const { data: newCustomer, error: createError } = await supabase
      .from('organizations')
      .insert({
        name,
        slug,
        account_type: 'tnc_customer',
        parent_tnc_id,
        managed_by: 'tnc',
        organization_type: 'shared', // TNC customers inherit network access
        status: 'active',
        business_type,
        industry,
        primary_contact_name,
        primary_contact_email,
        settings: {
          inherit_from_parent: true,
          portal_branding: 'inherit'
        },
        feature_flags: {
          inherit_from_parent: true
        }
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating TNC customer:', createError);
      return NextResponse.json(
        { error: 'Failed to create TNC customer organization' },
        { status: 500 }
      );
    }

    console.log(`Created TNC customer: ${newCustomer.name} under ${parentTnc.name}`);
    
    return NextResponse.json({
      success: true,
      customer: newCustomer,
      parent_tnc: parentTnc
    });

  } catch (error) {
    console.error('Error in POST /api/super-admin/tnc-customers:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}