import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import {
  requireRole,
} from "@/lib/auth/server";

export async function GET(request: NextRequest) {
  try {
    console.log('Super-admin audit logs API - Starting request')
    
    // Require super admin access
    const context = await authenticateApiRequestWithRoles(["SUPER_ADMIN"]);
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    
    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const action = searchParams.get('action')
    const userId = searchParams.get('user_id')
    const tableName = searchParams.get('table_name')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    
    const offset = (page - 1) * limit
    
    // Build query
    let query = supabase
      .from('audit_logs')
      .select(`
        *,
        profiles!audit_logs_user_id_fkey(email, first_name, last_name)
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)
    
    // Apply filters
    if (action) {
      query = query.eq('action', action)
    }
    
    if (userId) {
      query = query.eq('user_id', userId)
    }
    
    if (tableName) {
      query = query.eq('table_name', tableName)
    }
    
    if (startDate) {
      query = query.gte('created_at', startDate)
    }
    
    if (endDate) {
      query = query.lte('created_at', endDate)
    }
    
    const { data: auditLogs, error: auditError } = await query
    
    if (auditError) {
      console.error('Error fetching audit logs:', auditError)
      return NextResponse.json(
        { error: 'Failed to fetch audit logs', details: auditError.message },
        { status: 500 }
      )
    }
    
    // Get total count for pagination
    let countQuery = supabase
      .from('audit_logs')
      .select('*', { count: 'exact', head: true })
    
    // Apply same filters to count query
    if (action) countQuery = countQuery.eq('action', action)
    if (userId) countQuery = countQuery.eq('user_id', userId)
    if (tableName) countQuery = countQuery.eq('table_name', tableName)
    if (startDate) countQuery = countQuery.gte('created_at', startDate)
    if (endDate) countQuery = countQuery.lte('created_at', endDate)
    
    const { count, error: countError } = await countQuery
    
    if (countError) {
      console.error('Error getting audit logs count:', countError)
    }
    
    // Get unique actions for filter dropdown
    const { data: actions, error: actionsError } = await supabase
      .from('audit_logs')
      .select('action')
      .order('action')
    
    const uniqueActions = actions ? Array.from(new Set(actions.map(a => a.action))) : []
    
    // Get unique table names for filter dropdown
    const { data: tables, error: tablesError } = await supabase
      .from('audit_logs')
      .select('table_name')
      .order('table_name')
    
    const uniqueTables = tables ? Array.from(new Set(tables.map(t => t.table_name))) : []
    
    console.log(`Fetched ${auditLogs?.length || 0} audit logs`)
    
    return NextResponse.json({
      success: true,
      audit_logs: auditLogs || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        total_pages: Math.ceil((count || 0) / limit)
      },
      filters: {
        actions: uniqueActions,
        tables: uniqueTables
      }
    })
    
  } catch (error) {
    console.error('Error in audit logs API:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
