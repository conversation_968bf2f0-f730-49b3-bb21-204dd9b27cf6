import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';

// PRODUCTION EVENTS API - Enterprise-grade authentication
export async function GET(request: NextRequest) {
  try {
    console.log("Production super-admin events API - Starting request");
    
    // Authenticate request with super admin access
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { user } = authResult;
    console.log(`Authenticated user: ${user!.email} (${user!.role})`);
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get organization filter from query params
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('org_id');
    
    console.log('Filtering by organization ID:', orgId);
    
    // Organization access validation (simplified for super admin)
    if (orgId && orgId !== 'all') {
      console.log(`Filtering by organization: ${orgId}`);
    }

    // Get events with correct column names from database
    let query = supabase
      .from('events')
      .select(`
        id,
        organization_id,
        name,
        description,
        event_type,
        status,
        start_date,
        end_date,
        start_time,
        end_time,
        timezone,
        venue_name,
        venue_address,
        venue_city,
        max_attendees,
        current_attendees,
        base_price,
        currency,
        transportation_included,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false });

    // Apply organization filter if provided and not "all"
    if (orgId && orgId !== 'all') {
      query = query.eq('organization_id', orgId);
    } else if (user!.role !== 'SUPER_ADMIN') {
      // Non-super admin users can only see their organization's data
      query = query.eq('organization_id', user!.organization_id);
    }

    const { data: events, error: eventsError } = await query;

    if (eventsError) {
      console.error('Error fetching events:', eventsError);
      return NextResponse.json(
        { error: 'Failed to fetch events' },
        { status: 500 }
      );
    }

    console.log(`Found ${events?.length || 0} events`);
    
    return NextResponse.json({
      success: true,
      events: events || [],
      total: events?.length || 0,
      filtered_by_org: orgId && orgId !== 'all' ? orgId : null,
      user_context: {
        role: user!.role,
        userId: user!.id,
        email: user!.email
      }
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/events-simple:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}