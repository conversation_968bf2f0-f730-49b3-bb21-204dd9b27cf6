import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// POST: Provision a new TNC customer portal
export async function POST(request: NextRequest) {
  try {
    console.log("TNC Portal Provisioning API - Starting POST request");
    
    const body = await request.json();
    const { 
      customer_org_id, 
      parent_tnc_id, 
      portal_subdomain, 
      brand_name,
      custom_branding 
    } = body;
    
    // Validate required fields
    if (!customer_org_id || !parent_tnc_id) {
      return NextResponse.json(
        { error: 'Missing required fields: customer_org_id, parent_tnc_id' },
        { status: 400 }
      );
    }
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify the TNC customer organization exists and hierarchy is valid
    const { data: customerOrg, error: customerError } = await supabase
      .from('organizations')
      .select('id, name, account_type, parent_tnc_id')
      .eq('id', customer_org_id)
      .eq('account_type', 'tnc_customer')
      .eq('parent_tnc_id', parent_tnc_id)
      .single();

    if (customerError || !customerOrg) {
      return NextResponse.json(
        { error: 'Invalid TNC customer organization or hierarchy' },
        { status: 400 }
      );
    }

    // Check if portal already exists
    const { data: existingPortal, error: portalCheckError } = await supabase
      .from('tnc_customer_portal_configs')
      .select('id, portal_subdomain, status')
      .eq('tnc_customer_org_id', customer_org_id)
      .single();

    if (existingPortal && !portalCheckError) {
      return NextResponse.json(
        { 
          error: 'Portal already exists for this customer',
          existing_portal: existingPortal
        },
        { status: 409 }
      );
    }

    // Provision the portal using the database function
    const { data: portalResult, error: provisionError } = await supabase
      .rpc('provision_tnc_customer_portal', {
        customer_org_id,
        parent_tnc_id,
        portal_subdomain,
        brand_name
      });

    if (provisionError) {
      console.error('Error provisioning portal:', provisionError);
      return NextResponse.json(
        { error: 'Failed to provision portal', details: provisionError.message },
        { status: 500 }
      );
    }

    // Apply custom branding if provided
    if (custom_branding && portalResult) {
      const { error: brandingError } = await supabase
        .from('tnc_customer_portal_configs')
        .update({
          logo_url: custom_branding.logo_url,
          primary_color: custom_branding.primary_color,
          secondary_color: custom_branding.secondary_color,
          portal_settings: custom_branding.portal_settings || {}
        })
        .eq('id', portalResult);

      if (brandingError) {
        console.warn('Warning: Failed to apply custom branding:', brandingError);
      }
    }

    // Get the complete portal configuration
    const { data: portalConfig, error: configError } = await supabase
      .rpc('get_tnc_customer_portal_config', {
        customer_org_id
      });

    if (configError) {
      console.error('Error fetching portal config:', configError);
      return NextResponse.json(
        { error: 'Portal provisioned but failed to fetch configuration' },
        { status: 500 }
      );
    }

    console.log(`Portal provisioned successfully for ${customerOrg.name}`);
    
    return NextResponse.json({
      success: true,
      portal_config_id: portalResult,
      customer_organization: customerOrg,
      portal_configuration: portalConfig?.[0] || null,
      portal_url: portalConfig?.[0]?.portal_subdomain 
        ? `https://${portalConfig[0].portal_subdomain}.transflow.com`
        : null
    });

  } catch (error) {
    console.error('Error in POST /api/super-admin/tnc-portal-provisioning:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET: Get portal configuration for a TNC customer
export async function GET(request: NextRequest) {
  try {
    console.log("TNC Portal Provisioning API - Starting GET request");
    
    const { searchParams } = new URL(request.url);
    const customer_org_id = searchParams.get('customer_org_id');
    
    if (!customer_org_id) {
      return NextResponse.json(
        { error: 'customer_org_id parameter is required' },
        { status: 400 }
      );
    }
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get portal configuration
    const { data: portalConfig, error: configError } = await supabase
      .rpc('get_tnc_customer_portal_config', {
        customer_org_id
      });

    if (configError) {
      console.error('Error fetching portal config:', configError);
      return NextResponse.json(
        { error: 'Failed to fetch portal configuration' },
        { status: 500 }
      );
    }

    // Get customer permissions
    const { data: permissions, error: permissionsError } = await supabase
      .from('tnc_customer_permissions')
      .select('permission_category, permission_key, permission_value, inherited_from_parent')
      .eq('tnc_customer_org_id', customer_org_id);

    if (permissionsError) {
      console.warn('Warning: Failed to fetch permissions:', permissionsError);
    }

    return NextResponse.json({
      success: true,
      portal_configuration: portalConfig?.[0] || null,
      permissions: permissions || [],
      portal_url: portalConfig?.[0]?.portal_subdomain 
        ? `https://${portalConfig[0].portal_subdomain}.transflow.com`
        : null
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/tnc-portal-provisioning:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}