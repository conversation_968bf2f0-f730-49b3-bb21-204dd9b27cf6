import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';

// PRODUCTION DASHBOARD REPORTS API - Enterprise-grade authentication
export async function GET(request: NextRequest) {
  try {
    console.log("Production Dashboard Reports API - Starting request");
    
    // Authenticate request with super admin access
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { context } = authResult;
    console.log(`Authenticated user: ${context!.email} (${context!.roles.join(', ')})`);
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get organization filter from query params
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('org_id');
    const timeframe = searchParams.get('timeframe') || '30d';
    
    console.log('Generating reports for organization:', orgId, 'timeframe:', timeframe);

    // Calculate date range based on timeframe
    const now = new Date();
    const startDate = new Date();
    switch (timeframe) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Get basic statistics
    const [quotesResult, eventsResult, usersResult, affiliatesResult] = await Promise.allSettled([
      // Quotes stats
      supabase
        .from('quotes')
        .select('id, status, total_amount, created_at')
        .gte('created_at', startDate.toISOString())
        .then(({ data, error }) => ({ data: data || [], error })),
      
      // Events stats
      supabase
        .from('events')
        .select('id, status, created_at')
        .gte('created_at', startDate.toISOString())
        .then(({ data, error }) => ({ data: data || [], error })),
      
      // Users stats
      supabase
        .from('profiles')
        .select('id, roles, created_at')
        .gte('created_at', startDate.toISOString())
        .then(({ data, error }) => ({ data: data || [], error })),
      
      // Affiliates stats
      supabase
        .from('affiliate_companies')
        .select('id, status, created_at')
        .gte('created_at', startDate.toISOString())
        .then(({ data, error }) => ({ data: data || [], error }))
    ]);

    // Process results safely
    const quotes = quotesResult.status === 'fulfilled' ? quotesResult.value.data : [];
    const events = eventsResult.status === 'fulfilled' ? eventsResult.value.data : [];
    const users = usersResult.status === 'fulfilled' ? usersResult.value.data : [];
    const affiliates = affiliatesResult.status === 'fulfilled' ? affiliatesResult.value.data : [];

    // Calculate metrics
    const reportData = {
      summary: {
        totalQuotes: quotes.length,
        totalEvents: events.length,
        totalUsers: users.length,
        totalAffiliates: affiliates.length,
        timeframe,
        dateRange: {
          start: startDate.toISOString(),
          end: now.toISOString()
        }
      },
      quotes: {
        total: quotes.length,
        pending: quotes.filter(q => q.status === 'pending').length,
        confirmed: quotes.filter(q => q.status === 'confirmed').length,
        completed: quotes.filter(q => q.status === 'completed').length,
        totalRevenue: quotes.reduce((sum, q) => sum + (parseFloat(q.total_amount) || 0), 0)
      },
      events: {
        total: events.length,
        active: events.filter(e => e.status === 'active').length,
        completed: events.filter(e => e.status === 'completed').length
      },
      users: {
        total: users.length,
        byRole: users.reduce((acc, user) => {
          const role = user.roles?.[0] || 'UNKNOWN';
          acc[role] = (acc[role] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      },
      affiliates: {
        total: affiliates.length,
        active: affiliates.filter(a => a.status === 'active').length,
        pending: affiliates.filter(a => a.status === 'pending').length
      }
    };

    console.log(`Successfully generated report data for ${timeframe}`);

    return NextResponse.json({
      success: true,
      reportData,
      user_context: {
        roles: context!.roles,
        userId: context!.userId,
        email: context!.email
      }
    });

  } catch (error) {
    console.error('Dashboard Reports API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}