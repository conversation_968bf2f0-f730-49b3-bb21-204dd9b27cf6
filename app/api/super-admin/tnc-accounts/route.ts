import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

/**
 * GET /api/super-admin/tnc-accounts
 * Get all TNC accounts (tenant networks)
 */
export async function GET(request: NextRequest) {
  try {
    // Only super admin can manage TNC accounts
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Fetch all tenant networks with their configurations
    const { data: tncAccounts, error } = await supabase
      .from('saas_organizations')
      .select(`
        *,
        tenant_branding_config (*),
        tenant_billing_config (*),
        tenant_feature_gates (
          feature_key,
          enabled,
          configuration
        ),
        organizations (
          id,
          name,
          status,
          created_at
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching TNC accounts:', error);
      return NextResponse.json({ error: 'Failed to fetch TNC accounts' }, { status: 500 });
    }

    // Calculate statistics for each TNC account
    const tncAccountsWithStats = tncAccounts?.map(account => {
      const orgCount = account.organizations?.length || 0;
      const activeFeatures = account.tenant_feature_gates?.filter(f => f.enabled).length || 0;
      const totalFeatures = account.tenant_feature_gates?.length || 0;
      
      return {
        ...account,
        stats: {
          organization_count: orgCount,
          active_features: activeFeatures,
          total_features: totalFeatures,
          network_type: account.organization_type || 'shared'
        }
      };
    });

    return NextResponse.json({
      success: true,
      tnc_accounts: tncAccountsWithStats || []
    });

  } catch (error: any) {
    console.error('TNC accounts API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * POST /api/super-admin/tnc-accounts
 * Create a new TNC account (tenant network)
 */
export async function POST(request: NextRequest) {
  try {
    // Only super admin can create TNC accounts
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const body = await request.json();
    
    const { 
      name, 
      slug, 
      description,
      organization_type = 'segregated', // Default to segregated for TNC
      domain,
      branding = {},
      features = [],
      tnc_admin_user
    } = body;

    if (!name || !slug) {
      return NextResponse.json(
        { error: 'name and slug are required' },
        { status: 400 }
      );
    }

    // Validate tenant type
    const validTypes = ['shared', 'segregated', 'white_label'];
    if (!validTypes.includes(organization_type)) {
      return NextResponse.json(
        { error: `organization_type must be one of: ${validTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // Create tenant network
    const { data: tenant, error: tenantError } = await supabase
      .from('saas_organizations')
      .insert({
        name,
        slug,
        description,
        organization_type,
        status: 'active'
      })
      .select()
      .single();

    if (tenantError) {
      console.error('Error creating tenant:', tenantError);
      return NextResponse.json({ error: 'Failed to create TNC account' }, { status: 500 });
    }

    // Create branding configuration
    const brandingConfig = {
      organization_id: tenant.id,
      primary_color: branding.primary_color || '#3B82F6',
      secondary_color: branding.secondary_color || '#1F2937',
      accent_color: branding.accent_color || '#10B981',
      logo_url: branding.logo_url,
      favicon_url: branding.favicon_url,
      custom_css: branding.custom_css,
      white_label_enabled: organization_type === 'white_label'
    };

    const { error: brandingError } = await supabase
      .from('tenant_branding_config')
      .insert(brandingConfig);

    if (brandingError) {
      console.error('Error creating branding config:', brandingError);
    }

    // Create billing configuration
    const { error: billingError } = await supabase
      .from('tenant_billing_config')
      .insert({
        organization_id: tenant.id,
        billing_plan: 'enterprise',
        subscription_status: 'active'
      });

    if (billingError) {
      console.error('Error creating billing config:', billingError);
    }

    // Set up default feature gates for TNC
    const defaultTNCFeatures = [
      { key: 'quotes_management', enabled: true, config: { max_quotes_per_month: 10000 } },
      { key: 'real_time_updates', enabled: true, config: {} },
      { key: 'affiliate_management', enabled: true, config: { max_affiliates: 500 } },
      { key: 'custom_branding', enabled: true, config: {} },
      { key: 'embeddable_forms', enabled: true, config: {} },
      { key: 'advanced_analytics', enabled: true, config: {} },
      { key: 'white_label', enabled: organization_type === 'white_label', config: {} },
      { key: 'custom_domains', enabled: organization_type === 'white_label', config: {} },
      { key: 'api_access', enabled: true, config: { rate_limit: 10000 } },
      { key: 'priority_support', enabled: true, config: {} }
    ];

    // Add custom features if provided
    const allFeatures = [...defaultTNCFeatures, ...features];

    for (const feature of allFeatures) {
      await supabase.rpc('set_tenant_feature', {
        tenant_uuid: tenant.id,
        feature_name: feature.key,
        is_enabled: feature.enabled,
        config: feature.config || {}
      });
    }

    // Create TNC admin user if provided
    if (tnc_admin_user) {
      const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
        email: tnc_admin_user.email,
        password: tnc_admin_user.password || 'TNCAdmin123!',
        email_confirm: true,
        user_metadata: {
          first_name: tnc_admin_user.first_name,
          last_name: tnc_admin_user.last_name,
          role: 'TNC_ADMIN',
          organization_id: tenant.id
        }
      });

      if (authError) {
        console.error('Error creating TNC admin user:', authError);
      } else {
        // Create profile for TNC admin
        await supabase
          .from('profiles')
          .insert({
            id: authUser.user.id,
            email: tnc_admin_user.email,
            first_name: tnc_admin_user.first_name,
            last_name: tnc_admin_user.last_name,
            roles: ['TNC_ADMIN']
          });
      }
    }

    // Fetch the complete TNC account data
    const { data: completeTNCAccount, error: fetchError } = await supabase
      .from('saas_organizations')
      .select(`
        *,
        tenant_branding_config (*),
        tenant_billing_config (*),
        tenant_feature_gates (*)
      `)
      .eq('id', tenant.id)
      .single();

    if (fetchError) {
      console.error('Error fetching complete TNC account:', fetchError);
      return NextResponse.json({ 
        success: true, 
        tnc_account: tenant,
        message: 'TNC account created but failed to fetch complete data'
      });
    }

    return NextResponse.json({
      success: true,
      tnc_account: completeTNCAccount,
      message: `TNC account "${name}" created successfully`
    });

  } catch (error: any) {
    console.error('TNC account creation API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}