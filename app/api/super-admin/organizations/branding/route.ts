/**
 * GUG-32: White-Label Engine - Branding Management API
 * 
 * Handles CRUD operations for organization branding configurations
 */

import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 500 });
    }

    const supabase = createClient();

    const { data: brandings, error } = await supabase
      .from('organization_branding')
      .select(`
        id,
        organization_id,
        subdomain,
        custom_domain,
        brand_name,
        logo_url,
        favicon_url,
        primary_color,
        secondary_color,
        accent_color,
        background_color,
        text_color,
        font_family,
        font_url,
        header_style,
        footer_style,
        sidebar_style,
        email_header_color,
        email_footer_text,
        email_signature,
        is_active,
        created_at,
        updated_at,
        organizations!inner(
          id,
          name
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching branding configurations:', error);
      return NextResponse.json({ 
        error: 'Failed to fetch branding configurations', 
        details: error.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      brandings: brandings || []
    });

  } catch (error) {
    console.error('Error in branding GET API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 500 });
    }

    const body = await request.json();
    const {
      organization_id,
      subdomain,
      custom_domain,
      brand_name,
      logo_url,
      favicon_url,
      primary_color,
      secondary_color,
      accent_color,
      background_color,
      text_color,
      font_family,
      font_url,
      header_style,
      footer_style,
      sidebar_style,
      email_header_color,
      email_footer_text,
      email_signature,
      is_active
    } = body;

    // Validate required fields
    if (!organization_id || !subdomain || !brand_name) {
      return NextResponse.json({
        error: 'Missing required fields: organization_id, subdomain, and brand_name are required'
      }, { status: 400 });
    }

    const supabase = createClient();

    // Check if subdomain is already taken
    const { data: existingSubdomain } = await supabase
      .from('organization_branding')
      .select('id')
      .eq('subdomain', subdomain)
      .single();

    if (existingSubdomain) {
      return NextResponse.json({
        error: 'Subdomain is already taken'
      }, { status: 409 });
    }

    // Check if custom domain is already taken (if provided)
    if (custom_domain) {
      const { data: existingDomain } = await supabase
        .from('organization_branding')
        .select('id')
        .eq('custom_domain', custom_domain)
        .single();

      if (existingDomain) {
        return NextResponse.json({
          error: 'Custom domain is already taken'
        }, { status: 409 });
      }
    }

    // Create branding configuration
    const { data: branding, error: createError } = await supabase
      .from('organization_branding')
      .insert({
        organization_id,
        subdomain,
        custom_domain: custom_domain || null,
        brand_name,
        logo_url: logo_url || null,
        favicon_url: favicon_url || null,
        primary_color: primary_color || '#3B82F6',
        secondary_color: secondary_color || '#1E40AF',
        accent_color: accent_color || '#F59E0B',
        background_color: background_color || '#FFFFFF',
        text_color: text_color || '#1F2937',
        font_family: font_family || 'Inter',
        font_url: font_url || null,
        header_style: header_style || {},
        footer_style: footer_style || {},
        sidebar_style: sidebar_style || {},
        email_header_color: email_header_color || '#3B82F6',
        email_footer_text: email_footer_text || null,
        email_signature: email_signature || null,
        is_active: is_active !== undefined ? is_active : true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating branding configuration:', createError);
      return NextResponse.json({
        error: 'Failed to create branding configuration',
        details: createError.message
      }, { status: 500 });
    }

    console.log('Successfully created branding configuration:', branding.id);

    return NextResponse.json({
      success: true,
      branding,
      message: 'Branding configuration created successfully'
    });

  } catch (error) {
    console.error('Error in branding POST API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}