import { getSession } from '@/app/lib/auth';

export const runtime = 'nodejs'
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

/**
 * GET /api/super-admin/organizations/[id]/limits
 * Get comprehensive limits and usage overview for an organization
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const orgId = params.id;
    const url = new URL(request.url);
    const feature = url.searchParams.get('feature');
    const metric = url.searchParams.get('metric');

    // Use service role client for comprehensive data access
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Fetch organization to verify it exists
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, settings')
      .eq('id', orgId)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Fetch organization limits
    const { data: limits, error: limitsError } = await supabase
      .from('organization_limits')
      .select('*')
      .eq('organization_id', orgId);

    if (limitsError) {
      console.error('Error fetching limits:', limitsError);
      return NextResponse.json(
        { error: 'Failed to fetch limits' },
        { status: 500 }
      );
    }

    // Fetch current usage
    const { data: usage, error: usageError } = await supabase
      .from('organization_usage')
      .select('*')
      .eq('organization_id', orgId)
      .order('date', { ascending: false })
      .limit(1)
      .single();

    if (usageError && usageError.code !== 'PGRST116') {
      console.error('Error fetching usage:', usageError);
    }

    // Calculate usage percentages and status
    const limitsOverview = {
      organization_id: orgId,
      limits: limits || [],
      current_usage: usage || {},
      status: {
        users: {
          current: usage?.users_count || 0,
          limit: limits?.find(l => l.limit_type === 'users')?.limit_value || 100,
          percentage: Math.round(((usage?.users_count || 0) / (limits?.find(l => l.limit_type === 'users')?.limit_value || 100)) * 100)
        },
        quotes: {
          current: usage?.quotes_count || 0,
          limit: limits?.find(l => l.limit_type === 'quotes')?.limit_value || 1000,
          percentage: Math.round(((usage?.quotes_count || 0) / (limits?.find(l => l.limit_type === 'quotes')?.limit_value || 1000)) * 100)
        },
        storage: {
          current: usage?.storage_mb || 0,
          limit: limits?.find(l => l.limit_type === 'storage')?.limit_value || 5000,
          percentage: Math.round(((usage?.storage_mb || 0) / (limits?.find(l => l.limit_type === 'storage')?.limit_value || 5000)) * 100)
        }
      }
    };

    return NextResponse.json(limitsOverview);

  } catch (error) {
    console.error('Error in limits GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/super-admin/organizations/[id]/limits
 * Update limits for an organization
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const orgId = params.id;
    const body = await request.json();
    const { limit_type, limit_value, enabled } = body;

    if (!limit_type || limit_value === undefined) {
      return NextResponse.json(
        { error: 'Limit type and value are required' },
        { status: 400 }
      );
    }

    // Upsert limit setting
    const { data: limit, error: upsertError } = await supabase
      .from('organization_limits')
      .upsert({
        organization_id: orgId,
        limit_type,
        limit_value,
        enabled: enabled !== false,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (upsertError) {
      console.error('Error updating limit:', upsertError);
      return NextResponse.json(
        { error: 'Failed to update limit' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      limit
    });

  } catch (error) {
    console.error('Error in limits PUT:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
