import { getSession } from '@/app/lib/auth';

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/super-admin/organizations/[id]/features
 * Get all feature gates and permissions for an organization
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const orgId = params.id;
    const url = new URL(request.url);
    const featureKey = url.searchParams.get('feature');
    const userRole = url.searchParams.get('role');

    // Use service role client for comprehensive data access
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Fetch organization to verify it exists
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, settings')
      .eq('id', orgId)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Fetch organization feature settings
    const { data: features, error: featuresError } = await supabase
      .from('organization_features')
      .select('*')
      .eq('organization_id', orgId);

    if (featuresError) {
      console.error('Error fetching features:', featuresError);
      return NextResponse.json(
        { error: 'Failed to fetch features' },
        { status: 500 }
      );
    }

    // If checking a specific feature
    if (featureKey) {
      const feature = features?.find(f => f.feature_key === featureKey);
      return NextResponse.json({
        feature_key: featureKey,
        enabled: feature?.enabled || false,
        settings: feature?.settings || {},
        limits: feature?.limits || {}
      });
    }

    // Return all features
    const allFeatures = {
      organization_id: orgId,
      features: features || [],
      available_features: [
        'quotes_management',
        'user_management',
        'analytics',
        'branding',
        'api_access',
        'webhooks',
        'custom_domains',
        'white_labeling',
        'advanced_reporting',
        'bulk_operations'
      ]
    };

    return NextResponse.json(allFeatures);

  } catch (error) {
    console.error('Error in features GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/super-admin/organizations/[id]/features
 * Update feature settings for an organization
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const orgId = params.id;
    const body = await request.json();
    const { feature_key, enabled, settings, limits } = body;

    if (!feature_key) {
      return NextResponse.json(
        { error: 'Feature key is required' },
        { status: 400 }
      );
    }

    // Upsert feature setting
    const { data: feature, error: upsertError } = await supabase
      .from('organization_features')
      .upsert({
        organization_id: orgId,
        feature_key,
        enabled: enabled || false,
        settings: settings || {},
        limits: limits || {},
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (upsertError) {
      console.error('Error updating feature:', upsertError);
      return NextResponse.json(
        { error: 'Failed to update feature' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      feature
    });

  } catch (error) {
    console.error('Error in features PUT:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
