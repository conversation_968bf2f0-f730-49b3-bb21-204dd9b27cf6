import { getSession } from '@/app/lib/auth';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

// Utility function to generate secure password
function generateSecurePassword(): string {
  const length = 12;
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
  let password = "";
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}

/**
 * GET /api/super-admin/organizations/[id]/users
 * Get all users for a specific organization
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const orgId = params.id;

    // Verify organization exists
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name')
      .eq('id', orgId)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Get organization users with profile information
    const { data: users, error: usersError } = await supabase
      .from('user_companies')
      .select(`
        *,
        user:profiles(*)
      `)
      .eq('company_id', orgId)
      .order('created_at', { ascending: false });

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }

    // Get pending invitations
    const { data: invitations, error: invitationsError } = await supabase
      .from('user_invitations')
      .select('*')
      .eq('organization_id', orgId)
      .eq('status', 'PENDING')
      .order('created_at', { ascending: false });

    if (invitationsError) {
      console.error('Error fetching invitations:', invitationsError);
    }

    const response = {
      organization_id: orgId,
      organization_name: organization.name,
      users: users || [],
      pending_invitations: invitations || [],
      stats: {
        total_users: users?.length || 0,
        active_users: users?.filter(u => u.status === 'ACTIVE').length || 0,
        pending_invitations: invitations?.length || 0
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in organization users GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/super-admin/organizations/[id]/users
 * Add a new user to the organization
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const orgId = params.id;
    const body = await request.json();
    const { email, first_name, last_name, role, send_invitation, password } = body;

    if (!email || !first_name || !last_name || !role) {
      return NextResponse.json(
        { error: 'Email, first name, last name, and role are required' },
        { status: 400 }
      );
    }

    // Verify organization exists
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name')
      .eq('id', orgId)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    if (send_invitation) {
      // Create invitation record
      const { data: invitation, error: inviteError } = await supabase
        .from('user_invitations')
        .insert({
          organization_id: orgId,
          email: email.toLowerCase(),
          first_name,
          last_name,
          role,
          status: 'PENDING',
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (inviteError) {
        console.error('Error creating invitation:', inviteError);
        return NextResponse.json(
          { error: 'Failed to create invitation' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        invitation,
        message: 'Invitation sent successfully'
      });
    } else {
      // Create user directly
      const userPassword = password || generateSecurePassword();

      const { data: authUser, error: userError } = await supabase.auth.admin.createUser({
        email: email.toLowerCase(),
        password: userPassword,
        email_confirm: true,
        user_metadata: {
          full_name: `${first_name} ${last_name}`,
          first_name,
          last_name,
          role,
          organization_id: orgId,
          created_by_admin: true
        }
      });

      if (userError) {
        console.error('Error creating user:', userError);
        return NextResponse.json(
          { error: 'Failed to create user' },
          { status: 500 }
        );
      }

      // Create profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authUser.user.id,
          email: email.toLowerCase(),
          role,
          roles: [role],
          status: 'ACTIVE',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        console.error('Error creating profile:', profileError);
      }

      // Create organization association
      const { error: orgAssocError } = await supabase
        .from('user_companies')
        .insert({
          user_id: authUser.user.id,
          company_id: orgId,
          role,
          status: 'ACTIVE',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (orgAssocError) {
        console.error('Error creating organization association:', orgAssocError);
      }

      return NextResponse.json({
        success: true,
        user: {
          id: authUser.user.id,
          email,
          role,
          password: password ? null : userPassword
        },
        message: 'User created successfully'
      });
    }

  } catch (error) {
    console.error('Error in organization users POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
