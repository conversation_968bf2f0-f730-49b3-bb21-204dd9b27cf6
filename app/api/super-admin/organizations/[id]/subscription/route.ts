import { getSession } from '@/app/lib/auth';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/super-admin/organizations/[id]/subscription
 * Get organization's subscription details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const orgId = params.id;

    // Get organization info
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, settings')
      .eq('id', orgId)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Get current subscription
    const { data: subscription, error: subError } = await supabase
      .from('organization_subscriptions')
      .select('*')
      .eq('organization_id', orgId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (subError && subError.code !== 'PGRST116') {
      console.error('Error fetching subscription:', subError);
      return NextResponse.json(
        { error: 'Failed to fetch subscription' },
        { status: 500 }
      );
    }

    // Get subscription history
    const { data: history, error: historyError } = await supabase
      .from('organization_subscriptions')
      .select('*')
      .eq('organization_id', orgId)
      .order('created_at', { ascending: false });

    if (historyError) {
      console.error('Error fetching subscription history:', historyError);
    }

    // Get billing information
    const { data: billing, error: billingError } = await supabase
      .from('organization_billing')
      .select('*')
      .eq('organization_id', orgId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (billingError && billingError.code !== 'PGRST116') {
      console.error('Error fetching billing:', billingError);
    }

    const response = {
      organization_id: orgId,
      current_subscription: subscription || null,
      subscription_history: history || [],
      billing_info: billing || null,
      status: subscription?.status || 'inactive',
      plan: subscription?.plan_name || 'free',
      next_billing_date: subscription?.next_billing_date || null,
      features: subscription?.features || []
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in subscription GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/super-admin/organizations/[id]/subscription
 * Update organization's subscription
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const orgId = params.id;
    const body = await request.json();
    const { plan_name, status, features, billing_cycle } = body;

    if (!plan_name) {
      return NextResponse.json(
        { error: 'Plan name is required' },
        { status: 400 }
      );
    }

    // Create new subscription record
    const { data: subscription, error: subError } = await supabase
      .from('organization_subscriptions')
      .insert({
        organization_id: orgId,
        plan_name,
        status: status || 'active',
        features: features || [],
        billing_cycle: billing_cycle || 'monthly',
        start_date: new Date().toISOString(),
        next_billing_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (subError) {
      console.error('Error creating subscription:', subError);
      return NextResponse.json(
        { error: 'Failed to update subscription' },
        { status: 500 }
      );
    }

    // Update organization settings
    const { error: orgUpdateError } = await supabase
      .from('organizations')
      .update({
        settings: {
          ...organization?.settings,
          plan: plan_name,
          subscription_status: status || 'active'
        },
        updated_at: new Date().toISOString()
      })
      .eq('id', orgId);

    if (orgUpdateError) {
      console.error('Error updating organization settings:', orgUpdateError);
    }

    return NextResponse.json({
      success: true,
      subscription
    });

  } catch (error) {
    console.error('Error in subscription PUT:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
