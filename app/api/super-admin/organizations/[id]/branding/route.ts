import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient()
    const organizationId = params.id

    // Validate organization exists
    const { data: organization, error: orgError } = await supabase
      .from('companies')
      .select('id, name')
      .eq('id', organizationId)
      .single()

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      )
    }

    // Get branding data
    const { data: branding, error: brandingError } = await supabase
      .from('organization_branding')
      .select('*')
      .eq('organization_id', organizationId)
      .single()

    if (brandingError && brandingError.code !== 'PGRST116') {
      console.error('Error fetching branding data:', brandingError)
      return NextResponse.json(
        { error: 'Failed to fetch branding data' },
        { status: 500 }
      )
    }

    // Return default branding if none exists
    const brandingData = branding || {
      logo_url: '',
      primary_color: '#3b82f6',
      secondary_color: '#64748b',
      background_color: '#ffffff',
      custom_css: ''
    }

    return NextResponse.json({
      success: true,
      data: brandingData
    })

  } catch (error) {
    console.error('Error in branding GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient()
    const organizationId = params.id
    const body = await request.json()
    const { logo_url, primary_color, secondary_color, background_color, custom_css } = body

    // Validate organization exists
    const { data: organization, error: orgError } = await supabase
      .from('companies')
      .select('id, name')
      .eq('id', organizationId)
      .single()

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      )
    }

    // Validate color formats
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    if (primary_color && !colorRegex.test(primary_color)) {
      return NextResponse.json(
        { error: 'Invalid primary color format' },
        { status: 400 }
      )
    }
    if (secondary_color && !colorRegex.test(secondary_color)) {
      return NextResponse.json(
        { error: 'Invalid secondary color format' },
        { status: 400 }
      )
    }
    if (background_color && !colorRegex.test(background_color)) {
      return NextResponse.json(
        { error: 'Invalid background color format' },
        { status: 400 }
      )
    }

    // Validate logo URL if provided
    if (logo_url && logo_url.trim()) {
      try {
        new URL(logo_url)
      } catch {
        return NextResponse.json(
          { error: 'Invalid logo URL format' },
          { status: 400 }
        )
      }
    }

    // Check if branding record exists
    const { data: existingBranding } = await supabase
      .from('organization_branding')
      .select('id')
      .eq('organization_id', organizationId)
      .single()

    const brandingData = {
      organization_id: organizationId,
      logo_url: logo_url || null,
      primary_color: primary_color || '#3b82f6',
      secondary_color: secondary_color || '#64748b',
      background_color: background_color || '#ffffff',
      custom_css: custom_css || null,
      updated_at: new Date().toISOString()
    }

    if (existingBranding) {
      // Update existing branding
      const { error: updateError } = await supabase
        .from('organization_branding')
        .update(brandingData)
        .eq('organization_id', organizationId)

      if (updateError) {
        console.error('Error updating branding:', updateError)
        return NextResponse.json(
          { error: 'Failed to update branding' },
          { status: 500 }
        )
      }
    } else {
      // Create new branding record
      brandingData.created_at = new Date().toISOString()
      
      const { error: insertError } = await supabase
        .from('organization_branding')
        .insert(brandingData)

      if (insertError) {
        console.error('Error creating branding:', insertError)
        return NextResponse.json(
          { error: 'Failed to create branding' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Branding updated successfully',
      data: brandingData
    })

  } catch (error) {
    console.error('Error in branding PUT:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient()
    const organizationId = params.id

    // Validate organization exists
    const { data: organization, error: orgError } = await supabase
      .from('companies')
      .select('id, name')
      .eq('id', organizationId)
      .single()

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      )
    }

    // Delete branding data
    const { error: deleteError } = await supabase
      .from('organization_branding')
      .delete()
      .eq('organization_id', organizationId)

    if (deleteError) {
      console.error('Error deleting branding:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete branding' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Branding reset to defaults'
    })

  } catch (error) {
    console.error('Error in branding DELETE:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
