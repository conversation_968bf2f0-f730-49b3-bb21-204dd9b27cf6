import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import { } from '@/lib/auth/server';

// Permission templates
const PERMISSION_TEMPLATES = {
  basic_client: [
    { category: 'quotes', key: 'enabled', enabled: true },
    { category: 'quotes', key: 'send_to_affiliates', enabled: true },
    { category: 'events', key: 'enabled', enabled: true },
    { category: 'passengers', key: 'enabled', enabled: true },
    { category: 'affiliates', key: 'access', enabled: true },
    { category: 'analytics', key: 'basic', enabled: true },
    // Disabled features for basic client
    { category: 'quotes', key: 'export', enabled: false },
    { category: 'quotes', key: 'fixed_rate', enabled: false },
    { category: 'financial', key: 'view_costs', enabled: false },
    { category: 'analytics', key: 'advanced', enabled: false },
    { category: 'system', key: 'white_label', enabled: false },
  ],
  premium_client: [
    // All basic client permissions
    { category: 'quotes', key: 'enabled', enabled: true },
    { category: 'quotes', key: 'send_to_affiliates', enabled: true },
    { category: 'quotes', key: 'export', enabled: true },
    { category: 'quotes', key: 'fixed_rate', enabled: true },
    { category: 'quotes', key: 'assign', enabled: true },
    { category: 'events', key: 'enabled', enabled: true },
    { category: 'passengers', key: 'enabled', enabled: true },
    { category: 'passengers', key: 'export', enabled: true },
    { category: 'affiliates', key: 'access', enabled: true },
    { category: 'affiliates', key: 'select', enabled: true },
    { category: 'affiliates', key: 'view_rates', enabled: true },
    { category: 'analytics', key: 'basic', enabled: true },
    { category: 'analytics', key: 'advanced', enabled: true },
    { category: 'financial', key: 'view_costs', enabled: true },
    // Still disabled for premium
    { category: 'financial', key: 'view_profit', enabled: false },
    { category: 'affiliates', key: 'network_management', enabled: false },
    { category: 'system', key: 'white_label', enabled: false },
  ],
  tnc_enterprise: [
    // All premium client permissions plus enterprise features
    { category: 'quotes', key: 'enabled', enabled: true },
    { category: 'quotes', key: 'send_to_affiliates', enabled: true },
    { category: 'quotes', key: 'export', enabled: true },
    { category: 'quotes', key: 'fixed_rate', enabled: true },
    { category: 'quotes', key: 'assign', enabled: true },
    { category: 'quotes', key: 'reorder_affiliates', enabled: true },
    { category: 'events', key: 'enabled', enabled: true },
    { category: 'passengers', key: 'enabled', enabled: true },
    { category: 'passengers', key: 'export', enabled: true },
    { category: 'affiliates', key: 'access', enabled: true },
    { category: 'affiliates', key: 'select', enabled: true },
    { category: 'affiliates', key: 'negotiate', enabled: true },
    { category: 'affiliates', key: 'view_rates', enabled: true },
    { category: 'affiliates', key: 'network_management', enabled: true },
    { category: 'affiliates', key: 'set_network_rates', enabled: true },
    { category: 'affiliates', key: 'approve', enabled: true },
    { category: 'analytics', key: 'basic', enabled: true },
    { category: 'analytics', key: 'advanced', enabled: true },
    { category: 'analytics', key: 'financial', enabled: true },
    { category: 'financial', key: 'view_costs', enabled: true },
    { category: 'financial', key: 'view_rates', enabled: true },
    { category: 'financial', key: 'view_profit', enabled: true },
    { category: 'financial', key: 'manage_billing', enabled: true },
    { category: 'system', key: 'white_label', enabled: true },
  ],
};

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const organizationId = params.id;
    const { template_name } = await request.json();

    if (!template_name || !PERMISSION_TEMPLATES[template_name as keyof typeof PERMISSION_TEMPLATES]) {
      return NextResponse.json(
        { error: 'Invalid template name' },
        { status: 400 }
      );
    }

    const templatePermissions = PERMISSION_TEMPLATES[template_name as keyof typeof PERMISSION_TEMPLATES];

    // Delete existing permissions for this organization
    await supabase
      .from('organization_permissions')
      .delete()
      .eq('organization_id', organizationId);

    // Insert new permissions from template
    const permissionsToInsert = templatePermissions.map(perm => ({
      organization_id: organizationId,
      permission_category: perm.category,
      permission_key: perm.key,
      enabled: perm.enabled,
      configuration: {}
    }));

    const { error: insertError } = await supabase
      .from('organization_permissions')
      .insert(permissionsToInsert);

    if (insertError) {
      console.error('Error inserting template permissions:', insertError);
      return NextResponse.json(
        { error: 'Failed to apply template' },
        { status: 500 }
      );
    }

    return NextResponse.json({ 
      success: true,
      message: `${template_name} template applied successfully`,
      permissions_count: permissionsToInsert.length
    });

  } catch (error) {
    console.error('Error applying permission template:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}