import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

/**
 * GET /api/super-admin/organizations/[id]/permissions
 * Get organization-level permissions
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require admin roles
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const orgId = params.id;

    // Get organization permissions
    const { data: permissions, error: permissionsError } = await supabase
      .from('organization_permissions')
      .select('*')
      .eq('organization_id', orgId)
      .order('permission_category', { ascending: true })
      .order('permission_key', { ascending: true });

    if (permissionsError) {
      console.error('Error fetching organization permissions:', permissionsError);
      return NextResponse.json({ error: 'Failed to fetch permissions' }, { status: 500 });
    }

    // Get available permission templates
    const { data: templates, error: templatesError } = await supabase
      .from('organization_permission_templates')
      .select('*')
      .order('template_name');

    if (templatesError) {
      console.error('Error fetching permission templates:', templatesError);
    }

    // Group permissions by category
    const permissionsByCategory = permissions?.reduce((acc: any, permission: any) => {
      if (!acc[permission.permission_category]) {
        acc[permission.permission_category] = [];
      }
      acc[permission.permission_category].push(permission);
      return acc;
    }, {}) || {};

    return NextResponse.json({
      success: true,
      permissions: permissions || [],
      permissions_by_category: permissionsByCategory,
      available_templates: templates || []
    });

  } catch (error: any) {
    console.error('Organization permissions GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * POST /api/super-admin/organizations/[id]/permissions
 * Set organization permission
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require admin roles
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const orgId = params.id;
    const body = await request.json();
    
    const { permission_category, permission_key, enabled, configuration } = body;

    if (!permission_category || !permission_key) {
      return NextResponse.json(
        { error: 'permission_category and permission_key are required' },
        { status: 400 }
      );
    }

    // Upsert permission
    const { data: permission, error } = await supabase
      .from('organization_permissions')
      .upsert({
        organization_id: orgId,
        permission_category,
        permission_key,
        enabled: enabled ?? false,
        configuration: configuration || {},
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'organization_id,permission_category,permission_key'
      })
      .select()
      .single();

    if (error) {
      console.error('Error setting organization permission:', error);
      return NextResponse.json({ error: 'Failed to set permission' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      permission
    });

  } catch (error: any) {
    console.error('Organization permissions POST API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * PUT /api/super-admin/organizations/[id]/permissions
 * Update multiple organization permissions or apply template
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require admin roles
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const orgId = params.id;
    const body = await request.json();
    
    const { template_name } = body;

    if (!template_name) {
      return NextResponse.json(
        { error: 'template_name is required' },
        { status: 400 }
      );
    }

    // Apply template using database function
    const { data, error } = await supabase.rpc('apply_permission_template_to_organization', {
      org_id: orgId,
      template_name
    });

    if (error) {
      console.error('Error applying permission template:', error);
      return NextResponse.json({ error: 'Failed to apply template' }, { status: 500 });
    }

    // Get updated permissions
    const { data: permissions, error: permissionsError } = await supabase
      .from('organization_permissions')
      .select('*')
      .eq('organization_id', orgId)
      .order('permission_category', { ascending: true });

    if (permissionsError) {
      console.error('Error fetching updated permissions:', permissionsError);
    }

    return NextResponse.json({
      success: true,
      message: `Applied ${template_name} template successfully`,
      permissions: permissions || []
    });

  } catch (error: any) {
    console.error('Organization permissions PUT API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}