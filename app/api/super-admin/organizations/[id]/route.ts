import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

/**
 * GET /api/super-admin/organizations/[id]
 * Get detailed organization information including users, subscription, and analytics
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Use standardized auth pattern
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN']);
    
    // Use service role for comprehensive data access
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const orgId = params.id;

    // Fetch organization information
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', orgId)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Fetch organization users
    const { data: users, error: usersError } = await supabase
      .from('user_organizations')
      .select(`
        *,
        profiles:user_id (
          id,
          email,
          first_name,
          last_name,
          roles
        )
      `)
      .eq('organization_id', orgId);

    if (usersError) {
      console.error('Error fetching users:', usersError);
    }

    // Fetch subscription information
    const { data: subscription, error: subError } = await supabase
      .from('organization_subscriptions')
      .select('*')
      .eq('organization_id', orgId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (subError && subError.code !== 'PGRST116') {
      console.error('Error fetching subscription:', subError);
    }

    // Fetch analytics data
    const { data: analytics, error: analyticsError } = await supabase
      .from('organization_analytics')
      .select('*')
      .eq('organization_id', orgId)
      .order('date', { ascending: false })
      .limit(30);

    if (analyticsError) {
      console.error('Error fetching analytics:', analyticsError);
    }

    // Fetch recent activity
    const { data: activity, error: activityError } = await supabase
      .from('audit_logs')
      .select('*')
      .eq('organization_id', orgId)
      .order('created_at', { ascending: false })
      .limit(20);

    if (activityError) {
      console.error('Error fetching activity:', activityError);
    }

    // Fetch branding information
    const { data: branding, error: brandingError } = await supabase
      .from('organization_branding')
      .select('*')
      .eq('organization_id', orgId)
      .single();

    if (brandingError && brandingError.code !== 'PGRST116') {
      console.error('Error fetching branding:', brandingError);
    }

    // Compile response
    const response = {
      organization,
      users: users || [],
      subscription: subscription || null,
      analytics: analytics || [],
      activity: activity || [],
      branding: branding || null,
      stats: {
        total_users: users?.length || 0,
        active_users: users?.filter(u => u.status === 'ACTIVE').length || 0,
        total_quotes: 0, // TODO: Add quote count
        total_revenue: 0, // TODO: Add revenue calculation
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in organization details GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/super-admin/organizations/[id]
 * Update organization information
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const orgId = params.id;
    const body = await request.json();

    // Update organization
    const { data: organization, error: updateError } = await supabase
      .from('organizations')
      .update({
        name: body.name,
        email: body.email,
        phone: body.phone,
        address: body.address,
        industry: body.industry,
        settings: body.settings,
        updated_at: new Date().toISOString()
      })
      .eq('id', orgId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating organization:', updateError);
      return NextResponse.json(
        { error: 'Failed to update organization' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      organization
    });

  } catch (error) {
    console.error('Error in organization PUT:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/super-admin/organizations/[id]
 * Delete organization (soft delete)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const orgId = params.id;

    // Soft delete organization
    const { error: deleteError } = await supabase
      .from('organizations')
      .update({
        status: 'DELETED',
        updated_at: new Date().toISOString()
      })
      .eq('id', orgId);

    if (deleteError) {
      console.error('Error deleting organization:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete organization' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Organization deleted successfully'
    });

  } catch (error) {
    console.error('Error in organization DELETE:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
