import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    console.log("Super-admin organizations API - Starting request");
    
    // Check environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error', details: 'Missing Supabase credentials' },
        { status: 500 }
      );
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    console.log('Fetching organizations directly...');

    // Fetch all organizations for super admin with correct column names
    const { data: organizations, error: orgsError } = await supabase
      .from('organizations')
      .select(`
        id,
        name,
        slug,
        organization_type,
        account_type,
        parent_tnc_id,
        managed_by,
        status,
        business_type,
        industry,
        company_size,
        primary_contact_name,
        primary_contact_email,
        branding,
        settings,
        feature_flags,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false });

    if (orgsError) {
      console.error('Error fetching organizations:', orgsError);
      return NextResponse.json(
        { error: 'Failed to fetch organizations', details: orgsError.message },
        { status: 500 }
      );
    }

    console.log(`Found ${organizations?.length || 0} organizations`);
    
    return NextResponse.json({
      success: true,
      organizations: organizations || [],
      total: organizations?.length || 0
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/organizations:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}