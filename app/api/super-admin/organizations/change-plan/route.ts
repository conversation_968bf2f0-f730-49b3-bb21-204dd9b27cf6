import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    const body = await request.json()
    const { organization_id, new_plan } = body

    // Validate required fields
    if (!organization_id || !new_plan) {
      return NextResponse.json(
        { error: 'Missing required fields: organization_id, new_plan' },
        { status: 400 }
      )
    }

    // Validate plan
    const validPlans = ['Essential', 'Professional', 'Business', 'Enterprise']
    if (!validPlans.includes(new_plan)) {
      return NextResponse.json(
        { error: 'Invalid plan name' },
        { status: 400 }
      )
    }

    // Check if organization exists
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, plan')
      .eq('id', organization_id)
      .single()

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      )
    }

    // Update organization plan
    const { error: updateError } = await supabase
      .from('companies')
      .update({
        plan: new_plan,
        updated_at: new Date().toISOString()
      })
      .eq('id', organization_id)

    if (updateError) {
      console.error('Error updating organization plan:', updateError)
      return NextResponse.json(
        { error: 'Failed to update organization plan' },
        { status: 500 }
      )
    }

    // Create subscription history record
    const { error: historyError } = await supabase
      .from('subscription_history')
      .insert({
        organization_id: organization_id,
        old_plan: organization.plan,
        new_plan: new_plan,
        changed_at: new Date().toISOString(),
        changed_by: 'SUPER_ADMIN' // TODO: Get actual admin user ID
      })

    if (historyError) {
      console.error('Error creating subscription history:', historyError)
      // Don't fail the request for history logging issues
    }

    // TODO: Handle billing changes, prorations, etc.
    // TODO: Send notification emails
    // TODO: Update feature access based on new plan

    return NextResponse.json({
      success: true,
      message: `Successfully changed plan to ${new_plan}`,
      organization_id: organization_id,
      old_plan: organization.plan,
      new_plan: new_plan
    })

  } catch (error) {
    console.error('Error changing subscription plan:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
