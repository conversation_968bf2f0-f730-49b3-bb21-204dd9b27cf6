import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from "next/server";
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export async function GET(request: NextRequest) {
  try {
    console.log("Super-admin subscriptions analytics GET API called");

    // Use standardized auth pattern
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);

    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Failed to get user' }, { status: 401 });
    }

    // Fetch subscription analytics data
    const [
      organizationsResult,
      userOrganizationsResult,
      profilesResult
    ] = await Promise.all([
      supabase.from("organizations").select("*"),
      supabase.from("user_organizations").select("*"),
      supabase.from("profiles").select("*")
    ]);

    if (organizationsResult.error) {
      console.error("Error fetching organizations:", organizationsResult.error);
      return NextResponse.json({ error: "Failed to fetch organizations" }, { status: 500 });
    }

    if (userOrganizationsResult.error) {
      console.error("Error fetching user organizations:", userOrganizationsResult.error);
      return NextResponse.json({ error: "Failed to fetch user organizations" }, { status: 500 });
    }

    if (profilesResult.error) {
      console.error("Error fetching profiles:", profilesResult.error);
      return NextResponse.json({ error: "Failed to fetch profiles" }, { status: 500 });
    }

    const organizations = organizationsResult.data || [];
    const userOrganizations = userOrganizationsResult.data || [];
    const profiles = profilesResult.data || [];

    // Calculate analytics
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const analytics = {
      totalOrganizations: organizations.length,
      activeOrganizations: organizations.filter(org => org.status === 'active').length,
      totalUsers: profiles.length,
      activeUsers: profiles.filter(profile => profile.status === 'active').length,
      userOrganizationLinks: userOrganizations.length,
      activeLinks: userOrganizations.filter(uo => uo.status === 'active').length,
      superAdmins: profiles.filter(profile => profile.roles?.includes('SUPER_ADMIN')).length,
      recentOrganizations: organizations.filter(org => 
        new Date(org.created_at) > thirtyDaysAgo
      ).length,
      organizationTypes: {
        shared: organizations.filter(org => org.organization_type === 'shared').length,
        segregated: organizations.filter(org => org.organization_type === 'segregated').length,
        white_label: organizations.filter(org => org.organization_type === 'white_label').length
      },
      userRoleDistribution: {
        SUPER_ADMIN: profiles.filter(p => p.roles?.includes('SUPER_ADMIN')).length,
        ADMIN: profiles.filter(p => p.roles?.includes('ADMIN')).length,
        EVENT_MANAGER: profiles.filter(p => p.roles?.includes('EVENT_MANAGER')).length,
        AFFILIATE_ADMIN: profiles.filter(p => p.roles?.includes('AFFILIATE_ADMIN')).length,
        AFFILIATE_USER: profiles.filter(p => p.roles?.includes('AFFILIATE_USER')).length,
        CUSTOMER: profiles.filter(p => p.roles?.includes('CUSTOMER')).length,
        DRIVER: profiles.filter(p => p.roles?.includes('DRIVER')).length
      }
    };

    console.log("Successfully calculated subscription analytics");

    return NextResponse.json({
      success: true,
      analytics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Error in super-admin subscriptions analytics API:", error);
    return NextResponse.json({ 
      error: "Internal Server Error", 
      details: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}