import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from "next/server";
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export async function POST(request: NextRequest) {
  try {
    console.log("Super-admin user-organizations bulk POST API called");

    // Use standardized auth pattern
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);

    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Failed to get user' }, { status: 401 });
    }

    const body = await request.json();
    const { action, userOrganizationIds } = body;

    if (!action || !userOrganizationIds || !Array.isArray(userOrganizationIds)) {
      return NextResponse.json({ 
        error: "Missing required fields: action, userOrganizationIds (array)" 
      }, { status: 400 });
    }

    if (userOrganizationIds.length === 0) {
      return NextResponse.json({ 
        error: "userOrganizationIds array cannot be empty" 
      }, { status: 400 });
    }

    console.log(`Performing bulk ${action} on ${userOrganizationIds.length} user-organization relationships`);

    let updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Define bulk operations
    switch (action) {
      case 'activate':
        updateData.status = 'active';
        break;
      case 'suspend':
        updateData.status = 'suspended';
        break;
      case 'deactivate':
        updateData.status = 'inactive';
        break;
      case 'remove':
        // For remove, we'll delete the relationships
        const { error: deleteError } = await supabase
          .from("user_organizations")
          .delete()
          .in("id", userOrganizationIds);

        if (deleteError) {
          console.error("Error in bulk delete:", deleteError);
          return NextResponse.json({ 
            error: "Failed to remove user-organization relationships", 
            details: deleteError.message 
          }, { status: 500 });
        }

        console.log(`Successfully removed ${userOrganizationIds.length} user-organization relationships`);

        return NextResponse.json({
          success: true,
          affected: userOrganizationIds.length,
          message: `Successfully removed ${userOrganizationIds.length} user-organization relationships`
        });

      default:
        return NextResponse.json({ 
          error: `Invalid action: ${action}. Valid actions: activate, suspend, deactivate, remove` 
        }, { status: 400 });
    }

    // For non-remove actions, update the relationships
    const { data: updatedRelationships, error: updateError } = await supabase
      .from("user_organizations")
      .update(updateData)
      .in("id", userOrganizationIds)
      .select("id");

    if (updateError) {
      console.error(`Error in bulk ${action}:`, updateError);
      return NextResponse.json({ 
        error: `Failed to ${action} user-organization relationships`, 
        details: updateError.message 
      }, { status: 500 });
    }

    const affectedCount = updatedRelationships?.length || 0;
    console.log(`Successfully ${action}d ${affectedCount} user-organization relationships`);

    return NextResponse.json({
      success: true,
      affected: affectedCount,
      message: `Successfully ${action}d ${affectedCount} user-organization relationships`
    });

  } catch (error) {
    console.error("Error in user-organizations bulk API:", error);
    return NextResponse.json({ 
      error: "Internal Server Error", 
      details: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}