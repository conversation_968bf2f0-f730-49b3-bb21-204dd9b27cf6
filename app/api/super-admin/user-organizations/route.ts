import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export const runtime = 'nodejs'

export async function GET(request: NextRequest) {
  try {
    console.log("Super-admin user-organizations GET API called");

    // Use the same authentication pattern as admin/reports route
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is super admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];
    if (!userRoles.includes('SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden - Super Admin access required' }, { status: 403 });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get("organization_id");
    const status = url.searchParams.get("status");
    const role = url.searchParams.get("role");
    const limit = parseInt(url.searchParams.get("limit") || "100");
    const offset = parseInt(url.searchParams.get("offset") || "0");

    console.log(`Fetching user-organizations with params: organizationId=${organizationId}, status=${status}, role=${role}`);

    // Fetch user-organization relationships with populated user and organization data
    let query = supabase
      .from("user_organizations")
      .select(`
        *,
        user:profiles!user_organizations_user_id_fkey(
          id,
          email,
          full_name,
          phone,
          created_at
        ),
        organization:organizations!user_organizations_organization_id_fkey(
          id,
          name,
          slug,
          organization_type
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (organizationId) {
      query = query.eq("organization_id", organizationId);
    }
    if (status) {
      query = query.eq("status", status);
    }
    if (role) {
      query = query.eq("role", role);
    }

    const { data: userOrganizations, error, count } = await query;

    if (error) {
      console.error("Error fetching user-organizations:", error);
      return NextResponse.json({ error: "Failed to fetch user-organizations", details: error.message }, { status: 500 });
    }

    console.log(`Successfully fetched ${userOrganizations?.length || 0} user-organization relationships`);

    return NextResponse.json({
      success: true,
      userOrganizations: userOrganizations || [],
      total: count || userOrganizations?.length || 0,
      pagination: {
        limit,
        offset,
        total: count || userOrganizations?.length || 0
      }
    });

  } catch (error) {
    console.error("Error in super-admin user-organizations API:", error);
    return NextResponse.json({
      error: "Internal Server Error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("Super-admin user-organizations POST API called");

    // Use the same authentication pattern as admin/reports route
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is super admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];
    if (!userRoles.includes('SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { user_id, organization_id, role, status = 'active', permissions = {} } = body;

    if (!user_id || !organization_id || !role) {
      return NextResponse.json({
        error: "Missing required fields: user_id, organization_id, role"
      }, { status: 400 });
    }

    console.log(`Creating user-organization relationship: user=${user_id}, org=${organization_id}, role=${role}`);

    // Check if relationship already exists
    const { data: existing } = await supabase
      .from("user_organizations")
      .select("id")
      .eq("user_id", user_id)
      .eq("organization_id", organization_id)
      .single();

    if (existing) {
      return NextResponse.json({
        error: "User is already assigned to this organization"
      }, { status: 409 });
    }

    // Create the user-organization relationship
    const { data: newRelationship, error: createError } = await supabase
      .from("user_organizations")
      .insert({
        user_id,
        organization_id,
        role,
        status,
        permissions,
        joined_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select(`
        *,
        user:profiles!user_organizations_user_id_fkey(id, email, full_name),
        organization:organizations!user_organizations_organization_id_fkey(id, name, slug)
      `)
      .single();

    if (createError) {
      console.error("Error creating user-organization relationship:", createError);
      return NextResponse.json({
        error: "Failed to create user-organization relationship",
        details: createError.message
      }, { status: 500 });
    }

    console.log("Successfully created user-organization relationship");

    return NextResponse.json({
      success: true,
      userOrganization: newRelationship,
      message: "User successfully added to organization"
    });

  } catch (error) {
    console.error("Error in user-organizations POST API:", error);
    return NextResponse.json({
      error: "Internal Server Error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}