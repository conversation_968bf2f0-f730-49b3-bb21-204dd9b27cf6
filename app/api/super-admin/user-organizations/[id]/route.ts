import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from "next/server";
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("Super-admin user-organizations PATCH API called for ID:", params.id);

    // Use standardized auth pattern
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);

    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Failed to get user' }, { status: 401 });
    }

    const body = await request.json();
    const { role, status, permissions } = body;

    if (!role && !status && !permissions) {
      return NextResponse.json({ 
        error: "At least one field (role, status, permissions) must be provided" 
      }, { status: 400 });
    }

    console.log(`Updating user-organization relationship ${params.id}:`, { role, status, permissions });

    // Update the user-organization relationship
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (role) updateData.role = role;
    if (status) updateData.status = status;
    if (permissions) updateData.permissions = permissions;

    const { data: updatedRelationship, error: updateError } = await supabase
      .from("user_organizations")
      .update(updateData)
      .eq("id", params.id)
      .select(`
        *,
        user:profiles!user_organizations_user_id_fkey(id, email, full_name),
        organization:organizations!user_organizations_organization_id_fkey(id, name, slug)
      `)
      .single();

    if (updateError) {
      console.error("Error updating user-organization relationship:", updateError);
      return NextResponse.json({ 
        error: "Failed to update user-organization relationship", 
        details: updateError.message 
      }, { status: 500 });
    }

    if (!updatedRelationship) {
      return NextResponse.json({ 
        error: "User-organization relationship not found" 
      }, { status: 404 });
    }

    console.log("Successfully updated user-organization relationship");

    return NextResponse.json({
      success: true,
      userOrganization: updatedRelationship,
      message: "User-organization relationship updated successfully"
    });

  } catch (error) {
    console.error("Error in user-organizations PATCH API:", error);
    return NextResponse.json({ 
      error: "Internal Server Error", 
      details: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("Super-admin user-organizations DELETE API called for ID:", params.id);

    // Use standardized auth pattern
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);

    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Failed to get user' }, { status: 401 });
    }

    console.log(`Deleting user-organization relationship: ${params.id}`);

    // Delete the user-organization relationship
    const { error: deleteError } = await supabase
      .from("user_organizations")
      .delete()
      .eq("id", params.id);

    if (deleteError) {
      console.error("Error deleting user-organization relationship:", deleteError);
      return NextResponse.json({ 
        error: "Failed to delete user-organization relationship", 
        details: deleteError.message 
      }, { status: 500 });
    }

    console.log("Successfully deleted user-organization relationship");

    return NextResponse.json({
      success: true,
      message: "User successfully removed from organization"
    });

  } catch (error) {
    console.error("Error in user-organizations DELETE API:", error);
    return NextResponse.json({ 
      error: "Internal Server Error", 
      details: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}