/**
 * BACKEND UI AUDIT FIX: Payment Statistics API
 */

import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 500 });
    }

    const supabase = createClient();

    // Mock payment statistics (replace with actual payment table queries)
    const stats = {
      total_revenue: 125750.50,
      total_transactions: 342,
      pending_payments: 8950.25,
      affiliate_payouts: 89325.35,
      platform_fees: 12575.05
    };

    return NextResponse.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Error fetching payment stats:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch payment statistics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}