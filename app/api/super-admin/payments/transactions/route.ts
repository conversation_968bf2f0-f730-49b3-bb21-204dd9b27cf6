/**
 * BACKEND UI AUDIT FIX: Payment Transactions API
 */

import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 500 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const days = parseInt(searchParams.get('days') || '30');
    const minAmount = parseFloat(searchParams.get('min_amount') || '0');
    const maxAmount = parseFloat(searchParams.get('max_amount') || '999999');

    // Mock payment transactions (replace with actual payment table queries)
    const mockTransactions = [
      {
        id: 'txn_1234567890abcdef',
        trip_id: 'trip_abc123',
        amount: 125.50,
        currency: 'USD',
        status: 'completed',
        payment_method: 'Credit Card',
        client_id: 'client_123',
        affiliate_company_id: 'affiliate_456',
        affiliate_share: 100.40,
        platform_fee: 25.10,
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        processed_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 60000).toISOString(),
        client_name: 'John Doe',
        affiliate_name: 'Elite City Transport'
      },
      {
        id: 'txn_2345678901bcdefg',
        trip_id: 'trip_def456',
        amount: 89.75,
        currency: 'USD',
        status: 'pending',
        payment_method: 'PayPal',
        client_id: 'client_456',
        affiliate_company_id: 'affiliate_789',
        affiliate_share: 71.80,
        platform_fee: 17.95,
        created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        client_name: 'Jane Smith',
        affiliate_name: 'City Tours Transportation'
      },
      {
        id: 'txn_3456789012cdefgh',
        trip_id: 'trip_ghi789',
        amount: 250.00,
        currency: 'USD',
        status: 'completed',
        payment_method: 'Bank Transfer',
        client_id: 'client_789',
        affiliate_company_id: 'affiliate_123',
        affiliate_share: 200.00,
        platform_fee: 50.00,
        created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        processed_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 120000).toISOString(),
        client_name: 'Corporate Client LLC',
        affiliate_name: 'Luxury Concierge Services'
      }
    ];

    // Apply filters
    let filteredTransactions = mockTransactions;

    if (status && status !== 'all') {
      filteredTransactions = filteredTransactions.filter(t => t.status === status);
    }

    filteredTransactions = filteredTransactions.filter(t => {
      const transactionDate = new Date(t.created_at);
      const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      return transactionDate >= cutoffDate;
    });

    filteredTransactions = filteredTransactions.filter(t => 
      t.amount >= minAmount && t.amount <= maxAmount
    );

    return NextResponse.json({
      success: true,
      transactions: filteredTransactions
    });

  } catch (error) {
    console.error('Error fetching payment transactions:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch payment transactions',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}