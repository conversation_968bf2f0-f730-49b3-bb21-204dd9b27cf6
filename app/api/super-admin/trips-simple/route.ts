import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';

// PRODUCTION TRIPS API - Enterprise-grade authentication
export async function GET(request: NextRequest) {
  try {
    console.log("Production super-admin trips API - Starting request");
    
    // Authenticate request with super admin access
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { user } = authResult;
    console.log(`Authenticated user: ${user!.email} (${user!.role})`);
    
    // Get organization filter from query params
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('org_id');
    
    // Organization access validation (simplified for super admin)
    if (orgId && orgId !== 'all') {
      console.log(`Filtering by organization: ${orgId}`);
    }
    
    console.log('Filtering by organization ID:', orgId);
    console.log('Note: trips table does not exist in database');

    // Return empty trips data since table doesn't exist
    const trips: any[] = [];

    console.log(`Found ${trips.length} trips (table does not exist)`);
    
    return NextResponse.json({
      success: true,
      trips: trips,
      total: trips.length,
      filtered_by_org: orgId && orgId !== 'all' ? orgId : null,
      note: 'Trips table does not exist in database',
      user_context: {
        role: user!.role,
        userId: user!.id,
        email: user!.email
      }
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/trips-simple:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}