import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { toUserRoles } from '@/app/lib/auth/roles';
import { NextRequest, NextResponse } from 'next/server';

interface Event {
  id: string;
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  location: string;
  total_passengers: number;
  status: string;
  customer_id: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
  customer?: {
    id: string;
    email: string;
    full_name: string;
  };
  organization?: {
    id: string;
    name: string;
    slug: string;
  };
}

export async function GET(request: NextRequest) {
  try {
    console.log("Super-admin events GET API called");

    // Use standardized auth pattern (same as working quotes API)
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);

    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Failed to get user' }, { status: 401 });
    }

    const url = new URL(request.url);
    const status = url.searchParams.get("status");
    const organizationId = url.searchParams.get("organization_id");
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const offset = parseInt(url.searchParams.get("offset") || "0");
    const sortBy = url.searchParams.get("sort_by") || "created_at";
    const sortOrder = url.searchParams.get("sort_order") || "desc";

    console.log(`Fetching events with params: status=${status}, organizationId=${organizationId}, limit=${limit}, offset=${offset}`);

    let query = supabase
      .from("events")
      .select(`
        *,
        customer:profiles!events_customer_id_fkey(id, email, full_name),
        organization:organizations!events_organization_id_fkey(id, name, slug)
      `)
      .order(sortBy as any, { ascending: sortOrder === "asc" })
      .range(offset, offset + limit - 1);

    if (status) {
      query = query.eq("status", status);
    }
    if (organizationId) {
      query = query.eq("organization_id", organizationId);
    }

    const { data: events, error, count } = await query;

    if (error) {
      console.error("Error fetching events:", error);
      return NextResponse.json({ error: "Failed to fetch events", details: error.message }, { status: 500 });
    }

    console.log(`Successfully fetched ${events?.length || 0} events`);

    return NextResponse.json({
      success: true,
      events: events || [],
      total: count || events?.length || 0,
      pagination: {
        limit,
        offset,
        total: count || events?.length || 0
      }
    });

  } catch (error) {
    console.error("Error in super-admin events API:", error);
    return NextResponse.json({ 
      error: "Internal Server Error", 
      details: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};

export async function GET_OLD(request: NextRequest) {
  try {
    console.log('Super-admin events API - Starting request');

    // Create a service role client that bypasses authentication
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get organization filter from query params
    const { searchParams } = new URL(request.url);
    const orgFilter = searchParams.get('org');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log('Super-admin events API - Params:', { orgFilter, status, limit, offset });

    // Build the query - use auth.users instead of profiles for the foreign key relationship
    let query = supabase
      .from('events')
      .select(`
        id,
        name,
        description,
        start_date,
        end_date,
        location,
        total_passengers,
        status,
        customer_id,
        organization_id,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Organization filtering is now done in the transformation loop

    // Apply status filter if specified
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    const { data: events, error } = await query;

    if (error) {
      console.error('Error fetching events:', error);
      return NextResponse.json({ error: 'Failed to fetch events' }, { status: 500 });
    }

    console.log(`Super-admin events API - Found ${events?.length || 0} events`);

    // Get customer and organization data for each event
    const transformedEvents = [];

    for (const event of events || []) {
      // Get customer profile data
      const { data: profile } = await supabase
        .from('profiles')
        .select(`
          id,
          email,
          full_name
        `)
        .eq('id', event.customer_id)
        .single();

      // Get user organizations separately
      let userOrganizations = [];
      if (profile) {
        const { data: orgData } = await supabase
          .from('user_organizations')
          .select(`
            organization_id,
            organizations(id, name, slug)
          `)
          .eq('user_id', profile.id);

        userOrganizations = orgData || [];
      }

      // Apply organization filter if specified
      if (orgFilter && orgFilter !== 'all') {
        const hasMatchingOrg = userOrganizations.some(
          uo => uo.organization_id === orgFilter
        );
        if (!hasMatchingOrg) {
          continue; // Skip this event if it doesn't match the organization filter
        }
      }

      transformedEvents.push({
        id: event.id,
        name: event.name,
        description: event.description,
        start_date: event.start_date,
        end_date: event.end_date,
        location: event.location,
        total_passengers: event.total_passengers,
        status: event.status,
        customer_id: event.customer_id,
        organization_id: event.organization_id,
        created_at: event.created_at,
        updated_at: event.updated_at,
        customer: {
          id: profile?.id,
          email: profile?.email,
          full_name: profile?.full_name
        },
        organization: {
          id: userOrganizations[0]?.organizations?.id,
          name: userOrganizations[0]?.organizations?.name,
          slug: userOrganizations[0]?.organizations?.slug
        }
      });
    }

    return NextResponse.json({
      events: transformedEvents,
      success: true
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Error in super-admin events API:', error);
    return NextResponse.json(
      { error: `Failed to fetch events: ${errorMessage}` },
      { status: 500 }
    );
  }
} 