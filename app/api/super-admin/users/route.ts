import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

/**
 * GET /api/super-admin/users
 * Get all users for super admin management
 * Uses the new unified authentication system
 */
export async function GET(request: NextRequest) {
  try {
    console.log("Super-admin users GET API called");

    // Use unified authentication system
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const { context } = authResult;
    const supabase = createClient();

    console.log('Super-admin users API - Starting request');

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const status = searchParams.get('status');

    console.log(`Query params: limit=${limit}, offset=${offset}, search=${search}, role=${role}, status=${status}`);

    // Build the query for users with their organizations
    let query = supabase
      .from('profiles')
      .select(`
        id,
        email,
        full_name,
        phone,
        roles,
        created_at,
        updated_at,
        user_organizations!inner(
          id,
          role,
          status,
          organization:organizations!inner(
            id,
            name,
            slug,
            organization_type
          )
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (search) {
      query = query.or(`email.ilike.%${search}%,full_name.ilike.%${search}%`);
    }

    if (role) {
      query = query.contains('roles', [role]);
    }

    const { data: users, error, count } = await query;

    if (error) {
      console.error('Error fetching users:', error);
      return NextResponse.json({ 
        error: 'Failed to fetch users', 
        details: error.message 
      }, { status: 500 });
    }

    console.log(`Successfully fetched ${users?.length || 0} users`);

    return NextResponse.json({
      success: true,
      users: users || [],
      total: count || users?.length || 0,
      pagination: {
        limit,
        offset,
        total: count || users?.length || 0
      }
    });

  } catch (error) {
    console.error('Error in super-admin users API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}

/**
 * POST /api/super-admin/users
 * Create a new user (super admin only)
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Super-admin users API - POST request');

    // Use unified authentication system
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const body = await request.json();
    const {
      email,
      password,
      first_name,
      last_name,
      role,
      organization_id,
      permissions_template,
      send_invitation = true
    } = body;

    // Validate required fields
    if (!email || !first_name || !last_name || !role) {
      return NextResponse.json({
        error: 'Missing required fields: email, first_name, last_name, role are required'
      }, { status: 400 });
    }

    const supabase = createClient();

    // Create user in auth.users table
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password: password || generateSecurePassword(),
      email_confirm: true,
      user_metadata: {
        first_name,
        last_name,
        roles: [role]
      }
    });

    if (authError) {
      console.error('Error creating auth user:', authError);
      return NextResponse.json({
        error: 'Failed to create user account',
        details: authError.message
      }, { status: 500 });
    }

    // Create profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: authUser.user.id,
        email,
        full_name: `${first_name} ${last_name}`,
        roles: [role],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (profileError) {
      console.error('Error creating profile:', profileError);
      // Clean up auth user if profile creation fails
      await supabase.auth.admin.deleteUser(authUser.user.id);
      return NextResponse.json({
        error: 'Failed to create user profile',
        details: profileError.message
      }, { status: 500 });
    }

    // If organization_id is provided, create user-organization relationship
    if (organization_id) {
      const { error: orgError } = await supabase
        .from('user_organizations')
        .insert({
          user_id: authUser.user.id,
          organization_id,
          role,
          status: 'active',
          joined_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (orgError) {
        console.error('Error creating user-organization relationship:', orgError);
        // Note: We don't fail the entire operation for this
      }
    }

    console.log('Successfully created user:', profile.id);

    return NextResponse.json({
      success: true,
      user: profile,
      message: 'User created successfully'
    });

  } catch (error) {
    console.error('Error in users POST API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}

// Helper function to generate secure password
function generateSecurePassword(): string {
  const length = 12;
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
  let password = "";
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}