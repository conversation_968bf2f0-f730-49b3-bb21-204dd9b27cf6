import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    const body = await request.json()
    const { email, role, organization_id, send_invitation } = body

    // Validate required fields
    if (!email || !role || !organization_id) {
      return NextResponse.json(
        { error: 'Missing required fields: email, role, organization_id' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate role
    const validRoles = ['SUPER_ADMIN', 'ADMIN', 'MANAGER', 'CLIENT', 'CLIENT_COORDINATOR']
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('profiles')
      .select('id, email')
      .eq('email', email)
      .single()

    if (existingUser) {
      // User exists, check if they're already associated with this organization
      const { data: existingAssociation } = await supabase
        .from('user_companies')
        .select('id')
        .eq('user_id', existingUser.id)
        .eq('company_id', organization_id)
        .single()

      if (existingAssociation) {
        return NextResponse.json(
          { error: 'User is already associated with this organization' },
          { status: 409 }
        )
      }

      // Add existing user to organization
      const { error: associationError } = await supabase
        .from('user_companies')
        .insert({
          user_id: existingUser.id,
          company_id: organization_id,
          role: role,
          status: 'ACTIVE'
        })

      if (associationError) {
        console.error('Error associating user with organization:', associationError)
        return NextResponse.json(
          { error: 'Failed to associate user with organization' },
          { status: 500 }
        )
      }

      // Update user role in profiles
      const { error: roleUpdateError } = await supabase
        .from('profiles')
        .update({ 
          roles: [role],
          updated_at: new Date().toISOString()
        })
        .eq('id', existingUser.id)

      if (roleUpdateError) {
        console.error('Error updating user role:', roleUpdateError)
      }

      if (send_invitation) {
        // TODO: Send invitation email to existing user
        console.log(`Sending invitation email to existing user: ${email}`)
      }

      return NextResponse.json({
        success: true,
        message: send_invitation ? 'Invitation sent to existing user' : 'Existing user added to organization',
        user_id: existingUser.id
      })
    }

    // User doesn't exist, create new user
    if (send_invitation) {
      // Create invitation record for new user
      const { data: invitation, error: invitationError } = await supabase
        .from('user_invitations')
        .insert({
          email: email,
          role: role,
          organization_id: organization_id,
          status: 'PENDING',
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
        })
        .select()
        .single()

      if (invitationError) {
        console.error('Error creating invitation:', invitationError)
        return NextResponse.json(
          { error: 'Failed to create invitation' },
          { status: 500 }
        )
      }

      // TODO: Send invitation email
      console.log(`Sending invitation email to new user: ${email}`)

      return NextResponse.json({
        success: true,
        message: 'Invitation sent successfully',
        invitation_id: invitation.id
      })
    } else {
      // Create user directly without invitation
      // Note: This creates a placeholder user that will need to set up their account
      const { data: newUser, error: userError } = await supabase.auth.admin.createUser({
        email: email,
        email_confirm: true,
        user_metadata: {
          role: role,
          organization_id: organization_id
        }
      })

      if (userError) {
        console.error('Error creating user:', userError)
        return NextResponse.json(
          { error: 'Failed to create user account' },
          { status: 500 }
        )
      }

      // Create profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: newUser.user.id,
          email: email,
          roles: [role],
          status: 'ACTIVE'
        })

      if (profileError) {
        console.error('Error creating profile:', profileError)
        return NextResponse.json(
          { error: 'Failed to create user profile' },
          { status: 500 }
        )
      }

      // Associate user with organization
      const { error: associationError } = await supabase
        .from('user_companies')
        .insert({
          user_id: newUser.user.id,
          company_id: organization_id,
          role: role,
          status: 'ACTIVE'
        })

      if (associationError) {
        console.error('Error associating user with organization:', associationError)
        return NextResponse.json(
          { error: 'Failed to associate user with organization' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'User created successfully',
        user_id: newUser.user.id
      })
    }

  } catch (error) {
    console.error('Error in user invitation/creation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
