import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { } from '@/lib/auth/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const userId = params.id;
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    // Get user permissions for this organization
    const { data: userPermissions, error: userError } = await supabase
      .from('user_granular_permissions')
      .select('*')
      .eq('user_id', userId)
      .or(`organization_id.is.null,organization_id.eq.${organizationId}`)
      .order('created_at', { ascending: false });

    if (userError) {
      console.error('Error fetching user permissions:', userError);
      return NextResponse.json(
        { error: 'Failed to fetch user permissions' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      userPermissions: userPermissions || []
    });

  } catch (error) {
    console.error('Error in two-tier permissions API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const userId = params.id;
    const { userPermissions, organizationId } = await request.json();

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    // Delete existing user permissions for this organization
    await supabase
      .from('user_granular_permissions')
      .delete()
      .eq('user_id', userId)
      .eq('organization_id', organizationId);

    // Insert new permissions
    if (userPermissions && userPermissions.length > 0) {
      const permissionsToInsert = userPermissions.map((perm: any) => ({
        user_id: userId,
        organization_id: organizationId,
        permission_category: perm.permission_category,
        permission_key: perm.permission_key,
        granted: perm.granted,
        expires_at: perm.expires_at || null
      }));

      const { error: insertError } = await supabase
        .from('user_granular_permissions')
        .insert(permissionsToInsert);

      if (insertError) {
        console.error('Error inserting user permissions:', insertError);
        return NextResponse.json(
          { error: 'Failed to save user permissions' },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error saving two-tier permissions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}