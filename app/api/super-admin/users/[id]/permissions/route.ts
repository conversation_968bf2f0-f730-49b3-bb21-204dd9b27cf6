import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`Fetching permissions for user: ${params.id}`);
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase configuration');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user profile and permissions
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select(`
        id,
        email,
        full_name,
        role,
        user_organizations (
          organization_id,
          role,
          organizations (
            id,
            name,
            slug
          )
        )
      `)
      .eq('id', params.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }

    if (!profile) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Transform the data for the frontend
    const userPermissions = {
      id: profile.id,
      email: profile.email,
      fullName: profile.full_name,
      platformRole: profile.role,
      organizations: profile.user_organizations?.map((uo: any) => ({
        id: uo.organizations.id,
        name: uo.organizations.name,
        slug: uo.organizations.slug,
        role: uo.role
      })) || []
    };

    return NextResponse.json({
      user: userPermissions,
      success: true
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/users/[id]/permissions:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { platformRole, organizations } = body;

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Update platform role
    if (platformRole) {
      const { error: roleError } = await supabase
        .from('profiles')
        .update({ role: platformRole })
        .eq('id', params.id);

      if (roleError) {
        console.error('Error updating platform role:', roleError);
        return NextResponse.json(
          { error: 'Failed to update platform role' },
          { status: 500 }
        );
      }
    }

    // Update organization roles
    if (organizations && Array.isArray(organizations)) {
      // First, remove existing organization associations
      const { error: deleteError } = await supabase
        .from('user_organizations')
        .delete()
        .eq('user_id', params.id);

      if (deleteError) {
        console.error('Error removing existing organization roles:', deleteError);
        return NextResponse.json(
          { error: 'Failed to update organization roles' },
          { status: 500 }
        );
      }

      // Then, add new organization associations
      if (organizations.length > 0) {
        const orgInserts = organizations.map((org: any) => ({
          user_id: params.id,
          organization_id: org.organizationId,
          role: org.role
        }));

        const { error: insertError } = await supabase
          .from('user_organizations')
          .insert(orgInserts);

        if (insertError) {
          console.error('Error inserting new organization roles:', insertError);
          return NextResponse.json(
            { error: 'Failed to update organization roles' },
            { status: 500 }
          );
        }
      }
    }

    return NextResponse.json({
      message: 'User permissions updated successfully',
      success: true
    });

  } catch (error) {
    console.error('Error in PUT /api/super-admin/users/[id]/permissions:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}