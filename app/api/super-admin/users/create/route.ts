import { NextRequest, NextResponse } from 'next/server'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    // Use standardized auth pattern
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN']);

    const body = await request.json()
    const { email, role, organization_id } = body

    // Validate required fields
    if (!email || !role) {
      return NextResponse.json(
        { error: 'Missing required fields: email, role' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate role
    const validRoles = ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN', 'TENANT_MANAGER', 'CLIENT', 'CLIENT_COORDINATOR', 'AFFILIATE', 'DRIVER', 'PASSENGER']
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      )
    }

    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('profiles')
      .select('id, email')
      .eq('email', email)
      .single()

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      )
    }

    // Generate a temporary password
    const tempPassword = Math.random().toString(36).slice(-12) + 'A1!'

    const { data: newUser, error: userError } = await supabase.auth.admin.createUser({
      email: email,
      password: tempPassword,
      email_confirm: true,
      user_metadata: {
        role: role,
        organization_id: organization_id,
        created_by_admin: true,
        temp_password: true
      }
    })

    if (userError) {
      console.error('Error creating user:', userError)
      return NextResponse.json(
        { error: 'Failed to create user account' },
        { status: 500 }
      )
    }

    // Create profile
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: newUser.user.id,
        email: email,
        roles: [role],
        status: 'ACTIVE',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

    if (profileError) {
      console.error('Error creating profile:', profileError)
      return NextResponse.json(
        { error: 'Failed to create user profile' },
        { status: 500 }
      )
    }

    // Associate user with organization if provided
    if (organization_id) {
      const { error: associationError } = await supabase
        .from('user_companies')
        .insert({
          user_id: newUser.user.id,
          organization_id: organization_id,
          role: role,
          status: 'ACTIVE',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (associationError) {
        console.error('Error associating user with organization:', associationError)
        // Don't fail the request, just log the error
      }
    }

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      user_id: newUser.user.id,
      email: email,
      role: role
    })

  } catch (error) {
    console.error('Error in user creation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
