import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

/**
 * GET /api/super-admin/affiliates
 * Get all affiliate companies for super admin with proper authentication
 */
export async function GET(request: NextRequest) {
  try {
    console.log("Production Affiliates API - Starting request");
    
    // Authenticate request with super admin access
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { user } = authResult;
    console.log(`Authenticated user: ${user!.email} (${user!.role})`);

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    console.log('Fetching affiliates with filters:', { status, limit, offset });

    // Query affiliate companies
    let query = supabase
      .from('affiliate_companies')
      .select(`
        id,
        name,
        legal_name,
        primary_contact_email,
        primary_contact_phone,
        status,
        created_at,
        updated_at,
        business_license,
        fleet_size,
        service_areas,
        website,
        street_address,
        average_rating,
        approved_at,
        approval_status
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply status filter if provided
    if (status) {
      query = query.eq('status', status);
    }

    const { data: affiliates, error, count } = await query;

    if (error) {
      console.error('Error fetching affiliates:', error);
      return NextResponse.json(
        { error: 'Failed to fetch affiliates' },
        { status: 500 }
      );
    }

    // Transform data for frontend
    const transformedAffiliates = affiliates?.map(affiliate => ({
      id: affiliate.id,
      companyName: affiliate.name,
      legalName: affiliate.legal_name,
      contactEmail: affiliate.primary_contact_email,
      contactPhone: affiliate.primary_contact_phone,
      status: affiliate.status,
      approvalStatus: affiliate.approval_status,
      businessLicense: affiliate.business_license,
      fleetSize: affiliate.fleet_size,
      serviceAreas: affiliate.service_areas,
      website: affiliate.website,
      businessAddress: affiliate.street_address,
      rating: affiliate.average_rating || 0,
      approvalDate: affiliate.approved_at,
      createdAt: affiliate.created_at,
      updatedAt: affiliate.updated_at
    })) || [];

    console.log(`Successfully fetched ${transformedAffiliates.length} affiliates`);

    return NextResponse.json({
      success: true,
      affiliates: transformedAffiliates,
      total: count || transformedAffiliates.length,
      pagination: {
        limit,
        offset,
        total: count || transformedAffiliates.length
      },
      user_context: {
        role: user!.role,
        userId: user!.id,
        email: user!.email
      }
    });

  } catch (error) {
    console.error('Affiliates API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}