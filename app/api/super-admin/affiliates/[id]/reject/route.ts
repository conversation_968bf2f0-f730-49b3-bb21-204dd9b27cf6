import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { authenticateSuperAdminRequest } from '@/lib/auth/api-authentication';

export const runtime = 'nodejs';



const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(
      "Super-admin affiliate rejection API - Starting request for ID:",
      params.id
    );

    // Authenticate request with super admin access
    const authResult = await authenticateSuperAdminRequest(request);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { user } = authResult;
    console.log(`Authenticated user: ${user!.email} (${user!.role})`);

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get request body for rejection reasons
    const body = await request.json().catch(() => ({}));
    const { reasons = [], customReason = "", send_notification = true } = body;

    // Combine structured reasons with custom reason
    const allReasons = [...reasons];
    if (customReason.trim()) {
      allReasons.push(`Custom: ${customReason.trim()}`);
    }

    if (allReasons.length === 0) {
      return NextResponse.json(
        { error: "At least one rejection reason is required" },
        { status: 400 }
      );
    }

    // Get current affiliate data for audit trail
    const { data: currentAffiliate, error: fetchError } = await supabase
      .from("affiliate_companies")
      .select("*")
      .eq("id", params.id)
      .single();

    if (fetchError) {
      console.error("Error fetching affiliate:", fetchError);
      return NextResponse.json(
        { error: "Affiliate not found", details: fetchError.message },
        { status: 404 }
      );
    }

    const previousStatus = currentAffiliate.application_status;

    // Update affiliate status to rejected
    const { data: updatedAffiliate, error: updateError } = await supabase
      .from("affiliate_companies")
      .update({
        status: "inactive",
        application_status: "rejected",
        updated_at: new Date().toISOString(),
      })
      .eq("id", params.id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating affiliate status:", updateError);
      return NextResponse.json(
        { error: "Failed to reject affiliate", details: updateError.message },
        { status: 500 }
      );
    }

    // Create audit log entry
    const { error: auditError } = await supabase.from("audit_logs").insert({
      user_id: "7ab6f229-1250-485b-8a17-1947237b0ca3", // TODO: Get from session
      action: "affiliate_rejected",
      table_name: "affiliate_companies",
      record_id: params.id,
      details: JSON.stringify({
        previous_status: previousStatus,
        new_status: "rejected",
        rejection_reasons: allReasons,
        custom_reason: customReason,
        affiliate_name: currentAffiliate.name,
      }),
    });

    if (auditError) {
      console.error("Error creating audit log:", auditError);
      // Don't fail the request for audit log errors
    }

    // Note: affiliate_approval_history table doesn't exist yet
    // History is tracked via audit_logs table above
    console.log("Rejection history tracked via audit_logs");

    // Send notification email if requested
    if (send_notification) {
      try {
        const notificationResponse = await fetch(
          `${request.nextUrl.origin}/api/super-admin/affiliates/${params.id}/notify`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              type: "rejection",
              rejection_reasons: allReasons,
              custom_message: customReason,
            }),
          }
        );

        if (!notificationResponse.ok) {
          console.error("Failed to send notification email");
        }
      } catch (notificationError) {
        console.error("Error sending notification:", notificationError);
        // Don't fail the request for notification errors
      }
    }

    console.log(
      `Affiliate ${params.id} rejected successfully with reasons:`,
      allReasons
    );

    return NextResponse.json({
      success: true,
      message: "Affiliate rejected successfully",
      affiliate: updatedAffiliate,
      rejection_reasons: allReasons,
      notification_sent: send_notification,
      user_context: {
        role: user!.role,
        organization_id: user!.organization_id,
        is_super_admin: user!.is_super_admin
      }
    });
  } catch (error) {
    console.error("Error in affiliate rejection API:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
