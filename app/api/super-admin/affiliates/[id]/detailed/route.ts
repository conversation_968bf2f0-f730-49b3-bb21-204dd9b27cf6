import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
/**
 * Enhanced Affiliate Details API
 * 
 * This endpoint fetches comprehensive affiliate data including:
 * - Company information
 * - Vehicles and fleet data
 * - Documents and compliance status
 * - Performance metrics
 * - Rate cards
 * - Service areas
 * - Audit logs
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate request with super admin access
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { user } = authResult;
    console.log(`Authenticated user: ${user!.email} (${user!.role})`);

    // Use service role client for server-side operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    const affiliateId = params.id;

    // Fetch main affiliate company data
    const { data: affiliate, error: affiliateError } = await supabase
      .from('affiliate_companies')
      .select('*')
      .eq('id', affiliateId)
      .single();

    if (affiliateError || !affiliate) {
      return NextResponse.json(
        { error: 'Affiliate not found' },
        { status: 404 }
      );
    }

    // Fetch vehicles data (handle gracefully if table doesn't exist)
    let vehicles = [];
    try {
      const { data: vehiclesData, error: vehiclesError } = await supabase
        .from('vehicles')
        .select('*')
        .eq('company_id', affiliateId);
      if (!vehiclesError) vehicles = vehiclesData || [];
    } catch (error) {
      console.log('Vehicles table not accessible:', error);
    }

    // Fetch company documents (handle gracefully if table doesn't exist)
    let documents = [];
    try {
      const { data: documentsData, error: documentsError } = await supabase
        .from('company_documents')
        .select('*')
        .eq('company_id', affiliateId);
      if (!documentsError) documents = documentsData || [];
    } catch (error) {
      console.log('Company documents table not accessible:', error);
    }

    // Fetch rate cards (handle gracefully if table doesn't exist)
    let rateCards = [];
    try {
      const { data: rateCardsData, error: rateCardsError } = await supabase
        .from('rate_cards')
        .select('*')
        .eq('company_id', affiliateId);
      if (!rateCardsError) rateCards = rateCardsData || [];
    } catch (error) {
      console.log('Rate cards table not accessible:', error);
    }

    // Fetch service areas (handle gracefully if table doesn't exist)
    let serviceAreas = [];
    try {
      const { data: serviceAreasData, error: serviceAreasError } = await supabase
        .from('affiliate_service_areas')
        .select('*')
        .eq('affiliate_company_id', affiliateId);
      if (!serviceAreasError) serviceAreas = serviceAreasData || [];
    } catch (error) {
      console.log('Service areas table not accessible:', error);
    }

    // Fetch drivers (handle gracefully if table doesn't exist)
    let drivers = [];
    try {
      const { data: driversData, error: driversError } = await supabase
        .from('affiliate_drivers')
        .select('*')
        .eq('affiliate_company_id', affiliateId);
      if (!driversError) drivers = driversData || [];
    } catch (error) {
      console.log('Drivers table not accessible:', error);
    }

    // Fetch audit logs (handle gracefully if table doesn't exist)
    let auditLogs = [];
    try {
      const { data: auditLogsData, error: auditLogsError } = await supabase
        .from('audit_logs')
        .select('*')
        .eq('record_id', affiliateId)
        .order('created_at', { ascending: false })
        .limit(50);
      if (!auditLogsError) auditLogs = auditLogsData || [];
    } catch (error) {
      console.log('Audit logs table not accessible:', error);
    }

    // Fetch performance metrics from quotes and offers (handle gracefully)
    let performanceData = [];
    try {
      const { data: performanceDataResult, error: performanceError } = await supabase
        .from('quote_affiliate_offers')
        .select(`
          status,
          rate_amount,
          created_at,
          responded_at
        `)
        .eq('company_id', affiliateId);
      if (!performanceError) performanceData = performanceDataResult || [];
    } catch (error) {
      console.log('Performance data not accessible:', error);
    }

    // Calculate performance metrics
    const totalOffers = performanceData?.length || 0;
    const acceptedOffers = performanceData?.filter(offer => offer.status === 'accepted').length || 0;
    const rejectedOffers = performanceData?.filter(offer => offer.status === 'rejected').length || 0;
    const pendingOffers = performanceData?.filter(offer => offer.status === 'pending').length || 0;
    const counterOffers = performanceData?.filter(offer => offer.status === 'counter_offered').length || 0;

    const acceptanceRate = totalOffers > 0 ? (acceptedOffers / totalOffers) * 100 : 0;
    const responseRate = totalOffers > 0 ? ((acceptedOffers + rejectedOffers + counterOffers) / totalOffers) * 100 : 0;

    // Calculate average response time
    const respondedOffers = performanceData?.filter(offer => offer.responded_at) || [];
    const avgResponseTime = respondedOffers.length > 0 
      ? respondedOffers.reduce((sum, offer) => {
          const responseTime = new Date(offer.responded_at).getTime() - new Date(offer.created_at).getTime();
          return sum + responseTime;
        }, 0) / respondedOffers.length / (1000 * 60) // Convert to minutes
      : 0;

    // Calculate average rate
    const ratedOffers = performanceData?.filter(offer => offer.rate_amount) || [];
    const avgRate = ratedOffers.length > 0
      ? ratedOffers.reduce((sum, offer) => sum + parseFloat(offer.rate_amount), 0) / ratedOffers.length
      : 0;

    // Group vehicles by type
    const vehiclesByType = (vehicles || []).reduce((acc, vehicle) => {
      const type = vehicle.type || 'Unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Group documents by status
    const documentsByStatus = (documents || []).reduce((acc, doc) => {
      const status = doc.status || 'PENDING';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Calculate service coverage (with safe JSON parsing)
    let citiesCovered = [];
    try {
      if (affiliate.cities_covered) {
        if (typeof affiliate.cities_covered === 'string') {
          // Try to parse as JSON first, if that fails, split by comma
          try {
            citiesCovered = JSON.parse(affiliate.cities_covered);
          } catch {
            citiesCovered = affiliate.cities_covered.split(',').map(s => s.trim()).filter(Boolean);
          }
        } else if (Array.isArray(affiliate.cities_covered)) {
          citiesCovered = affiliate.cities_covered;
        }
      }
    } catch (error) {
      console.log('Error parsing cities_covered:', error);
      citiesCovered = [];
    }

    let airportsServed = [];
    try {
      if (affiliate.airports_served) {
        if (typeof affiliate.airports_served === 'string') {
          // Try to parse as JSON first, if that fails, split by comma
          try {
            airportsServed = JSON.parse(affiliate.airports_served);
          } catch {
            airportsServed = affiliate.airports_served.split(',').map(s => s.trim()).filter(Boolean);
          }
        } else if (Array.isArray(affiliate.airports_served)) {
          airportsServed = affiliate.airports_served;
        }
      }
    } catch (error) {
      console.log('Error parsing airports_served:', error);
      airportsServed = [];
    }

    // Prepare comprehensive response
    const detailedAffiliate = {
      // Basic company information
      ...affiliate,
      
      // Fleet information
      fleet: {
        vehicles: vehicles || [],
        vehiclesByType,
        totalVehicles: vehicles?.length || 0,
        vehicleTypes: Object.keys(vehiclesByType)
      },

      // Documents and compliance
      compliance: {
        documents: documents || [],
        documentsByStatus,
        totalDocuments: documents?.length || 0,
        verifiedDocuments: documentsByStatus.APPROVED || 0,
        pendingDocuments: documentsByStatus.PENDING || 0,
        rejectedDocuments: documentsByStatus.REJECTED || 0
      },

      // Performance metrics
      performance: {
        totalOffers,
        acceptedOffers,
        rejectedOffers,
        pendingOffers,
        counterOffers,
        acceptanceRate: Math.round(acceptanceRate * 100) / 100,
        responseRate: Math.round(responseRate * 100) / 100,
        avgResponseTime: Math.round(avgResponseTime * 100) / 100, // in minutes
        avgRate: Math.round(avgRate * 100) / 100,
        rating: affiliate.rating || 0,
        completionRate: affiliate.completion_rate || 0
      },

      // Service information
      service: {
        rateCards: rateCards || [],
        serviceAreas: serviceAreas || [],
        citiesCovered,
        airportsServed,
        languagesSpoken: affiliate.languages_spoken 
          ? (typeof affiliate.languages_spoken === 'string'
              ? JSON.parse(affiliate.languages_spoken)
              : affiliate.languages_spoken)
          : [],
        dispatchSoftware: affiliate.dispatch_software || null,
        tripStatusUpdates: affiliate.trip_status_updates || null
      },

      // Staff information
      staff: {
        drivers: drivers || [],
        totalDrivers: drivers?.length || 0
      },

      // Operational details
      operations: {
        chargeForDelays: affiliate.charge_for_delays || false,
        chargeForStops: affiliate.charge_for_stops || false,
        offerMeetGreet: affiliate.offer_meet_greet || false,
        roadShows: affiliate.road_shows || false,
        griddGnetMember: affiliate.gridd_gnet_member || false,
        addonsLa: affiliate.addons_la || false,
        directBilling: affiliate.direct_billing || false,
        livePhoneSupport: affiliate.live_phone_support || false,
        dressCode: affiliate.dress_code || null,
        cancellationPolicy: affiliate.cancellation_policy || null,
        meetGreet: affiliate.meet_greet || null
      },

      // Status and progress
      status: {
        applicationStatus: affiliate.application_status || 'Draft',
        businessRegistrationStatus: affiliate.business_registration_status || 'Not Started',
        insuranceStatus: affiliate.insurance_status || 'Not Started',
        fleetInspectionStatus: affiliate.fleet_inspection_status || 'Not Started',
        rateAgreementStatus: affiliate.rate_agreement_status || 'Not Started',
        documentStatus: affiliate.document_status || 'Not Started',
        progress: affiliate.progress || 0,
        applicationSubmittedAt: affiliate.application_submitted_at
      },

      // Audit trail
      auditLogs: auditLogs || []
    };

    return NextResponse.json({
      affiliate: detailedAffiliate,
      success: true
    });

  } catch (error) {
    console.error('Error fetching detailed affiliate data:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      affiliateId: params.id
    });
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        affiliateId: params.id
      },
      { status: 500 }
    );
  }
}
