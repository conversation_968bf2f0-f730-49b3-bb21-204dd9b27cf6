import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const affiliateId = params.id

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated and has super admin access
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is super admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single()

    if (!profile?.roles?.includes('SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Fetch audit log entries for the affiliate
    const { data: auditEntries, error: auditError } = await supabase
      .from('audit_logs')
      .select(`
        id,
        event_type,
        table_name,
        record_id,
        old_values,
        new_values,
        created_by,
        created_at,
        profiles:created_by (
          email,
          first_name,
          last_name
        )
      `)
      .or(`record_id.eq.${affiliateId},new_values->>company_id.eq.${affiliateId},old_values->>company_id.eq.${affiliateId}`)
      .order('created_at', { ascending: false })
      .limit(50)

    if (auditError) {
      console.error('Error fetching audit log:', auditError)
      return NextResponse.json({ error: 'Failed to fetch audit log' }, { status: 500 })
    }

    // Transform audit entries for display
    const transformedEntries = (auditEntries || []).map(entry => {
      const userEmail = entry.profiles?.email || 'System'
      const userName = entry.profiles?.first_name && entry.profiles?.last_name 
        ? `${entry.profiles.first_name} ${entry.profiles.last_name}`
        : userEmail

      // Generate human-readable action description
      let action = entry.event_type
      let details = ''

      switch (entry.event_type) {
        case 'affiliate_created':
          action = 'Affiliate Created'
          details = 'New affiliate company registered'
          break
        case 'affiliate_updated':
          action = 'Profile Updated'
          details = getUpdateDetails(entry.old_values, entry.new_values)
          break
        case 'status_changed':
          action = 'Status Changed'
          details = `Status changed from ${entry.old_values?.status || 'unknown'} to ${entry.new_values?.status || 'unknown'}`
          break
        case 'document_uploaded':
          action = 'Document Uploaded'
          details = entry.new_values?.document_type || 'Document uploaded'
          break
        case 'document_verified':
          action = 'Document Verified'
          details = entry.new_values?.document_type || 'Document verified'
          break
        case 'rate_card_updated':
          action = 'Rates Updated'
          details = 'Rate card configuration updated'
          break
        case 'fleet_updated':
          action = 'Fleet Updated'
          details = 'Vehicle fleet information updated'
          break
        case 'offer_created':
          action = 'Quote Offer Created'
          details = `New quote offer for ${entry.new_values?.quote_reference || 'quote'}`
          break
        case 'offer_updated':
          action = 'Quote Offer Updated'
          details = `Quote offer status changed to ${entry.new_values?.status || 'unknown'}`
          break
        default:
          action = entry.event_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
          details = 'Action performed'
      }

      return {
        id: entry.id,
        timestamp: entry.created_at,
        action,
        user: userName,
        userEmail,
        details,
        table: entry.table_name,
        eventType: entry.event_type
      }
    })

    return NextResponse.json({
      success: true,
      auditLog: transformedEntries
    })

  } catch (error) {
    console.error('Error in affiliate audit log API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

function getUpdateDetails(oldValues: any, newValues: any): string {
  if (!oldValues || !newValues) return 'Profile updated'
  
  const changes = []
  
  // Check common fields that might change
  if (oldValues.name !== newValues.name) {
    changes.push(`Name: ${oldValues.name} → ${newValues.name}`)
  }
  if (oldValues.email !== newValues.email) {
    changes.push(`Email: ${oldValues.email} → ${newValues.email}`)
  }
  if (oldValues.phone !== newValues.phone) {
    changes.push(`Phone: ${oldValues.phone} → ${newValues.phone}`)
  }
  if (oldValues.city !== newValues.city) {
    changes.push(`City: ${oldValues.city} → ${newValues.city}`)
  }
  if (oldValues.status !== newValues.status) {
    changes.push(`Status: ${oldValues.status} → ${newValues.status}`)
  }
  
  return changes.length > 0 ? changes.join(', ') : 'Profile updated'
}
