import { getSession, hasRole } from '@/app/lib/auth';

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { toUserRoles } from '@/app/lib/auth/roles';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // Require session and SUPER_ADMIN
  const session = await getSession(request);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if (!hasRole(session.roles, 'SUPER_ADMIN')) {
    return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: session.roles }, { status: 403 });
  }
  // Use authenticated Supabase client
  const supabase = await getSupabaseClient(request);
  if (!supabase) {
    return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
  }

  try {
    console.log('[API SA] GET /api/super-admin/trips - Request received');

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Get trips from the trips table
    let query = supabase
      .from('trips')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: trips, error: tripsError } = await query;

    if (tripsError) {
      console.error('[API SA Trips] Error fetching trips:', tripsError.message);
      return NextResponse.json({ message: "Error fetching trips", details: tripsError.message }, { status: 500 });
    }

    const transformedTrips = (trips || []).map((trip: any) => ({
      id: trip.id,
      quote_id: trip.quote_id,
      status: trip.status || 'pending',
      created_at: trip.created_at,
      updated_at: trip.updated_at,
      pickup_location: 'Sample Pickup Location',
      dropoff_location: 'Sample Dropoff Location',
      vehicle_type: 'luxury_sedan',
      passenger_count: 2,
      driver_name: 'John Doe',
      total_amount: 150.00,
      customer: {
        id: 'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455',
        email: '<EMAIL>',
        full_name: 'Super Admin User'
      },
      organization: {
        id: 'a7e12f2c-063c-4abc-b783-e172272e755c',
        name: 'City Tours LLC',
        slug: 'city-tours-llc'
      }
    }));

    console.log(`[API SA Trips] Found ${transformedTrips.length} trips`);
    return NextResponse.json({ trips: transformedTrips, success: true });

  } catch (error: any) {
    console.error('[API SA Trips] Error:', error.message);
    return NextResponse.json({ message: "Internal server error", details: error.message }, { status: 500 });
  }
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};