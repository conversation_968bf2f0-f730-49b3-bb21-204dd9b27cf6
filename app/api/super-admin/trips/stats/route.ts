/**
 * BACKEND UI AUDIT FIX: Trip Statistics API
 */

import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 500 });
    }

    // Mock trip statistics (replace with actual trip table queries)
    const stats = {
      total_trips: 1247,
      scheduled_trips: 23,
      in_progress_trips: 8,
      completed_trips: 1198,
      cancelled_trips: 18,
      total_revenue: 287650.75
    };

    return NextResponse.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Error fetching trip stats:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch trip statistics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}