import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
/**
 * GET /api/super-admin/enterprise/clients
 * Fetch all enterprise clients for super admin management
 */
export async function GET(request: NextRequest) {
  try {
    // Create Supabase client with cookies for authentication
    const cookieStore = cookies()
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Use service role for admin operations
    const adminSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Fetch all enterprise clients
    const { data: clients, error } = await adminSupabase
      .from('enterprise_clients')
      .select(`
        id,
        name,
        api_key,
        organization_id,
        rate_limit,
        status,
        webhook_url,
        webhook_secret,
        allowed_ips,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching clients:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch clients' },
        { status: 500 }
      )
    }

    // Mask API keys for security (show only first 12 characters)
    const maskedClients = clients?.map(client => ({
      ...client,
      api_key: client.api_key ? `${client.api_key.substring(0, 12)}...` : '',
      webhook_secret: client.webhook_secret ? '***' : null
    }))

    return NextResponse.json({
      success: true,
      clients: maskedClients || []
    })

  } catch (error) {
    console.error('Error in enterprise clients API:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
