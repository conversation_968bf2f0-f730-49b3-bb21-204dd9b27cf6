import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

/**
 * GET /api/super-admin/enterprise/applications
 * Get enterprise applications for super admin with proper authentication
 */
export async function GET(request: NextRequest) {
  try {
    console.log("Production Enterprise Applications API - Starting request");
    
    // Authenticate request with super admin access
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { user } = authResult;
    console.log(`Authenticated user: ${user!.email} (${user!.role})`);

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    console.log('Fetching enterprise applications with filters:', { status, limit, offset });

    // Query enterprise applications (using affiliate_companies as base)
    let query = supabase
      .from('affiliate_companies')
      .select(`
        id,
        name,
        primary_contact_email,
        primary_contact_phone,
        status,
        created_at,
        updated_at,
        approved_at,
        business_license,
        fleet_size,
        service_areas,
        website,
        street_address
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply status filter if provided
    if (status) {
      query = query.eq('status', status);
    }

    const { data: rawApplications, error, count } = await query;

    if (error) {
      console.error('Error fetching enterprise applications:', error);
      return NextResponse.json(
        { error: 'Failed to fetch applications' },
        { status: 500 }
      );
    }

    // Transform data to match enterprise application format
    const applications = rawApplications?.map(app => ({
      id: app.id,
      referenceNumber: `ENT-${app.id.substring(0, 8).toUpperCase()}`,
      companyName: app.name,
      primaryContactName: app.name, // Would need separate field
      primaryContactEmail: app.primary_contact_email,
      primaryContactPhone: app.primary_contact_phone,
      expectedApiVolume: app.fleet_size ? `${app.fleet_size * 100}/month` : 'Not specified',
      preferredPricingTier: 'Enterprise',
      industry: 'Transportation',
      status: mapApplicationStatus(app.status),
      submittedAt: app.created_at,
      reviewedAt: app.approved_at,
      rejectionReason: null, // Not available in current schema
      businessLicense: app.business_license,
      insuranceInfo: null, // Not available in current schema
      fleetSize: app.fleet_size,
      serviceAreas: app.service_areas,
      website: app.website,
      businessAddress: app.street_address,
      createdAt: app.created_at,
      updatedAt: app.updated_at
    })) || [];

    console.log(`Successfully fetched ${applications.length} enterprise applications`);

    return NextResponse.json({
      success: true,
      applications,
      total: count || applications.length,
      pagination: {
        limit,
        offset,
        total: count || applications.length
      },
      user_context: {
        role: user!.role,
        userId: user!.id,
        email: user!.email
      }
    });

  } catch (error) {
    console.error('Enterprise Applications API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to map affiliate status to enterprise application status
function mapApplicationStatus(affiliateStatus: string): string {
  switch (affiliateStatus) {
    case 'pending':
      return 'pending_review';
    case 'under_review':
      return 'under_review';
    case 'active':
      return 'approved';
    case 'rejected':
      return 'rejected';
    default:
      return 'pending_review';
  }
}