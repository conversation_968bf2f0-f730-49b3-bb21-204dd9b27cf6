import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
/**
 * PATCH /api/super-admin/enterprise/applications/[applicationId]/status
 * Update the status of an enterprise application
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { applicationId: string } }
) {
  try {
    // Create Supabase client with cookies for authentication
    const cookieStore = cookies()
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const { status } = await request.json()

    // Validate status
    const validStatuses = ['pending_review', 'under_review', 'approved', 'rejected']
    if (!status || !validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status' },
        { status: 400 }
      )
    }

    // Use service role for admin operations
    const adminSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Update application status
    const { data, error } = await adminSupabase
      .from('enterprise_applications')
      .update({
        status,
        reviewed_at: new Date().toISOString(),
        reviewed_by: user.id
      })
      .eq('id', params.applicationId)
      .select()
      .single()

    if (error) {
      console.error('Error updating application status:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update status' },
        { status: 500 }
      )
    }

    // If approved, create enterprise client
    if (status === 'approved') {
      try {
        // Generate API key (simple implementation)
        const apiKey = `tf_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`

        // Get a tenant ID
        const { data: tenant } = await adminSupabase.from('tenants').select('id').limit(1).single()

        if (tenant) {
          // Create enterprise client
          const { error: clientError } = await adminSupabase
            .from('enterprise_clients')
            .insert({
              name: data.company_name,
              api_key: apiKey,
              organization_id: tenant.id,
              rate_limit: 1000, // Default rate limit
              status: 'active',
              created_by: user.id
            })

          if (clientError) {
            console.error('Error creating enterprise client:', clientError)
          }
        }
      } catch (clientCreationError) {
        console.error('Error in client creation process:', clientCreationError)
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        id: data.id,
        status: data.status,
        reviewed_at: data.reviewed_at
      }
    })

  } catch (error) {
    console.error('Error in application status update API:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
