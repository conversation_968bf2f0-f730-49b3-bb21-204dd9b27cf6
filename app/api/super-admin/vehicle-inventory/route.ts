import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

/**
 * GET /api/super-admin/vehicle-inventory
 * Get vehicle inventory for super admin view
 */
export async function GET(request: NextRequest) {
  try {
    // Use standardized auth pattern
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const { searchParams } = new URL(request.url);
    const affiliateId = searchParams.get('affiliateId');

    let query = supabase
      .from('vehicles')
      .select(`
        *,
        affiliate_companies (
          id,
          name,
          city,
          state
        )
      `)
      .order('created_at', { ascending: false });

    if (affiliateId) {
      query = query.eq('affiliate_company_id', affiliateId);
    }

    const { data: vehicles, error } = await query;

    if (error) {
      console.error('Error fetching vehicle inventory:', error);
      return NextResponse.json({ error: 'Failed to fetch vehicle inventory' }, { status: 500 });
    }

    // Group vehicles by type for summary
    const vehicleSummary = vehicles?.reduce((acc: any, vehicle: any) => {
      const type = vehicle.vehicle_type || 'unknown';
      if (!acc[type]) {
        acc[type] = { count: 0, available: 0 };
      }
      acc[type].count++;
      if (vehicle.status === 'available') {
        acc[type].available++;
      }
      return acc;
    }, {});

    return NextResponse.json({
      success: true,
      vehicles: vehicles || [],
      summary: vehicleSummary || {},
      total_count: vehicles?.length || 0
    });

  } catch (error: any) {
    console.error('Vehicle inventory API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * POST /api/super-admin/vehicle-inventory
 * Add new vehicle to inventory
 */
export async function POST(request: NextRequest) {
  try {
    // Use standardized auth pattern
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const body = await request.json();
    
    const { 
      affiliate_company_id,
      vehicle_type,
      make,
      model,
      year,
      license_plate,
      capacity,
      status = 'available'
    } = body;

    if (!affiliate_company_id || !vehicle_type) {
      return NextResponse.json(
        { error: 'affiliate_company_id and vehicle_type are required' },
        { status: 400 }
      );
    }

    // Create vehicle
    const { data: vehicle, error } = await supabase
      .from('vehicles')
      .insert({
        affiliate_company_id,
        vehicle_type,
        make,
        model,
        year,
        license_plate,
        capacity,
        status
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating vehicle:', error);
      return NextResponse.json({ error: 'Failed to create vehicle' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      vehicle
    });

  } catch (error: any) {
    console.error('Vehicle creation API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}