import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';

// PRODUCTION QUOTES API - Enterprise-grade authentication
export async function GET(request: NextRequest) {
  try {
    console.log("Production super-admin quotes API - Starting request");
    
    // Authenticate request with super admin access
    const authResult = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { user } = authResult;
    console.log(`Authenticated user: ${user!.email} (${user!.role})`);
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get organization filter from query params
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('org_id');
    
    console.log('Filtering by organization ID:', orgId);
    
    // Organization access validation (simplified for super admin)
    if (orgId && orgId !== 'all') {
      console.log(`Filtering by organization: ${orgId}`);
    }

    // Build query with correct column names from database
    let query = supabase
      .from('quotes')
      .select(`
        id,
        status,
        pickup_address,
        dropoff_address,
        pickup_date,
        pickup_time,
        passenger_count,
        luggage_count,
        total_price,
        currency,
        reference_number,
        customer_name,
        customer_email,
        customer_phone,
        service_type,
        vehicle_type,
        special_requirements,
        estimated_distance,
        estimated_duration,
        internal_notes,
        customer_notes,
        created_at,
        updated_at,
        organization_id
      `)
      .order('created_at', { ascending: false })
      .limit(50);

    // Apply organization filter if provided and not "all"
    if (orgId && orgId !== 'all') {
      query = query.eq('organization_id', orgId);
    } else if (user!.role !== 'SUPER_ADMIN') {
      // Non-super admin users can only see their organization's data
      query = query.eq('organization_id', user!.organization_id);
    }

    const { data: quotes, error: quotesError } = await query;

    if (quotesError) {
      console.error('Error fetching quotes:', quotesError);
      return NextResponse.json(
        { error: 'Failed to fetch quotes' },
        { status: 500 }
      );
    }

    console.log(`Found ${quotes?.length || 0} quotes`);
    
    // Map database fields to QuoteRowData format expected by frontend
    const mappedQuotes = quotes?.map(quote => ({
      id: quote.id,
      reference_number: quote.reference_number,
      customer_id: quote.customer_name || '', // Use customer_name as fallback for customer_id
      service_type: quote.service_type || 'point', // Default to 'point' if not specified
      vehicle_type: quote.vehicle_type || '',
      pickup_location: quote.pickup_address || '',
      dropoff_location: quote.dropoff_address || '',
      date: quote.pickup_date || '',
      time: quote.pickup_time || '',
      duration: quote.estimated_duration || '',
      distance: quote.estimated_distance || '',
      status: quote.status || 'pending',
      passenger_count: quote.passenger_count,
      luggage_count: quote.luggage_count,
      special_requests: quote.special_requirements || '',
      priority: 'medium', // Default since priority column doesn't exist
      total_amount: quote.total_price,
      created_at: quote.created_at,
      updated_at: quote.updated_at,
      customer: {
        id: quote.customer_name || '',
        email: quote.customer_email || '',
        full_name: quote.customer_name || '',
        phone: quote.customer_phone || '',
        company_name: ''
      },
      duration_hours: null, // Not available in current schema
      flight_number: '', // Not available in current schema
      notes: quote.internal_notes || quote.customer_notes || '',
      // Add default values for other required fields
      is_multi_day: false,
      is_return_trip: false,
      car_seats_needed: false,
      infant_seats: null,
      toddler_seats: null,
      booster_seats: null,
      intermediate_stops: [],
      timeline: [],
      affiliate_count: 0,
      affiliate_responses: [],
      rate_proposals: [],
      is_vip: false,
      communications: []
    })) || [];
    
    return NextResponse.json({
      success: true,
      quotes: mappedQuotes,
      total: mappedQuotes.length,
      filtered_by_org: orgId && orgId !== 'all' ? orgId : null,
      user_context: {
        role: user!.role,
        userId: user!.id,
        email: user!.email
      }
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/quotes-simple:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}