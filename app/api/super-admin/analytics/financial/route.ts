import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * GET /api/super-admin/analytics/financial
 * Get comprehensive financial analytics and revenue metrics
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is super admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];
    if (!userRoles.includes('SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');
    const from = searchParams.get('from');
    const to = searchParams.get('to');

    // Build date filter
    const startDate = from || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
    const endDate = to || new Date().toISOString();

    // Get monthly revenue data
    const { data: monthlyRevenue } = await supabase
      .rpc('get_monthly_revenue', {
        p_start_date: startDate,
        p_end_date: endDate,
        p_organization_id: organizationId && organizationId !== 'all' ? organizationId : null
      });

    // Get revenue by service type
    const { data: revenueByService } = await supabase
      .from('quotes')
      .select('service_type, total_amount, admin_fee')
      .eq('status', 'accepted')
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .match(organizationId && organizationId !== 'all' ? { organization_id: organizationId } : {});

    const serviceTypeRevenue = revenueByService?.reduce((acc: any, quote: any) => {
      const serviceType = quote.service_type || 'Unknown';
      if (!acc[serviceType]) {
        acc[serviceType] = { revenue: 0, commission: 0, count: 0 };
      }
      acc[serviceType].revenue += quote.total_amount || 0;
      acc[serviceType].commission += quote.admin_fee || 0;
      acc[serviceType].count += 1;
      return acc;
    }, {}) || {};

    // Get top revenue generating tenants (if super admin viewing all)
    let topTenants = [];
    if (!organizationId || organizationId === 'all') {
      const { data: tenantRevenue } = await supabase
        .from('quotes')
        .select(`
          organization_id,
          total_amount,
          admin_fee,
          tenants!quotes_organization_id_fkey(name)
        `)
        .eq('status', 'accepted')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      const tenantRevenueMap = tenantRevenue?.reduce((acc: any, quote: any) => {
        const organizationId = quote.organization_id;
        const tenantName = quote.tenants?.name || 'Unknown';
        if (!acc[organizationId]) {
          acc[organizationId] = { 
            id: organizationId, 
            name: tenantName, 
            revenue: 0, 
            commission: 0, 
            quotes: 0 
          };
        }
        acc[organizationId].revenue += quote.total_amount || 0;
        acc[organizationId].commission += quote.admin_fee || 0;
        acc[organizationId].quotes += 1;
        return acc;
      }, {}) || {};

      topTenants = Object.values(tenantRevenueMap)
        .sort((a: any, b: any) => b.revenue - a.revenue)
        .slice(0, 10);
    }

    // Calculate summary metrics
    const totalRevenue = monthlyRevenue?.reduce((sum: number, month: any) => 
      sum + (month.total_revenue || 0), 0) || 0;
    const totalCommission = monthlyRevenue?.reduce((sum: number, month: any) => 
      sum + (month.commission_revenue || 0), 0) || 0;
    const totalQuotes = monthlyRevenue?.reduce((sum: number, month: any) => 
      sum + (month.total_quotes || 0), 0) || 0;
    const averageQuoteValue = totalQuotes > 0 ? totalRevenue / totalQuotes : 0;
    const commissionRate = totalRevenue > 0 ? (totalCommission / totalRevenue) * 100 : 0;

    // Get payment method distribution from quotes
    const paymentMethods = quotes?.reduce((acc: any, quote: any) => {
      const method = quote.payment_method || 'Unknown';
      acc[method] = (acc[method] || 0) + 1;
      return acc;
    }, {}) || {};

    // Convert to percentages
    const totalPayments = Object.values(paymentMethods).reduce((sum: any, count: any) => sum + count, 0);
    const paymentMethodDistribution = Object.entries(paymentMethods).reduce((acc: any, [method, count]: any) => {
      acc[method] = totalPayments > 0 ? Math.round((count / totalPayments) * 100) : 0;
      return acc;
    }, {});

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalRevenue: Math.round(totalRevenue),
          totalCommission: Math.round(totalCommission),
          totalQuotes,
          averageQuoteValue: Math.round(averageQuoteValue),
          commissionRate: Math.round(commissionRate * 100) / 100,
          growthRate: calculateGrowthRate(monthlyRevenue)
        },
        monthlyRevenue: monthlyRevenue || [],
        serviceTypeRevenue,
        topTenants,
        paymentMethodDistribution,
        dateRange: { startDate, endDate }
      }
    });

  } catch (error) {
    console.error('Financial analytics error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch financial analytics' },
      { status: 500 }
    );
  }
}

function calculateGrowthRate(monthlyData: any[]): number {
  if (!monthlyData || monthlyData.length < 2) return 0;
  
  const sortedData = monthlyData.sort((a, b) => a.month_year.localeCompare(b.month_year));
  const currentMonth = sortedData[sortedData.length - 1];
  const previousMonth = sortedData[sortedData.length - 2];
  
  if (!currentMonth || !previousMonth || previousMonth.total_revenue === 0) return 0;
  
  return Math.round(((currentMonth.total_revenue - previousMonth.total_revenue) / previousMonth.total_revenue) * 100 * 100) / 100;
}
