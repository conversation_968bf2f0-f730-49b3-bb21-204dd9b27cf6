import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * GET /api/super-admin/analytics/affiliates
 * Get comprehensive affiliate performance analytics
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is super admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];
    if (!userRoles.includes('SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');
    const from = searchParams.get('from');
    const to = searchParams.get('to');

    // Build date filter
    const startDate = from || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
    const endDate = to || new Date().toISOString();

    // Get affiliate performance report
    const { data: affiliatePerformance } = await supabase
      .rpc('get_affiliate_performance_report', {
        p_start_date: startDate,
        p_end_date: endDate,
        p_organization_id: organizationId && organizationId !== 'all' ? organizationId : null
      });

    // Get affiliate tier distribution
    const { data: affiliateTiers } = await supabase
      .from('affiliate_companies')
      .select('tier')
      .match(organizationId && organizationId !== 'all' ? { organization_id: organizationId } : {});

    const tierDistribution = affiliateTiers?.reduce((acc: any, affiliate: any) => {
      acc[affiliate.tier || 'Standard'] = (acc[affiliate.tier || 'Standard'] || 0) + 1;
      return acc;
    }, {}) || {};

    // Get response time trends
    const { data: responseTimeTrends } = await supabase
      .rpc('get_response_time_trends', {
        p_start_date: startDate,
        p_end_date: endDate,
        p_organization_id: organizationId && organizationId !== 'all' ? organizationId : null
      });

    // Get affiliate status distribution
    const { data: affiliateStatuses } = await supabase
      .from('affiliate_companies')
      .select('status')
      .match(organizationId && organizationId !== 'all' ? { organization_id: organizationId } : {});

    const statusDistribution = affiliateStatuses?.reduce((acc: any, affiliate: any) => {
      acc[affiliate.status] = (acc[affiliate.status] || 0) + 1;
      return acc;
    }, {}) || {};

    // Calculate summary metrics
    const totalAffiliates = affiliatePerformance?.length || 0;
    const activeAffiliates = statusDistribution.active || 0;
    const averageResponseTime = affiliatePerformance?.reduce((sum: number, affiliate: any) => 
      sum + (affiliate.average_response_time || 0), 0) / Math.max(totalAffiliates, 1) || 0;
    const averageConversionRate = affiliatePerformance?.reduce((sum: number, affiliate: any) => 
      sum + (affiliate.conversion_rate || 0), 0) / Math.max(totalAffiliates, 1) || 0;

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalAffiliates,
          activeAffiliates,
          averageResponseTime: Math.round(averageResponseTime * 100) / 100,
          averageConversionRate: Math.round(averageConversionRate * 100) / 100,
          utilizationRate: totalAffiliates > 0 ? Math.round((activeAffiliates / totalAffiliates) * 100) : 0
        },
        affiliatePerformance: affiliatePerformance || [],
        tierDistribution,
        statusDistribution,
        responseTimeTrends: responseTimeTrends || [],
        dateRange: { startDate, endDate }
      }
    });

  } catch (error) {
    console.error('Affiliate analytics error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch affiliate analytics' },
      { status: 500 }
    );
  }
}
