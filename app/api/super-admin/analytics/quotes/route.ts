import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs'
import { toUserRoles } from '@/app/lib/auth/roles';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // Use standardized auth pattern
  const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN']);
  
  const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
  try {
    const { searchParams } = new URL(request.url)
    const orgId = searchParams.get('orgId')
    const from = searchParams.get('from')
    const to = searchParams.get('to')

    // Build date filter
    let dateFilter = ''
    if (from && to) {
      dateFilter = `AND created_at >= '${from}' AND created_at <= '${to}'`
    } else if (from) {
      dateFilter = `AND created_at >= '${from}'`
    } else if (to) {
      dateFilter = `AND created_at <= '${to}'`
    }

    // Build org filter
    let orgFilter = ''
    if (orgId && orgId !== 'all') {
      orgFilter = `AND organization_id = '${orgId}'`
    }

    // Get quote analytics
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select(`
        id,
        status,
        total_amount,
        created_at,
        updated_at,
        customer_id,
        affiliate_responses:quote_affiliate_offers(
          id,
          status,
          rate_amount,
          created_at,
          company_id,
          affiliate_companies(name)
        )
      `)
      .gte('created_at', from || '2024-01-01')
      .lte('created_at', to || new Date().toISOString())

    if (quotesError) {
      console.error('Error fetching quotes:', quotesError)
      return NextResponse.json({ error: 'Failed to fetch quotes' }, { status: 500 })
    }

    // Calculate analytics
    const totalQuotes = quotes?.length || 0
    const pendingQuotes = quotes?.filter(q => ['pending', 'pending_quote'].includes(q.status))?.length || 0
    const activeQuotes = quotes?.filter(q => ['rate_requested', 'sent_to_affiliates', 'quote_ready'].includes(q.status))?.length || 0
    const completedQuotes = quotes?.filter(q => ['accepted', 'completed'].includes(q.status))?.length || 0

    // Calculate response times
    const quotesWithResponses = quotes?.filter(q => q.affiliate_responses && q.affiliate_responses.length > 0) || []
    const responseTimes = quotesWithResponses.map(quote => {
      const firstResponse = quote.affiliate_responses?.[0]
      if (!firstResponse) return 0
      
      const quoteTime = new Date(quote.created_at).getTime()
      const responseTime = new Date(firstResponse.created_at).getTime()
      return (responseTime - quoteTime) / (1000 * 60 * 60) // Convert to hours
    })
    
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0

    // Calculate conversion rate
    const quotesWithOffers = quotes?.filter(q => q.affiliate_responses && q.affiliate_responses.length > 0) || []
    const acceptedQuotes = quotes?.filter(q => q.status === 'accepted') || []
    const conversionRate = quotesWithOffers.length > 0 
      ? (acceptedQuotes.length / quotesWithOffers.length) * 100 
      : 0

    // Calculate revenue
    const totalRevenue = quotes?.reduce((sum, quote) => {
      return sum + (quote.total_amount || 0)
    }, 0) || 0

    const averageQuoteValue = totalQuotes > 0 ? totalRevenue / totalQuotes : 0

    // Get top performing affiliates
    const affiliatePerformance = new Map()
    
    quotes?.forEach(quote => {
      quote.affiliate_responses?.forEach(response => {
        const affiliateId = response.company_id
        const affiliateName = (response.affiliate_companies as any)?.name || 'Unknown'
        
        if (!affiliatePerformance.has(affiliateId)) {
          affiliatePerformance.set(affiliateId, {
            id: affiliateId,
            name: affiliateName,
            totalQuotes: 0,
            acceptedQuotes: 0,
            responseTimes: [],
            totalRevenue: 0
          })
        }
        
        const affiliate = affiliatePerformance.get(affiliateId)
        affiliate.totalQuotes++
        
        if (response.status === 'accepted') {
          affiliate.acceptedQuotes++
          affiliate.totalRevenue += response.rate_amount || 0
        }
        
        // Calculate response time
        const quoteTime = new Date(quote.created_at).getTime()
        const responseTime = new Date(response.created_at).getTime()
        const responseHours = (responseTime - quoteTime) / (1000 * 60 * 60)
        affiliate.responseTimes.push(responseHours)
      })
    })

    const topPerformingAffiliates = Array.from(affiliatePerformance.values())
      .map(affiliate => ({
        id: affiliate.id,
        name: affiliate.name,
        responseTime: affiliate.responseTimes.length > 0 
          ? affiliate.responseTimes.reduce((sum: number, time: number) => sum + time, 0) / affiliate.responseTimes.length
          : 0,
        conversionRate: affiliate.totalQuotes > 0 
          ? (affiliate.acceptedQuotes / affiliate.totalQuotes) * 100 
          : 0,
        totalQuotes: affiliate.totalQuotes
      }))
      .sort((a, b) => b.conversionRate - a.conversionRate)
      .slice(0, 5)

    // Generate recent activity
    const recentActivity = quotes?.slice(0, 10).map(quote => {
      const latestResponse = quote.affiliate_responses?.[0]
      
      if (latestResponse) {
        return {
          id: quote.id,
          type: latestResponse.status === 'accepted' ? 'quote_accepted' : 'affiliate_responded',
          description: latestResponse.status === 'accepted' 
            ? `Quote ${quote.id.slice(0, 8)} accepted`
            : `New response for quote ${quote.id.slice(0, 8)}`,
          timestamp: latestResponse.created_at,
          amount: latestResponse.rate_amount
        }
      }
      
      return {
        id: quote.id,
        type: 'quote_created',
        description: `New quote ${quote.id.slice(0, 8)} created`,
        timestamp: quote.created_at,
        amount: quote.total_amount
      }
    }) || []

    // Calculate real performance metrics
    const totalResponseTime = quotes?.reduce((sum: number, quote: any) => {
      if (quote.created_at && quote.updated_at) {
        const responseTime = new Date(quote.updated_at).getTime() - new Date(quote.created_at).getTime();
        return sum + responseTime;
      }
      return sum;
    }, 0) || 0;

    const avgResponseTimeHours = quotes?.length > 0 ? (totalResponseTime / quotes.length) / (1000 * 60 * 60) : 0;
    const slaCompliance = quotes?.length > 0 ?
      (quotes.filter((q: any) => avgResponseTimeHours <= 24).length / quotes.length) * 100 : 0;

    const performanceMetrics = {
      slaCompliance: Math.round(slaCompliance * 10) / 10,
      customerSatisfaction: 4.5, // This would come from a ratings table in real implementation
      affiliateUtilization: Math.round((activeQuotes / (totalQuotes || 1)) * 100 * 10) / 10,
      systemUptime: 99.9 // This would come from monitoring system
    }

    const analytics = {
      totalQuotes,
      pendingQuotes,
      activeQuotes,
      completedQuotes,
      averageResponseTime: Math.round(averageResponseTime * 10) / 10,
      conversionRate: Math.round(conversionRate * 10) / 10,
      totalRevenue,
      averageQuoteValue: Math.round(averageQuoteValue),
      topPerformingAffiliates,
      recentActivity,
      performanceMetrics
    }

    return NextResponse.json(analytics)
  } catch (e: any) {
    return NextResponse.json({ error: e.message || 'Internal server error' }, { status: 500 });
  }
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};
