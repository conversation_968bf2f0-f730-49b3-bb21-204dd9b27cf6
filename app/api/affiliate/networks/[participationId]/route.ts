/**
 * GUG-30: Affiliate Network Management - Network Participation Management API
 * 
 * Handles individual network participation updates and management
 */

import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { NetworkManagementService } from '@/app/lib/services/network-management';

export const runtime = 'nodejs';

export async function PUT(
  request: NextRequest,
  { params }: { params: { participationId: string } }
) {
  try {
    const authResult = await authenticateApiRequestWithRoles([
      'AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN'
    ]);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 500 });
    }

    const body = await request.json();
    const {
      priority_level,
      auto_accept_quotes,
      max_concurrent_quotes,
      service_radius_km,
      available_hours,
      participation_status
    } = body;

    const networkService = new NetworkManagementService();
    
    const participation = await networkService.updateParticipationSettings(
      params.participationId,
      {
        priority_level,
        auto_accept_quotes,
        max_concurrent_quotes,
        service_radius_km,
        available_hours,
        participation_status
      }
    );

    return NextResponse.json({
      success: true,
      participation,
      message: 'Network participation updated successfully'
    });

  } catch (error) {
    console.error('Error updating network participation:', error);
    return NextResponse.json({ 
      error: 'Failed to update network participation',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { participationId: string } }
) {
  try {
    const authResult = await authenticateApiRequestWithRoles([
      'AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN'
    ]);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 500 });
    }

    const { searchParams } = new URL(request.url);
    const affiliateCompanyId = searchParams.get('company_id');
    const networkId = searchParams.get('network_id');

    if (!affiliateCompanyId || !networkId) {
      return NextResponse.json({
        error: 'Company ID and Network ID are required'
      }, { status: 400 });
    }

    const networkService = new NetworkManagementService();
    await networkService.leaveNetwork(affiliateCompanyId, networkId);

    return NextResponse.json({
      success: true,
      message: 'Successfully left network'
    });

  } catch (error) {
    console.error('Error leaving network:', error);
    return NextResponse.json({ 
      error: 'Failed to leave network',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}