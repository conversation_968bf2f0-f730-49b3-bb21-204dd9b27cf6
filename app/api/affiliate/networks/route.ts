/**
 * GUG-30: Affiliate Network Management - Affiliate Networks API
 * 
 * Handles affiliate network participation operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { NetworkManagementService } from '@/app/lib/services/network-management';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequestWithRoles([
      'AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN'
    ]);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 500 });
    }

    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('company_id');

    if (!companyId) {
      return NextResponse.json({
        error: 'Company ID is required'
      }, { status: 400 });
    }

    const networkService = new NetworkManagementService();
    const participations = await networkService.getAffiliateNetworks(companyId);

    return NextResponse.json({
      success: true,
      participations
    });

  } catch (error) {
    console.error('Error fetching affiliate networks:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch affiliate networks',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateApiRequestWithRoles([
      'AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN'
    ]);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 500 });
    }

    const body = await request.json();
    const {
      affiliate_company_id,
      network_id,
      priority_level,
      auto_accept_quotes,
      max_concurrent_quotes,
      service_radius_km,
      available_hours
    } = body;

    // Validate required fields
    if (!affiliate_company_id || !network_id) {
      return NextResponse.json({
        error: 'Missing required fields: affiliate_company_id and network_id are required'
      }, { status: 400 });
    }

    // For non-super-admin users, verify they have access to this company
    if (!authResult.context.roles.includes('SUPER_ADMIN')) {
      // Add company access verification logic here
      // This would check if the user belongs to the affiliate company
    }

    const networkService = new NetworkManagementService();
    
    const participation = await networkService.joinNetwork(
      affiliate_company_id,
      network_id,
      {
        priority_level,
        auto_accept_quotes,
        max_concurrent_quotes,
        service_radius_km,
        available_hours
      }
    );

    return NextResponse.json({
      success: true,
      participation,
      message: 'Successfully joined network'
    });

  } catch (error) {
    console.error('Error joining network:', error);
    return NextResponse.json({ 
      error: 'Failed to join network',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}