import { createServerClient, type CookieOptions } from '@supabase/ssr'

export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import type { Database } from '@/lib/database.types'

const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

export async function GET(request: Request) {
  try {
    console.log('[api/affiliate/direct-quote-access] Starting request')
    // Get query parameters
    const url = new URL(request.url)
    const quoteIds = url.searchParams.get('quoteIds')
    
    if (!quoteIds) {
      console.log('[api/affiliate/direct-quote-access] No quoteIds provided')
      return NextResponse.json({ quotes: [] })
    }

    // Parse the quote IDs
    const quoteIdArray = quoteIds.split(',')
    console.log(`[api/affiliate/direct-quote-access] Fetching ${quoteIdArray.length} quotes directly`)

    const supabase = createSupabaseClient()
    
    // Get the user's session for authentication
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      console.error('[api/affiliate/direct-quote-access] No session found')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log(`[api/affiliate/direct-quote-access] User authenticated: ${session.user.id}`)

    // Get the user's affiliate companies 
    const { data: companies } = await supabase
      .from('affiliate_companies')
      .select('id')
      .eq('owner_id', session.user.id)

    if (!companies || companies.length === 0) {
      console.error('[api/affiliate/direct-quote-access] No affiliate companies found for user')
      return NextResponse.json({ error: 'No affiliated companies found' }, { status: 403 })
    }

    const companyIds = companies.map(company => company.id)
    console.log(`[api/affiliate/direct-quote-access] Found ${companyIds.length} affiliated companies`)

    // Use direct SQL to bypass RLS and fetch quotes linked to the companies via quote_offers
    const { data: quotes, error: quotesError } = await supabase.rpc(
      'get_quotes_for_affiliate' as any, // Cast to any to bypass type error for now
      {
        quote_ids: quoteIdArray,
        company_ids: companyIds
      }
    )

    if (quotesError) {
      console.error('[api/affiliate/direct-quote-access] Error fetching quotes:', quotesError)
      return NextResponse.json({ error: 'Error fetching quotes' }, { status: 500 })
    }

    const numQuotesFetched = Array.isArray(quotes) ? quotes.length : 0;
    console.log(`[api/affiliate/direct-quote-access] Successfully fetched ${numQuotesFetched} quotes`)
    
    return NextResponse.json({ quotes: quotes || [] })
  } catch (error) {
    console.error('[api/affiliate/direct-quote-access] Error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
} 