import { NextResponse } from "next/server"

export const runtime = 'nodejs'
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }

    // Get the affiliate company for the current user through profiles
    const { data: affiliateCompany, error: affiliateError } = await supabase
      .from('profiles')
      .select(`
        id,
        affiliate_companies!owner_id (
          id
        )
      `)
      .eq('id', user.id)
      .single()

    if (affiliateError) {
      console.error('Error fetching affiliate company:', affiliateError)
      return NextResponse.json(
        { error: 'Error fetching affiliate company' },
        { status: 500 }
      )
    }

    if (!affiliateCompany?.affiliate_companies?.[0]) {
      console.error('No affiliate company found for user:', user.id)
      return NextResponse.json(
        { error: 'No affiliate company found' },
        { status: 404 }
      )
    }

    const affiliateCompanyId = affiliateCompany.affiliate_companies[0].id

    // Get the quote offers for the affiliate company
    const { data: quoteOffers, error: offersError } = await supabase
      .from('quote_offers')
      .select(`
        *,
        quotes (
          id,
          reference_number,
          customer_name,
          service_type,
          vehicle_type,
          pickup_location,
          dropoff_location,
          date,
          time,
          duration,
          distance,
          status,
          passenger_count,
          luggage_count,
          priority,
          total_amount,
          special_requests,
          base_rate,
          service_fee,
          expiry_time,
          contact_name,
          contact_email,
          contact_phone,
          city,
          duration_hours,
          is_multi_day,
          flight_number,
          is_return_trip,
          return_date,
          return_time,
          return_flight_number,
          car_seats_needed,
          infant_seats,
          toddler_seats,
          booster_seats,
          intermediate_stops
        )
      `)
      .eq('company_id', affiliateCompanyId)
      .order('created_at', { ascending: false })
    
    if (offersError) {
      console.error('Error fetching quote offers:', offersError)
      return NextResponse.json(
        { error: 'Error fetching quote offers' },
        { status: 500 }
      )
    }

    // For each quote, fetch related trips
    if (quoteOffers && quoteOffers.length > 0) {
      // Extract all quote IDs
      const quoteIds = quoteOffers
        .map(offer => {
          // Extract quote IDs from either the quote_id field or from the quotes object/array
          let ids = [offer.quote_id];
          
          // Handle if quotes is an array or object
          if (Array.isArray(offer.quotes)) {
            ids = [...ids, ...offer.quotes.map((q: any) => q.id).filter(Boolean)];
          } else if (offer.quotes && typeof offer.quotes === 'object' && offer.quotes.id) {
            ids.push(offer.quotes.id);
          }
          
          return ids;
        })
        .flat()
        .filter(Boolean);

      // Remove duplicates from quote IDs
      const uniqueQuoteIds = Array.from(new Set(quoteIds));
      console.log('Quote IDs for trip lookup:', uniqueQuoteIds);

      if (uniqueQuoteIds.length > 0) {
        try {
          // Fetch all trips for these quotes
          const { data: trips, error: tripsError } = await supabase
            .from('trips')
            .select(`
              id,
              quote_id,
              status,
              created_at,
              updated_at,
              driver_id,
              company_id,
              estimated_distance,
              estimated_duration,
              scheduled_pickup_date,
              scheduled_pickup_time,
              estimated_amount,
              final_amount,
              trip_notes,
              reference_number
            `)
            .in('quote_id', uniqueQuoteIds);

          if (tripsError) {
            console.error('Error fetching trips:', tripsError);
          } else if (trips && trips.length > 0) {
            console.log(`Found ${trips.length} trips for ${uniqueQuoteIds.length} quotes`);
            console.log('First trip sample:', JSON.stringify(trips[0], null, 2));
            
            // Group trips by quote_id
            const tripsByQuoteId = trips.reduce((acc: Record<string, any[]>, trip: any) => {
              if (!acc[trip.quote_id]) {
                acc[trip.quote_id] = [];
              }
              acc[trip.quote_id].push(trip);
              return acc;
            }, {});

            console.log('Trips grouped by quote ID:', Object.keys(tripsByQuoteId));

            // Add trips to each quote
            quoteOffers.forEach(offer => {
              // Try different ways to get the quote ID
              const possibleQuoteIds = [offer.quote_id];
              
              if (typeof offer.quotes === 'object' && !Array.isArray(offer.quotes) && offer.quotes?.id) {
                possibleQuoteIds.push(offer.quotes.id);
                // Add trips to the quote object
                offer.quotes.trips = [];
                
                for (const qid of possibleQuoteIds) {
                  if (tripsByQuoteId[qid] && tripsByQuoteId[qid].length > 0) {
                    offer.quotes.trips = tripsByQuoteId[qid];
                    console.log(`Added ${offer.quotes.trips.length} trips to quote ${qid}`);
                    break; // Found trips, no need to check other IDs
                  }
                }
              } 
              else if (Array.isArray(offer.quotes) && offer.quotes.length > 0) {
                // Handle array of quotes
                offer.quotes.forEach((quote: any) => {
                  if (quote.id) {
                    possibleQuoteIds.push(quote.id);
                    quote.trips = [];
                    
                    for (const qid of possibleQuoteIds) {
                      if (tripsByQuoteId[qid] && tripsByQuoteId[qid].length > 0) {
                        quote.trips = tripsByQuoteId[qid];
                        console.log(`Added ${quote.trips.length} trips to quote ${qid} in array`);
                        break; // Found trips, no need to check other IDs
                      }
                    }
                  }
                });
              }
            });
          } else {
            console.log('No trips found for these quotes');
          }
        } catch (error) {
          console.error('Error in trips processing:', error);
        }
      }
    }

    return NextResponse.json(quoteOffers)
  } catch (error) {
    console.error('Error fetching quote offers:', error)
    return NextResponse.json(
      { error: 'Error fetching quote offers' },
      { status: 500 }
    )
  }
} 