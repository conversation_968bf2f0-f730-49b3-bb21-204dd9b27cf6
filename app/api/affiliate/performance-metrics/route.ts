import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/lib/database.types'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseClient()
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Verify user has access to this company
    const { data: userCompany, error: accessError } = await supabase
      .from('affiliate_users')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', companyId)
      .single()

    if (accessError || !userCompany) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Performance metrics update would go here
    // const { error: updateError } = await supabase
    //   .rpc('update_affiliate_performance_metrics', {
    //     p_company_id: companyId
    //   })

    // Get performance metrics
    const { data: metrics, error: metricsError } = await supabase
      .from('affiliate_analytics')
      .select('*')
      .eq('company_id', companyId)
      .single()

    if (metricsError && metricsError.code !== 'PGRST116') {
      console.error('Error fetching performance metrics:', metricsError)
      return NextResponse.json(
        { error: 'Failed to fetch performance metrics' },
        { status: 500 }
      )
    }

    // If no metrics exist, create default ones
    if (!metrics) {
      const defaultMetrics = {
        company_id: companyId,
        date: new Date().toISOString().split('T')[0],
        average_rating: 0,
        completion_rate: 0,
        total_trips: 0,
        total_revenue: 0,
        peak_hours: {},
        popular_routes: {},
        service_distribution: {}
      }

      const { data: newMetrics, error: insertError } = await supabase
        .from('affiliate_analytics')
        .insert(defaultMetrics)
        .select()
        .single()

      if (insertError) {
        console.error('Error creating default metrics:', insertError)
        return NextResponse.json(
          { error: 'Failed to create performance metrics' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        metrics: newMetrics,
        tierInfo: {
          currentTier: 'Standard',
          tierScore: 0,
          nextTier: 'Premium',
          nextThreshold: 75,
          metricsBreakdown: {
            complianceScore: 0,
            customerRating: 0,
            responseTime: 0,
            arrivalPerformance: 0,
            totalTrips: 0
          }
        }
      })
    }

    // Calculate tier info based on available fields
    const tierInfo = {
      currentTier: 'Standard', // Default tier
      tierScore: 0,
      nextTier: 'Premium',
      nextThreshold: 75,
      metricsBreakdown: {
        complianceScore: 0,
        customerRating: metrics.average_rating || 0,
        responseTime: 0,
        arrivalPerformance: metrics.completion_rate || 0,
        totalTrips: metrics.total_trips || 0
      }
    }

    return NextResponse.json({
      metrics,
      tierInfo
    })

  } catch (error) {
    console.error('Error in performance metrics API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseClient()
    const body = await request.json()
    const { companyId, metricType, value } = body

    if (!companyId || !metricType || value === undefined) {
      return NextResponse.json(
        { error: 'Company ID, metric type, and value are required' },
        { status: 400 }
      )
    }

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Verify user has access to this company
    const { data: userCompany, error: accessError } = await supabase
      .from('affiliate_users')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', companyId)
      .single()

    if (accessError || !userCompany) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Handle different metric types
    switch (metricType) {
      case 'quote_response':
        // Record quote response time (commented out - table doesn't exist)
        // const { error: responseError } = await supabase
        //   .from('quote_response_tracking')
        //   .insert({
        //     quote_id: value.quoteId,
        //     company_id: companyId,
        //     sent_at: value.sentAt,
        //     responded_at: value.respondedAt,
        //     response_time_minutes: value.responseTimeMinutes,
        //     response_type: value.responseType
        //   })
        break

      case 'driver_arrival':
        // Record driver arrival time (commented out - table doesn't exist)
        // const { error: arrivalError } = await supabase
        //   .from('driver_arrival_tracking')
        //   .insert({
        //     trip_id: value.tripId,
        //     driver_id: value.driverId,
        //     company_id: companyId,
        //     scheduled_arrival_time: value.scheduledTime,
        //     actual_arrival_time: value.actualTime,
        //     minutes_difference: value.minutesDifference,
        //     is_on_time: value.isOnTime,
        //     arrival_location_lat: value.lat,
        //     arrival_location_lng: value.lng
        //   })
        break

      default:
        return NextResponse.json(
          { error: 'Invalid metric type' },
          { status: 400 }
        )
    }

    // Performance metrics update would go here
    // const { error: updateError } = await supabase
    //   .rpc('update_affiliate_performance_metrics', {
    //     p_company_id: companyId
    //   })

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error in performance metrics POST API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
