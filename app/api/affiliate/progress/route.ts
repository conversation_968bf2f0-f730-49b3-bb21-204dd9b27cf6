import { NextRequest, NextResponse } from 'next/server';
import { createAuthenticatedSupabaseClient, requireAuth } from "@/lib/auth/server";
import { ProgressCalculationService } from '@/app/lib/services/progressCalculation';

export const runtime = 'nodejs'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user and supabase client
    const user = await requireAuth();
    const supabase = await createAuthenticatedSupabaseClient();

    // Get company ID from header
    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    if (!companyIdFromHeader) {
      return NextResponse.json({ error: 'X-Affiliate-Company-ID header is required' }, { status: 400 });
    }

    // Verify user has access to this company
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_users')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', companyIdFromHeader)
      .single();

    if (userCompanyError || !userCompany) {
      return NextResponse.json({ error: 'Access denied to this company' }, { status: 403 });
    }

    // Fetch company data
    const { data: companyData, error: companyError } = await supabase
      .from('affiliate_companies')
      .select('*')
      .eq('id', companyIdFromHeader)
      .single();

    if (companyError) {
      console.error('Error fetching company data:', companyError);
      return NextResponse.json({ error: 'Failed to fetch company data' }, { status: 500 });
    }

    // Parse JSON fields in company data
    const processedCompanyData = { ...companyData };
    try {
      if (companyData.airports_served && typeof companyData.airports_served === 'string') {
        processedCompanyData.airports_served = JSON.parse(companyData.airports_served);
      }
      if (companyData.languages_spoken && typeof companyData.languages_spoken === 'string') {
        processedCompanyData.languages_spoken = JSON.parse(companyData.languages_spoken);
      }
      if (companyData.dispatch_software && typeof companyData.dispatch_software === 'string') {
        processedCompanyData.dispatch_software = JSON.parse(companyData.dispatch_software);
      }
      if (companyData.cities_covered && typeof companyData.cities_covered === 'string') {
        processedCompanyData.cities_covered = JSON.parse(companyData.cities_covered);
      }
    } catch (parseError) {
      console.warn('Error parsing JSON fields:', parseError);
    }

    // Fetch vehicles data from the main vehicles table
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('vehicles')
      .select('*')
      .eq('affiliate_company_id', companyIdFromHeader);

    if (vehiclesError) {
      console.error('Error fetching vehicles:', vehiclesError);
      return NextResponse.json({ error: 'Failed to fetch vehicles' }, { status: 500 });
    }

    // Fetch rate cards
    const { data: rateCards, error: rateCardsError } = await supabase
      .from('rate_cards')
      .select('*')
      .eq('affiliate_company_id', companyIdFromHeader);

    if (rateCardsError) {
      console.error('Error fetching rate cards:', rateCardsError);
      return NextResponse.json({ error: 'Failed to fetch rate cards' }, { status: 500 });
    }

    // Calculate progress
    const progressData = ProgressCalculationService.calculateCompleteProgress(
      processedCompanyData,
      vehicles || [],
      rateCards || []
    );

    return NextResponse.json({
      success: true,
      data: progressData,
      meta: {
        companyId: companyIdFromHeader,
        vehiclesCount: (vehicles || []).length,
        rateCardsCount: (rateCards || []).length,
        calculatedAt: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('GET /api/affiliate/progress: Unexpected error', {
      error: error.message,
      stack: error.stack
    });
    return NextResponse.json({
      error: 'An unexpected error occurred while calculating progress'
    }, { status: 500 });
  }
}
