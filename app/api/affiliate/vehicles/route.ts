import { NextRequest, NextResponse } from "next/server";

export const runtime = 'nodejs'
import { cookies } from "next/headers"; // Import cookies
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { z } from "zod";

// Zod schema for vehicle creation (matches frontend schema + affiliate_company_id)
const vehicleCreateSchema = z.object({
  type: z.string().min(1, "Vehicle type is required"),
  make: z.string().min(1, "Make is required"),
  model: z.string().min(1, "Model is required"),
  year: z.coerce
    .number()
    .int()
    .gte(1900)
    .lte(new Date().getFullYear() + 2), // Coerce to number and validate range
  capacity: z.coerce
    .number()
    .int()
    .positive("Capacity must be a positive number"), // Coerce to number and validate
  license_plate: z.string().min(1, "License plate is required"),
  insurance_policy_number: z.string().optional(),
  status: z
    .enum(["active", "inactive", "maintenance", "deactivated"])
    .default("active"),
  images: z.array(z.string().url()).optional(),
  // affiliate_company_id will be derived from the authenticated user session
});

// Updated for standardized role architecture using migration 027
const VALID_COMPANY_ROLES_GET = ["MANAGER", "DISPATCHER", "DRIVER"];
const VALID_COMPANY_ROLES_POST = ["MANAGER"];

// Use the database function for role normalization
async function normalizeRole(supabase: any, role: string): Promise<string> {
  const { data, error } = await supabase.rpc('normalize_affiliate_role', { input_role: role });
  if (error) {
    console.warn('Role normalization failed, using uppercase:', error);
    return role.toUpperCase();
  }
  return data;
}

async function authorizeAffiliateAndGetCompany(
  supabase: ReturnType<typeof createServerClient>,
  userId: string,
  companyIdFromHeader: string | null,
  allowedRoles: string[]
) {
  if (!companyIdFromHeader) {
    console.error("X-Affiliate-Company-ID header is missing");
    return {
      error: NextResponse.json(
        { error: "X-Affiliate-Company-ID header is required" },
        { status: 400 }
      ),
      companyId: null,
      userRole: null,
    };
  }

  // Validate if companyIdFromHeader is a UUID (basic validation)
  const uuidRegex =
    /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  if (!uuidRegex.test(companyIdFromHeader)) {
    console.error("Invalid X-Affiliate-Company-ID header format");
    return {
      error: NextResponse.json(
        { error: "Invalid X-Affiliate-Company-ID header format" },
        { status: 400 }
      ),
      companyId: null,
      userRole: null,
    };
  }

  const { data: companyAccess, error: accessError } = await supabase
    .from("affiliate_users")
    .select("role, employment_status")
    .eq("user_id", userId)
    .eq("affiliate_company_id", companyIdFromHeader)
    .single();

  if (accessError || !companyAccess) {
    console.error(
      "Error fetching affiliate company access or access denied:",
      accessError
    );
    return {
      error: NextResponse.json(
        { error: "Affiliate company access denied or not found." },
        { status: 403 }
      ),
      companyId: null,
      userRole: null,
    };
  }

  if (companyAccess.employment_status !== "active") {
    console.error("User is not active in this company:", companyAccess.employment_status);
    return {
      error: NextResponse.json(
        { error: "User is not active in this company." },
        { status: 403 }
      ),
      companyId: null,
      userRole: null,
    };
  }

  // Normalize role for consistent checking
  const normalizedRole = await normalizeRole(supabase, companyAccess.role);
  
  if (!allowedRoles.includes(normalizedRole)) {
    console.error(
      "User role not permitted for this operation:",
      companyAccess.role,
      "normalized to:",
      normalizedRole
    );
    return {
      error: NextResponse.json(
        { error: "User role not permitted for this operation." },
        { status: 403 }
      ),
      companyId: null,
      userRole: null,
    };
  }

  return {
    error: null,
    companyId: companyIdFromHeader,
    userRole: companyAccess.role,
  };
}

export async function GET(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            /* Readonly, ignore */
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, "", options);
          } catch (error) {
            /* Readonly, ignore */
          }
        },
      },
    }
  );

  console.log("GET /api/affiliate/vehicles: Attempting to get user");
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();
  if (userError || !user) {
    console.error("User not authenticated:", userError);
    return NextResponse.json(
      { error: "User not authenticated" },
      { status: 401 }
    );
  }
  console.log("GET /api/affiliate/vehicles: User obtained:", user.id);

  const companyIdFromHeader = request.headers.get("X-Affiliate-Company-ID");
  const authResult = await authorizeAffiliateAndGetCompany(
    supabase,
    user.id,
    companyIdFromHeader,
    VALID_COMPANY_ROLES_GET
  );

  if (authResult.error) {
    return authResult.error;
  }
  const { companyId } = authResult;
  console.log(
    `Authorized for companyId: ${companyId} with role ${authResult.userRole}`
  );

  try {
    const { data: vehicles, error: vehiclesError } = await supabase
      .from("vehicles")
      .select("*")
      .eq("affiliate_company_id", companyId);

    if (vehiclesError) {
      console.error("GET /api/affiliate/vehicles: Error fetching vehicles.", {
        affiliateCompanyId: companyId,
        vehiclesError: vehiclesError.message,
      });
      return NextResponse.json(
        { error: vehiclesError.message },
        { status: 500 }
      );
    }

    return NextResponse.json(vehicles || []);
  } catch (error: any) {
    console.error("GET /api/affiliate/vehicles: Catch-all error.", {
      error: error.message,
      stack: error.stack,
    });
    return NextResponse.json(
      { error: "An unexpected error occurred." },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            /* Readonly, ignore */
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, "", options);
          } catch (error) {
            /* Readonly, ignore */
          }
        },
      },
    }
  );

  console.log("POST /api/affiliate/vehicles: Attempting to get user");
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();
  if (userError || !user) {
    console.error("User not authenticated:", userError);
    return NextResponse.json(
      { error: "User not authenticated" },
      { status: 401 }
    );
  }
  console.log("POST /api/affiliate/vehicles: User obtained:", user.id);

  const companyIdFromHeader = request.headers.get("X-Affiliate-Company-ID");
  const authResult = await authorizeAffiliateAndGetCompany(
    supabase,
    user.id,
    companyIdFromHeader,
    VALID_COMPANY_ROLES_POST
  );

  if (authResult.error) {
    return authResult.error;
  }
  const { companyId } = authResult;
  console.log(
    `Authorized for companyId: ${companyId} with role ${authResult.userRole} for POST`
  );

  try {
    const body = await request.json();
    const parsedBody = vehicleCreateSchema.safeParse(body);

    if (!parsedBody.success) {
      console.error("POST /api/affiliate/vehicles: Validation failed.", {
        errors: parsedBody.error.format(),
      });
      return NextResponse.json(
        { error: "Invalid input", details: parsedBody.error.format() },
        { status: 400 }
      );
    }

    const {
      type,
      make,
      model,
      year,
      license_plate,
      status,
      insurance_policy_number,
      images,
      capacity,
    } = parsedBody.data;

    // Get organization_id from the affiliate company
    const { data: companyData, error: companyError } = await supabase
      .from("affiliate_companies")
      .select("organization_id")
      .eq("id", companyId)
      .single();

    if (companyError || !companyData) {
      console.error("Error fetching company organization_id:", companyError);
      return NextResponse.json(
        { error: "Failed to get company information" },
        { status: 500 }
      );
    }

    // Prepare vehicle data - only include fields that exist in the database schema
    const vehicleData: any = {
      organization_id: companyData.organization_id,  // Required field
      affiliate_company_id: companyId,  // Fixed: correct column name
      vehicle_type: type,               // Fixed: correct column name
      make,
      model,
      year,
      license_plate,
      status,
      passenger_capacity: capacity,     // Fixed: correct column name
    };

    // Only add images if provided (JSONB field exists)
    if (images && images.length > 0) {
      vehicleData.images = images;
    }

    // Store insurance_policy_number in the correct field
    if (insurance_policy_number) {
      vehicleData.insurance_policy_number = insurance_policy_number;
    }

    const { data: newVehicle, error: insertError } = await supabase
      .from("vehicles")
      .insert([vehicleData])
      .select()
      .single();

    if (insertError) {
      console.error("POST /api/affiliate/vehicles: Error inserting vehicle.", {
        affiliateCompanyId: companyId,
        insertError: insertError.message,
      });
      return NextResponse.json({ error: insertError.message }, { status: 500 });
    }

    return NextResponse.json(newVehicle, { status: 201 });
  } catch (error: any) {
    console.error("POST /api/affiliate/vehicles: Catch-all error.", {
      error: error.message,
      stack: error.stack,
    });
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: "Invalid JSON payload." },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: "An unexpected error occurred." },
      { status: 500 }
    );
  }
}
