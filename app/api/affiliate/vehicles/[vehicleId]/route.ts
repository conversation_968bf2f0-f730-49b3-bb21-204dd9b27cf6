import { NextRequest, NextResponse } from "next/server";

export const runtime = 'nodejs'
import { cookies } from "next/headers"; // Import cookies
import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { z } from "zod";
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

// Zod schema for vehicle update (all fields optional, but at least one must be present)
// Note: 'type' might not be updatable or have specific logic if it changes how rates apply.
// For now, allowing type update.
const vehicleUpdateSchema = z
  .object({
    type: z.string().min(1, "Vehicle type is required").optional(),
    make: z.string().min(1, "Make is required").optional(),
    model: z.string().min(1, "Model is required").optional(),
    year: z.coerce
      .number()
      .int()
      .gte(1900)
      .lte(new Date().getFullYear() + 2)
      .optional(), // Coerce to number and validate range
    capacity: z.coerce
      .number()
      .int()
      .positive("Capacity must be a positive number")
      .optional(), // Coerce to number and validate
    license_plate: z.string().min(1, "License plate is required").optional(),
    insurance_policy_number: z.string().optional(), // Keep optional as it is an update
    status: z
      .enum(["active", "inactive", "maintenance", "deactivated"])
      .optional(),
    images: z.array(z.string().url()).optional(),
  })
  .refine((data) => Object.keys(data).length > 0, {
    message: "At least one field must be provided for update",
  });

interface RouteContext {
  params: {
    vehicleId: string;
  };
}

// Updated for simplified role architecture
const VALID_COMPANY_ROLES_WRITE = ["MANAGER"];
const VALID_ROLES_PUT_DELETE = ["MANAGER"]; // For PUT and DELETE operations

// Re-usable authorization function (can be moved to a shared lib later)
async function authorizeAffiliateAndGetCompany(
  supabase: ReturnType<typeof createServerClient>,
  userId: string,
  companyIdFromHeader: string | null,
  allowedRoles: string[]
) {
  if (!companyIdFromHeader) {
    console.error("X-Affiliate-Company-ID header is missing");
    return {
      error: NextResponse.json(
        { error: "X-Affiliate-Company-ID header is required" },
        { status: 400 }
      ),
      companyId: null,
      userRole: null,
    };
  }
  const uuidRegex =
    /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  if (!uuidRegex.test(companyIdFromHeader)) {
    console.error("Invalid X-Affiliate-Company-ID header format");
    return {
      error: NextResponse.json(
        { error: "Invalid X-Affiliate-Company-ID header format" },
        { status: 400 }
      ),
      companyId: null,
      userRole: null,
    };
  }

  const { data: companyAccess, error: accessError } = await supabase
    .from("affiliate_users")
    .select("role, employment_status")
    .eq("user_id", userId)
    .eq("affiliate_company_id", companyIdFromHeader)
    .single();

  if (accessError || !companyAccess) {
    console.error(
      "Error fetching affiliate company access or access denied:",
      accessError
    );
    return {
      error: NextResponse.json(
        { error: "Affiliate company access denied or not found." },
        { status: 403 }
      ),
      companyId: null,
      userRole: null,
    };
  }

  if (companyAccess.employment_status !== "active") {
    console.error("User is not active in this company:", companyAccess.employment_status);
    return {
      error: NextResponse.json(
        { error: "User is not active in this company." },
        { status: 403 }
      ),
      companyId: null,
      userRole: null,
    };
  }

  if (!allowedRoles.includes(companyAccess.role)) {
    console.error(
      "User role not permitted for this operation:",
      companyAccess.role
    );
    return {
      error: NextResponse.json(
        { error: "User role not permitted for this operation." },
        { status: 403 }
      ),
      companyId: null,
      userRole: null,
    };
  }

  return {
    error: null,
    companyId: companyIdFromHeader,
    userRole: companyAccess.role,
  };
}

export async function PUT(request: NextRequest, { params }: RouteContext) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            /* Readonly, ignore */
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, "", options);
          } catch (error) {
            /* Readonly, ignore */
          }
        },
      },
    }
  );
  const { vehicleId } = params;

  console.log(
    `PUT /api/affiliate/vehicles/${vehicleId}: Attempting to get user`
  );
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error(
      `PUT /api/affiliate/vehicles/${vehicleId}: Auth error or no user.`,
      {
        authError: authError?.message,
        userId: user?.id,
      }
    );
    return NextResponse.json(
      { error: authError?.message || "Unauthorized" },
      { status: 401 }
    );
  }
  console.log(
    `PUT /api/affiliate/vehicles/${vehicleId}: User obtained:`,
    user.id
  );

  if (!vehicleId) {
    console.error(
      `PUT /api/affiliate/vehicles/${vehicleId}: Vehicle ID is required.`
    );
    return NextResponse.json(
      { error: "Vehicle ID is required" },
      { status: 400 }
    );
  }

  const companyIdFromHeader = request.headers.get("X-Affiliate-Company-ID");
  const authResult = await authorizeAffiliateAndGetCompany(
    supabase,
    user.id,
    companyIdFromHeader,
    VALID_ROLES_PUT_DELETE
  );

  if (authResult.error) {
    return authResult.error;
  }
  const { companyId } = authResult;
  console.log(
    `Authorized for companyId: ${companyId} with role ${authResult.userRole} for PUT [vehicleId]`
  );

  try {
    const body = await request.json();
    const parsedBody = vehicleUpdateSchema.safeParse(body);

    if (!parsedBody.success) {
      console.error(
        `PUT /api/affiliate/vehicles/${vehicleId}: Validation failed.`,
        { errors: parsedBody.error.format() }
      );
      return NextResponse.json(
        { error: "Invalid input", details: parsedBody.error.format() },
        { status: 400 }
      );
    }

    // Map frontend field names to database column names
    const updateData: any = {};
    if (parsedBody.data.type) updateData.vehicle_type = parsedBody.data.type;
    if (parsedBody.data.make) updateData.make = parsedBody.data.make;
    if (parsedBody.data.model) updateData.model = parsedBody.data.model;
    if (parsedBody.data.year) updateData.year = parsedBody.data.year;
    if (parsedBody.data.capacity) updateData.passenger_capacity = parsedBody.data.capacity;
    if (parsedBody.data.license_plate) updateData.license_plate = parsedBody.data.license_plate;
    if (parsedBody.data.insurance_policy_number) updateData.insurance_policy_number = parsedBody.data.insurance_policy_number;
    if (parsedBody.data.status) updateData.status = parsedBody.data.status;
    if (parsedBody.data.images) updateData.images = parsedBody.data.images;

    const { data: updatedVehicle, error: updateError } = await supabase
      .from("vehicles")
      .update(updateData)
      .eq("id", vehicleId)
      .eq("affiliate_company_id", companyId)
      .select()
      .single();

    if (updateError) {
      console.error(
        `PUT /api/affiliate/vehicles/${vehicleId}: Error updating vehicle.`,
        {
          vehicleId,
          companyId,
          updateError: updateError.message,
        }
      );
      if (updateError.code === "PGRST116") {
        return NextResponse.json(
          { error: "Vehicle not found or not owned by affiliate." },
          { status: 404 }
        );
      }
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    return NextResponse.json(updatedVehicle);
  } catch (error: any) {
    console.error(
      `PUT /api/affiliate/vehicles/${vehicleId}: Catch-all error.`,
      { error: error.message, stack: error.stack }
    );
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: "Invalid JSON payload." },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: "An unexpected error occurred." },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteContext) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            /* Readonly, ignore */
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, "", options);
          } catch (error) {
            /* Readonly, ignore */
          }
        },
      },
    }
  );
  const { vehicleId } = params;

  console.log(
    `DELETE /api/affiliate/vehicles/${vehicleId}: Attempting to get user`
  );
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error(
      `DELETE /api/affiliate/vehicles/${vehicleId}: Auth error or no user.`,
      {
        authError: authError?.message,
        userId: user?.id,
      }
    );
    return NextResponse.json(
      { error: authError?.message || "Unauthorized" },
      { status: 401 }
    );
  }
  console.log(
    `DELETE /api/affiliate/vehicles/${vehicleId}: User obtained:`,
    user.id
  );

  if (!vehicleId) {
    console.error(
      `DELETE /api/affiliate/vehicles/${vehicleId}: Vehicle ID is required.`
    );
    return NextResponse.json(
      { error: "Vehicle ID is required" },
      { status: 400 }
    );
  }

  const companyIdFromHeader = request.headers.get("X-Affiliate-Company-ID");
  const authResult = await authorizeAffiliateAndGetCompany(
    supabase,
    user.id,
    companyIdFromHeader,
    VALID_ROLES_PUT_DELETE
  );

  if (authResult.error) {
    return authResult.error;
  }
  const { companyId } = authResult;
  console.log(
    `Authorized for companyId: ${companyId} with role ${authResult.userRole} for DELETE [vehicleId]`
  );

  try {
    const { error: deleteError, count } = await supabase
      .from("vehicles")
      .delete({ count: "exact" })
      .eq("id", vehicleId)
      .eq("affiliate_company_id", companyId);

    if (deleteError) {
      console.error("Error deleting vehicle:", deleteError);
      if (
        deleteError.code === "PGRST116" ||
        (deleteError.details && deleteError.details.includes("0 rows"))
      ) {
        return NextResponse.json(
          { error: "Vehicle not found or not owned by affiliate." },
          { status: 404 }
        );
      }
      return NextResponse.json({ error: deleteError.message }, { status: 500 });
    }

    if (count === 0) {
      console.warn(
        `DELETE /api/affiliate/vehicles/${vehicleId}: Vehicle not found or not owned by affiliate for companyId`,
        companyId
      );
      return NextResponse.json(
        { error: "Vehicle not found or not owned by affiliate." },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { message: "Vehicle deleted successfully" },
      { status: 200 }
    );
  } catch (error: any) {
    console.error(
      `DELETE /api/affiliate/vehicles/${vehicleId}: Catch-all error.`,
      { error: error.message, stack: error.stack }
    );
    return NextResponse.json(
      { error: "An unexpected error occurred." },
      { status: 500 }
    );
  }
}
