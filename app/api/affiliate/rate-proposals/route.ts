import { NextResponse } from "next/server"

export const runtime = 'nodejs'
import { createServerClient, type CookieOptions } from "@supabase/ssr"
import { cookies } from "next/headers"
import type { Database } from "@/lib/database.types"

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

export async function POST(request: Request) {
  try {
    const supabase = createSupabaseClient()
    const body = await request.json()

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }

    // Get the affiliate company for the current user
    const { data: affiliateCompany, error: affiliateError } = await supabase
      .from('affiliate_companies')
      .select('id')
      .eq('owner_id', user.id)
      .single()
    
    if (affiliateError) {
      console.error('Error fetching affiliate company:', affiliateError)
      return NextResponse.json(
        { error: 'Error fetching affiliate company' },
        { status: 500 }
      )
    }

    // Validate the request body
    const { quote_offer_id, rate_amount, rate_currency, notes } = body

    if (!quote_offer_id || !rate_amount || !rate_currency) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check if the quote offer exists and belongs to this affiliate
    const { data: quoteOffer, error: offerError } = await supabase
      .from('quote_offers')
      .select('id, status')
      .eq('id', quote_offer_id)
      .eq('company_id', affiliateCompany.id)
      .single()
    
    if (offerError) {
      console.error('Error fetching quote offer:', offerError)
      return NextResponse.json(
        { error: 'Quote offer not found or does not belong to this affiliate' },
        { status: 404 }
      )
    }

    if (quoteOffer.status !== 'pending') {
      return NextResponse.json(
        { error: 'Quote offer is not in pending status' },
        { status: 400 }
      )
    }

    // Create the rate proposal
    const { data: rateProposal, error: proposalError } = await supabase
      .from('rate_proposals')
      .insert({
        quote_offer_id,
        rate_amount,
        rate_currency,
        notes: notes || '',
        status: 'pending'
      })
      .select()
      .single()
    
    if (proposalError) {
      console.error('Error creating rate proposal:', proposalError)
      return NextResponse.json(
        { error: 'Error creating rate proposal' },
        { status: 500 }
      )
    }

    // Update the quote offer status
    const { error: updateError } = await supabase
      .from('quote_offers')
      .update({ status: 'responded' })
      .eq('id', quote_offer_id)
    
    if (updateError) {
      console.error('Error updating quote offer status:', updateError)
      // Don't return an error here, as the rate proposal was created successfully
    }

    return NextResponse.json(rateProposal)
  } catch (error) {
    console.error('Error submitting rate proposal:', error)
    return NextResponse.json(
      { error: 'Error submitting rate proposal' },
      { status: 500 }
    )
  }
} 