import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import * as z from "zod";
import {
  createAuthenticatedSupabaseClient,
  requireAuth,
} from "@/lib/auth/server";
import { createStandardizedResponse } from "@/app/lib/auth/standardized";

// Schema for company creation, mirroring the frontend form
const companyCreationSchema = z.object({
  companyName: z.string().min(2).max(100),
  contactEmail: z.string().email(),
  contactPhone: z.string().min(10).max(20),
  addressLine1: z.string().min(5).max(150),
  addressLine2: z.string().max(150).optional().or(z.literal("")),
  city: z.string().min(2).max(50),
  stateProvince: z.string().min(2).max(50),
  postalCode: z.string().min(3).max(20),
  country: z.string().min(2).max(50),
  website: z.string().url().optional().or(z.literal("")),
  dba: z.string().optional().or(z.literal("")),
  ownerName: z.string().optional().or(z.literal("")),
  yearEstablished: z.string().optional().or(z.literal("")),
  federalTaxId: z.string().optional().or(z.literal("")),
});

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    if (!user) {
      console.error("POST /api/affiliate/companies: Not authenticated");
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    const data = await request.json();
    console.log(
      "POST /api/affiliate/companies: Received data",
      JSON.stringify(data)
    );

    // Validate input data
    const result = companyCreationSchema.safeParse(data);
    if (!result.success) {
      console.error(
        "POST /api/affiliate/companies: Validation failed",
        result.error
      );
      return NextResponse.json(
        { error: "Invalid input data", details: result.error.format() },
        { status: 400 }
      );
    }

    const companyData = result.data;

    // Get user's organizations - enforce proper multi-tenant isolation
    const supabase = await createAuthenticatedSupabaseClient();
    
    console.log("POST /api/affiliate/companies: User authenticated", { userId: user.id, userEmail: user.email });
    
    const { data: userOrgs, error: orgError } = await supabase
      .from('user_organizations')
      .select('organization_id, role')
      .eq('user_id', user.id)
      .eq('status', 'active');

    if (orgError) {
      console.error("POST /api/affiliate/companies: Error fetching user organizations", {
        error: orgError,
        userId: user.id,
        userEmail: user.email
      });
      return NextResponse.json(
        {
          error: "Failed to fetch user organizations",
          details: orgError.message,
          debug: {
            userId: user.id,
            userEmail: user.email
          }
        },
        { status: 500 }
      );
    }

    // Enforce explicit organization assignment - no auto-assignment
    if (!userOrgs || userOrgs.length === 0) {
      console.log("POST /api/affiliate/companies: User not assigned to any organization", {
        userId: user.id,
        userEmail: user.email
      });
      
      return NextResponse.json(
        {
          error: "User must be assigned to an organization before creating companies",
          details: "Please contact support to complete your account setup",
          code: "ORGANIZATION_ASSIGNMENT_REQUIRED"
        },
        { status: 403 }
      );
    }

    // Use the first active organization (or we could add logic to select the primary one)
    const userOrg = userOrgs[0];
    
    console.log("POST /api/affiliate/companies: User organizations found", {
      userId: user.id,
      userEmail: user.email,
      organizationCount: userOrgs.length,
      selectedOrganizationId: userOrg.organization_id,
      userRole: userOrg.role
    });

    // Create the company data object - map form fields to database fields
    const insertData = {
      organization_id: userOrg.organization_id,
      name: companyData.companyName,
      primary_contact_email: companyData.contactEmail,
      primary_contact_phone: companyData.contactPhone,
      street_address: companyData.addressLine1,
      city: companyData.city,
      state_province: companyData.stateProvince,
      postal_code: companyData.postalCode,
      country: companyData.country,
      // These fields will be stored if they exist in the database schema
      dba_name: companyData.dba || null,
      tax_id: companyData.federalTaxId || null,
      status: "active",
      approval_status: "pending", // Restore proper approval workflow
      created_by: user.id,
      // owner_id: user.id, // Field doesn't exist in current table schema
    };

    console.log(
      "POST /api/affiliate/companies: Inserting company data",
      JSON.stringify(insertData, null, 2)
    );

    // Insert company into database - use authenticated client to respect RLS
    const { data: company, error: insertError } = await supabase
      .from("affiliate_companies")
      .insert(insertData)
      .select()
      .single();

    if (insertError) {
      console.error("POST /api/affiliate/companies: Insert error", {
        error: insertError,
        code: insertError.code,
        message: insertError.message,
        details: insertError.details,
        hint: insertError.hint,
      });
      return NextResponse.json(
        {
          error: "Failed to create company",
          details: insertError.message,
          code: insertError.code,
        },
        { status: 500 }
      );
    }

    console.log(
      "POST /api/affiliate/companies: Company inserted successfully",
      company
    );

    // Create association between user and company
    console.log(
      "POST /api/affiliate/companies: Creating user-company association",
      {
        user_id: user.id,
        affiliate_company_id: company.id,
        role: "MANAGER",
        employment_status: "active",
      }
    );

    // Create new association (allow multiple companies per user) - use service role client to bypass RLS
    const serviceSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    const { data: linkData, error: linkError } = await serviceSupabase
      .from("affiliate_users")
      .insert({
        user_id: user.id,
        affiliate_company_id: company.id,
        role: "MANAGER", // Default role for company creator (standardized with migration 027)
        employment_status: "active", // Must match enum values
      })
      .select()
      .single();

    if (linkError) {
      console.error("POST /api/affiliate/companies: Link error", {
        error: linkError,
        code: linkError.code,
        message: linkError.message,
        details: linkError.details,
        hint: linkError.hint,
      });

      // Handle duplicate association gracefully (user already associated with this company)
      if (
        linkError.code === "23505" &&
        linkError.message?.includes("user_id, affiliate_company_id")
      ) {
        console.log(
          "POST /api/affiliate/companies: User already associated with company, continuing..."
        );
        // This is not an error - the association already exists
      } else {
        return NextResponse.json(
          {
            error: "Failed to link user to company",
            details: linkError.message,
            code: linkError.code,
          },
          { status: 500 }
        );
      }
    }

    console.log(
      "POST /api/affiliate/companies: User-company association created successfully",
      linkData
    );

    console.log("POST /api/affiliate/companies: Success", company.id);
    return NextResponse.json({
      message: "Company created successfully and submitted for approval",
      company: {
        id: company.id,
        name: company.name,
        approval_status: company.approval_status,
        onboarding_status: company.onboarding_status
      },
      next_steps: [
        "Complete your company onboarding process",
        "Upload required documents and configure rate cards",
        "Your company will be reviewed and approved by our team",
        "Once approved, you can start receiving quote requests"
      ]
    });
  } catch (error) {
    console.error("POST /api/affiliate/companies: Unhandled error", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
