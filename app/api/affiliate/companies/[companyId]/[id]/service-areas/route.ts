import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

/**
 * GUG-26: Advanced Affiliate Features - Company-specific Service Areas API
 * 
 * Handles CRUD operations for affiliate service areas by company ID
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("Affiliate company service-areas GET API called for company:", params.id);

    // Authenticate as affiliate user
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const { context } = authResult;
    const supabase = createClient();

    // For non-super-admin users, verify they have access to this company
    if (!context.roles.includes('SUPER_ADMIN')) {
      const { data: userCompany, error: accessError } = await supabase
        .from('affiliate_users')
        .select('id')
        .eq('user_id', context.userId)
        .eq('company_id', params.id)
        .single();

      if (accessError || !userCompany) {
        return NextResponse.json({
          error: 'Access denied to this company'
        }, { status: 403 });
      }
    }

    // Fetch service areas for the company
    const { data: serviceAreas, error } = await supabase
      .from('affiliate_service_areas')
      .select('*')
      .eq('company_id', params.id)
      .order('priority', { ascending: true });

    if (error) {
      console.error('Error fetching service areas:', error);
      return NextResponse.json({ 
        error: 'Failed to fetch service areas', 
        details: error.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      serviceAreas: serviceAreas || []
    });

  } catch (error) {
    console.error('Error in company service-areas GET API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("Affiliate company service-areas POST API called for company:", params.id);

    // Authenticate as affiliate user
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const { context } = authResult;
    const body = await request.json();
    const supabase = createClient();

    // For non-super-admin users, verify they have access to this company
    if (!context.roles.includes('SUPER_ADMIN')) {
      const { data: userCompany, error: accessError } = await supabase
        .from('affiliate_users')
        .select('id')
        .eq('user_id', context.userId)
        .eq('company_id', params.id)
        .single();

      if (accessError || !userCompany) {
        return NextResponse.json({
          error: 'Access denied to this company'
        }, { status: 403 });
      }
    }

    const {
      area_name,
      area_type,
      coverage_radius,
      pricing_multiplier,
      minimum_rate,
      maximum_rate,
      service_types,
      is_active,
      priority,
      notes
    } = body;

    // Validate required fields
    if (!area_name || !area_type || !pricing_multiplier) {
      return NextResponse.json({
        error: 'Missing required fields: area_name, area_type, pricing_multiplier'
      }, { status: 400 });
    }

    // Create new service area
    const { data: serviceArea, error } = await supabase
      .from('affiliate_service_areas')
      .insert({
        company_id: params.id,
        area_name,
        area_type,
        coverage_radius: coverage_radius || null,
        pricing_multiplier,
        minimum_rate: minimum_rate || null,
        maximum_rate: maximum_rate || null,
        service_types: service_types || [],
        is_active: is_active !== undefined ? is_active : true,
        priority: priority || 1,
        notes: notes || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating service area:', error);
      return NextResponse.json({ 
        error: 'Failed to create service area', 
        details: error.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      serviceArea,
      message: 'Service area created successfully'
    });

  } catch (error) {
    console.error('Error in company service-areas POST API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}