import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { hasRole } from '@/app/lib/auth';
import { UserRole } from '@/app/lib/auth/roles';

// Schema for company updates - comprehensive to handle all onboarding fields
const companyUpdateSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  email: z.string().email().optional(),
  phone: z.string().min(10).max(20).optional(),
  address: z.string().min(5).max(150).optional(),
  address_line_2: z.string().max(150).optional().or(z.literal('')),
  city: z.string().min(2).max(50).optional(),
  state: z.string().min(2).max(50).optional(),
  zip: z.string().min(3).max(20).optional(),
  country: z.string().min(2).max(50).optional(),
  website: z.string().url().optional().or(z.literal('')),
  dba: z.string().max(100).optional().or(z.literal('')),
  owner_name: z.string().max(100).optional().or(z.literal('')),
  year_established: z.string().optional().or(z.literal('')),
  federal_tax_id: z.string().max(50).optional().or(z.literal('')),
  contact_email: z.string().email().optional().or(z.literal('')),
  contact_phone: z.string().max(20).optional().or(z.literal('')),

  // Boolean fields
  charge_for_delays: z.boolean().optional(),
  charge_for_stops: z.boolean().optional(),
  offer_meet_greet: z.boolean().optional(),
  road_shows: z.boolean().optional(),
  gridd_gnet_member: z.boolean().optional(),
  addons_la: z.boolean().optional(),
  direct_billing: z.boolean().optional(),
  live_phone_support: z.boolean().optional(),

  // Array fields (stored as JSON or comma-separated strings)
  airports_served: z.array(z.string()).optional(),
  languages_spoken: z.array(z.string()).optional(),
  dispatch_software: z.array(z.string()).optional().or(z.string().optional()),
  trip_status_updates: z.array(z.string()).optional().or(z.string().optional()),
  cities_covered: z.array(z.string()).optional(),

  // Text fields for policies and procedures
  dress_code: z.string().optional().or(z.literal('')),
  cancellation_policy: z.string().optional().or(z.literal('')),
  meet_greet: z.string().optional().or(z.literal('')),

  // Document fields (for file uploads - stored as strings/URLs)
  business_license: z.string().optional().or(z.literal('')).or(z.null()),
  insurance_certificate: z.string().optional().or(z.literal('')).or(z.null()),

  // Status fields
  business_registration_status: z.string().optional(),
  insurance_status: z.string().optional(),
  fleet_inspection_status: z.string().optional(),
  rate_agreement_status: z.string().optional(),
  application_status: z.string().optional(),
  document_status: z.string().optional(),
  progress: z.number().min(0).max(100).optional(),
  application_submitted_at: z.string().optional(),
});

export async function GET(request: NextRequest, { params }: { params: { companyId: string } }) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  // Enforce multi-tenant security: require X-Affiliate-Company-ID header to match param
  const headerCompanyId = request.headers.get('x-affiliate-company-id');
  if (!headerCompanyId || headerCompanyId !== params.companyId) {
    return NextResponse.json({ error: 'Missing or mismatched X-Affiliate-Company-ID header.' }, { status: 403 });
  }

  // Auth check
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
  }

  // Fetch the company by ID, RLS will enforce access
  const { data: company, error } = await supabase
    .from('affiliate_companies')
    .select('*')
    .eq('id', params.companyId)
    .single();

  if (error) {
    return NextResponse.json({ error: 'Company not found or not accessible', details: error.message }, { status: 404 });
  }
  if (!company) {
    return NextResponse.json({ error: 'Company not found' }, { status: 404 });
  }

  // Parse JSON fields back to arrays for frontend consumption
  const processedCompany = { ...company };

  console.log('GET company: Raw company data from database:', {
    id: company.id,
    name: company.name,
    network_participation: company.network_participation
  });

  try {
    if (company.airports_served && typeof company.airports_served === 'string') {
      processedCompany.airports_served = JSON.parse(company.airports_served);
    }
    if (company.languages_spoken && typeof company.languages_spoken === 'string') {
      processedCompany.languages_spoken = JSON.parse(company.languages_spoken);
    }
    if (company.dispatch_software && typeof company.dispatch_software === 'string') {
      processedCompany.dispatch_software = JSON.parse(company.dispatch_software);
    }
    if (company.trip_status_updates && typeof company.trip_status_updates === 'string') {
      processedCompany.trip_status_updates = JSON.parse(company.trip_status_updates);
    }
    if (company.cities_covered && typeof company.cities_covered === 'string') {
      processedCompany.cities_covered = JSON.parse(company.cities_covered);
    }
  } catch (parseError) {
    console.warn('Error parsing JSON fields:', parseError);
    // Keep original values if parsing fails
  }

  console.log('GET company: Processed company data being returned:', {
    id: processedCompany.id,
    name: processedCompany.name,
    network_participation: processedCompany.network_participation
  });

  return NextResponse.json(processedCompany, { status: 200 });
}

export async function PUT(request: NextRequest, { params }: { params: { companyId: string } }) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  try {
    // Enforce multi-tenant security: require X-Affiliate-Company-ID header to match param
    const headerCompanyId = request.headers.get('x-affiliate-company-id');
    if (!headerCompanyId || headerCompanyId !== params.companyId) {
      return NextResponse.json({ error: 'Missing or mismatched X-Affiliate-Company-ID header.' }, { status: 403 });
    }

    // Auth check
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    // Verify user has access to this company
    const { data: userCompanyLink, error: linkError } = await supabase
      .from('affiliate_users')
      .select('role, status')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', params.companyId)
      .eq('status', 'ACTIVE')
      .single();

    if (linkError || !userCompanyLink) {
      return NextResponse.json({ error: 'Access denied to this company' }, { status: 403 });
    }

    // Only owners and dispatchers can update company details
    if (!hasRole([userCompanyLink.role as UserRole], ['AFFILIATE', 'AFFILIATE_DISPATCH'])) {
      return NextResponse.json({ error: 'Insufficient permissions to update company' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    console.log('Company update request body:', JSON.stringify(body, null, 2));

    const validationResult = companyUpdateSchema.safeParse(body);

    if (!validationResult.success) {
      console.error('Validation failed:', validationResult.error.format());
      console.error('Original body:', body);
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.format(),
        receivedData: body
      }, { status: 400 });
    }

    const updateData = validationResult.data;

    // Separate core fields from extended fields that might not exist in database
    const coreFields = {
      name: updateData.name,
      email: updateData.email,
      phone: updateData.phone,
      address: updateData.address,
      city: updateData.city,
      state: updateData.state,
      zip: updateData.zip,
      country: updateData.country,
      website: updateData.website,
      business_license: updateData.business_license || '',
      // Try to store DBA directly as a column first
      dba: updateData.dba,
      owner_name: updateData.owner_name,
      year_established: updateData.year_established,
      federal_tax_id: updateData.federal_tax_id,
    };

    // Extended fields that will be stored in network_participation JSONB
    const extendedFields = {
      dba: updateData.dba,
      owner_name: updateData.owner_name,
      year_established: updateData.year_established,
      federal_tax_id: updateData.federal_tax_id,
      contact_email: updateData.contact_email,
      contact_phone: updateData.contact_phone,
      address_line_2: updateData.address_line_2,
      charge_for_delays: updateData.charge_for_delays,
      charge_for_stops: updateData.charge_for_stops,
      offer_meet_greet: updateData.offer_meet_greet,
      road_shows: updateData.road_shows,
      gridd_gnet_member: updateData.gridd_gnet_member,
      addons_la: updateData.addons_la,
      direct_billing: updateData.direct_billing,
      live_phone_support: updateData.live_phone_support,
      airports_served: updateData.airports_served,
      languages_spoken: updateData.languages_spoken,
      dispatch_software: updateData.dispatch_software,
      trip_status_updates: updateData.trip_status_updates,
      cities_covered: updateData.cities_covered,
      dress_code: updateData.dress_code,
      cancellation_policy: updateData.cancellation_policy,
      meet_greet: updateData.meet_greet,
      insurance_certificate: updateData.insurance_certificate || '',
    };

    // Remove undefined values from core fields
    const processedData: any = Object.fromEntries(
      Object.entries(coreFields).filter(([_, value]) => value !== undefined)
    );

    // Get existing network_participation data and merge with new extended fields
    const { data: existingCompany } = await supabase
      .from('affiliate_companies')
      .select('network_participation')
      .eq('id', params.companyId)
      .single();

    const existingNetworkData = existingCompany?.network_participation || {};

    // Use the original body that was already parsed
    console.log('🔥 API: Using original body for network_participation:', JSON.stringify(body.network_participation, null, 2));

    // If network_participation is provided in the request, use it directly
    const updatedNetworkData = body.network_participation ?
      {
        ...existingNetworkData,
        ...body.network_participation
      } :
      {
        ...existingNetworkData,
        ...Object.fromEntries(
          Object.entries(extendedFields).filter(([_, value]) => value !== undefined)
        )
      };

    processedData.network_participation = updatedNetworkData;

    // Calculate progress based on completed fields
    const calculateProgress = (coreData: any, networkData: any) => {
      // Step 1: Basic Information (25%)
      const basicFields = ['name', 'owner_name', 'year_established', 'federal_tax_id'];
      const basicCompleted = basicFields.filter(field => {
        const value = field === 'name' ? coreData[field] : networkData[field];
        return value && value.toString().trim() !== '';
      }).length;
      const basicProgress = (basicCompleted / basicFields.length) * 25;

      // Step 2: Contact & Location (25%)
      const contactFields = ['contact_email', 'contact_phone', 'address', 'city', 'state', 'zip', 'country'];
      const contactCompleted = contactFields.filter(field => {
        const value = ['address', 'city', 'state', 'zip', 'country'].includes(field)
          ? coreData[field]
          : networkData[field];
        return value && value.toString().trim() !== '';
      }).length;
      const contactProgress = (contactCompleted / contactFields.length) * 25;

      // Step 3: Operations & Services (30%)
      const operationsFields = ['airports_served', 'languages_spoken', 'dispatch_software', 'cities_covered'];
      const operationsCompleted = operationsFields.filter(field => {
        const value = networkData[field];
        return value && (
          (Array.isArray(value) && value.length > 0) ||
          (typeof value === 'string' && value.trim() !== '' && value !== '[]')
        );
      }).length;
      const operationsProgress = (operationsCompleted / operationsFields.length) * 30;

      // Step 4: Policies & Preferences (20%)
      const policiesFields = ['dress_code', 'cancellation_policy', 'meet_greet'];
      const policiesCompleted = policiesFields.filter(field => {
        const value = networkData[field];
        return value && value.toString().trim() !== '';
      }).length;
      const policiesProgress = (policiesCompleted / policiesFields.length) * 20;

      return Math.round(basicProgress + contactProgress + operationsProgress + policiesProgress);
    };

    // Add updated timestamp and recalculate progress (store in network_participation if progress column doesn't exist)
    processedData.updated_at = new Date().toISOString();
    const calculatedProgress = calculateProgress(processedData, updatedNetworkData);

    // Store progress in network_participation to avoid column issues
    updatedNetworkData.progress = calculatedProgress;
    processedData.network_participation = updatedNetworkData;

    console.log('🔥 API: About to save processedData:', JSON.stringify(processedData, null, 2));
    console.log('🔥 API: DBA in updatedNetworkData:', updatedNetworkData.dba);
    console.log('🔥 API: Full updatedNetworkData:', JSON.stringify(updatedNetworkData, null, 2));
    console.log('🔥 API: Original request network_participation:', JSON.stringify(body.network_participation, null, 2));

    // Update the company
    const { data: updatedCompany, error: updateError } = await supabase
      .from('affiliate_companies')
      .update(processedData)
      .eq('id', params.companyId)
      .select()
      .single();

    console.log('🔥 API: Update result:', { updatedCompany, updateError });
    console.log('🔥 API: DBA in response:', updatedCompany?.network_participation?.dba);

    if (updateError) {
      console.error('Error updating company:', updateError);
      return NextResponse.json({
        error: 'Failed to update company',
        details: updateError.message
      }, { status: 500 });
    }

    return NextResponse.json(updatedCompany, { status: 200 });

  } catch (error) {
    console.error('Error in PUT /api/affiliate/companies/[companyId]:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}