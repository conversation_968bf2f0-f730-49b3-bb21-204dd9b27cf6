import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

/**
 * GUG-26: Advanced Affiliate Features - Service Areas API
 * 
 * Manages geographic service areas for affiliate companies
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { companyId: string } }
) {
  try {
    console.log("Affiliate service areas GET API called");

    // Use unified authentication system
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const { context } = authResult;
    const supabase = createClient();
    const { companyId } = params;

    // Verify user has access to this company
    if (!context.roles.includes('SUPER_ADMIN')) {
      const { data: userCompany, error: accessError } = await supabase
        .from('user_organizations')
        .select('organization_id')
        .eq('user_id', context.userId)
        .eq('organization_id', companyId)
        .single();

      if (accessError || !userCompany) {
        return NextResponse.json({ 
          error: 'Access denied to this company' 
        }, { status: 403 });
      }
    }

    // Fetch service areas for the company
    const { data: serviceAreas, error } = await supabase
      .from('affiliate_service_areas')
      .select('*')
      .eq('affiliate_company_id', companyId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching service areas:', error);
      return NextResponse.json({ 
        error: 'Failed to fetch service areas', 
        details: error.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      serviceAreas: serviceAreas || []
    });

  } catch (error) {
    console.error('Error in service areas GET API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { companyId: string } }
) {
  try {
    console.log("Affiliate service areas POST API called");

    // Use unified authentication system
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const { context } = authResult;
    const supabase = createClient();
    const { companyId } = params;

    // Verify user has access to this company
    if (!context.roles.includes('SUPER_ADMIN')) {
      const { data: userCompany, error: accessError } = await supabase
        .from('user_organizations')
        .select('organization_id')
        .eq('user_id', context.userId)
        .eq('organization_id', companyId)
        .single();

      if (accessError || !userCompany) {
        return NextResponse.json({ 
          error: 'Access denied to this company' 
        }, { status: 403 });
      }
    }

    const body = await request.json();
    const {
      name,
      center_lat,
      center_lng,
      radius_miles,
      pricing_multiplier,
      service_types,
      operating_hours,
      special_conditions,
      is_active = true
    } = body;

    // Validate required fields
    if (!name || !center_lat || !center_lng || !radius_miles) {
      return NextResponse.json({
        error: 'Missing required fields: name, center_lat, center_lng, radius_miles'
      }, { status: 400 });
    }

    // Create service area
    const { data: serviceArea, error: createError } = await supabase
      .from('affiliate_service_areas')
      .insert({
        affiliate_company_id: companyId,
        name,
        center_lat,
        center_lng,
        radius_miles,
        pricing_multiplier: pricing_multiplier || 1.0,
        service_types: service_types || [],
        operating_hours: operating_hours || {},
        special_conditions,
        is_active,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating service area:', createError);
      return NextResponse.json({
        error: 'Failed to create service area',
        details: createError.message
      }, { status: 500 });
    }

    console.log('Successfully created service area:', serviceArea.id);

    return NextResponse.json({
      success: true,
      serviceArea,
      message: 'Service area created successfully'
    });

  } catch (error) {
    console.error('Error in service areas POST API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}