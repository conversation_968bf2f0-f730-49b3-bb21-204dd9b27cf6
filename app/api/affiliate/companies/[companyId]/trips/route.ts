import { createServerClient, type CookieOptions } from '@supabase/ssr';

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { hasRole } from '@/app/lib/auth';
import { UserRole } from '@/app/lib/auth/roles';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
/**
 * GET /api/affiliate/companies/[companyId]/trips
 * Fetch trips for a specific affiliate company
 */
export async function GET(
  request: NextRequest, 
  { params }: { params: { companyId: string } }
) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  try {
    // Enforce multi-tenant security: require X-Affiliate-Company-ID header to match param
    const headerCompanyId = request.headers.get('x-affiliate-company-id');
    if (!headerCompanyId || headerCompanyId !== params.companyId) {
      return NextResponse.json({ error: 'Missing or mismatched X-Affiliate-Company-ID header.' }, { status: 403 });
    }

    // Auth check
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    // Check if user has access to this company
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_users')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', params.companyId)
      .maybeSingle();

    if (userCompanyError) {
      console.error('Error checking user company access:', userCompanyError);
      return NextResponse.json({ error: 'Error checking company access' }, { status: 500 });
    }

    if (!userCompany) {
      return NextResponse.json({ error: 'You do not have access to this company' }, { status: 403 });
    }

    // Define allowed roles for this operation
    // Updated for simplified role architecture
    const ALLOWED_COMPANY_ROLES = ['MANAGER', 'DISPATCHER', 'DRIVER'];
    
    // Verify user's role within the company
    if (!hasRole([userCompany.role as UserRole], ALLOWED_ROLES)) {
      return NextResponse.json({ error: 'Insufficient permissions to view trips for this company' }, { status: 403 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // Build query - start simple and add joins if they work
    let query = supabase
      .from('trips')
      .select('*')
      .eq('affiliate_company_id', params.companyId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Filter by status if provided
    if (status) {
      const statusArray = status.split(',').map(s => s.trim());
      query = query.in('status', statusArray);
    }

    const { data: trips, error: tripsError } = await query;

    if (tripsError) {
      console.error('Error fetching trips:', tripsError);
      return NextResponse.json({ error: 'Failed to fetch trips', details: tripsError.message }, { status: 500 });
    }

    // If no trips found, return empty array
    if (!trips || trips.length === 0) {
      return NextResponse.json([]);
    }

    // Try to fetch related data separately to avoid join issues
    const tripIds = trips.map(trip => trip.id);
    const driverIds = trips.map(trip => trip.driver_id).filter(Boolean);
    const vehicleIds = trips.map(trip => trip.vehicle_id).filter(Boolean);
    const quoteIds = trips.map(trip => trip.quote_id).filter(Boolean);

    // Fetch drivers
    let drivers: any[] = [];
    if (driverIds.length > 0) {
      const { data: driversData } = await supabase
        .from('drivers')
        .select('id, first_name, last_name, phone')
        .in('id', driverIds);
      drivers = driversData || [];
    }

    // Fetch vehicles
    let vehicles: any[] = [];
    if (vehicleIds.length > 0) {
      const { data: vehiclesData } = await supabase
        .from('vehicles')
        .select('id, type, make, model, license_plate')
        .in('id', vehicleIds);
      vehicles = vehiclesData || [];
    }

    // Fetch quotes
    let quotes: any[] = [];
    if (quoteIds.length > 0) {
      const { data: quotesData } = await supabase
        .from('quotes')
        .select('id, pickup_location, dropoff_location, passengers')
        .in('id', quoteIds);
      quotes = quotesData || [];
    }

    // Transform the data
    const transformedTrips = trips.map(trip => {
      const driver = drivers.find(d => d.id === trip.driver_id);
      const vehicle = vehicles.find(v => v.id === trip.vehicle_id);
      const quote = quotes.find(q => q.id === trip.quote_id);

      return {
        ...trip,
        driver,
        vehicle,
        quote,
        pickup_location: trip.pickup_location || quote?.pickup_location || '',
        dropoff_location: trip.dropoff_location || quote?.dropoff_location || '',
        passengers: trip.passengers || quote?.passengers || 1,
      };
    });

    return NextResponse.json(transformedTrips);

  } catch (error) {
    console.error('Error in GET /api/affiliate/companies/[companyId]/trips:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
