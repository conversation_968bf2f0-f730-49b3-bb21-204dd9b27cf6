import { createServerClient, type CookieOptions } from '@supabase/ssr';

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
/**
 * GET /api/affiliate/companies/[companyId]/drivers/[driverId]
 * Fetch a specific driver for an affiliate company
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { companyId: string; driverId: string } }
) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  // Enforce multi-tenant security: require X-Affiliate-Company-ID header to match param
  const headerCompanyId = request.headers.get('x-affiliate-company-id');
  if (!headerCompanyId || headerCompanyId !== params.companyId) {
    return NextResponse.json({ error: 'Missing or mismatched X-Affiliate-Company-ID header.' }, { status: 403 });
  }

  // Auth check
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
  }

  try {
    // Check if user has access to this company
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_users')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', params.companyId)
      .maybeSingle();

    if (userCompanyError) {
      console.error('Error checking user company access:', userCompanyError);
      return NextResponse.json({ error: 'Error checking company access' }, { status: 500 });
    }

    if (!userCompany) {
      return NextResponse.json({ error: 'You do not have access to this company' }, { status: 403 });
    }

    // Fetch the specific driver
    const { data: driver, error: driverError } = await supabase
      .from('affiliate_drivers')
      .select('*')
      .eq('id', params.driverId)
      .eq('company_id', params.companyId)
      .single();

    if (driverError) {
      console.error('Error fetching driver:', driverError);
      return NextResponse.json({ error: 'Failed to fetch driver' }, { status: 500 });
    }

    if (!driver) {
      return NextResponse.json({ error: 'Driver not found' }, { status: 404 });
    }

    // Transform the data to match the client-side interface
    const transformedDriver = {
      id: driver.id,
      firstName: driver.first_name,
      lastName: driver.last_name,
      email: driver.email,
      phone: driver.phone,
      status: driver.status,
      licenseInfo: {
        license_number: driver.license_number,
        license_expiry: driver.license_expiry,
        background_check_status: driver.background_check_status,
        drug_test_status: driver.drug_test_status,
      },
      metrics: {
        rating: driver.rating,
      },
      createdAt: driver.created_at,
      updatedAt: driver.updated_at,
    };

    return NextResponse.json(transformedDriver);
  } catch (error) {
    console.error('Error in GET /api/affiliate/companies/[companyId]/drivers/[driverId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/affiliate/companies/[companyId]/drivers/[driverId]
 * Update a specific driver for an affiliate company
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { companyId: string; driverId: string } }
) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  // Enforce multi-tenant security: require X-Affiliate-Company-ID header to match param
  const headerCompanyId = request.headers.get('x-affiliate-company-id');
  if (!headerCompanyId || headerCompanyId !== params.companyId) {
    return NextResponse.json({ error: 'Missing or mismatched X-Affiliate-Company-ID header.' }, { status: 403 });
  }

  // Auth check
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
  }

  try {
    // Check if user has access to this company with appropriate role
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_users')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', params.companyId)
      .maybeSingle();

    if (userCompanyError) {
      console.error('Error checking user company access:', userCompanyError);
      return NextResponse.json({ error: 'Error checking company access' }, { status: 500 });
    }

    if (!userCompany) {
      return NextResponse.json({ error: 'You do not have access to this company' }, { status: 403 });
    }

    // Get user's system roles for permission checking
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    const userRoles = userProfile?.roles || [];

    // Check if user has permission to update drivers
    // Updated for simplified role architecture
    const hasSystemAccess = userRoles.includes('AFFILIATE') || userRoles.includes('SUPER_ADMIN');
    const hasCompanyAccess = userCompany.role === 'MANAGER';
    
    if (!hasSystemAccess && !hasCompanyAccess) {
      return NextResponse.json({ error: 'You do not have permission to update drivers' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();

    // Create update object
    const updateData: Record<string, any> = {
      updated_by: user.id,
      updated_at: new Date().toISOString(),
    };

    // Add fields to update if they exist in request
    if (body.firstName !== undefined) updateData.first_name = body.firstName;
    if (body.lastName !== undefined) updateData.last_name = body.lastName;
    if (body.email !== undefined) updateData.email = body.email;
    if (body.phone !== undefined) updateData.phone = body.phone;
    if (body.status !== undefined) updateData.status = body.status;
    if (body.licenseInfo !== undefined) updateData.license_info = body.licenseInfo;

    // Update the driver
    const { data: updatedDriver, error: updateError } = await supabase
      .from('affiliate_drivers')
      .update(updateData)
      .eq('id', params.driverId)
      .eq('company_id', params.companyId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating driver:', updateError);
      return NextResponse.json({ error: 'Failed to update driver' }, { status: 500 });
    }

    if (!updatedDriver) {
      return NextResponse.json({ error: 'Driver not found' }, { status: 404 });
    }

    // Transform to client format
    const transformedDriver = {
      id: updatedDriver.id,
      firstName: updatedDriver.first_name,
      lastName: updatedDriver.last_name,
      email: updatedDriver.email,
      phone: updatedDriver.phone,
      status: updatedDriver.status,
      licenseInfo: updatedDriver.license_info,
      metrics: updatedDriver.metrics,
      createdAt: updatedDriver.created_at,
      updatedAt: updatedDriver.updated_at,
    };

    return NextResponse.json(transformedDriver);
  } catch (error) {
    console.error('Error in PATCH /api/affiliate/companies/[companyId]/drivers/[driverId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/affiliate/companies/[companyId]/drivers/[driverId]
 * Delete a specific driver for an affiliate company
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { companyId: string; driverId: string } }
) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  // Enforce multi-tenant security: require X-Affiliate-Company-ID header to match param
  const headerCompanyId = request.headers.get('x-affiliate-company-id');
  if (!headerCompanyId || headerCompanyId !== params.companyId) {
    return NextResponse.json({ error: 'Missing or mismatched X-Affiliate-Company-ID header.' }, { status: 403 });
  }

  // Auth check
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
  }

  try {
    // Check if user has access to this company with appropriate role
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_users')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', params.companyId)
      .maybeSingle();

    if (userCompanyError) {
      console.error('Error checking user company access:', userCompanyError);
      return NextResponse.json({ error: 'Error checking company access' }, { status: 500 });
    }

    if (!userCompany) {
      return NextResponse.json({ error: 'You do not have access to this company' }, { status: 403 });
    }

    // Get user's system roles for permission checking
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    const userRoles = userProfile?.roles || [];

    // Check if user has permission to delete drivers
    // Updated for simplified role architecture - only MANAGER or system access can delete
    const hasSystemAccess = userRoles.includes('AFFILIATE') || userRoles.includes('SUPER_ADMIN');
    const hasCompanyAccess = userCompany.role === 'MANAGER';
    
    if (!hasSystemAccess && !hasCompanyAccess) {
      return NextResponse.json({ error: 'You do not have permission to delete drivers' }, { status: 403 });
    }

    // Delete the driver (use affiliate_drivers table)
    const { error: deleteError } = await supabase
      .from('affiliate_drivers')
      .delete()
      .eq('id', params.driverId)
      .eq('affiliate_company_id', params.companyId);

    if (deleteError) {
      console.error('Error deleting driver:', deleteError);
      return NextResponse.json({ error: 'Failed to delete driver' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/affiliate/companies/[companyId]/drivers/[driverId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
} 