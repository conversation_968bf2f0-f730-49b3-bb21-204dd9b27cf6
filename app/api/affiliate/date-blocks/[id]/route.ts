import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

/**
 * GUG-26: Advanced Affiliate Features - Date Blocks Individual Operations
 * 
 * Handles UPDATE and DELETE operations for individual date blocks
 */

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Affiliate date-blocks DELETE API called for ID:', params.id);

    // Authenticate as affiliate user
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const { context } = authResult;
    const supabase = createClient();

    // First, get the date block to verify ownership
    const { data: dateBlock, error: fetchError } = await supabase
      .from('affiliate_date_blocks')
      .select('id, company_id')
      .eq('id', params.id)
      .single();

    if (fetchError || !dateBlock) {
      return NextResponse.json({
        error: 'Date block not found'
      }, { status: 404 });
    }

    // For non-super-admin users, verify they have access to this company
    if (!context.roles.includes('SUPER_ADMIN')) {
      const { data: userCompany, error: accessError } = await supabase
        .from('affiliate_users')
        .select('id')
        .eq('user_id', context.userId)
        .eq('affiliate_company_id', dateBlock.company_id)
        .single();

      if (accessError || !userCompany) {
        return NextResponse.json({
          error: 'Access denied to this date block'
        }, { status: 403 });
      }
    }

    // Delete the date block
    const { error: deleteError } = await supabase
      .from('affiliate_date_blocks')
      .delete()
      .eq('id', params.id);

    if (deleteError) {
      console.error('Error deleting date block:', deleteError);
      return NextResponse.json({
        error: 'Failed to delete date block',
        details: deleteError.message
      }, { status: 500 });
    }

    console.log('Successfully deleted date block:', params.id);

    return NextResponse.json({
      success: true,
      message: 'Date block deleted successfully'
    });

  } catch (error) {
    console.error('Error in date block DELETE API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Affiliate date-blocks PATCH API called for ID:', params.id);

    // Authenticate as affiliate user
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const { context } = authResult;
    const body = await request.json();
    const supabase = createClient();

    // First, get the date block to verify ownership
    const { data: existingBlock, error: fetchError } = await supabase
      .from('affiliate_date_blocks')
      .select('id, company_id')
      .eq('id', params.id)
      .single();

    if (fetchError || !existingBlock) {
      return NextResponse.json({
        error: 'Date block not found'
      }, { status: 404 });
    }

    // For non-super-admin users, verify they have access to this company
    if (!context.roles.includes('SUPER_ADMIN')) {
      const { data: userCompany, error: accessError } = await supabase
        .from('affiliate_users')
        .select('id')
        .eq('user_id', context.userId)
        .eq('affiliate_company_id', existingBlock.company_id)
        .single();

      if (accessError || !userCompany) {
        return NextResponse.json({
          error: 'Access denied to this date block'
        }, { status: 403 });
      }
    }

    // Update the date block
    const { data: updatedBlock, error: updateError } = await supabase
      .from('affiliate_date_blocks')
      .update({
        ...body,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating date block:', updateError);
      return NextResponse.json({
        error: 'Failed to update date block',
        details: updateError.message
      }, { status: 500 });
    }

    console.log('Successfully updated date block:', params.id);

    return NextResponse.json({
      success: true,
      dateBlock: updatedBlock,
      message: 'Date block updated successfully'
    });

  } catch (error) {
    console.error('Error in date block PATCH API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}