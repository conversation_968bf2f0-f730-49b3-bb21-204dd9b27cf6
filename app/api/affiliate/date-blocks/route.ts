import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

/**
 * GUG-26: Advanced Affiliate Features - Date Blocks API
 * 
 * Handles CRUD operations for affiliate availability calendar and date blocks
 * Allows affiliates to block out dates when they're unavailable
 */

export async function GET(request: NextRequest) {
  try {
    console.log("Affiliate date-blocks GET API called");

    // Authenticate as affiliate user
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const { context } = authResult;
    const supabase = createClient();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('affiliate_company_id');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    // Build query for date blocks
    let query = supabase
      .from('affiliate_date_blocks')
      .select(`
        id,
        company_id,
        block_type,
        start_date,
        end_date,
        start_time,
        end_time,
        reason,
        is_recurring,
        recurring_pattern,
        affected_services,
        created_by,
        created_at,
        updated_at,
        affiliate_company:affiliate_companies!inner(
          id,
          name
        )
      `)
      .order('start_date', { ascending: true });

    // Filter by company if specified
    if (companyId) {
      query = query.eq('affiliate_company_id', companyId);
    }

    // Filter by date range if specified
    if (startDate) {
      query = query.gte('start_date', startDate);
    }
    if (endDate) {
      query = query.lte('end_date', endDate);
    }

    // For non-super-admin users, filter by their companies
    if (!context.roles.includes('SUPER_ADMIN')) {
      const { data: userCompanies, error: companiesError } = await supabase
        .from('affiliate_users')
        .select('company_id')
        .eq('user_id', context.userId);

      if (companiesError) {
        console.error('Error fetching user companies:', companiesError);
        return NextResponse.json({ 
          error: 'Failed to fetch user companies' 
        }, { status: 500 });
      }

      const companyIds = userCompanies.map(uc => uc.company_id);
      if (companyIds.length === 0) {
        return NextResponse.json({
          success: true,
          dateBlocks: [],
          total: 0
        });
      }

      query = query.in('affiliate_company_id', companyIds);
    }

    const { data: dateBlocks, error } = await query;

    if (error) {
      console.error('Error fetching date blocks:', error);
      return NextResponse.json({ 
        error: 'Failed to fetch date blocks', 
        details: error.message 
      }, { status: 500 });
    }

    console.log(`Successfully fetched ${dateBlocks?.length || 0} date blocks`);

    return NextResponse.json({
      success: true,
      dateBlocks: dateBlocks || [],
      total: dateBlocks?.length || 0
    });

  } catch (error) {
    console.error('Error in affiliate date-blocks API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('Affiliate date-blocks API - POST request');

    // Authenticate as affiliate user
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const { context } = authResult;
    const body = await request.json();
    const {
      affiliate_company_id: company_id,
      block_type,
      start_date,
      end_date,
      start_time,
      end_time,
      reason,
      is_recurring = false,
      recurring_pattern,
      affected_services = []
    } = body;

    // Validate required fields
    if (!company_id || !block_type || !start_date || !end_date || !reason) {
      return NextResponse.json({
        error: 'Missing required fields: company_id, block_type, start_date, end_date, reason are required'
      }, { status: 400 });
    }

    const supabase = createClient();

    // For non-super-admin users, verify they have access to this company
    if (!context.roles.includes('SUPER_ADMIN')) {
      const { data: userCompany, error: accessError } = await supabase
        .from('affiliate_users')
        .select('id')
        .eq('user_id', context.userId)
        .eq('affiliate_company_id', company_id)
        .single();

      if (accessError || !userCompany) {
        return NextResponse.json({
          error: 'Access denied to this company'
        }, { status: 403 });
      }
    }

    // Create date block
    const { data: dateBlock, error: createError } = await supabase
      .from('affiliate_date_blocks')
      .insert({
        affiliate_company_id: company_id,
        block_type,
        start_date,
        end_date,
        start_time,
        end_time,
        reason,
        is_recurring,
        recurring_pattern,
        affected_services,
        created_by: context.userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating date block:', createError);
      return NextResponse.json({
        error: 'Failed to create date block',
        details: createError.message
      }, { status: 500 });
    }

    console.log('Successfully created date block:', dateBlock.id);

    return NextResponse.json({
      success: true,
      dateBlock,
      message: 'Date block created successfully'
    });

  } catch (error) {
    console.error('Error in date blocks POST API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}