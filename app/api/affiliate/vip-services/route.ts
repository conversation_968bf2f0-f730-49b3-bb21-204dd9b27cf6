import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createClient } from '@/lib/supabase/server'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // VIP services functionality not implemented yet - table doesn't exist
    return NextResponse.json({
      services: []
    })

  } catch (error) {
    console.error('Error in VIP services GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    const body = await request.json()
    const {
      company_id,
      service_type,
      service_name,
      description,
      base_price,
      is_active,
      available_vehicle_types,
      service_config
    } = body

    if (!company_id || !service_type || !service_name || base_price === undefined) {
      return NextResponse.json(
        { error: 'Company ID, service type, service name, and base price are required' },
        { status: 400 }
      )
    }

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // VIP services functionality not implemented yet - table doesn't exist
    return NextResponse.json(
      { error: 'VIP services not implemented yet' },
      { status: 501 }
    )

  } catch (error) {
    console.error('Error in VIP services POST API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
