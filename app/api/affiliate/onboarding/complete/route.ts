import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createClient } from '@/lib/supabase/server';
import { enforceAccessControl } from '@/lib/access-control';

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const {
      company_id,
      onboarding_checklist,
      documents_uploaded,
      rate_cards_configured,
      service_areas_defined,
      banking_info_provided
    } = body;

    if (!company_id) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    // Verify user has access to this company
    const { data: affiliateUser, error: affiliateError } = await supabase
      .from('affiliate_users')
      .select('role, affiliate_company_id')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', company_id)
      .single();

    // Use role normalization for backward compatibility
    const normalizedRole = affiliateUser?.role ?
      (affiliateUser.role === 'admin' || affiliateUser.role === 'manager' ? 'MANAGER' : affiliateUser.role.toUpperCase())
      : null;

    if (affiliateError || !affiliateUser || normalizedRole !== 'MANAGER') {
      return NextResponse.json({
        error: 'Access denied. Must be company manager to complete onboarding.'
      }, { status: 403 });
    }

    // Get company details
    const { data: company, error: companyError } = await supabase
      .from('affiliate_companies')
      .select('*')
      .eq('id', company_id)
      .single();

    if (companyError || !company) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 });
    }

    // Validate onboarding requirements
    const validationErrors: string[] = [];

    if (!documents_uploaded || documents_uploaded.length === 0) {
      validationErrors.push('Required documents must be uploaded');
    }

    if (!rate_cards_configured) {
      validationErrors.push('At least one rate card must be configured');
    }

    if (!service_areas_defined) {
      validationErrors.push('Service areas must be defined');
    }

    if (!banking_info_provided) {
      validationErrors.push('Banking information must be provided');
    }

    // Check for required documents
    const requiredDocuments = [
      'business_license',
      'insurance_certificate',
      'dot_authority', // if applicable
      'vehicle_registration'
    ];

    const missingDocs = requiredDocuments.filter(doc =>
      !documents_uploaded.some((uploaded: any) => uploaded.type === doc)
    );

    if (missingDocs.length > 0) {
      validationErrors.push(`Missing required documents: ${missingDocs.join(', ')}`);
    }

    if (validationErrors.length > 0) {
      return NextResponse.json({
        error: 'Onboarding validation failed',
        validation_errors: validationErrors
      }, { status: 400 });
    }

    // Update company onboarding status
    const { data: updatedCompany, error: updateError } = await supabase
      .from('affiliate_companies')
      .update({
        onboarding_status: 'completed',
        approval_status: 'under_review',
        onboarding_completed_at: new Date().toISOString(),
        onboarding_checklist: onboarding_checklist,
        updated_at: new Date().toISOString()
      })
      .eq('id', company_id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating company onboarding status:', updateError);
      return NextResponse.json({ error: 'Failed to complete onboarding' }, { status: 500 });
    }

    // Create audit log entry
    const { error: auditError } = await supabase
      .from('audit_logs')
      .insert({
        user_id: user.id,
        organization_id: company.organization_id,
        action: 'affiliate_onboarding_completed',
        table_name: 'affiliate_companies',
        record_id: company_id,
        new_values: {
          onboarding_status: 'completed',
          approval_status: 'under_review'
        },
        metadata: {
          documents_count: documents_uploaded.length,
          rate_cards_configured,
          service_areas_defined,
          banking_info_provided
        }
      });

    if (auditError) {
      console.error('Error creating audit log:', auditError);
    }

    // Notify admins about completed onboarding
    try {
      await fetch('/api/emails/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template: 'affiliate_onboarding_completed',
          to: '<EMAIL>', // Should come from org settings
          data: {
            company_name: company.name,
            company_id: company_id,
            completion_date: new Date().toISOString(),
            admin_review_url: `${process.env.NEXT_PUBLIC_APP_URL}/super-admin/affiliates/${company_id}`
          }
        })
      });
    } catch (emailError) {
      console.error('Error sending onboarding notification:', emailError);
      // Don't fail the request for email errors
    }

    // Send confirmation email to affiliate
    try {
      await fetch('/api/emails/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template: 'affiliate_onboarding_confirmation',
          to: company.primary_contact_email,
          data: {
            company_name: company.name,
            next_steps: [
              'Your application is now under review by our team',
              'You will receive an approval notification within 2-3 business days',
              'Once approved, you can start receiving quote requests'
            ]
          }
        })
      });
    } catch (emailError) {
      console.error('Error sending confirmation email:', emailError);
    }

    return NextResponse.json({
      success: true,
      message: 'Onboarding completed successfully',
      company: {
        id: updatedCompany.id,
        name: updatedCompany.name,
        onboarding_status: updatedCompany.onboarding_status,
        approval_status: updatedCompany.approval_status
      },
      next_steps: [
        'Your application is now under review by our team',
        'You will receive an approval notification within 2-3 business days',
        'Once approved, you can start receiving quote requests'
      ]
    });

  } catch (error) {
    console.error('Error in affiliate onboarding completion:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to check onboarding status and requirements
export async function GET(request: NextRequest) {
  try {
    // Authenticate affiliate user
    const context = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    
    const supabase = createClient();
    const url = new URL(request.url);
    const company_id = url.searchParams.get('company_id');

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!company_id) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    // Verify user has access to this company
    const { data: affiliateUser, error: affiliateError } = await supabase
      .from('affiliate_users')
      .select('role, affiliate_company_id')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', company_id)
      .single();

    if (affiliateError || !affiliateUser) {
      return NextResponse.json({
        error: 'Access denied. Must be associated with this company.'
      }, { status: 403 });
    }

    // Get onboarding status and requirements
    const { data: company, error: companyError } = await supabase
      .from('affiliate_companies')
      .select(`
        id,
        name,
        onboarding_status,
        approval_status,
        onboarding_checklist,
        onboarding_completed_at
      `)
      .eq('id', company_id)
      .single();

    if (companyError || !company) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 });
    }

    // Check uploaded documents
    const { data: documents, error: docsError } = await supabase
      .from('document_uploads')
      .select('id, type, status, uploaded_at')
      .eq('affiliate_company_id', company_id);

    // Check rate cards
    const { data: rateCards, error: rateCardsError } = await supabase
      .from('rate_cards')
      .select('id, name, status')
      .eq('affiliate_company_id', company_id);

    // Check service areas (this might be in company settings or separate table)
    const serviceAreasConfigured = company.onboarding_checklist?.service_areas_configured || false;

    const onboardingStatus = {
      company: {
        id: company.id,
        name: company.name,
        onboarding_status: company.onboarding_status,
        approval_status: company.approval_status,
        completed_at: company.onboarding_completed_at
      },
      requirements: {
        documents_uploaded: {
          completed: (documents?.length || 0) > 0,
          count: documents?.length || 0,
          details: documents || []
        },
        rate_cards_configured: {
          completed: (rateCards?.length || 0) > 0,
          count: rateCards?.length || 0,
          active_count: rateCards?.filter(rc => rc.status === 'active').length || 0
        },
        service_areas_defined: {
          completed: serviceAreasConfigured
        },
        banking_info_provided: {
          completed: company.onboarding_checklist?.banking_info_provided || false
        }
      },
      can_complete: false
    };

    // Determine if onboarding can be completed
    onboardingStatus.can_complete =
      onboardingStatus.requirements.documents_uploaded.completed &&
      onboardingStatus.requirements.rate_cards_configured.completed &&
      onboardingStatus.requirements.service_areas_defined.completed &&
      onboardingStatus.requirements.banking_info_provided.completed;

    return NextResponse.json(onboardingStatus);

  } catch (error) {
    console.error('Error checking onboarding status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}