import { NextRequest, NextResponse } from 'next/server';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

/**
 * GUG-26: Advanced Affiliate Features - Counter Offers API
 * 
 * Handles counter-offer submissions from affiliates
 */

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Affiliate counter-offer POST API called for quote:', params.id);

    // Authenticate as affiliate user
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const { context } = authResult;
    const body = await request.json();
    const {
      counter_amount,
      justification,
      company_id,
      valid_until_hours = 24
    } = body;

    // Validate required fields
    if (!counter_amount || !company_id) {
      return NextResponse.json({
        error: 'Missing required fields: counter_amount and company_id are required'
      }, { status: 400 });
    }

    const supabase = createClient();

    // Verify the quote exists and get original offer details
    const { data: originalOffer, error: offerError } = await supabase
      .from('quote_affiliate_offers')
      .select(`
        id,
        quote_id,
        company_id,
        rate_amount,
        status,
        quotes!inner(
          id,
          reference_number,
          service_type,
          pickup_location,
          dropoff_location
        )
      `)
      .eq('quote_id', params.id)
      .eq('company_id', company_id)
      .single();

    if (offerError || !originalOffer) {
      return NextResponse.json({
        error: 'Original offer not found'
      }, { status: 404 });
    }

    // Check if affiliate can submit counter-offers
    if (originalOffer.status !== 'sent' && originalOffer.status !== 'pending') {
      return NextResponse.json({
        error: 'Cannot submit counter-offer for this quote status'
      }, { status: 400 });
    }

    // For non-super-admin users, verify they have access to this company
    if (!context.roles.includes('SUPER_ADMIN')) {
      const { data: userCompany, error: accessError } = await supabase
        .from('affiliate_users')
        .select('id')
        .eq('user_id', context.userId)
        .eq('company_id', company_id)
        .single();

      if (accessError || !userCompany) {
        return NextResponse.json({
          error: 'Access denied to this company'
        }, { status: 403 });
      }
    }

    // Calculate expiry time
    const validUntil = new Date();
    validUntil.setHours(validUntil.getHours() + valid_until_hours);

    // Create counter-offer record
    const { data: counterOffer, error: createError } = await supabase
      .from('affiliate_counter_offers')
      .insert({
        original_offer_id: originalOffer.id,
        quote_id: params.id,
        company_id,
        original_amount: originalOffer.rate_amount,
        counter_amount,
        justification: justification || null,
        status: 'pending',
        valid_until: validUntil.toISOString(),
        submitted_by: context.userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating counter-offer:', createError);
      return NextResponse.json({
        error: 'Failed to create counter-offer',
        details: createError.message
      }, { status: 500 });
    }

    // Update the original offer status to indicate counter-offer submitted
    const { error: updateError } = await supabase
      .from('quote_affiliate_offers')
      .update({
        status: 'counter_offered',
        updated_at: new Date().toISOString()
      })
      .eq('id', originalOffer.id);

    if (updateError) {
      console.error('Error updating original offer status:', updateError);
      // Don't fail the request, just log the error
    }

    // Create notification for the client/admin
    const { error: notificationError } = await supabase
      .from('notifications')
      .insert({
        type: 'counter_offer_received',
        title: 'Counter Offer Received',
        message: `${originalOffer.quotes.reference_number}: Counter offer of $${counter_amount} received`,
        data: {
          quote_id: params.id,
          counter_offer_id: counterOffer.id,
          original_amount: originalOffer.rate_amount,
          counter_amount,
          company_id
        },
        created_at: new Date().toISOString()
      });

    if (notificationError) {
      console.error('Error creating notification:', notificationError);
      // Don't fail the request, just log the error
    }

    console.log('Successfully created counter-offer:', counterOffer.id);

    return NextResponse.json({
      success: true,
      counterOffer,
      message: 'Counter offer submitted successfully'
    });

  } catch (error) {
    console.error('Error in counter-offer POST API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}