import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { triggerQuoteUpdateBroadcast } from "@/lib/websocket/quote-broadcaster";

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`[Affiliate Reject API] Rejecting offer: ${params.id}`);

    const supabase = createClient();

    // Get the current user session
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error("[Affiliate Reject API] Authentication failed - Error:", authError);
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the user's affiliate company first
    const { data: userCompany, error: companyError } = await supabase
      .from("affiliate_users")
      .select(`
        affiliate_company_id,
        role,
        status,
        company:affiliate_companies!inner(
          id,
          name,
          status
        )
      `)
      .eq("user_id", user.id)
      .single();

    if (companyError || !userCompany) {
      console.error("[Affiliate Reject API] User company not found:", companyError);
      return NextResponse.json(
        { error: "Affiliate company not found" },
        { status: 404 }
      );
    }

    // Get the offer details and verify it belongs to this affiliate
    const { data: offer, error: offerError } = await supabase
      .from("quote_affiliate_offers")
      .select(`
        *,
        quotes!inner(
          id,
          reference_number,
          status
        )
      `)
      .eq("id", params.id)
      .eq("company_id", userCompany.affiliate_company_id) // Ensure offer belongs to this affiliate
      .single();

    if (offerError || !offer) {
      console.error("[Affiliate Reject API] Offer not found or unauthorized:", offerError);
      return NextResponse.json(
        { error: "Offer not found or unauthorized" },
        { status: 404 }
      );
    }

    // Check if the offer is in a valid state to be rejected
    const validStatuses = ["pending", "sent", "PENDING", "SENT"];
    if (!validStatuses.includes(offer.status)) {
      return NextResponse.json(
        { error: `Cannot reject offer with status: ${offer.status}` },
        { status: 400 }
      );
    }

    // Update the offer status to rejected
    const { data: updatedOffer, error: updateError } = await supabase
      .from("quote_affiliate_offers")
      .update({
        status: "REJECTED",
        updated_at: new Date().toISOString(),
        updated_by: user.id
      })
      .eq("id", params.id)
      .select(`
        *,
        quotes!inner(
          id,
          reference_number,
          status
        ),
        affiliate_companies!inner(
          id,
          name
        )
      `)
      .single();

    if (updateError) {
      console.error("[Affiliate Reject API] Error updating offer:", updateError);
      return NextResponse.json(
        { error: "Failed to reject offer" },
        { status: 500 }
      );
    }

    // Add timeline entry
    await supabase.from("quote_timeline").insert({
      quote_id: offer.quote_id,
      action: "AFFILIATE_REJECTED",
      status: "affiliate_rejected",
      created_by: user.id,
      details: JSON.stringify({
        offer_id: params.id,
        affiliate_company_id: offer.company_id,
        affiliate_name: updatedOffer.affiliate_companies.name,
        rejection_reason: "Affiliate declined the offer"
      })
    });

    // Check if we need to send to the next affiliate in the selection order
    try {
      const { data: nextAffiliateResult, error: nextAffiliateError } = await supabase
        .rpc('handle_affiliate_rejection', {
          p_rejected_offer_id: params.id
        });

      if (nextAffiliateError) {
        console.error("[Affiliate Reject API] Error handling next affiliate:", nextAffiliateError);
        // Don't fail the rejection, just log the error
      } else if (nextAffiliateResult) {
        console.log("[Affiliate Reject API] Next affiliate handling result:", nextAffiliateResult);

        // Update quote status based on rejection result
        if (nextAffiliateResult.next_affiliate_company_id) {
          // There's a next affiliate, update quote status
          await supabase
            .from('quotes')
            .update({
              status: 'sent_to_next_affiliate',
              updated_at: new Date().toISOString()
            })
            .eq('id', offer.quote_id);
        } else if (nextAffiliateResult.all_rejected) {
          // All affiliates have rejected, mark quote as unavailable
          await supabase
            .from('quotes')
            .update({
              status: 'no_affiliates_available',
              updated_at: new Date().toISOString()
            })
            .eq('id', offer.quote_id);
        }
      }
    } catch (nextAffiliateError) {
      console.error("[Affiliate Reject API] Error in next affiliate handling:", nextAffiliateError);
      // Don't fail the rejection, just log the error
    }

    // Create audit log entry
    try {
      await supabase.from('audit_logs').insert({
        table_name: 'quote_affiliate_offers',
        record_id: params.id,
        action: 'update',
        old_values: { status: offer.status },
        new_values: { status: 'REJECTED' },
        user_id: user.id,
        metadata: {
          quote_id: offer.quote_id,
          action_type: 'affiliate_rejection',
          affiliate_name: updatedOffer.affiliate_companies.name,
          quote_reference: updatedOffer.quotes.reference_number
        }
      });
    } catch (auditError) {
      console.error('[Affiliate Reject API] Error creating audit log:', auditError);
      // Don't fail the rejection for audit errors
    }

    // Broadcast real-time update
    try {
      await triggerQuoteUpdateBroadcast(
        offer.quote_id,
        "affiliate_rejected",
        user.id,
        {
          action: "affiliate_rejected",
          offer_id: params.id,
          affiliate_name: updatedOffer.affiliate_companies.name
        }
      );
    } catch (broadcastError) {
      console.error("[Affiliate Reject API] Error broadcasting update:", broadcastError);
      // Don't fail the rejection for broadcast errors
    }

    console.log(`[Affiliate Reject API] Successfully rejected offer: ${params.id}`);

    return NextResponse.json({
      success: true,
      data: {
        offer: updatedOffer,
        message: "Offer rejected successfully"
      }
    });

  } catch (error) {
    console.error("[Affiliate Reject API] Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
