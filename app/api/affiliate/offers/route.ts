import { NextRequest, NextResponse } from "next/server";

export const runtime = 'nodejs'
import { Database } from "@/lib/types/supabase";
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { cookies } from 'next/headers';
import { triggerAffiliateResponseBroadcast } from '@/lib/websocket/quote-broadcaster';

type AffiliateOffer = Database["public"]["Tables"]["quote_affiliate_offers"]["Row"];

/**
 * GET /api/affiliate/offers
 * Fetch affiliate offers for the authenticated affiliate user
 */
export async function GET(request: NextRequest) {
  try {
    // Create Supabase client with proper auth (same pattern as working portals)
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Verify user is authenticated (same pattern as working portals)
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      console.log("GET /api/affiliate/offers - Authentication failed:", authError?.message);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log("GET /api/affiliate/offers - User authenticated:", user.id);

    // Get request parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get("status");
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    const affiliateCompanyId = searchParams.get("companyId");

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("roles")
      .eq("id", user.id)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      );
    }

    // Check if user is an affiliate
    if (!profile.roles?.includes("AFFILIATE")) {
      return NextResponse.json(
        { error: "Access denied. Only affiliates can view offers." },
        { status: 403 }
      );
    }

    // Get the affiliate company for this user using the new multi-company system
    let companyId = affiliateCompanyId;
    if (!companyId) {
      // First try the new system (affiliate_users)
      const { data: userCompany, error: userCompanyError } = await supabase
        .from("affiliate_users")
        .select("affiliate_company_id")
        .eq("user_id", user.id)
        .eq("status", "ACTIVE")
        .single();

      if (userCompany) {
        companyId = userCompany.affiliate_company_id;
      } else {
        // Fallback to old system (owner_id)
        const { data: affiliateCompany, error: companyError } = await supabase
          .from("affiliate_companies")
          .select("id")
          .eq("owner_id", user.id)
          .single();

        if (companyError || !affiliateCompany) {
          return NextResponse.json(
            { error: "Affiliate company not found. Please ensure you are associated with an active affiliate company." },
            { status: 404 }
          );
        }
        companyId = affiliateCompany.id;
      }
    }

    // Build the query - fetch offers first, then quotes separately due to RLS issues
    let query = supabase
      .from("quote_affiliate_offers")
      .select(`
        id,
        quote_id,
        offer_price,
        status,
        notes,
        created_at,
        updated_at,
        expires_at,
        counter_offer_amount,
        counter_offer_note,
        is_counter_offer
      `)
      .eq("affiliate_company_id", companyId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    // Add status filter if provided
    if (status) {
      query = query.eq("status", status);
    }

    const { data: offers, error: offersError } = await query;

    if (offersError) {
      console.error("Error fetching affiliate offers:", offersError);
      return NextResponse.json(
        { error: "Failed to fetch affiliate offers" },
        { status: 500 }
      );
    }

    console.log(`Found ${offers?.length || 0} offers for company ${companyId}`);

    // Fetch quote data separately for each offer
    if (offers && offers.length > 0) {
      const quoteIds = offers.map(offer => offer.quote_id);

      const { data: quotes, error: quotesError } = await supabase
        .from("quotes")
        .select(`
          id,
          reference_number,
          pickup_city,
          dropoff_city,
          pickup_date,
          pickup_time,
          passenger_count,
          vehicle_type,
          service_type,
          status,
          special_requests,
          contact_name,
          contact_email,
          contact_phone,
          customer_id
        `)
        .in("id", quoteIds);

      if (quotesError) {
        console.error("Error fetching quotes:", quotesError);
        // Return offers without quote data rather than failing completely
        return NextResponse.json(offers, { status: 200 });
      }

      // Merge quote data with offers
      const offersWithQuotes = offers.map(offer => {
        const quote = quotes?.find(q => q.id === offer.quote_id);
        return {
          ...offer,
          quote: quote || null
        };
      });

      console.log(`Successfully merged quote data for ${offersWithQuotes.length} offers`);
      return NextResponse.json(offersWithQuotes, { status: 200 });
    }

    return NextResponse.json(offers || [], { status: 200 });
  } catch (error) {
    console.error("Error in GET /api/affiliate/offers:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/affiliate/offers
 * Submit a counter-offer for a quote
 */
export async function POST(request: NextRequest) {
  try {
    // Create Supabase client with proper auth (same pattern as working portals)
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Verify user is authenticated (same pattern as working portals)
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      console.log("POST /api/affiliate/offers - Authentication failed:", authError?.message);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log("POST /api/affiliate/offers - User authenticated:", user.id);

    // Get request parameters
    const body = await request.json();
    const { quoteId, amount, notes } = body;

    // Validate required fields
    if (!quoteId || !amount) {
      return NextResponse.json(
        { error: "Quote ID and amount are required" },
        { status: 400 }
      );
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("roles")
      .eq("id", user.id)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      );
    }

    // Check if user is an affiliate
    if (!profile.roles?.includes("AFFILIATE")) {
      return NextResponse.json(
        { error: "Only affiliates can submit counter-offers" },
        { status: 403 }
      );
    }

    // Get the affiliate company for this user
    const { data: affiliateCompany, error: companyError } = await supabase
      .from("affiliate_companies")
      .select("id")
      .eq("user_id", user.id)
      .single();

    if (companyError || !affiliateCompany) {
      return NextResponse.json(
        { error: "Affiliate company not found" },
        { status: 404 }
      );
    }

    // Check if there's an existing offer for this quote and affiliate
    const { data: existingOffer, error: offerError } = await supabase
      .from("quote_affiliate_offers")
      .select("id, status")
      .eq("quote_id", quoteId)
      .eq("affiliate_company_id", affiliateCompany.id)
      .single();

    if (offerError && offerError.code !== "PGRST116") {
      // PGRST116 is "not found" which is expected if no offer exists
      return NextResponse.json(
        { error: "Error checking existing offers" },
        { status: 500 }
      );
    }

    if (existingOffer) {
      // Update existing offer
      const { data: updatedOffer, error: updateError } = await supabase
        .from("quote_affiliate_offers")
        .update({
          amount: parseFloat(amount),
          notes: notes || null,
          status: "COUNTER_OFFERED",
          updated_at: new Date().toISOString(),
        })
        .eq("id", existingOffer.id)
        .select()
        .single();

      if (updateError) {
        return NextResponse.json(
          { error: "Failed to update counter-offer" },
          { status: 500 }
        );
      }

      // Broadcast real-time update for counter-offer
      await triggerAffiliateResponseBroadcast(
        quoteId,
        affiliateCompany.id,
        'counter_offer',
        {
          amount: parseFloat(amount),
          message: notes,
          companyName: affiliateCompany.name || 'Unknown Company'
        }
      );

      return NextResponse.json(updatedOffer, { status: 200 });
    } else {
      // Create new offer
      const { data: newOffer, error: createError } = await supabase
        .from("quote_affiliate_offers")
        .insert({
          quote_id: quoteId,
          affiliate_company_id: affiliateCompany.id,
          amount: parseFloat(amount),
          notes: notes || null,
          status: "COUNTER_OFFERED",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (createError) {
        return NextResponse.json(
          { error: "Failed to create counter-offer" },
          { status: 500 }
        );
      }

      // Broadcast real-time update for new counter-offer
      await triggerAffiliateResponseBroadcast(
        quoteId,
        affiliateCompany.id,
        'counter_offer',
        {
          amount: parseFloat(amount),
          message: notes,
          companyName: affiliateCompany.name || 'Unknown Company'
        }
      );

      return NextResponse.json(newOffer, { status: 201 });
    }
  } catch (error: any) {
    console.error("POST /api/affiliate/offers error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
