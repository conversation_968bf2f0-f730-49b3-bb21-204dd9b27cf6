import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export const runtime = 'nodejs'

// Updated schema to match actual database columns
const rateCardSchema = z.object({
  id: z.string().uuid().optional(),
  
  // Required fields
  vehicle_type: z.string().min(1, "Vehicle type is required"),
  
  // Pricing model - match database enum values
  pricing_model: z.enum(['flat_rate', 'distance_based', 'time_based', 'zone_based', 'hourly', 'hybrid', 'dynamic']).default('flat_rate'),
  
  // Core pricing fields - match database schema exactly
  base_rate: z.number().nonnegative().default(0),
  per_mile_rate: z.number().nonnegative().default(0),
  per_minute_rate: z.number().nonnegative().default(0),
  per_hour_rate: z.number().nonnegative().default(0),
  minimum_charge: z.number().nonnegative().default(0),
  minimum_distance: z.number().nonnegative().default(0),
  minimum_time: z.number().nonnegative().default(0),
  
  // Additional fees
  airport_fee: z.number().nonnegative().default(0),
  fuel_surcharge_rate: z.number().nonnegative().default(0),
  cancellation_fee: z.number().nonnegative().default(0),
  additional_passenger_fee: z.number().nonnegative().default(0),
  luggage_fee_per_bag: z.number().nonnegative().default(0),
  
  // Passenger and luggage limits
  max_passengers_included: z.number().positive().default(1),
  max_luggage_included: z.number().nonnegative().default(2),
  
  // Multipliers
  peak_hour_multiplier: z.number().positive().default(1.0),
  weekend_multiplier: z.number().positive().default(1.0),
  holiday_multiplier: z.number().positive().default(1.0),
  late_night_multiplier: z.number().positive().default(1.0),
  
  // Booking constraints
  min_advance_booking_hours: z.number().nonnegative().default(2),
  max_advance_booking_days: z.number().positive().default(365),
  
  // Settings
  status: z.enum(['active', 'inactive', 'draft']).default('active'),
  auto_quote: z.boolean().default(true),
  preferred_partner: z.boolean().default(false),
  tolls_included: z.boolean().default(false),
  gratuity_included: z.boolean().default(false),
  
  // Optional descriptive fields
  name: z.string().optional(),
  description: z.string().optional(),
  service_type: z.enum(['airport_transfer', 'point_to_point', 'hourly_charter', 'event_transportation', 'corporate_travel', 'wedding_transport']).default("point_to_point"), // Required in database
  rate_card_code: z.string().optional(),
  coverage_areas: z.array(z.string()).default([]),
  operating_hours: z.record(z.any()).default({}),
  blackout_dates: z.array(z.string()).default([]),
  cancellation_policy: z.record(z.any()).default({}),
  terms_and_conditions: z.string().optional(),
  notes: z.string().optional(),
  custom_fields: z.record(z.any()).default({}),
  
  // Geographic fields
  city: z.string().optional(),
  state_province: z.string().optional(),
  country: z.string().default('USA'),
  
  // Dates
  effective_date: z.string().optional(),
  expiry_date: z.string().optional(),
  
  // Priority
  priority_level: z.number().default(1),
});

async function authenticateAffiliateUser(supabase: any, userId: string, companyId: string) {
  const { data: affiliateUser, error } = await supabase
    .from('affiliate_users')
    .select('role, employment_status, affiliate_company_id')
    .eq('user_id', userId)
    .eq('affiliate_company_id', companyId)
    .eq('employment_status', 'active')
    .single();

  if (error || !affiliateUser) {
    throw new Error('Unauthorized: User not found in affiliate company');
  }

  if (!['MANAGER', 'DISPATCHER'].includes(affiliateUser.role)) {
    throw new Error('Unauthorized: Insufficient permissions');
  }

  return affiliateUser;
}

export async function GET(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            // Readonly, ignore
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, "", options);
          } catch (error) {
            // Readonly, ignore
          }
        },
      },
    }
  );

  try {
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    // Get company ID from header
    const companyId = request.headers.get('X-Affiliate-Company-ID');
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID required' }, { status: 400 });
    }

    // Authenticate affiliate user
    await authenticateAffiliateUser(supabase, user.id, companyId);

    // Fetch rate cards for the company
    const { data: rateCards, error: fetchError } = await supabase
      .from('rate_cards')
      .select('*')
      .eq('affiliate_company_id', companyId)
      .order('created_at', { ascending: false });

    if (fetchError) {
      console.error('Error fetching rate cards:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch rate cards' }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: rateCards || [] });

  } catch (error: any) {
    console.error('Rate cards GET error:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            // Readonly, ignore
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, "", options);
          } catch (error) {
            // Readonly, ignore
          }
        },
      },
    }
  );

  try {
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    // Get company ID from header
    const companyId = request.headers.get('X-Affiliate-Company-ID');
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID required' }, { status: 400 });
    }

    // Authenticate affiliate user
    await authenticateAffiliateUser(supabase, user.id, companyId);

    // Get organization ID from affiliate company
    const { data: company, error: companyError } = await supabase
      .from('affiliate_companies')
      .select('organization_id')
      .eq('id', companyId)
      .single();

    if (companyError || !company) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();
    
    // Handle both single rate card and array of rate cards
    const rateCardsData = Array.isArray(body) ? body : [body];
    
    // Validate each rate card
    const validatedRateCards = rateCardsData.map(cardData => {
      const validation = rateCardSchema.safeParse(cardData);
      if (!validation.success) {
        throw new Error(`Validation failed: ${validation.error.message}`);
      }
      return validation.data;
    });

    // Prepare rate cards for insertion
    const rateCardsToInsert = validatedRateCards.map(card => ({
      ...card,
      affiliate_company_id: companyId,
      organization_id: company.organization_id,
      name: card.name || `${card.vehicle_type} - ${card.pricing_model}`,
      created_by: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }));

    // Insert rate cards
    const { data: insertedRateCards, error: insertError } = await supabase
      .from('rate_cards')
      .insert(rateCardsToInsert)
      .select();

    if (insertError) {
      console.error('Error inserting rate cards:', insertError);
      return NextResponse.json({ 
        error: 'Failed to save rate cards', 
        details: insertError.message 
      }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      data: insertedRateCards,
      message: `Successfully saved ${insertedRateCards?.length || 0} rate card(s)`
    });

  } catch (error: any) {
    console.error('Rate cards POST error:', error);
    return NextResponse.json({ 
      error: error.message || 'Internal server error',
      details: error.stack
    }, { status: 500 });
  }
}