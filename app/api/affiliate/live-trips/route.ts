import { NextRequest, NextResponse } from "next/server"
import { createAuthenticatedSupabaseClient, requireAuth } from "@/lib/auth/server";

export async function GET(request: NextRequest) {
  try {
    // Require authentication
    const user = await requireAuth();
    const supabase = await createAuthenticatedSupabaseClient();

    // Get user's companies
    const { data: userCompanies, error: companiesError } = await supabase
      .from('affiliate_users')
      .select(`
        affiliate_company_id,
        affiliate_companies (
          id,
          name,
          city,
          state
        )
      `)
      .eq('user_id', user.id)
      .eq('employment_status', 'active')

    if (companiesError) {
      console.error('Error fetching user companies:', companiesError)
      return NextResponse.json(
        { error: 'Failed to fetch user companies' },
        { status: 500 }
      )
    }

    if (!userCompanies || userCompanies.length === 0) {
      return NextResponse.json({ trips: [] })
    }

    const companyIds = userCompanies.map(uc => uc.affiliate_company_id)

    // Get active trips for user's companies
    const { data: trips, error: tripsError } = await supabase
      .from('trips')
      .select(`
        id,
        quote_id,
        company_id,
        status,
        driver_id,
        vehicle_id,
        created_at,
        updated_at,
        quotes (
          id,
          reference_number,
          service_type,
          vehicle_type,
          pickup_location,
          dropoff_location,
          date,
          time,
          duration,
          distance,
          passenger_count,
          luggage_count,
          total_amount,
          contact_name,
          contact_email,
          contact_phone,
          city,
          flight_number,
          special_requests
        ),
        affiliate_companies (
          id,
          name,
          city,
          state
        )
      `)
      .in('company_id', companyIds)
      .in('status', ['assigned', 'confirmed', 'en_route', 'arrived', 'in_progress'])
      .order('created_at', { ascending: false })
      .limit(100)

    if (tripsError) {
      console.error('Error fetching trips:', tripsError)
      return NextResponse.json(
        { error: 'Failed to fetch trips' },
        { status: 500 }
      )
    }

    // Transform the data to match the frontend expectations
    const transformedTrips = trips?.map((trip: any) => ({
      id: trip.id,
      status: trip.status,
      referenceNumber: trip.quotes?.reference_number || 'N/A',
      serviceType: trip.quotes?.service_type || 'point',
      vehicleType: trip.quotes?.vehicle_type || 'Sedan',
      pickupLocation: trip.quotes?.pickup_location || 'Unknown',
      dropoffLocation: trip.quotes?.dropoff_location || 'Unknown',
      date: trip.quotes?.date || new Date().toISOString().split('T')[0],
      time: trip.quotes?.time || '00:00',
      duration: trip.quotes?.duration || 'N/A',
      distance: trip.quotes?.distance || 'N/A',
      passengerCount: trip.quotes?.passenger_count || 1,
      luggageCount: trip.quotes?.luggage_count || 0,
      totalAmount: trip.quotes?.total_amount || 0,
      contactName: trip.quotes?.contact_name || 'Unknown',
      contactEmail: trip.quotes?.contact_email || '',
      contactPhone: trip.quotes?.contact_phone || '',
      city: trip.quotes?.city || '',
      flightNumber: trip.quotes?.flight_number || null,
      specialRequests: trip.quotes?.special_requests || null,
      company: {
        id: trip.affiliate_companies?.id || trip.company_id,
        name: trip.affiliate_companies?.name || 'Unknown Company',
        city: trip.affiliate_companies?.city || '',
        state: trip.affiliate_companies?.state || ''
      },
      driver: null, // Will be populated when driver assignment is implemented
      vehicle: null, // Will be populated when vehicle assignment is implemented
      createdAt: trip.created_at,
      updatedAt: trip.updated_at
    })) || []

    return NextResponse.json({
      trips: transformedTrips,
      total: transformedTrips.length
    })

  } catch (error) {
    console.error('Error in affiliate live trips API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
