import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import type { Database } from '@/lib/database.types'

const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

export async function GET(request: Request) {
  try {
    console.log('[api/affiliate/quotes] Starting request')
    
    // Get query parameters
    const url = new URL(request.url)
    const quoteIds = url.searchParams.get('quoteIds')
    
    if (!quoteIds) {
      console.log('[api/affiliate/quotes] No quoteIds provided')
      return NextResponse.json({ quotes: [] })
    }

    // Parse the quote IDs
    const quoteIdArray = quoteIds.split(',')
    console.log(`[api/affiliate/quotes] Fetching ${quoteIdArray.length} quotes`)

    const supabase = createSupabaseClient()
    
    // Get the user's session for authentication
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      console.error('[api/affiliate/quotes] No session found')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log(`[api/affiliate/quotes] User authenticated: ${session.user.id}`)

    // Check if the user has proper affiliate role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (!profile || !profile.role || !profile.role.includes('AFFILIATE')) {
      console.error(`[api/affiliate/quotes] User is not an affiliate: ${profile?.role}`)
      return NextResponse.json({ error: 'Not authorized as affiliate' }, { status: 403 })
    }

    // Get all companies affiliated with this user
    const { data: affiliateCompanies } = await supabase
      .from('affiliate_companies')
      .select('id')
      .eq('owner_id', session.user.id)

    if (!affiliateCompanies || affiliateCompanies.length === 0) {
      console.log('[api/affiliate/quotes] No affiliate companies found for user')
      return NextResponse.json({ quotes: [] })
    }

    const companyIds = affiliateCompanies.map(company => company.id)
    console.log(`[api/affiliate/quotes] User has ${companyIds.length} affiliate companies: ${companyIds.join(', ')}`)

    // Verify that all requested quotes are linked to the user's affiliate companies
    // through either affiliate_company_id, company_id or quote_offers
    const authorizedQuoteIds: string[] = []
    
    // Two approaches to get quotes:
    // 1. Directly assigned quotes (where affiliate_company_id or company_id matches)
    const { data: directQuotes } = await supabase
      .from('quotes')
      .select('id')
      .in('id', quoteIdArray)
      .or(`affiliate_company_id.in.(${companyIds.join(',')}),company_id.in.(${companyIds.join(',')})`)

    if (directQuotes && directQuotes.length > 0) {
      authorizedQuoteIds.push(...directQuotes.map(q => q.id))
      console.log(`[api/affiliate/quotes] Found ${directQuotes.length} directly assigned quotes`)
    }

    // 2. Quotes linked through quote_offers
    const { data: offerQuotes } = await supabase
      .from('quote_offers')
      .select('quote_id')
      .in('company_id', companyIds)
      .in('quote_id', quoteIdArray)

    if (offerQuotes && offerQuotes.length > 0) {
      const offerQuoteIds = offerQuotes
        .map(o => o.quote_id)
        .filter((id): id is string => id !== null);
      
      // Add only new IDs that aren't already in the array
      offerQuoteIds.forEach(id => {
        if (!authorizedQuoteIds.includes(id)) {
          authorizedQuoteIds.push(id)
        }
      })
      console.log(`[api/affiliate/quotes] Found ${offerQuoteIds.length} quotes via offers`)
    }

    console.log(`[api/affiliate/quotes] User is authorized to access ${authorizedQuoteIds.length} of ${quoteIdArray.length} requested quotes`)

    if (authorizedQuoteIds.length === 0) {
      console.log('[api/affiliate/quotes] No authorized quotes found')
      return NextResponse.json({ quotes: [] })
    }
    
    // Fetch the complete data for all authorized quotes
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select(`
        id,
        reference_number,
        service_type,
        vehicle_type,
        pickup_location,
        dropoff_location,
        date,
        time,
        passenger_count,
        luggage_count,
        special_requests,
        distance,
        duration,
        status,
        customer_id,
        contact_email,
        contact_name,
        contact_phone,
        priority,
        created_at,
        updated_at,
        total_amount,
        duration_hours,
        is_multi_day,
        flight_number,
        is_return_trip,
        return_date,
        return_time,
        return_flight_number,
        car_seats_needed,
        infant_seats,
        toddler_seats,
        booster_seats,
        intermediate_stops
      `)
      .in('id', authorizedQuoteIds)
    
    if (quotesError) {
      console.error('[api/affiliate/quotes] Error fetching quotes:', quotesError)
      return NextResponse.json({ error: 'Error fetching quotes' }, { status: 500 })
    }

    // If we have quotes with customer_ids, fetch the associated profiles
    const customerIds = quotes?.filter(q => q.customer_id).map(q => q.customer_id) || []
    
    let customerProfiles: Record<string, any> = {}
    
    if (customerIds.length > 0) {
      console.log(`[api/affiliate/quotes] Fetching ${customerIds.length} customer profiles`)
      
      try {
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, full_name, email, phone_number')
          .in('id', customerIds)
        
        if (profilesError) {
          console.error('[api/affiliate/quotes] Error fetching profiles:', profilesError)
        } else if (profiles) {
          // Create a map of customer profiles by ID for easy lookups
          profiles.forEach(profile => {
            customerProfiles[profile.id] = profile
          })
          console.log(`[api/affiliate/quotes] Found ${profiles.length} customer profiles`)
        }
      } catch (error) {
        console.error('[api/affiliate/quotes] Error in profiles fetch:', error)
      }
    }
    
    // Add customer data to each quote
    const quotesWithCustomers = quotes?.map(quote => {
      const customerProfile = quote.customer_id ? customerProfiles[quote.customer_id] : null
      
      return {
        ...quote,
        customer: customerProfile ? {
          id: customerProfile.id,
          full_name: customerProfile.full_name,
          email: customerProfile.email,
          phone_number: customerProfile.phone_number
        } : null
      }
    }) || []

    // Return the quotes found (may be empty if none were authorized)
    console.log(`[api/affiliate/quotes] Successfully fetched ${quotes?.length || 0} quotes`)
    return NextResponse.json({ quotes: quotesWithCustomers || [] })
  } catch (error) {
    console.error('[api/affiliate/quotes] Error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
} 