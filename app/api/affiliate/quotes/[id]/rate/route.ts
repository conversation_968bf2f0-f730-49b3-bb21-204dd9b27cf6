import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { triggerQuoteUpdateBroadcast } from "@/lib/websocket/quote-broadcaster";

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`[Affiliate Rate API] Submitting rate for quote: ${params.id}`);

    const supabase = createClient();

    // Get the current user session
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      console.error("[Affiliate Rate API] Authentication error:", authError);
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { rateAmount } = body;

    if (!rateAmount || isNaN(Number(rateAmount))) {
      return NextResponse.json(
        { error: "Valid rate amount is required" },
        { status: 400 }
      );
    }

    // Get the user's affiliate company
    const { data: userCompany, error: companyError } = await supabase
      .from("affiliate_users")
      .select(`
        affiliate_company_id,
        role,
        status,
        company:affiliate_companies!inner(
          id,
          name,
          status
        )
      `)
      .eq("user_id", user.id)
      .single();

    if (companyError || !userCompany) {
      console.error("[Affiliate Rate API] User company not found:", companyError);
      return NextResponse.json(
        { error: "Affiliate company not found" },
        { status: 404 }
      );
    }

    // Get the quote details
    const { data: quote, error: quoteError } = await supabase
      .from("quotes")
      .select(`
        id,
        reference_number,
        status,
        pickup_location,
        dropoff_location,
        date,
        time
      `)
      .eq("id", params.id)
      .single();

    if (quoteError || !quote) {
      console.error("[Affiliate Rate API] Quote not found:", quoteError);
      return NextResponse.json(
        { error: "Quote not found" },
        { status: 404 }
      );
    }

    // Check if there's already an offer for this quote from this affiliate
    const { data: existingOffer, error: offerCheckError } = await supabase
      .from("quote_affiliate_offers")
      .select("id, status, rate_amount")
      .eq("quote_id", params.id)
      .eq("company_id", userCompany.affiliate_company_id)
      .single();

    if (offerCheckError && offerCheckError.code !== 'PGRST116') {
      console.error("[Affiliate Rate API] Error checking existing offer:", offerCheckError);
      return NextResponse.json(
        { error: "Failed to check existing offer" },
        { status: 500 }
      );
    }

    let offer;
    if (existingOffer) {
      // Update existing offer
      const { data: updatedOffer, error: updateError } = await supabase
        .from("quote_affiliate_offers")
        .update({
          rate_amount: Number(rateAmount),
          status: "submitted",
          updated_at: new Date().toISOString(),
          updated_by: user.id,
          notes: `Rate updated to $${rateAmount}`
        })
        .eq("id", existingOffer.id)
        .select(`
          *,
          affiliate_companies!inner(
            id,
            name
          )
        `)
        .single();

      if (updateError) {
        console.error("[Affiliate Rate API] Error updating offer:", updateError);
        return NextResponse.json(
          { error: "Failed to update rate offer" },
          { status: 500 }
        );
      }

      offer = updatedOffer;
    } else {
      // Create new offer
      const { data: newOffer, error: createError } = await supabase
        .from("quote_affiliate_offers")
        .insert({
          quote_id: params.id,
          company_id: userCompany.affiliate_company_id,
          rate_amount: Number(rateAmount),
          rate_currency: "USD",
          status: "submitted",
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
          created_by: user.id,
          updated_by: user.id,
          notes: `Rate proposal: $${rateAmount}`
        })
        .select(`
          *,
          affiliate_companies!inner(
            id,
            name
          )
        `)
        .single();

      if (createError) {
        console.error("[Affiliate Rate API] Error creating offer:", createError);
        return NextResponse.json(
          { error: "Failed to create rate offer" },
          { status: 500 }
        );
      }

      offer = newOffer;
    }

    // Add timeline entry
    await supabase.from("quote_timeline").insert({
      quote_id: params.id,
      action: "RATE_SUBMITTED",
      status: "rate_submitted",
      created_by: user.id,
      details: JSON.stringify({
        offer_id: offer.id,
        affiliate_company_id: userCompany.affiliate_company_id,
        affiliate_name: userCompany.company.name,
        rate_amount: Number(rateAmount),
        currency: "USD",
        action_type: existingOffer ? "rate_updated" : "rate_submitted"
      })
    });

    // Broadcast real-time update
    try {
      await triggerQuoteUpdateBroadcast(
        params.id,
        "rate_submitted",
        user.id,
        {
          action: "rate_submitted",
          offer_id: offer.id,
          affiliate_name: userCompany.affiliate_companies.name,
          rate_amount: Number(rateAmount)
        }
      );
    } catch (broadcastError) {
      console.error("[Affiliate Rate API] Error broadcasting update:", broadcastError);
      // Don't fail the request for broadcast errors
    }

    console.log(`[Affiliate Rate API] Successfully submitted rate: ${rateAmount} for quote: ${params.id}`);

    return NextResponse.json({
      success: true,
      data: {
        offer,
        message: `Rate ${existingOffer ? 'updated' : 'submitted'} successfully`
      }
    });

  } catch (error) {
    console.error("[Affiliate Rate API] Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
