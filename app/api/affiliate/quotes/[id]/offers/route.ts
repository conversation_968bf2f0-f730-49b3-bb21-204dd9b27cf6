import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";

export const runtime = 'nodejs'
import { cookies } from "next/headers";
import { NextResponse } from 'next/server';
import { createAffiliateOfferWithBroadcast } from '@/lib/websocket/quote-integration';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's company
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_users')
      .select('affiliate_company_id')
      .eq('user_id', session.user.id)
      .single();

    if (userCompanyError || !userCompany) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 });
    }

    // Get offers for the quote and company
    const { data: offers, error: offersError } = await supabase
      .from('quote_affiliate_offers')
      .select('*')
      .eq('quote_id', params.id)
      .eq('company_id', userCompany.affiliate_company_id)
      .order('created_at', { ascending: false });

    if (offersError) {
      console.error('Error fetching offers:', offersError);
      return NextResponse.json({ error: 'Failed to fetch offers' }, { status: 500 });
    }

    return NextResponse.json(offers);
  } catch (error) {
    console.error('Error in GET /api/affiliate/quotes/[quoteId]/offers:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's company
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_users')
      .select('affiliate_company_id, role')
      .eq('user_id', session.user.id)
      .single();

    if (userCompanyError || !userCompany) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 });
    }

    // Updated for simplified role architecture - check both system and company roles
    const userRoles = session.user.app_metadata?.roles || [];
    const hasSystemAccess = userRoles.includes('AFFILIATE') || userRoles.includes('SUPER_ADMIN');
    const hasCompanyAccess = ['MANAGER', 'DISPATCHER'].includes(userCompany.role);
    
    if (!hasSystemAccess && !hasCompanyAccess) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get request body
    const { rateAmount, currency, notes, expiresAt } = await request.json();

    if (!rateAmount || !currency) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Check if offer already exists
    const { data: existingOffer, error: existingOfferError } = await supabase
      .from('quote_affiliate_offers')
      .select('id')
      .eq('quote_id', params.id)
      .eq('company_id', userCompany.affiliate_company_id)
      .single();

    if (existingOffer) {
      return NextResponse.json({ error: 'Offer already exists' }, { status: 409 });
    }

    // Create offer with WebSocket broadcasting
    const offerResult = await createAffiliateOfferWithBroadcast(
      params.id,
      userCompany.affiliate_company_id,
      rateAmount,
      currency,
      notes,
      expiresAt,
      session.user.id
    );

    if (!offerResult.success) {
      console.error('Error creating offer:', offerResult.error);
      return NextResponse.json({ error: offerResult.error }, { status: 500 });
    }

    return NextResponse.json(offerResult.data);
  } catch (error) {
    console.error('Error in POST /api/affiliate/quotes/[quoteId]/offers:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { quoteId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's company
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_users')
      .select('affiliate_company_id, role')
      .eq('user_id', session.user.id)
      .single();

    if (userCompanyError || !userCompany) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 });
    }

    // Updated for simplified role architecture - check both system and company roles
    const userRoles = session.user.app_metadata?.roles || [];
    const hasSystemAccess = userRoles.includes('AFFILIATE') || userRoles.includes('SUPER_ADMIN');
    const hasCompanyAccess = ['MANAGER', 'DISPATCHER'].includes(userCompany.role);
    
    if (!hasSystemAccess && !hasCompanyAccess) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get request body
    const { offerId, rateAmount, currency, notes, expiresAt } = await request.json();

    if (!offerId) {
      return NextResponse.json({ error: 'Missing offer ID' }, { status: 400 });
    }

    // Update offer
    const { data: offer, error: offerError } = await supabase
      .from('quote_affiliate_offers')
      .update({
        rate_amount: rateAmount,
        currency,
        notes,
        expires_at: expiresAt,
        updated_by: session.user.id
      })
      .eq('id', offerId)
      .eq('company_id', userCompany.affiliate_company_id)
      .select()
      .single();

    if (offerError) {
      console.error('Error updating offer:', offerError);
      return NextResponse.json({ error: 'Failed to update offer' }, { status: 500 });
    }

    return NextResponse.json(offer);
  } catch (error) {
    console.error('Error in PATCH /api/affiliate/quotes/[quoteId]/offers:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { quoteId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's company
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_users')
      .select('affiliate_company_id, role')
      .eq('user_id', session.user.id)
      .single();

    if (userCompanyError || !userCompany) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 });
    }

    // Updated for simplified role architecture - check both system and company roles
    const userRoles = session.user.app_metadata?.roles || [];
    const hasSystemAccess = userRoles.includes('AFFILIATE') || userRoles.includes('SUPER_ADMIN');
    const hasCompanyAccess = ['MANAGER', 'DISPATCHER'].includes(userCompany.role);
    
    if (!hasSystemAccess && !hasCompanyAccess) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get offer ID from URL
    const url = new URL(request.url);
    const offerId = url.searchParams.get('offerId');
    if (!offerId) {
      return NextResponse.json({ error: 'Missing offer ID' }, { status: 400 });
    }

    // Delete offer
    const { error: deleteError } = await supabase
      .from('quote_affiliate_offers')
      .delete()
      .eq('id', offerId)
      .eq('company_id', userCompany.affiliate_company_id);

    if (deleteError) {
      console.error('Error deleting offer:', deleteError);
      return NextResponse.json({ error: 'Failed to delete offer' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/affiliate/quotes/[quoteId]/offers:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 