// Fixed version of the affiliate user-companies API route
import { NextRequest, NextResponse } from "next/server";
import { createAuthenticatedSupabaseClient, requireAuth } from "@/lib/auth/server";

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth();

    console.log('GET /api/affiliate/user-companies: Fetching companies for user', user.id);
    
    // Use authenticated client to respect RLS policies
    const supabase = await createAuthenticatedSupabaseClient();

    // Fixed query with correct column names
    const { data, error: fetchError } = await supabase
      .from('affiliate_users')
      .select(`
        id,
        role,
        employment_status,
        created_at,
        company:affiliate_companies (
          id,
          name,
          primary_contact_email,
          primary_contact_phone,
          street_address,
          city,
          state_province,
          postal_code,
          country,
          status,
          created_at,
          updated_at
        )
      `)
      .eq('user_id', user.id)
      .eq('employment_status', 'active');

    console.log('GET /api/affiliate/user-companies: Query result:', {
      data: data ? data.map(item => ({
        ...item,
        company: Array.isArray(item.company) ? item.company[0] : item.company
      })) : null,
      error: fetchError,
      userId: user.id
    });

    if (fetchError) {
      console.error('GET /api/affiliate/user-companies: Failed to fetch companies', fetchError);

      // Handle expected scenarios gracefully for new users
      if (fetchError.message?.includes('connection') ||
        fetchError.code === 'ECONNREFUSED' ||
        fetchError.code === 'PGRST116' || // No rows found
        fetchError.message?.includes('No rows')) {
        console.log("GET /api/affiliate/user-companies: No companies found for user (expected for new affiliates)");
        return NextResponse.json([]);
      }

      return NextResponse.json(
        { error: "Failed to fetch affiliate company associations." },
        { status: 500 }
      );
    }

    if (!data || data.length === 0) {
      console.log('GET /api/affiliate/user-companies: No companies found for user');
      return NextResponse.json([]);
    }

    console.log('GET /api/affiliate/user-companies: Returning', data.length, 'companies');
    return NextResponse.json(data);

  } catch (error: any) {
    console.error("GET /api/affiliate/user-companies: Unexpected error:", error);

    // Handle authentication errors properly
    if (error.message?.includes('Unauthorized')) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}