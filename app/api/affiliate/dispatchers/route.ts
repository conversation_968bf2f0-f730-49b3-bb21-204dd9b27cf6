import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { z } from 'zod'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
const createDispatcherSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  selectedCompanies: z.array(z.string()).optional()
})

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookieStore.set(name, value, options)
            } catch (error) {
              // Handle error
            }
          },
          remove(name: string, options: CookieOptions) {
            try {
              cookieStore.set(name, '', options)
            } catch (error) {
              // Handle error
            }
          },
        },
      }
    )
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const validationResult = createDispatcherSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.format()
      }, { status: 400 })
    }

    const { email, firstName, lastName, selectedCompanies } = validationResult.data

    // Check if user has access to the companies they're trying to assign
    if (selectedCompanies && selectedCompanies.length > 0) {
      // Get user's organizations first
      const { data: userOrgs, error: userOrgError } = await supabase
        .from('user_organizations')
        .select('organization_id')
        .eq('user_id', user.id)
        .eq('role', 'AFFILIATE')

      if (userOrgError || !userOrgs?.length) {
        return NextResponse.json({ error: 'User must have AFFILIATE role to manage dispatchers' }, { status: 403 })
      }

      const userOrgIds = userOrgs.map(org => org.organization_id)
      
      const { data: ownedCompanies, error: companiesError } = await supabase
        .from('affiliate_companies')
        .select('id, organization_id')
        .in('organization_id', userOrgIds)
        .in('id', selectedCompanies)

      if (companiesError) {
        console.error('Error checking company ownership:', companiesError)
        return NextResponse.json({ error: 'Failed to verify company ownership' }, { status: 500 })
      }

      if (ownedCompanies.length !== selectedCompanies.length) {
        return NextResponse.json({ error: 'You can only assign dispatchers to companies you own' }, { status: 403 })
      }
    }

    // Generate a temporary password
    const tempPassword = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8)

    // Create the dispatcher user account using service role
    const supabaseAdmin = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookieStore.set(name, value, options)
            } catch (error) {
              // Handle error
            }
          },
          remove(name: string, options: CookieOptions) {
            try {
              cookieStore.set(name, '', options)
            } catch (error) {
              // Handle error
            }
          },
        },
      }
    )

    // Check if user already exists by looking in profiles table
    const { data: existingProfile, error: profileLookupError } = await supabaseAdmin
      .from('profiles')
      .select('id, email')
      .eq('email', email)
      .maybeSingle()
    
    let authData
    if (existingProfile && !profileLookupError) {
      // User exists, get their auth data
      const { data: existingUser, error: userError } = await supabaseAdmin.auth.admin.getUserById(existingProfile.id)
      if (existingUser && existingUser.user) {
        authData = { user: existingUser.user }
        console.log('Using existing user for dispatcher:', email)
      } else {
        return NextResponse.json({ error: 'Failed to retrieve existing user data' }, { status: 500 })
      }
    } else {
      // Create new user
      const { data: newUserData, error: createUserError } = await supabaseAdmin.auth.admin.createUser({
        email,
        password: tempPassword,
        email_confirm: true,
        user_metadata: {
          first_name: firstName,
          last_name: lastName,
          full_name: `${firstName} ${lastName}`,
          user_type: 'dispatcher'
        }
      })

      if (createUserError) {
        console.error('Error creating dispatcher user:', createUserError)
        if (createUserError.message?.includes('email_exists')) {
          return NextResponse.json({ error: 'A user with this email already exists. Please use a different email.' }, { status: 422 })
        }
        return NextResponse.json({ error: 'Failed to create dispatcher account' }, { status: 500 })
      }

      if (!newUserData.user) {
        return NextResponse.json({ error: 'Failed to create dispatcher account' }, { status: 500 })
      }
      
      authData = newUserData
    }

    // Create or update profile record using raw SQL to bypass RLS
    const { error: profileError } = await supabaseAdmin.rpc('create_dispatcher_profile', {
      user_id: authData.user.id,
      user_email: email,
      first_name_param: firstName,
      last_name_param: lastName,
      full_name_param: `${firstName} ${lastName}`
    })

    // If the function doesn't exist, fall back to direct insert with RLS bypass
    if (profileError && profileError.message?.includes('function')) {
      // Try direct SQL insert
      const { error: sqlError } = await supabaseAdmin
        .rpc('exec_sql', {
          sql: `
            INSERT INTO public.profiles (id, email, first_name, last_name, full_name, roles)
            VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (id) DO UPDATE SET
              email = EXCLUDED.email,
              first_name = EXCLUDED.first_name,
              last_name = EXCLUDED.last_name,
              full_name = EXCLUDED.full_name,
              roles = EXCLUDED.roles
          `,
          params: [authData.user.id, email, firstName, lastName, `${firstName} ${lastName}`, '{DISPATCHER}']
        })
      
      if (sqlError) {
        console.error('Error creating dispatcher profile with SQL:', sqlError)
        return NextResponse.json({ error: 'Failed to create dispatcher profile' }, { status: 500 })
      }
    } else if (profileError) {
      console.error('Error creating/updating dispatcher profile:', profileError)
      return NextResponse.json({ error: 'Failed to create dispatcher profile' }, { status: 500 })
    }

    // Get user's companies if no specific companies selected
    let companiesToAssign = selectedCompanies || []
    
    if (!selectedCompanies || selectedCompanies.length === 0) {
      // Try to get company context from header
      const companyIdFromHeader = request.headers.get('x-company-id') || request.headers.get('X-Affiliate-Company-ID')
      if (companyIdFromHeader) {
        companiesToAssign = [companyIdFromHeader]
      } else {
        // If no header, get user's first available company
        const { data: userOrgs, error: userOrgError } = await supabase
          .from('user_organizations')
          .select('organization_id')
          .eq('user_id', user.id)
          .eq('role', 'AFFILIATE')
          .limit(1)

        if (userOrgError || !userOrgs?.length) {
          return NextResponse.json({ error: 'User must have AFFILIATE role to create dispatchers' }, { status: 403 })
        }

        const { data: userCompanies, error: companiesError } = await supabase
          .from('affiliate_companies')
          .select('id')
          .eq('organization_id', userOrgs[0].organization_id)
          .limit(1)

        if (companiesError || !userCompanies?.length) {
          return NextResponse.json({ error: 'No affiliate companies found for user' }, { status: 400 })
        }

        companiesToAssign = [userCompanies[0].id]
      }
    }

    // Link dispatcher to companies
    if (companiesToAssign.length > 0) {
      const companyLinks = companiesToAssign.map(companyId => ({
        user_id: authData.user.id,
        affiliate_company_id: companyId,
        role: 'DISPATCHER', // Standardized role format (migration 027)
        employment_status: 'active'
      }))

      const { error: linkError } = await supabaseAdmin
        .from('affiliate_users')
        .insert(companyLinks)

      if (linkError) {
        console.error('Error linking dispatcher to companies:', linkError)
        return NextResponse.json({ error: 'Failed to assign companies to dispatcher' }, { status: 500 })
      }
    }

    // TODO: Send email with login credentials
    // For now, we'll just return the temporary password in the response
    // In production, this should be sent via email

    return NextResponse.json({
      message: 'Dispatcher created successfully',
      dispatcher: {
        id: authData.user.id,
        email,
        firstName,
        lastName,
        companies: companiesToAssign,
        tempPassword // Remove this in production
      }
    })

  } catch (error) {
    console.error('Error creating dispatcher:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookieStore.set(name, value, options)
            } catch (error) {
              // Handle error
            }
          },
          remove(name: string, options: CookieOptions) {
            try {
              cookieStore.set(name, '', options)
            } catch (error) {
              // Handle error
            }
          },
        },
      }
    )
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's companies
    const { data: userOrgs, error: userOrgError } = await supabase
      .from('user_organizations')
      .select('organization_id')
      .eq('user_id', user.id)
      .eq('role', 'AFFILIATE')

    if (userOrgError || !userOrgs?.length) {
      return NextResponse.json({ error: 'User must have AFFILIATE role to view dispatchers' }, { status: 403 })
    }

    const userOrgIds = userOrgs.map(org => org.organization_id)
    
    const { data: userCompanies, error: companiesError } = await supabase
      .from('affiliate_companies')
      .select('id, organization_id')
      .in('organization_id', userOrgIds)

    if (companiesError) {
      console.error('Error fetching user companies:', companiesError)
      return NextResponse.json({ error: 'Failed to fetch companies' }, { status: 500 })
    }

    const companyIds = userCompanies.map(c => c.id)

    if (companyIds.length === 0) {
      return NextResponse.json({ dispatchers: [] })
    }

    // Get dispatchers linked to user's companies
    const { data: dispatcherLinks, error: linksError } = await supabase
      .from('affiliate_users')
      .select(`
        user_id,
        affiliate_company_id,
        role,
        employment_status,
        profiles!inner(
          id,
          email,
          first_name,
          last_name,
          roles,
          created_at
        )
      `)
      .in('affiliate_company_id', companyIds)
      .eq('role', 'DISPATCHER')

    if (linksError) {
      console.error('Error fetching dispatchers:', linksError)
      return NextResponse.json({ error: 'Failed to fetch dispatchers' }, { status: 500 })
    }

    // Group dispatchers by user and collect their companies
    const dispatchersMap = new Map()
    
    dispatcherLinks.forEach((link: any) => {
      const profile = link.profiles
      if (!dispatchersMap.has(profile.id)) {
        dispatchersMap.set(profile.id, {
          id: profile.id,
          email: profile.email,
          first_name: profile.first_name,
          last_name: profile.last_name,
          status: link.employment_status.toLowerCase(),
          created_at: profile.created_at,
          companies: []
        })
      }
      dispatchersMap.get(profile.id).companies.push(link.affiliate_company_id)
    })

    const dispatchers = Array.from(dispatchersMap.values())

    return NextResponse.json({ dispatchers })

  } catch (error) {
    console.error('Error fetching dispatchers:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
