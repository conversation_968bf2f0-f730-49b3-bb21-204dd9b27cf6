import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/lib/database.types'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseClient()
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Verify user has access to this company
    const { data: userCompany, error: accessError } = await supabase
      .from('affiliate_users')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_company_id', companyId)
      .single()

    if (accessError || !userCompany) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Calculate compliance metrics
    const complianceMetrics = await calculateComplianceMetrics(supabase, companyId)

    return NextResponse.json(complianceMetrics)

  } catch (error) {
    console.error('Error in compliance metrics API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function calculateComplianceMetrics(supabase: any, companyId: string) {
  // Get business documents compliance
  const businessDocuments = await calculateBusinessDocumentsScore(supabase, companyId)

  // Get fleet compliance
  const fleetCompliance = await calculateFleetComplianceScore(supabase, companyId)

  // Get driver compliance
  const driverCompliance = await calculateDriverComplianceScore(supabase, companyId)

  // Calculate overall score (weighted average)
  const overallScore = (
    (businessDocuments.score * 0.4) +
    (fleetCompliance.score * 0.3) +
    (driverCompliance.score * 0.3)
  )

  // Generate recommendations
  const recommendations = generateRecommendations(businessDocuments, fleetCompliance, driverCompliance)

  // Update compliance tracking table
  await supabase
    .from('compliance_document_tracking')
    .upsert({
      company_id: companyId,
      business_documents_score: businessDocuments.score,
      vehicle_documents_score: fleetCompliance.score,
      driver_documents_score: driverCompliance.score,
      overall_compliance_score: overallScore,
      total_required_documents: businessDocuments.total + fleetCompliance.documentsTotal + driverCompliance.documentsTotal,
      completed_documents: businessDocuments.completed + fleetCompliance.documentsComplete + driverCompliance.documentsComplete,
      expired_documents: businessDocuments.expired + fleetCompliance.expired + driverCompliance.expired,
      expiring_soon_documents: businessDocuments.expiring + fleetCompliance.expiring + driverCompliance.expiring,
      last_calculated_at: new Date().toISOString()
    })

  return {
    overallScore,
    businessDocuments,
    fleetCompliance,
    driverCompliance,
    recommendations
  }
}

async function calculateBusinessDocumentsScore(supabase: any, companyId: string) {
  // Mock business documents for now - in real implementation, this would check actual documents
  const requiredDocuments = [
    'Business License',
    'General Liability Insurance',
    'Commercial Auto Insurance',
    'Workers Compensation',
    'Operating Permit'
  ]

  // For now, return mock data
  const completed = 3
  const total = requiredDocuments.length
  const expired = 0
  const expiring = 1

  return {
    score: (completed / total) * 100,
    completed,
    total,
    expired,
    expiring
  }
}

async function calculateFleetComplianceScore(supabase: any, companyId: string) {
  // Get vehicles for this company
  const { data: vehicles, error: vehiclesError } = await supabase
    .from('vehicles')
    .select('id, status')
    .eq('affiliate_company_id', companyId)

  if (vehiclesError) {
    console.error('Error fetching vehicles:', vehiclesError)
    return { score: 0, activeVehicles: 0, totalVehicles: 0, documentsComplete: 0, documentsTotal: 0, expired: 0, expiring: 0 }
  }

  const totalVehicles = vehicles?.length || 0
  const activeVehicles = vehicles?.filter((v: any) => v.status === 'active').length || 0

  // Mock document compliance for vehicles
  const documentsPerVehicle = 4 // Registration, Insurance, Inspection, etc.
  const documentsTotal = totalVehicles * documentsPerVehicle
  const documentsComplete = Math.floor(documentsTotal * 0.8) // 80% completion rate
  const expired = Math.floor(documentsTotal * 0.05) // 5% expired
  const expiring = Math.floor(documentsTotal * 0.1) // 10% expiring

  const score = documentsTotal > 0 ? (documentsComplete / documentsTotal) * 100 : 0

  return {
    score,
    activeVehicles,
    totalVehicles,
    documentsComplete,
    documentsTotal,
    expired,
    expiring
  }
}

async function calculateDriverComplianceScore(supabase: any, companyId: string) {
  // Get drivers for this company
  const { data: drivers, error: driversError } = await supabase
    .from('affiliate_drivers')
    .select('id, employment_status')
    .eq('affiliate_company_id', companyId)

  if (driversError) {
    console.error('Error fetching drivers:', driversError)
    return { score: 0, activeDrivers: 0, totalDrivers: 0, documentsComplete: 0, documentsTotal: 0, expired: 0, expiring: 0 }
  }

  const totalDrivers = drivers?.length || 0
  const activeDrivers = drivers?.filter((d: any) => d.employment_status === 'active').length || 0

  // Mock document compliance for drivers
  const documentsPerDriver = 5 // License, Background Check, Medical, etc.
  const documentsTotal = totalDrivers * documentsPerDriver
  const documentsComplete = Math.floor(documentsTotal * 0.85) // 85% completion rate
  const expired = Math.floor(documentsTotal * 0.03) // 3% expired
  const expiring = Math.floor(documentsTotal * 0.08) // 8% expiring

  const score = documentsTotal > 0 ? (documentsComplete / documentsTotal) * 100 : 0

  return {
    score,
    activeDrivers,
    totalDrivers,
    documentsComplete,
    documentsTotal,
    expired,
    expiring
  }
}

function generateRecommendations(businessDocs: any, fleetDocs: any, driverDocs: any) {
  const recommendations = []

  if (businessDocs.score < 80) {
    recommendations.push('Complete missing business documentation to improve compliance score')
  }

  if (fleetDocs.score < 80) {
    recommendations.push('Update vehicle documentation and ensure all vehicles have current insurance')
  }

  if (driverDocs.score < 80) {
    recommendations.push('Verify all driver licenses and background checks are current')
  }

  if (businessDocs.expiring > 0 || fleetDocs.expiring > 0 || driverDocs.expiring > 0) {
    recommendations.push('Renew expiring documents to maintain compliance status')
  }

  if (recommendations.length === 0) {
    recommendations.push('Excellent compliance! Continue monitoring document expiration dates')
  }

  return recommendations
}
