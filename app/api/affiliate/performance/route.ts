import { NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"

export async function GET() {
  try {
    // Authenticate affiliate user
    const context = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    
    const supabase = createClient()

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError

    // Add a check for user null value
    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    // Get the affiliate details for the current user
    const { data: affiliate, error: affiliateError } = await supabase
      .from('affiliate_details')
      .select('id')
      .eq('user_id', user.id)
      .single()
    if (affiliateError) throw affiliateError

    // Get the affiliate performance metrics
    const { data: performance, error: performanceError } = await supabase
      .from('affiliate_performance_metrics')
      .select('*')
      .eq('affiliate_company_id', affiliate.id)
      .order('period_end', { ascending: false })
      .limit(1)
      .single()
    if (performanceError) throw performanceError

    return NextResponse.json(performance)
  } catch (error) {
    console.error('Error fetching affiliate performance:', error)
    return NextResponse.json(
      { error: 'Error fetching affiliate performance' },
      { status: 500 }
    )
  }
}

export async function POST() {
  try {
    const supabase = createClient()

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError

    // Add a check for user null value
    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    // Get the affiliate details for the current user
    const { data: affiliate, error: affiliateError } = await supabase
      .from('affiliate_details')
      .select('id')
      .eq('user_id', user.id)
      .single()
    if (affiliateError) throw affiliateError

    // Calculate metrics for the current period
    const periodStart = new Date()
    periodStart.setMonth(periodStart.getMonth() - 1)
    const periodEnd = new Date()

    // Get trips for the period
    const { data: trips, error: tripsError } = await supabase
      .from('trips')
      .select(`
        *,
        service_quality_logs (
          vehicle_condition_rating,
          driver_professionalism_rating,
          pickup_time_diff
        )
      `)
      .eq('affiliate_company_id', affiliate.id)
      .gte('created_at', periodStart.toISOString())
      .lte('created_at', periodEnd.toISOString())
    if (tripsError) throw tripsError

    // Calculate performance metrics
    const totalTrips = trips?.length || 0
    const completedTrips = trips?.filter(t => t.status === 'COMPLETED').length || 0
    const cancelledTrips = trips?.filter(t => t.status === 'CANCELLED').length || 0
    const totalRevenue = trips?.reduce((sum, trip) => sum + (trip.total_fare || 0), 0) || 0
    const totalCommission = totalRevenue * 0.15 // Example commission rate

    // Calculate average rating and on-time percentage
    let totalRating = 0
    let onTimeTrips = 0
    let totalPickupDiff = 0

    trips?.forEach(trip => {
      if (trip.service_quality_logs?.[0]) {
        const log = trip.service_quality_logs[0]
        totalRating += (log.vehicle_condition_rating + log.driver_professionalism_rating) / 2
        if (log.pickup_time_diff <= 5) onTimeTrips++ // Consider 5 minutes as on-time
        totalPickupDiff += log.pickup_time_diff
      }
    })

    const averageRating = totalRating / (completedTrips || 1)
    const onTimePercentage = (onTimeTrips / (completedTrips || 1)) * 100
    const averageResponseTime = totalPickupDiff / (completedTrips || 1)

    // Insert new performance metrics
    const { data: newPerformance, error: insertError } = await supabase
      .from('affiliate_performance_metrics')
      .insert({
        affiliate_company_id: affiliate.id,
        period_start: periodStart.toISOString(),
        period_end: periodEnd.toISOString(),
        total_trips: totalTrips,
        completed_trips: completedTrips,
        cancelled_trips: cancelledTrips,
        total_revenue: totalRevenue,
        total_commission: totalCommission,
        average_rating: averageRating,
        response_time_average: `${Math.floor(averageResponseTime)}:${Math.floor((averageResponseTime % 1) * 60)}`,
        on_time_percentage: onTimePercentage,
        service_quality_score: averageRating,
        customer_complaints: trips?.filter(t => t.service_quality_logs?.[0]?.issues_reported?.length > 0).length || 0
      })
      .select()
      .single()
    if (insertError) throw insertError

    return NextResponse.json(newPerformance)
  } catch (error) {
    console.error('Error creating affiliate performance metrics:', error)
    return NextResponse.json(
      { error: 'Error creating affiliate performance metrics' },
      { status: 500 }
    )
  }
} 