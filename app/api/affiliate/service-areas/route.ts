import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
import { createClient } from '@/lib/supabase/server';

/**
 * GUG-26: Advanced Affiliate Features - Service Areas API
 * 
 * Handles CRUD operations for affiliate service areas management
 */

export async function GET(request: NextRequest) {
  try {
    console.log("Affiliate service-areas GET API called");

    // Authenticate as affiliate user
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('company_id');

    if (!companyId) {
      return NextResponse.json({ 
        error: 'Company ID is required' 
      }, { status: 400 });
    }

    // Fetch service areas for the company
    const { data: serviceAreas, error } = await supabase
      .from('affiliate_service_areas')
      .select('*')
      .eq('company_id', companyId)
      .order('priority', { ascending: true });

    if (error) {
      console.error('Error fetching service areas:', error);
      return NextResponse.json({ 
        error: 'Failed to fetch service areas', 
        details: error.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      serviceAreas: serviceAreas || []
    });

  } catch (error) {
    console.error('Error in service-areas GET API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("Affiliate service-areas POST API called");

    // Authenticate as affiliate user
    const authResult = await authenticateApiRequestWithRoles(['AFFILIATE', 'DISPATCHER', 'SUPER_ADMIN']);
    
    if (!authResult.success) {
      return NextResponse.json({ 
        error: authResult.error 
      }, { status: authResult.statusCode || 500 });
    }

    const supabase = createClient();
    const body = await request.json();

    const {
      company_id,
      area_name,
      area_type,
      coverage_radius,
      pricing_multiplier,
      minimum_rate,
      maximum_rate,
      service_types,
      is_active,
      priority,
      notes
    } = body;

    // Validate required fields
    if (!company_id || !area_name || !area_type || !pricing_multiplier) {
      return NextResponse.json({
        error: 'Missing required fields: company_id, area_name, area_type, pricing_multiplier'
      }, { status: 400 });
    }

    // Create new service area
    const { data: serviceArea, error } = await supabase
      .from('affiliate_service_areas')
      .insert({
        company_id,
        area_name,
        area_type,
        coverage_radius: coverage_radius || null,
        pricing_multiplier,
        minimum_rate: minimum_rate || null,
        maximum_rate: maximum_rate || null,
        service_types: service_types || [],
        is_active: is_active !== undefined ? is_active : true,
        priority: priority || 1,
        notes: notes || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating service area:', error);
      return NextResponse.json({ 
        error: 'Failed to create service area', 
        details: error.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      serviceArea,
      message: 'Service area created successfully'
    });

  } catch (error) {
    console.error('Error in service-areas POST API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}