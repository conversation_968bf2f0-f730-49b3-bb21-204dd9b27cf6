import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/app/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getSession(request)
    
    if (!session) {
      return NextResponse.json({ 
        error: 'No session found',
        authenticated: false
      })
    }
    
    return NextResponse.json({
      authenticated: true,
      user: {
        id: session.user_id,
        email: session.email,
        role: session.role,
        roles: session.roles,
        organization_id: session.organization_id
      },
      session_details: {
        hasRole: !!session.role,
        roleType: typeof session.role,
        rolesType: typeof session.roles,
        allSessionKeys: Object.keys(session)
      }
    })
    
  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      authenticated: false
    }, { status: 500 })
  }
}
