import { NextRequest, NextResponse } from "next/server";
import { createAuthenticatedSupabaseClient, requireAuth } from "@/lib/auth/server";

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth();
    if (!user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    const supabase = await createAuthenticatedSupabaseClient();

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    // Get user organizations
    const { data: userOrgs, error: orgsError } = await supabase
      .from('user_organizations')
      .select(`
        *,
        organization:organizations(*)
      `)
      .eq('user_id', user.id);

    // Get all organizations (to see what exists)
    const { data: allOrgs, error: allOrgsError } = await supabase
      .from('organizations')
      .select('id, name, slug, organization_type, status');

    // Get audit logs for this user
    const { data: auditLogs, error: auditError } = await supabase
      .from('audit_logs')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(10);

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        aud: user.aud,
        role: user.role
      },
      profile: {
        data: profile,
        error: profileError
      },
      userOrganizations: {
        data: userOrgs,
        error: orgsError
      },
      allOrganizations: {
        data: allOrgs,
        error: allOrgsError
      },
      auditLogs: {
        data: auditLogs,
        error: auditError
      }
    });

  } catch (error) {
    console.error("Debug API error:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}