import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

export async function POST(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // SQL for the missing functions
    const migrationSQL = `
      -- Function to create dispatcher profile (bypasses RLS)
      CREATE OR REPLACE FUNCTION public.create_dispatcher_profile(
          user_id UUID,
          user_email TEXT,
          first_name_param TEXT,
          last_name_param TEXT,
          full_name_param TEXT
      )
      RETURNS VOID AS $func$
      BEGIN
          INSERT INTO public.profiles (id, email, first_name, last_name, full_name, roles)
          VALUES (user_id, user_email, first_name_param, last_name_param, full_name_param, ARRAY['DISPATCHER'])
          ON CONFLICT (id) DO UPDATE SET
              email = EXCLUDED.email,
              first_name = EXCLUDED.first_name,
              last_name = EXCLUDED.last_name,
              full_name = EXCLUDED.full_name,
              roles = EXCLUDED.roles,
              updated_at = NOW();
      END;
      $func$ LANGUAGE plpgsql SECURITY DEFINER;

      -- Grant execute permissions to service role
      GRANT EXECUTE ON FUNCTION public.create_dispatcher_profile(UUID, TEXT, TEXT, TEXT, TEXT) TO service_role;
      GRANT EXECUTE ON FUNCTION public.create_dispatcher_profile(UUID, TEXT, TEXT, TEXT, TEXT) TO authenticated;
    `;

    // Execute the SQL
    const { data, error } = await supabase.rpc('exec', {
      sql: migrationSQL
    });

    if (error) {
      console.error('Error applying migration:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Dispatcher functions applied successfully',
      data 
    });

  } catch (error: any) {
    console.error('Migration error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}