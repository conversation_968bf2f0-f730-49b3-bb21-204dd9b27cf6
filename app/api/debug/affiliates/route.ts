import { createServerClient, type CookieOptions } from '@supabase/ssr'

export const runtime = 'nodejs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import { Database } from '@/lib/types/supabase'

// Debug endpoint to list all affiliates with detailed information
// GET /api/debug/affiliates?city=Boston

export async function GET(req: Request) {
  try {
    console.log('[DEBUG API] Starting debug affiliates request');
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            cookieStore.set(name, value, options);
          },
          remove(name: string, options: CookieOptions) {
            cookieStore.set(name, '', options);
          },
        },
      }
    );
    
    // Verify session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" }, 
        { status: 401 }
      )
    }
    
    console.log(`[DEBUG API] Session found for user: ${session.user.id} (${session.user.email})`);
    
    // Get query parameters
    const url = new URL(req.url)
    const city = url.searchParams.get('city')
    
    // Get all affiliates first
    console.log('[DEBUG API] Fetching all affiliates');
    const { data: allAffiliates, error: allError } = await supabase
      .from('affiliate_companies')
      .select('*')
    
    if (allError) {
      console.error(`[DEBUG API] Error fetching all affiliates: ${allError.message}`);
      return NextResponse.json({ error: allError.message }, { status: 500 })
    }
    
    console.log(`[DEBUG API] Found ${allAffiliates?.length || 0} total affiliates`);
    
    // Log all affiliates for debugging
    allAffiliates?.forEach((affiliate, index) => {
      console.log(`[DEBUG API] Affiliate ${index + 1}:`, {
        id: affiliate.id,
        name: affiliate.name,
        city: affiliate.city,
        state: affiliate.state,
        status: affiliate.status,
        email: affiliate.email
      });
    });
    
    // Filter by city if provided
    let filteredAffiliates = allAffiliates;
    if (city) {
      console.log(`[DEBUG API] Filtering by city: ${city}`);
      
      // Exact match
      const exactMatches = allAffiliates?.filter(
        a => a.city && a.city.toLowerCase() === city.toLowerCase()
      );
      
      console.log(`[DEBUG API] Exact city matches: ${exactMatches?.length || 0}`);
      exactMatches?.forEach((affiliate, index) => {
        console.log(`[DEBUG API] Exact match ${index + 1}:`, {
          id: affiliate.id,
          name: affiliate.name,
          city: affiliate.city
        });
      });
      
      // Partial match
      const partialMatches = allAffiliates?.filter(
        a => a.city && a.city.toLowerCase().includes(city.toLowerCase())
      );
      
      console.log(`[DEBUG API] Partial city matches: ${partialMatches?.length || 0}`);
      partialMatches?.forEach((affiliate, index) => {
        console.log(`[DEBUG API] Partial match ${index + 1}:`, {
          id: affiliate.id,
          name: affiliate.name,
          city: affiliate.city
        });
      });
      
      // Filter active affiliates
      const activeExactMatches = exactMatches?.filter(a => a.status === 'active');
      const activePartialMatches = partialMatches?.filter(a => a.status === 'active');
      
      console.log(`[DEBUG API] Active exact matches: ${activeExactMatches?.length || 0}`);
      console.log(`[DEBUG API] Active partial matches: ${activePartialMatches?.length || 0}`);
      
      // Return filtered results
      return NextResponse.json({
        total: allAffiliates?.length || 0,
        exactMatches: exactMatches || [],
        partialMatches: partialMatches || [],
        activeExactMatches: activeExactMatches || [],
        activePartialMatches: activePartialMatches || []
      })
    }
    
    // Count active affiliates
    const activeAffiliates = allAffiliates?.filter(a => a.status === 'active');
    console.log(`[DEBUG API] Active affiliates: ${activeAffiliates?.length || 0}`);
    
    // Return all affiliates
    return NextResponse.json({
      total: allAffiliates?.length || 0,
      active: activeAffiliates?.length || 0,
      affiliates: allAffiliates || []
    })
  } catch (error: any) {
    console.error('[DEBUG API] Error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
} 