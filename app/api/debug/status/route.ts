import { createServerClient } from '@supabase/ssr';

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function GET() {
  const cookieStore = cookies();
  
  // Create a regular client for authenticated operations
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set() {
          // Server-side requests don't need to set cookies
        },
        remove() {
          // Server-side requests don't need to remove cookies
        },
      },
    }
  );

  // Create an admin client for privileged operations
  const supabaseAdmin = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set() {
          // Server-side requests don't need to set cookies
        },
        remove() {
          // Server-side requests don't need to remove cookies
        },
      },
    }
  );

  try {
    // Fetch authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // Get debug auth context
    const { data: authContext, error: authError } = await supabaseAdmin.rpc('debug_auth_context');

    // Get affiliate_companies table structure
    const { data: tableColumns, error: tableError } = await supabaseAdmin.rpc('execute_select_sql', {
      sql_statement: `
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'affiliate_companies'
        ORDER BY ordinal_position
      `
    });

    // Get RLS policies for affiliate_companies
    const { data: rlsPolicies, error: rlsError } = await supabaseAdmin.rpc('execute_select_sql', {
      sql_statement: `
        SELECT policyname, cmd, qual, with_check, roles
        FROM pg_policies
        WHERE schemaname = 'public' AND tablename = 'affiliate_companies'
      `
    });

    // Check if user has AFFILIATE role
    let userRoles = null;
    let affiliateRoleTest = null;
    
    if (user) {
      const { data: profileData, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, role, roles, email')
        .eq('id', user.id)
        .single();
        
      userRoles = profileData;
      
      // Test if user can insert into affiliate_companies
      try {
        const testInsertResult = await supabase.rpc('test_role_access', {
          test_email: user.email
        });
        affiliateRoleTest = testInsertResult;
      } catch (testError) {
        affiliateRoleTest = { error: (testError as Error).message };
      }
    }
    
    // Force PostgREST schema reload
    await supabaseAdmin.rpc('notify_pgrst_reload');

    return NextResponse.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      auth: {
        user: user ? { id: user.id, email: user.email } : null,
        userError: userError?.message,
        profile: userRoles,
        roleTest: affiliateRoleTest,
        authContext
      },
      database: {
        affiliateCompaniesTable: tableColumns,
        tableError: tableError?.message,
        rlsPolicies,
        rlsError: rlsError?.message
      },
      env: {
        nodeEnv: process.env.NODE_ENV,
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL
      }
    });
  } catch (err) {
    console.error('Debug status error:', err);
    return NextResponse.json(
      { error: 'Failed to get system status', details: (err as Error).message },
      { status: 500 }
    );
  }
} 