import { NextResponse } from 'next/server'

export const runtime = 'nodejs'
// import { createServerClient, type CookieOptions } from '@supabase/ssr'
// import { cookies } from 'next/headers'
import { createClient } from '@/lib/supabase/server' // Using our custom server client
// import { NextRequest } from 'next/server' // Not used

// Admin client for privileged operations
// const supabaseAdminUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321' // Not needed if using our server client
// const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU' // Not needed

// Use our server client, configured with service role key by default if an env var is set
const supabaseAdmin = createClient({ 
  serviceRole: process.env.SUPABASE_SERVICE_ROLE_KEY 
});

export async function GET(req: Request) {
  try {
    // Create client with service role to bypass RLS - supabaseAdmin is already this
    // const supabase = createClient({ 
    //   serviceRole: process.env.SUPABASE_SERVICE_ROLE_KEY 
    // });
    const supabase = supabaseAdmin; // Use the admin client directly
    
    // Get the last 5 users created
    const { data: recentUsersData, error: usersError } = await supabase.auth.admin.listUsers({
      perPage: 5,
      page: 1,
      // sortBy is not a direct option for supabase.auth.admin.listUsers(). 
      // Sorting typically happens client-side or if the API offers specific sort fields.
      // Removing sortBy for now to prevent errors.
      // sortBy: {
      //   column: 'created_at',
      //   order: 'desc'
      // }
    });
    
    if (usersError) {
      return NextResponse.json(
        { error: 'Error fetching users', details: usersError.message },
        { status: 500 }
      );
    }
    
    // Get the most recent logs
    const { data: logs, error: logsError } = await supabase
      .from('logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(20);
      
    if (logsError) {
      return NextResponse.json(
        { error: 'Error fetching logs', details: logsError.message },
        { status: 500 }
      );
    }
    
    // Get a count of users with role mismatches
    const { data: mismatchData, error: mismatchError } = await supabase.rpc('execute_select_sql', {
      sql_string: `
        SELECT 
          COUNT(*) AS total_users,
          SUM(CASE WHEN p.id IS NULL THEN 1 ELSE 0 END) AS missing_profiles,
          SUM(CASE 
            WHEN p.id IS NOT NULL AND 
                 u.raw_user_meta_data->>'roles' IS DISTINCT FROM CAST(p.roles::text AS VARCHAR)
            THEN 1 ELSE 0 
          END) AS role_mismatches
        FROM 
          auth.users u
        LEFT JOIN 
          public.profiles p ON u.id = p.id;
      `
    });
    
    const { data: triggers, error: triggersError } = await supabase.rpc('execute_select_sql', {
      sql_string: `
        SELECT event_object_schema AS schema, 
               event_object_table AS table, 
               trigger_name, 
               event_manipulation AS event, 
               action_statement AS definition
        FROM information_schema.triggers
        WHERE event_object_table IN ('users', 'profiles')
        ORDER BY trigger_name;
      `
    });

    return NextResponse.json({
      recentUsers: recentUsersData?.users?.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()).map(user => ({
        id: user.id,
        email: user.email,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
        appMetadata: user.app_metadata,
        userMetadata: user.user_metadata
      })),
      logs: logs,
      roleMismatches: mismatchData,
      triggers: triggers,
      timestamp: new Date().toISOString()
    });
    
  } catch (error: any) {
    console.error('Error in auth-status API:', error);
    
    return NextResponse.json(
      { error: 'Unexpected error', details: error.message },
      { status: 500 }
    );
  }
}

// Define a SQL function to get RLS policies info
async function generatePoliciesInfoFunction(supabase: any) {
  return supabase.rpc(
    'create_function_get_policies_info',
    { 
      function_definition: `
      CREATE OR REPLACE FUNCTION get_policies_info(schema_pattern text DEFAULT '%')
      RETURNS TABLE (
        table_schema text,
        table_name text,
        policy_name text,
        policy_roles text[],
        cmd text,
        using_expr text,
        with_check_expr text
      )
      LANGUAGE sql
      SECURITY DEFINER
      AS $$
        SELECT
          n.nspname AS table_schema,
          c.relname AS table_name,
          p.polname AS policy_name,
          ARRAY(
            SELECT rolname 
            FROM pg_roles 
            WHERE oid = ANY(p.polroles)
          ) AS policy_roles,
          CASE p.polcmd
            WHEN 'r' THEN 'SELECT'
            WHEN 'a' THEN 'INSERT'
            WHEN 'w' THEN 'UPDATE'
            WHEN 'd' THEN 'DELETE'
            WHEN '*' THEN 'ALL'
          END AS cmd,
          pg_get_expr(p.polqual, p.polrelid) AS using_expr,
          pg_get_expr(p.polwithcheck, p.polrelid) AS with_check_expr
        FROM pg_policy p
        JOIN pg_class c ON p.polrelid = c.oid
        JOIN pg_namespace n ON c.relnamespace = n.oid
        WHERE n.nspname LIKE schema_pattern
        ORDER BY n.nspname, c.relname, p.polname;
      $$;
      `
    }
  )
}

// Define a SQL function to get current user permissions
async function generateUserPermissionsFunction(supabase: any) {
  return supabase.rpc(
    'create_function_get_my_permissions',
    {
      function_definition: `
      CREATE OR REPLACE FUNCTION get_my_permissions()
      RETURNS jsonb
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      DECLARE
        current_user_id uuid;
        current_user_email text;
        current_user_role text;
        result jsonb;
      BEGIN
        -- Get current user info
        SELECT
          auth.uid(),
          (SELECT email FROM auth.users WHERE id = auth.uid()),
          current_user
        INTO
          current_user_id,
          current_user_email,
          current_user_role;
          
        -- Build result
        result = jsonb_build_object(
          'user_id', current_user_id,
          'email', current_user_email,
          'db_role', current_user_role,
          'schema_privileges', (
            SELECT jsonb_agg(
              jsonb_build_object(
                'schema', nspname,
                'privileges', (
                  SELECT jsonb_object_agg(
                    privilege_type, 
                    CASE WHEN is_grantable = 'YES' THEN true ELSE false END
                  )
                  FROM information_schema.role_usage_grants
                  WHERE grantee = current_user_role AND object_schema = nspname
                )
              )
            )
            FROM pg_namespace
            WHERE nspname NOT LIKE 'pg_%' AND nspname != 'information_schema'
          ),
          'search_path', (
            SELECT setting FROM pg_settings WHERE name = 'search_path'
          )
        );
          
        RETURN result;
      END;
      $$;
      `
    }
  )
} 