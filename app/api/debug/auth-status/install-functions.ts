/**
 * <PERSON><PERSON><PERSON> to install the diagnostic SQL functions needed for auth debugging.
 * 
 * Run this script from the project root before using the auth-status API endpoint:
 * ts-node app/api/debug/auth-status/install-functions.ts
 */

import { createClient } from '@supabase/supabase-js'

// Get environment variables or use local defaults
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU' // Default Supabase local dev service key

// Create Supabase client with service role key
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

const GET_POLICIES_INFO_SQL = `
CREATE OR REPLACE FUNCTION public.get_policies_info(schema_pattern text DEFAULT '')
RETURNS TABLE (
  table_schema text,
  table_name text,
  policy_name text,
  policy_roles text[],
  cmd text,
  using_expr text,
  with_check_expr text
)
LANGUAGE sql
SECURITY DEFINER
SET search_path = pg_catalog, public
AS $$
  SELECT
    n.nspname AS table_schema,
    c.relname AS table_name,
    p.polname AS policy_name,
    ARRAY(
      SELECT rolname 
      FROM pg_roles 
      WHERE oid = ANY(p.polroles)
    ) AS policy_roles,
    CASE p.polcmd
      WHEN 'r' THEN 'SELECT'
      WHEN 'a' THEN 'INSERT'
      WHEN 'w' THEN 'UPDATE'
      WHEN 'd' THEN 'DELETE'
      WHEN '*' THEN 'ALL'
    END AS cmd,
    pg_get_expr(p.polqual, p.polrelid) AS using_expr,
    pg_get_expr(p.polwithcheck, p.polrelid) AS with_check_expr
  FROM pg_policy p
  JOIN pg_class c ON p.polrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  WHERE n.nspname LIKE schema_pattern OR schema_pattern = ''
  ORDER BY n.nspname, c.relname, p.polname;
$$;
`

const GET_MY_PERMISSIONS_SQL = `
CREATE OR REPLACE FUNCTION public.get_my_permissions()
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = pg_catalog, public
AS $$
DECLARE
  current_user_id uuid;
  current_user_email text;
  current_db_role text;
  current_search_path text;
  result jsonb;
BEGIN
  -- Get current user info from Supabase context if available
  BEGIN
    current_user_id := auth.uid();
    current_user_email := (SELECT u.email FROM auth.users u WHERE u.id = current_user_id);
  EXCEPTION 
    WHEN OTHERS THEN -- Handles cases where auth.uid() might not be available or user not in auth.users
      current_user_id := NULL;
      current_user_email := NULL;
  END;

  current_db_role := current_user; -- PostgreSQL built-in for current effective role
  current_search_path := current_setting('search_path');
    
  result = jsonb_build_object(
    'user_id_from_auth_uid', current_user_id,
    'email_from_auth_users', current_user_email,
    'current_database_role', current_db_role,
    'effective_search_path', current_search_path,
    'accessible_schemas', (
      SELECT jsonb_agg(DISTINCT nspname ORDER BY nspname)
      FROM pg_namespace
      WHERE has_schema_privilege(current_db_role, nspname, 'USAGE')
        AND nspname NOT LIKE 'pg_%'
        AND nspname <> 'information_schema'
    ),
    'table_privileges_in_public', (
        SELECT jsonb_agg(jsonb_build_object('table', table_name, 'privileges', privilege_type))
        FROM information_schema.role_table_grants 
        WHERE grantee = current_db_role AND table_schema = 'public'
    ),
    'table_privileges_in_auth', (
        SELECT jsonb_agg(jsonb_build_object('table', table_name, 'privileges', privilege_type))
        FROM information_schema.role_table_grants 
        WHERE grantee = current_db_role AND table_schema = 'auth'
    ),
     'table_privileges_in_saas_organizations', (
        SELECT jsonb_agg(jsonb_build_object('table', table_name, 'privileges', privilege_type))
        FROM information_schema.role_table_grants 
        WHERE grantee = current_db_role AND table_schema = 'saas_organizations'
    )
  );
    
  RETURN result;
END;
$$;
`

const GRANT_EXECUTE_SQL = `
GRANT EXECUTE ON FUNCTION public.get_policies_info(text) TO postgres, anon, authenticated, authenticator, service_role;
GRANT EXECUTE ON FUNCTION public.get_my_permissions() TO postgres, anon, authenticated, authenticator, service_role;
`

async function installFunctions() {
  console.log('Installing diagnostic functions for auth debugging (ensuring public schema and grants)...');
  
  try {
    console.log('\n--- Creating/Replacing public.get_policies_info function ---');
    const { error: policiesError } = await supabaseAdmin.rpc('execute_sql', { sql_statement: GET_POLICIES_INFO_SQL });
    if (policiesError) throw policiesError;
    console.log('public.get_policies_info function created/replaced.');

    console.log('\n--- Creating/Replacing public.get_my_permissions function ---');
    const { error: permissionsError } = await supabaseAdmin.rpc('execute_sql', { sql_statement: GET_MY_PERMISSIONS_SQL });
    if (permissionsError) throw permissionsError;
    console.log('public.get_my_permissions function created/replaced.');

    console.log('\n--- Granting EXECUTE permissions on functions ---');
    const { error: grantError } = await supabaseAdmin.rpc('execute_sql', { sql_statement: GRANT_EXECUTE_SQL });
    if (grantError) throw grantError;
    console.log('EXECUTE permissions granted to relevant roles.');
    
    console.log('\nFunction installation and permission grant complete!');
    console.log('You should now be able to use the /api/debug/auth-status endpoint effectively.');

  } catch (error: any) {
    console.error('\n--- ERROR DURING FUNCTION INSTALLATION ---');
    console.error('Message:', error.message);
    console.error('Details:', error.details);
    console.error('Hint:', error.hint);
    console.error('Full Error:', error);
    console.error('\nPlease check the SQL definitions and ensure your Supabase instance is running and accessible with the provided service key.');
  }
}

// Helper to run raw SQL (needed if execute_sql RPC isn't set up, but Supabase often has it)
// For this script, we assume direct SQL execution via a privileged client or an RPC like execute_sql is available.
// If direct .sql execution is preferred, these SQL blocks can be saved and run via `psql` or Supabase SQL editor.

// Run the installation
installFunctions(); 