import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'

export async function POST(request: NextRequest) {
  try {
    const response = NextResponse.json({ 
      success: true, 
      message: 'All authentication cookies cleared' 
    })
    
    // List of all possible auth cookies to clear
    const cookiesToClear = [
      'sb-127-auth-token',
      'sb-localhost-auth-token',
      'sb-127-auth-token-code-verifier',
      'supabase-auth-token',
      'supabase.auth.token',
      'sb-auth-token',
      'wwms-auth-session',
      'session_id',
      'refresh_attempts',
      'last_refresh_time',
      'refresh_count',
      'auth-cookies-synced',
      'user-id',
      'organization-id'
    ]
    
    // Clear each cookie
    cookiesToClear.forEach(cookieName => {
      response.cookies.set({
        name: cookieName,
        value: '',
        path: '/',
        maxAge: 0,
        httpOnly: false,
        secure: false,
        sameSite: 'lax'
      })
    })
    
    return response
    
  } catch (error) {
    console.error('Error clearing cookies:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
