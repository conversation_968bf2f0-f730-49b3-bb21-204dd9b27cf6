import { NextRequest, NextResponse } from "next/server";
import { createAuthenticatedSupabaseClient, requireAuth } from "@/lib/auth/server";

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    if (!user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    const supabase = await createAuthenticatedSupabaseClient();

    // Step 1: Create default organization if it doesn't exist
    const { data: existingOrg, error: checkError } = await supabase
      .from('organizations')
      .select('id')
      .eq('slug', 'transflow-shared')
      .single();

    let defaultOrgId;

    if (checkError && checkError.code === 'PGRST116') {
      // Organization doesn't exist, create it
      const { data: newOrg, error: createError } = await supabase
        .from('organizations')
        .insert({
          name: 'TransFlow Shared',
          slug: 'transflow-shared',
          organization_type: 'shared',
          status: 'active',
          settings: { allowGlobalNetwork: true, upsell_threshold: 10 }
        })
        .select('id')
        .single();

      if (createError) {
        return NextResponse.json({
          error: "Failed to create default organization",
          details: createError.message
        }, { status: 500 });
      }

      defaultOrgId = newOrg.id;
    } else if (existingOrg) {
      defaultOrgId = existingOrg.id;
    } else {
      return NextResponse.json({
        error: "Failed to check for existing organization",
        details: checkError?.message
      }, { status: 500 });
    }

    // Step 2: Check if current user has organization association
    const { data: userOrg, error: userOrgError } = await supabase
      .from('user_organizations')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (userOrgError && userOrgError.code === 'PGRST116') {
      // User doesn't have organization association, create it
      const { data: profile } = await supabase
        .from('profiles')
        .select('roles')
        .eq('id', user.id)
        .single();

      const userRole = profile?.roles?.[0] || 'CLIENT';

      const { data: newUserOrg, error: createUserOrgError } = await supabase
        .from('user_organizations')
        .insert({
          user_id: user.id,
          organization_id: defaultOrgId,
          role: userRole,
          status: 'active',
          joined_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createUserOrgError) {
        return NextResponse.json({
          error: "Failed to create user-organization association",
          details: createUserOrgError.message
        }, { status: 500 });
      }

      // Create audit log
      await supabase
        .from('audit_logs')
        .insert({
          user_id: user.id,
          organization_id: defaultOrgId,
          action: 'user_organization_fixed',
          table_name: 'user_organizations',
          record_id: user.id,
          new_values: {
            email: user.email,
            role: userRole,
            fixed_via: 'debug_api',
            organization_id: defaultOrgId
          }
        });
    }

    // Step 3: Verify the fix
    const { data: verifyOrgs } = await supabase
      .from('organizations')
      .select('id, name, slug');

    const { data: verifyUserOrgs } = await supabase
      .from('user_organizations')
      .select('id, role, organization_id')
      .eq('user_id', user.id);

    return NextResponse.json({
      success: true,
      message: "Organizations and user associations fixed",
      data: {
        defaultOrganizationId: defaultOrgId,
        allOrganizations: verifyOrgs,
        userOrganizations: verifyUserOrgs
      }
    });

  } catch (error) {
    console.error("Fix organizations API error:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}