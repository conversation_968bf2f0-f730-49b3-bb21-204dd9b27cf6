import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/app/lib/auth'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ 
        error: 'This endpoint is only available in development' 
      }, { status: 403 })
    }

    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ 
        error: 'No session found' 
      }, { status: 401 })
    }

    const { role } = await request.json()
    
    if (!role) {
      return NextResponse.json({ 
        error: 'Role is required' 
      }, { status: 400 })
    }

    // Valid roles
    const validRoles = [
      'SUPER_ADMIN',
      'CLIENT',
      'CLIENT_COORDINATOR',
      'AFFILIATE',
      'AFFILIATE_DISPATCH',
      'DRIVER',
      'PASSENGER'
    ]

    if (!validRoles.includes(role)) {
      return NextResponse.json({ 
        error: `Invalid role. Valid roles: ${validRoles.join(', ')}` 
      }, { status: 400 })
    }

    // Create admin client
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // First check if user exists
    const { data: existingProfile, error: fetchError } = await supabaseAdmin
      .from('profiles')
      .select('id, email, role, roles')
      .eq('id', session.user_id)
      .single()

    if (fetchError) {
      console.error('Error fetching user profile:', fetchError)
      return NextResponse.json({
        error: `User not found: ${fetchError.message}`
      }, { status: 404 })
    }

    console.log('Updating user role:', {
      userId: session.user_id,
      currentRole: existingProfile.role,
      newRole: role
    })

    // Update user's role (without updated_at since it might not exist)
    const { data: updatedProfile, error } = await supabaseAdmin
      .from('profiles')
      .update({
        role: role,
        roles: [role]
      })
      .eq('id', session.user_id)
      .select()
      .single()

    if (error) {
      console.error('Error updating user role:', error)
      return NextResponse.json({
        error: `Failed to update role: ${error.message}`,
        details: error
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `Role updated to ${role}`,
      user: {
        id: updatedProfile.id,
        email: updatedProfile.email,
        role: updatedProfile.role,
        roles: updatedProfile.roles
      }
    })

  } catch (error) {
    console.error('Error in update-role endpoint:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
