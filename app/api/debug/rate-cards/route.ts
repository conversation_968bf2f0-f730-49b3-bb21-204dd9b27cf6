import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    console.log('[DEBUG API] Starting debug rate cards request')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    console.log('[DEBUG API] Fetching all rate cards')

    // Get all rate cards
    const { data: rateCards, error } = await supabase
      .from('rate_cards')
      .select(`
        id, affiliate_company_id, vehicle_type, pricing_model,
        base_rate, per_mile_rate, per_hour_rate,
        airport_fee, minimum_charge, status, auto_quote
      `)
      .order('affiliate_company_id')

    if (error) {
      console.error('[DEBUG API] Error fetching rate cards:', error)
      return NextResponse.json({ error: 'Failed to fetch rate cards' }, { status: 500 })
    }

    console.log(`[DEBUG API] Found ${rateCards?.length || 0} total rate cards`)

    // Log each rate card
    rateCards?.forEach((rateCard, index) => {
      console.log(`[DEBUG API] Rate Card ${index + 1}:`, {
        id: rateCard.id,
        affiliate_company_id: rateCard.affiliate_company_id,
        vehicle_type: rateCard.vehicle_type,
        pricing_model: rateCard.pricing_model,
        base_rate: rateCard.base_rate,
        per_mile_rate: rateCard.per_mile_rate,
        per_hour_rate: rateCard.per_hour_rate,
        airport_fee: rateCard.airport_fee,
        auto_quote: rateCard.auto_quote,
        status: rateCard.status
      })
    })

    // Count active rate cards
    const activeRateCards = rateCards?.filter(rc => rc.auto_quote === true) || []
    console.log(`[DEBUG API] Auto-quote enabled rate cards: ${activeRateCards.length}`)

    // Group by company
    const byCompany = rateCards?.reduce((acc: any, rc) => {
      if (!acc[rc.affiliate_company_id]) {
        acc[rc.affiliate_company_id] = []
      }
      acc[rc.affiliate_company_id].push(rc)
      return acc
    }, {}) || {}

    console.log(`[DEBUG API] Rate cards by company:`, Object.keys(byCompany).map(companyId => ({
      affiliate_company_id: companyId,
      count: byCompany[companyId].length,
      auto_quote_count: byCompany[companyId].filter((rc: any) => rc.auto_quote).length
    })))

    return NextResponse.json({
      success: true,
      total_rate_cards: rateCards?.length || 0,
      active_rate_cards: activeRateCards.length,
      rate_cards: rateCards,
      by_company: byCompany
    })

  } catch (error) {
    console.error('[DEBUG API] Error in rate cards debug:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
