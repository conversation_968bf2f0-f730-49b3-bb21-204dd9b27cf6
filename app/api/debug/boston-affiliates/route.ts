import { NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Debug endpoint to check Boston affiliates
// GET /api/debug/boston-affiliates
export async function GET(request: Request) {
  try {
    console.log('[DEBUG] Fetching all Boston affiliates');
    
    // Get the URL parameters
    const url = new URL(request.url);
    const cityFilter = url.searchParams.get('city') || 'Boston';
    
    // Fetch all affiliates
    const { data: allAffiliates, error: affiliatesError } = await supabase
      .from('affiliate_companies')
      .select('*');
    
    if (affiliatesError) {
      console.error(`[DEBUG] Error fetching affiliates: ${affiliatesError.message}`);
      return NextResponse.json({ error: affiliatesError.message }, { status: 500 });
    }
    
    // Log all affiliates
    console.log(`[DEBUG] Found ${allAffiliates?.length || 0} total affiliates`);
    
    // Filter for Boston affiliates with detailed info
    const bostonAffiliates = allAffiliates?.filter(a => {
      // Check if city contains Boston (case insensitive)
      const cityMatch = a.city && a.city.toLowerCase().includes(cityFilter.toLowerCase());
      
      // Log detailed info about each affiliate's city field
      console.log(`[DEBUG] Affiliate ${a.id}: ${a.name}`);
      console.log(`  - City: "${a.city}" (${typeof a.city})`);
      console.log(`  - City length: ${a.city ? a.city.length : 0}`);
      console.log(`  - City trimmed: "${a.city ? a.city.trim() : ''}" (${a.city ? a.city.trim().length : 0})`);
      console.log(`  - Status: ${a.status}`);
      console.log(`  - City match: ${cityMatch}`);
      
      // Include in results if city matches or if we want to see all
      return cityMatch;
    });
    
    console.log(`[DEBUG] Found ${bostonAffiliates?.length || 0} Boston affiliates`);
    
    // Get rate cards for these affiliates
    const { data: rateCards, error: rateCardsError } = await supabase
      .from('rate_cards')
      .select('*')
      .in('company_id', bostonAffiliates.map(a => a.id));
    
    if (rateCardsError) {
      console.warn(`[DEBUG] Error fetching rate cards: ${rateCardsError.message}`);
    }
    
    // Enhance affiliate data with rate cards
    const enhancedAffiliates = bostonAffiliates.map(affiliate => {
      const affiliateRateCards = rateCards?.filter(rc => rc.affiliate_affiliate_company_id === affiliate.id) || [];
      
      return {
        ...affiliate,
        rate_cards: affiliateRateCards,
        vehicle_types: affiliateRateCards.map(rc => rc.vehicle_type),
        base_rates: affiliateRateCards.reduce((acc: any, rc: any) => {
          acc[rc.vehicle_type] = rc.base_rate;
          return acc;
        }, {})
      };
    });
    
    // Return the enhanced data
    return NextResponse.json({
      total: allAffiliates?.length || 0,
      boston_affiliates: enhancedAffiliates,
      filter: cityFilter
    });
  } catch (error: any) {
    console.error(`[DEBUG] Error: ${error.message}`);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 