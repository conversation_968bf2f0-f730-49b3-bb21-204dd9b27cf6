import { NextRequest, NextResponse } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SuccessResponses, ErrorResponses } from '@/lib/utils/api-error-handler'
import { getSession } from '@/app/lib/auth'

export const runtime = 'nodejs'

export const GET = withErrorHandler(async (request: NextRequest) => {
  console.log('=== AUTH DEBUG ===')
  console.log('Cookies:', request.cookies.getAll())
  console.log('Headers:', Object.fromEntries(request.headers.entries()))
  
  const session = await getSession(request)
  console.log('Session result:', session)
  
  const debugData = {
    session: session,
    cookies: request.cookies.getAll(),
    hasAuthCookie: !!request.cookies.get('sb-127-auth-token'),
    cookieValue: request.cookies.get('sb-127-auth-token')?.value?.substring(0, 50) + '...'
  }
  
  return SuccessResponses.ok(debugData, 'Auth debug information retrieved')
}, 'GET /api/debug/auth-check')
