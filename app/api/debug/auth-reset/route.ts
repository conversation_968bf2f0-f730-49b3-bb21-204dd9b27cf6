import { createServerClient, type CookieOptions } from '@supabase/ssr';

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

interface ResetRequest {
  email: string;
  resetToken?: string; // For security, could implement a token-based validation
}

export async function POST(request: NextRequest) {
  try {
    // Initialize Supabase client
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            cookieStore.set({ name, value, ...options });
          },
          remove(name: string, options: CookieOptions) {
            cookieStore.delete({ name, ...options });
          },
        },
      }
    );

    // Get request body
    const body: ResetRequest = await request.json();
    
    if (!body.email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // For security, we could check for an admin token or other validation here
    // This is intentionally kept simple for troubleshooting
    // In production, add additional security checks

    // Call the reset_problematic_user function
    const { data, error } = await supabase.rpc(
      'reset_problematic_user',
      { user_email: body.email }
    );

    if (error) {
      console.error('Error resetting user:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Unexpected error in auth-reset API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 