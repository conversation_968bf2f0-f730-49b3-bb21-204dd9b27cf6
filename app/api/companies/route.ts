import { NextResponse } from "next/server"
import { query } from "@/lib/db"
import { with<PERSON>rror<PERSON><PERSON><PERSON>, ErrorResponses, SuccessResponses } from '@/lib/utils/api-error-handler'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export const GET = withErrorHandler(async (req: Request) => {
  const { data, error } = await query('SELECT * FROM companies')

  if (error) {
    return ErrorResponses.databaseError('Failed to fetch companies', error)
  }

  return SuccessResponses.ok(data, 'Companies retrieved successfully')
}, 'GET /api/companies')

export const POST = withErrorHandler(async (req: Request) => {
  const { name, description, logo_url, website, address, phone, email } =
    await req.json()

  // Validate required fields
  const requiredFields = { name, description, logo_url, website, address, phone, email }
  for (const [field, value] of Object.entries(requiredFields)) {
    if (!value) {
      return ErrorResponses.missingField(field)
    }
  }

  const { data, error } = await query(
    'INSERT INTO companies (name, description, logo_url, website, address, phone, email) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *',
    [name, description, logo_url, website, address, phone, email]
  )

  if (error) {
    return ErrorResponses.databaseError('Failed to create company', error)
  }

  return SuccessResponses.created(data[0], 'Company created successfully')
}, 'POST /api/companies')
