import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequest } from '@/lib/auth/api-authentication';

export const runtime = 'nodejs';



export async function POST(request: NextRequest) {
  try {
    console.log("Organization switch API POST - Starting request");
    const { organizationId } = await request.json();

    if (!organizationId) {
      return NextResponse.json(
        { error: "Organization ID is required" },
        { status: 400 }
      );
    }

    // Use standardized auth pattern
    const authResult = await authenticateApiRequest(request, ['SUPER_ADMIN', 'TNC_ADMIN', 'CLIENT_COORDINATOR', 'AFFILIATE'], false);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 401 });
    }
    
    const user = authResult.user;
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Verify user has access to this organization
    const { data: userOrg, error: userOrgError } = await supabase
      .from('user_organizations')
      .select(`
        role,
        organizations:organization_id (
          id,
          name,
          slug,
          status,
          settings,
          branding
        )
      `)
      .eq('user_id', user.id)
      .eq('organization_id', organizationId)
      .single();

    if (userOrgError || !userOrg) {
      return NextResponse.json({ error: 'Organization not found or access denied' }, { status: 404 });
    }

    const organization = userOrg.organizations;

    // Set organization context in user settings
    const { error: settingError } = await supabase
      .from('user_settings')
      .upsert({
        user_id: user.id,
        setting_name: "app.current_organization_id",
        setting_value: organizationId,
        updated_at: new Date().toISOString(),
      });

    if (settingError) {
      console.error('Error updating organization setting:', settingError);
      return NextResponse.json({ error: 'Failed to update organization context' }, { status: 500 });
    }

    // Create response with organization context
    const response = NextResponse.json({
      success: true,
      organization: {
        id: organization.id,
        name: organization.name,
        slug: organization.slug,
        status: organization.status,
        user_role: userOrg.role,
      },
    });

    // Set headers for client-side context
    response.headers.set("x-current-organization-id", organizationId);

    return response;

  } catch (error: any) {
    console.error("Organization switch API error:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log("Organization switch GET API called");

    // Use standardized auth pattern
    const authResult = await authenticateApiRequest(request, ['SUPER_ADMIN', 'TNC_ADMIN', 'CLIENT_COORDINATOR', 'AFFILIATE'], false);
    
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.statusCode || 401 });
    }
    
    const user = authResult.user;
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Fetch user's accessible organizations
    const { data: userOrgs, error: userOrgsError } = await supabase
      .from('user_organizations')
      .select(`
        role,
        organizations:organization_id (
          id,
          name,
          slug,
          status,
          settings,
          branding
        )
      `)
      .eq('user_id', user.id);

    if (userOrgsError) {
      console.error('Error fetching user organizations:', userOrgsError);
      return NextResponse.json({ error: 'Failed to fetch organizations' }, { status: 500 });
    }

    // Format organizations for the frontend
    const availableOrganizations = userOrgs?.map(uo => ({
      id: uo.organizations.id,
      name: uo.organizations.name,
      slug: uo.organizations.slug,
      status: uo.organizations.status,
      user_role: uo.role,
      branding: uo.organizations.branding,
    })) || [];

    // Get current organization from user settings
    const { data: currentOrgSetting } = await supabase
      .from('user_settings')
      .select('setting_value')
      .eq('user_id', user.id)
      .eq('setting_name', 'app.current_organization_id')
      .maybeSingle();

    let currentOrganization = currentOrgSetting?.setting_value;
    
    // If no current org set or org not accessible, use first available
    if (!currentOrganization || !availableOrganizations.find(org => org.id === currentOrganization)) {
      currentOrganization = availableOrganizations[0]?.id || null;
    }

    return NextResponse.json({
      success: true,
      availableOrganizations,
      currentOrganization,
      user: {
        id: user.id,
        email: user.email,
      },
    });
  } catch (error) {
    console.error("Error getting organization context:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}