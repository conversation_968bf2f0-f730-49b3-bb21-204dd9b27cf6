import { NextRequest, NextResponse } from 'next/server';
import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { websocketManager } from '@/lib/websocket/server';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
// Global variable to store the WebSocket server instance
let io: SocketIOServer | undefined;

/**
 * Initialize WebSocket server for real-time communication
 * This endpoint sets up the Socket.IO server for real-time quote updates
 */
export async function GET(request: NextRequest) {
  try {
    // Check if WebSocket server is already initialized
    if (io) {
      return NextResponse.json({ 
        success: true, 
        message: 'WebSocket server already running',
        stats: websocketManager.getConnectionStats()
      });
    }

    // In Next.js App Router, we need to handle WebSocket differently
    // This endpoint provides information about WebSocket setup
    return NextResponse.json({
      success: true,
      message: 'WebSocket server configuration ready',
      endpoint: '/api/socket',
      instructions: 'WebSocket server will be initialized when first client connects'
    });

  } catch (error) {
    console.error('WebSocket initialization error:', error);
    return NextResponse.json(
      { error: 'Failed to initialize WebSocket server' },
      { status: 500 }
    );
  }
}

/**
 * Handle WebSocket upgrade requests
 * This is called when a client tries to establish a WebSocket connection
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'broadcast_quote_update':
        const { quoteId, updateData } = body;
        websocketManager.broadcastQuoteUpdate(quoteId, updateData);
        return NextResponse.json({ success: true, message: 'Quote update broadcasted' });

      case 'broadcast_notification':
        const { userId, notification } = body;
        websocketManager.broadcastToUser(userId, notification);
        return NextResponse.json({ success: true, message: 'Notification sent' });

      case 'get_stats':
        return NextResponse.json({ 
          success: true, 
          stats: websocketManager.getConnectionStats() 
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('WebSocket API error:', error);
    return NextResponse.json(
      { error: 'WebSocket operation failed' },
      { status: 500 }
    );
  }
}
