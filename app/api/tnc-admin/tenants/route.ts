import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

/**
 * GET /api/tnc-admin/tenants
 * Get all tenants with their configurations for TNC admins
 */
export async function GET(request: NextRequest) {
  try {
    // Require TNC_ADMIN or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let query = supabase
      .from('saas_organizations')
      .select(`
        *,
        tenant_branding_config (*),
        tenant_billing_config (*),
        tenant_feature_gates (
          feature_key,
          enabled,
          configuration
        ),
        organizations (
          id,
          name,
          status,
          created_at
        )
      `)
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,slug.ilike.%${search}%`);
    }

    const { data: tenants, error } = await query;

    if (error) {
      console.error('Error fetching tenants:', error);
      return NextResponse.json({ error: 'Failed to fetch tenants' }, { status: 500 });
    }

    // Calculate tenant statistics
    const tenantsWithStats = tenants?.map(tenant => {
      const orgCount = tenant.organizations?.length || 0;
      const activeFeatures = tenant.tenant_feature_gates?.filter(f => f.enabled).length || 0;
      const totalFeatures = tenant.tenant_feature_gates?.length || 0;
      
      return {
        ...tenant,
        stats: {
          organization_count: orgCount,
          active_features: activeFeatures,
          total_features: totalFeatures,
          feature_utilization: totalFeatures > 0 ? (activeFeatures / totalFeatures) * 100 : 0
        }
      };
    });

    return NextResponse.json({
      success: true,
      tenants: tenantsWithStats || []
    });

  } catch (error: any) {
    console.error('TNC Admin tenants API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * POST /api/tnc-admin/tenants
 * Create a new tenant (TNC admin level)
 */
export async function POST(request: NextRequest) {
  try {
    // Require TNC_ADMIN or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const body = await request.json();
    
    const { 
      name, 
      slug, 
      description, 
      billing_plan = 'enterprise',
      features = [],
      branding = {},
      admin_user 
    } = body;

    if (!name || !slug) {
      return NextResponse.json(
        { error: 'name and slug are required' },
        { status: 400 }
      );
    }

    // Create tenant
    const { data: tenant, error: tenantError } = await supabase
      .from('saas_organizations')
      .insert({
        name,
        slug,
        description,
        status: 'active'
      })
      .select()
      .single();

    if (tenantError) {
      console.error('Error creating tenant:', tenantError);
      return NextResponse.json({ error: 'Failed to create tenant' }, { status: 500 });
    }

    // Create billing configuration
    const { error: billingError } = await supabase
      .from('tenant_billing_config')
      .insert({
        organization_id: tenant.id,
        billing_plan,
        subscription_status: 'active'
      });

    if (billingError) {
      console.error('Error creating billing config:', billingError);
    }

    // Create branding configuration
    const { error: brandingError } = await supabase
      .from('tenant_branding_config')
      .insert({
        organization_id: tenant.id,
        ...branding
      });

    if (brandingError) {
      console.error('Error creating branding config:', brandingError);
    }

    // Set up feature gates
    if (features.length > 0) {
      for (const feature of features) {
        await supabase.rpc('set_tenant_feature', {
          tenant_uuid: tenant.id,
          feature_name: feature.key,
          is_enabled: feature.enabled,
          config: feature.configuration || {}
        });
      }
    }

    // Create admin user if provided
    if (admin_user) {
      const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
        email: admin_user.email,
        password: admin_user.password || 'TempPassword123!',
        email_confirm: true,
        user_metadata: {
          first_name: admin_user.first_name,
          last_name: admin_user.last_name,
          role: 'TENANT_ADMIN'
        }
      });

      if (authError) {
        console.error('Error creating admin user:', authError);
      } else {
        // Create profile
        await supabase
          .from('profiles')
          .insert({
            id: authUser.user.id,
            email: admin_user.email,
            first_name: admin_user.first_name,
            last_name: admin_user.last_name,
            roles: ['TENANT_ADMIN']
          });
      }
    }

    // Fetch the complete tenant data
    const { data: completeTenant, error: fetchError } = await supabase
      .from('saas_organizations')
      .select(`
        *,
        tenant_branding_config (*),
        tenant_billing_config (*),
        tenant_feature_gates (*)
      `)
      .eq('id', tenant.id)
      .single();

    if (fetchError) {
      console.error('Error fetching complete tenant:', fetchError);
      return NextResponse.json({ 
        success: true, 
        tenant,
        message: 'Tenant created but failed to fetch complete data'
      });
    }

    return NextResponse.json({
      success: true,
      tenant: completeTenant
    });

  } catch (error: any) {
    console.error('TNC Admin create tenant API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}