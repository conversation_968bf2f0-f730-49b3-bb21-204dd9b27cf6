import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

/**
 * GET /api/tnc-admin/tenant-features
 * Get all tenant feature configurations for TNC admins
 */
export async function GET(request: NextRequest) {
  try {
    // Require TNC_ADMIN or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    let query = supabase
      .from('tenant_feature_gates')
      .select(`
        *,
        saas_organizations:organization_id (
          id,
          name,
          slug,
          status
        )
      `)
      .order('created_at', { ascending: false });

    if (organizationId) {
      query = query.eq('organization_id', organizationId);
    }

    const { data: features, error } = await query;

    if (error) {
      console.error('Error fetching tenant features:', error);
      return NextResponse.json({ error: 'Failed to fetch tenant features' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      features: features || []
    });

  } catch (error: any) {
    console.error('TNC Admin tenant features API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * POST /api/tnc-admin/tenant-features
 * Create or update tenant feature configuration
 */
export async function POST(request: NextRequest) {
  try {
    // Require TNC_ADMIN or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const body = await request.json();
    
    const { organization_id, feature_key, enabled, configuration } = body;

    if (!organization_id || !feature_key) {
      return NextResponse.json(
        { error: 'organization_id and feature_key are required' },
        { status: 400 }
      );
    }

    // Use the set_tenant_feature function
    const { data, error } = await supabase.rpc('set_tenant_feature', {
      tenant_uuid: organization_id,
      feature_name: feature_key,
      is_enabled: enabled ?? false,
      config: configuration || {}
    });

    if (error) {
      console.error('Error setting tenant feature:', error);
      return NextResponse.json({ error: 'Failed to set tenant feature' }, { status: 500 });
    }

    // Fetch the updated feature
    const { data: updatedFeature, error: fetchError } = await supabase
      .from('tenant_feature_gates')
      .select('*')
      .eq('organization_id', organization_id)
      .eq('feature_key', feature_key)
      .single();

    if (fetchError) {
      console.error('Error fetching updated feature:', fetchError);
      return NextResponse.json({ error: 'Feature updated but failed to fetch result' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      feature: updatedFeature
    });

  } catch (error: any) {
    console.error('TNC Admin set tenant feature API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}

/**
 * PUT /api/tnc-admin/tenant-features
 * Bulk update multiple tenant features
 */
export async function PUT(request: NextRequest) {
  try {
    // Require TNC_ADMIN or SUPER_ADMIN role
    const context = await authenticateApiRequestWithRoles(['TNC_ADMIN', 'SUPER_ADMIN']);
    
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
    const body = await request.json();
    
    const { organization_id, features } = body;

    if (!organization_id || !Array.isArray(features)) {
      return NextResponse.json(
        { error: 'organization_id and features array are required' },
        { status: 400 }
      );
    }

    // Update features in batch
    const results = [];
    for (const feature of features) {
      const { data, error } = await supabase.rpc('set_tenant_feature', {
        tenant_uuid: organization_id,
        feature_name: feature.feature_key,
        is_enabled: feature.enabled ?? false,
        config: feature.configuration || {}
      });

      if (error) {
        console.error(`Error setting feature ${feature.feature_key}:`, error);
        results.push({ feature_key: feature.feature_key, success: false, error: error.message });
      } else {
        results.push({ feature_key: feature.feature_key, success: true });
      }
    }

    return NextResponse.json({
      success: true,
      results
    });

  } catch (error: any) {
    console.error('TNC Admin bulk update tenant features API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: error.message?.includes('Forbidden') ? 403 : 500 }
    );
  }
}