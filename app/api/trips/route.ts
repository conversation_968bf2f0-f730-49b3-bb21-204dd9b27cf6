import { NextResponse } from "next/server"
import { createClient } from '@/lib/supabase/server'
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, ErrorResponses, SuccessResponses } from '@/lib/utils/api-error-handler'

export const GET = withError<PERSON>andler(async (req: Request) => {
  const supabase = createClient()
  
  // Get the user session
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    return ErrorResponses.unauthorized('Authentication required')
  }

    // Get query parameters
    const url = new URL(req.url)
    const status = url.searchParams.get('status')
    const limit = parseInt(url.searchParams.get('limit') || '50')
    
    // Build the query - join with quotes to filter by customer_id
    let query = supabase
      .from('trips')
      .select(`
        *,
        quotes!inner (
          customer_id,
          pickup_location,
          dropoff_location,
          date,
          time,
          vehicle_type,
          passenger_count,
          luggage_count,
          special_requests,
          total_amount
        )
      `)
      .eq('quotes.customer_id', session.user.id)
      .order('created_at', { ascending: false })
      .limit(limit)
    
    // Add status filter if provided
    if (status && status !== 'all') {
      query = query.eq('status', status)
    }
    
    // Execute the query
    const { data: tripsData, error } = await query
    
    if (error) {
      console.error('Error fetching trips:', error)
      return ErrorResponses.databaseError('Failed to fetch trips', error)
    }
    
    // Transform the data to flatten the structure
    const trips = tripsData?.map(trip => ({
      id: trip.id,
      quote_id: trip.quote_id,
      status: trip.status,
      created_at: trip.created_at,
      updated_at: trip.updated_at,
      // Include data from the quotes table
      pickup_location: trip.quotes.pickup_location,
      dropoff_location: trip.quotes.dropoff_location,
      date: trip.quotes.date,
      time: trip.quotes.time,
      vehicle_type: trip.quotes.vehicle_type,
      passenger_count: trip.quotes.passenger_count || 1,
      luggage_count: trip.quotes.luggage_count || 0,
      special_requests: trip.quotes.special_requests || [],
      total_amount: trip.quotes.total_amount,
      // Add mock data for UI display
      driver_name: trip.status === 'assigned' || trip.status === 'en_route' || trip.status === 'in_progress' ? 'John Driver' : null,
      driver_rating: 4.8,
      driver_trips: 120,
      estimated_arrival: trip.status === 'en_route' ? '10 minutes' : null,
      completed_at: trip.status === 'completed' ? trip.updated_at : null,
      is_rated: false
    })) || []
    
    // If no trips found, return empty array
    if (!trips || trips.length === 0) {
      // Check if there are any accepted quotes that should be trips
      const { data: acceptedQuotes, error: quotesError } = await supabase
        .from('quotes')
        .select('*')
        .eq('customer_id', session.user.id)
        .eq('status', 'accepted')
        .order('created_at', { ascending: false })
      
      if (!quotesError && acceptedQuotes && acceptedQuotes.length > 0) {
        // Convert accepted quotes to trips
        const mockTrips = acceptedQuotes.map(quote => ({
          id: quote.id,
          quote_id: quote.id,
          pickup_location: quote.pickup_location,
          dropoff_location: quote.dropoff_location,
          date: quote.date,
          time: quote.time,
          vehicle_type: quote.vehicle_type,
          status: 'confirmed',
          passenger_count: quote.passenger_count || 1,
          luggage_count: quote.luggage_count || 0,
          special_requests: quote.special_requests,
          total_amount: quote.total_amount,
          created_at: quote.created_at,
          updated_at: quote.updated_at
        }))
        
        return NextResponse.json({ trips: mockTrips }, { status: 200 })
      }
      
      return SuccessResponses.ok([], 'No trips found')
    }
    
    return SuccessResponses.ok(trips, 'Trips retrieved successfully')
}, 'GET /api/trips')

export const POST = withErrorHandler(async (req: Request) => {
  const supabase = createClient()
  
  // Get the user session
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    return ErrorResponses.unauthorized('Authentication required')
  }
    
    // Get request body
    const body = await req.json()
    
    // Validate required fields
    const requiredFields = ['quote_id', 'pickup_location', 'dropoff_location', 'date', 'time', 'vehicle_type']
    for (const field of requiredFields) {
      if (!body[field]) {
        return ErrorResponses.missingField(field)
      }
    }
    
    // Add customer_id to the trip
    const tripData = {
      ...body,
      customer_id: session.user.id,
      status: body.status || 'confirmed',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    // Insert the trip
    const { data: trip, error } = await supabase
      .from('trips')
      .insert(tripData)
      .select()
      .single()
    
    if (error) {
      console.error('Error creating trip:', error)
      return ErrorResponses.databaseError('Failed to create trip', error)
    }
    
    // Log the action
    await supabase
      .from('audit_logs')
      .insert({
        user_id: session.user.id,
        action: 'create_trip',
        resource_type: 'trip',
        resource_id: trip.id,
        details: JSON.stringify(tripData)
      })
    
    return SuccessResponses.created(trip, 'Trip created successfully')
}, 'POST /api/trips')

export const PATCH = withErrorHandler(async (req: Request) => {
  const supabase = createClient()
  
  // Get the user session
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    return ErrorResponses.unauthorized('Authentication required')
  }
    
    // Get request body
    const body = await req.json()
    const { id, ...updates } = body
    
    if (!id) {
      return ErrorResponses.missingField('id')
    }
    
    // Verify that the trip belongs to the user
    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .select(`
        *,
        quotes!inner (
          customer_id
        )
      `)
      .eq('id', id)
      .single()
    
    if (tripError) {
      console.error('Error fetching trip:', tripError)
      return ErrorResponses.notFound('Trip')
    }
    
    // Check if the trip belongs to the user
    if (trip.quotes.customer_id !== session.user.id) {
      return ErrorResponses.forbidden('Access denied to this trip')
    }
    
    // Only allow updating certain fields
    const allowedUpdates = ['special_requests', 'passenger_count', 'luggage_count']
    
    const filteredUpdates: Record<string, any> = {}
    for (const key of allowedUpdates) {
      if (key in updates) {
        filteredUpdates[key] = updates[key]
      }
    }
    
    // Update the trip
    const { data: updatedTrip, error: updateError } = await supabase
      .from('trips')
      .update(filteredUpdates)
      .eq('id', id)
      .select()
      .single()
    
    if (updateError) {
      console.error('Error updating trip:', updateError)
      return ErrorResponses.databaseError('Failed to update trip', updateError)
    }
    
    // Log the action
    await supabase
      .from('audit_logs')
      .insert({
        user_id: session.user.id,
        action: 'update_trip',
        resource_type: 'trip',
        resource_id: id,
        details: JSON.stringify(filteredUpdates)
      })
    
    return SuccessResponses.ok(updatedTrip, 'Trip updated successfully')
}, 'PATCH /api/trips') 