import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs';
import { createClient } from '@/lib/supabase/server';



import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
interface RouteParams {
  params: {
    tripId: string;
  };
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const supabase = createClient();
    const { tripId } = params;
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { payment_method_id, authorize_sharing } = body;

    if (!payment_method_id || !authorize_sharing) {
      return NextResponse.json({ 
        error: 'Payment method ID and authorization required' 
      }, { status: 400 });
    }

    // Get trip details and verify customer ownership
    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .select(`
        id,
        status,
        customer_id,
        quote_id,
        affiliate_company_id,
        quotes!inner(
          customer_id,
          organization_id,
          customer_name,
          customer_email
        ),
        affiliate_companies!inner(
          id,
          name,
          primary_contact_email
        )
      `)
      .eq('id', tripId)
      .single();

    if (tripError || !trip) {
      return NextResponse.json({ error: 'Trip not found' }, { status: 404 });
    }

    // Verify user is the customer for this trip
    if (trip.customer_id !== user.id && trip.quotes.customer_id !== user.id) {
      return NextResponse.json({ 
        error: 'You can only share payment information for your own trips' 
      }, { status: 403 });
    }

    // Verify trip is confirmed/completed
    if (!['confirmed', 'in_progress', 'completed'].includes(trip.status)) {
      return NextResponse.json({ 
        error: 'Payment information can only be shared for confirmed trips' 
      }, { status: 400 });
    }

    // Get payment method details (securely)
    const { data: paymentMethod, error: paymentError } = await supabase
      .from('payment_methods')
      .select(`
        id,
        type,
        last4,
        exp_month,
        exp_year,
        billing_address_id,
        billing_addresses(
          street,
          city,
          state,
          zip_code,
          country
        )
      `)
      .eq('id', payment_method_id)
      .eq('user_id', user.id)
      .single();

    if (paymentError || !paymentMethod) {
      return NextResponse.json({ error: 'Payment method not found' }, { status: 404 });
    }

    // Create payment sharing record
    const { data: paymentShare, error: shareError } = await supabase
      .from('trip_payment_shares')
      .insert({
        trip_id: tripId,
        quote_id: trip.quote_id,
        customer_id: user.id,
        affiliate_company_id: trip.affiliate_company_id,
        payment_method_id: payment_method_id,
        card_type: paymentMethod.type,
        card_last4: paymentMethod.last4,
        card_exp_month: paymentMethod.exp_month,
        card_exp_year: paymentMethod.exp_year,
        billing_address: paymentMethod.billing_addresses,
        authorized_at: new Date().toISOString(),
        status: 'shared'
      })
      .select()
      .single();

    if (shareError) {
      console.error('Error creating payment share:', shareError);
      return NextResponse.json({ error: 'Failed to share payment information' }, { status: 500 });
    }

    // Log the payment sharing event
    const { error: timelineError } = await supabase
      .from('quote_timeline')
      .insert({
        quote_id: trip.quote_id,
        event_type: 'payment_shared',
        status: 'payment_shared',
        title: 'Payment Information Shared',
        description: `Customer authorized sharing of payment method ending in ${paymentMethod.last4} with affiliate`,
        actor_id: user.id,
        actor_type: 'customer',
        event_data: {
          trip_id: tripId,
          payment_method_last4: paymentMethod.last4,
          affiliate_company_id: trip.affiliate_company_id,
          timestamp: new Date().toISOString()
        },
        visible_to_customer: true,
        visible_to_affiliate: true
      });

    if (timelineError) {
      console.error('Error logging payment share to timeline:', timelineError);
    }

    // Send notification email to affiliate
    try {
      await fetch('/api/emails/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template: 'payment_information_shared',
          to: trip.affiliate_companies.primary_contact_email,
          data: {
            affiliate_name: trip.affiliate_companies.name,
            customer_name: trip.quotes.customer_name,
            trip_id: tripId,
            card_type: paymentMethod.type.toUpperCase(),
            card_last4: paymentMethod.last4,
            billing_address: paymentMethod.billing_addresses
          }
        })
      });
    } catch (emailError) {
      console.error('Error sending payment share notification:', emailError);
      // Don't fail the request for email errors
    }

    return NextResponse.json({ 
      success: true,
      message: 'Payment information shared successfully',
      payment_share: {
        id: paymentShare.id,
        card_last4: paymentMethod.last4,
        card_type: paymentMethod.type,
        shared_at: paymentShare.authorized_at
      }
    });

  } catch (error) {
    console.error('Error in /api/trips/[tripId]/share-payment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}