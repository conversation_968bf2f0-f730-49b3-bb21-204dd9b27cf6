import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    console.log("Organizations capabilities API - Starting request");
    
    // Use standardized auth pattern
    const context = await authenticateApiRequestWithRoles([
      'SUPER_ADMIN', 'CLIENT', 'AFFILIATE', 'DISPATCHER', 'DRIVER', 'PASSENGER'
    ]);

    if (!context.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Use the actual authenticated user ID
    const userId = context.user.id;

    // Get all organizations for the super admin
    const { data: userOrgs, error: userOrgsError } = await supabase
      .from("user_organizations")
      .select(`
        organization_id,
        role,
        status,
        organizations (
          id,
          name,
          slug,
          domain,
          account_type,
          organization_type,
          subscription_plan,
          permission_template,
          status
        )
      `)
      .eq("user_id", userId)
      .eq("status", "active");

    if (userOrgsError) {
      console.error('Error fetching user organizations:', userOrgsError);
      return NextResponse.json(
        { error: 'Failed to fetch organizations' },
        { status: 500 }
      );
    }

    // Transform to the expected format with account_type
    const organizationsWithCapabilities = userOrgs?.map((uo) => ({
      id: uo.organizations.id,
      name: uo.organizations.name,
      slug: uo.organizations.slug,
      domain: uo.organizations.domain,
      account_type: uo.organizations.account_type || 'direct_client', // Four-tier business logic
      organization_type: uo.organizations.organization_type, // Architecture level
      subscription_plan: uo.organizations.subscription_plan || 'free_trial',
      permission_template: uo.organizations.permission_template || 'basic_client',
      status: uo.organizations.status,
      user_role: uo.role,
      capabilities: {
        can_have_white_labeling: uo.organizations.subscription_plan === 'enterprise',
        can_have_custom_domain: uo.organizations.subscription_plan === 'enterprise',
        can_have_custom_branding: ['professional', 'enterprise'].includes(uo.organizations.subscription_plan || 'free_trial'),
        has_white_labeling: false, // Will be implemented later
        has_custom_domain: false, // Will be implemented later
        has_custom_branding: false, // Will be implemented later
      }
    })) || [];

    console.log(`Found ${organizationsWithCapabilities.length} organizations for super admin`);
    
    return NextResponse.json({
      success: true,
      organizations: organizationsWithCapabilities,
      total: organizationsWithCapabilities.length
    });

    // Get user's current organization setting
    const { data: userSettings } = await supabase
      .from("user_settings")
      .select("setting_value")
      .eq("user_id", user.id)
      .eq("setting_name", "app.current_organization_id")
      .maybeSingle();

    const currentOrganizationId = userSettings?.setting_value || null;

    // Get organizations the user has access to
    let organizationsQuery = supabase
      .from("organization_capabilities")
      .select("*");

    // Super admins can see all organizations
    if (profile.roles?.includes("SUPER_ADMIN")) {
      // No additional filtering needed
    } else {
      // Regular users can only see organizations they belong to
      const { data: userOrganizations } = await supabase
        .from("user_organizations")
        .select("organization_id")
        .eq("user_id", user.id)
        .eq("status", "active");

      if (!userOrganizations || userOrganizations.length === 0) {
        return NextResponse.json({
          success: true,
          organizations: [],
          currentOrganization: null,
          message: "No organizations found for user"
        });
      }

      const orgIds = userOrganizations.map(uo => uo.organization_id);
      organizationsQuery = organizationsQuery.in("id", orgIds);
    }

    const { data: organizations, error: orgsError } = await organizationsQuery
      .eq("status", "active")
      .order("client_level", { ascending: true })
      .order("name", { ascending: true });

    if (orgsError) {
      console.error("Error fetching organizations:", orgsError);
      return NextResponse.json(
        { success: false, message: "Failed to fetch organizations" },
        { status: 500 }
      );
    }

    // Enhance organizations with additional metadata
    const enhancedOrganizations = await Promise.all(
      (organizations || []).map(async (org) => {
        // Get user count for this organization
        const { count: usersCount } = await supabase
          .from("user_organizations")
          .select("*", { count: "exact", head: true })
          .eq("organization_id", org.id)
          .eq("status", "active");

        // Get last activity (most recent event or quote)
        const { data: lastActivity } = await supabase
          .from("events")
          .select("created_at")
          .eq("organization_id", org.id)
          .order("created_at", { ascending: false })
          .limit(1)
          .single();

        return {
          ...org,
          users_count: usersCount || 0,
          last_active: lastActivity?.created_at || org.created_at,
        };
      })
    );

    return NextResponse.json({
      success: true,
      organizations: enhancedOrganizations,
      currentOrganization: currentOrganizationId,
      userProfile: {
        id: profile.id,
        email: profile.email,
        roles: profile.roles,
        full_name: profile.full_name
      }
    });

  } catch (error) {
    console.error("Organization capabilities API error:", error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, message: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { organizationId, action } = body;

    if (!organizationId) {
      return NextResponse.json(
        { success: false, message: "Organization ID is required" },
        { status: 400 }
      );
    }

    // Verify user has access to this organization
    const { data: userOrg, error: userOrgError } = await supabase
      .from("user_organizations")
      .select("*")
      .eq("user_id", user.id)
      .eq("organization_id", organizationId)
      .eq("status", "active")
      .single();

    if (userOrgError || !userOrg) {
      return NextResponse.json(
        { success: false, message: "Access denied to this organization" },
        { status: 403 }
      );
    }

    // Get organization capabilities from organizations table
    const { data: organization, error: orgError } = await supabase
      .from("organizations")
      .select(`
        id,
        name,
        slug,
        organization_type,
        subscription_plan,
        permission_template,
        has_white_labeling,
        has_custom_domain,
        has_custom_branding,
        status
      `)
      .eq("id", organizationId)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { success: false, message: "Organization not found" },
        { status: 404 }
      );
    }

    // Handle different actions
    switch (action) {
      case "get_capabilities":
        return NextResponse.json({
          success: true,
          organization,
          capabilities: {
            can_have_white_labeling: organization.subscription_plan === 'enterprise',
            can_have_custom_domain: organization.subscription_plan === 'enterprise',
            can_have_custom_branding: ['professional', 'enterprise'].includes(organization.subscription_plan || 'free_trial'),
            has_white_labeling: organization.has_white_labeling || false,
            has_custom_domain: organization.has_custom_domain || false,
            has_custom_branding: organization.has_custom_branding || false,
          }
        });

      default:
        return NextResponse.json(
          { success: false, message: "Invalid action" },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error("Organization capabilities POST error:", error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}