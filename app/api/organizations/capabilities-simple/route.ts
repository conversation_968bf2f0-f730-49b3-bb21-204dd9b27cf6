import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { createClient } from '@supabase/supabase-js';
import { authenticateBasicRequest } from '@/lib/auth/api-authentication';

export const runtime = 'nodejs';

// PRODUCTION ORGANIZATIONS CAPABILITIES API - Enterprise-grade authentication
export async function GET(request: NextRequest) {
  try {
    console.log("Production organizations capabilities API - Starting request");
    
    // Authenticate request with basic access (all approved roles)
    const authResult = await authenticateBasicRequest(request);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { user } = authResult;
    console.log(`Authenticated user: ${user!.email} (${user!.role})`);
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Use authenticated user's ID instead of hardcoded value
    const userId = user!.id;

    // Get user's organizations
    const { data: userOrgs, error: userOrgsError } = await supabase
      .from('user_organizations')
      .select(`
        organization_id,
        role,
        status,
        organizations (
          id,
          name,
          slug,
          domain,
          organization_type,
          subscription_plan,
          permission_template,
          status
        )
      `)
      .eq('user_id', userId)
      .eq('status', 'active');

    if (userOrgsError) {
      console.error('Error fetching user organizations:', userOrgsError);
      return NextResponse.json(
        { error: 'Failed to fetch organizations' },
        { status: 500 }
      );
    }

    // Transform to the expected format with client_level mapping
    const organizationsWithCapabilities = userOrgs?.map((uo) => {
      // Map subscription_plan to client_level for frontend compatibility
      const getClientLevel = (subscriptionPlan: string) => {
        switch (subscriptionPlan) {
          case 'enterprise': return 'Enterprise';
          case 'professional': return 'Professional';
          case 'free_trial': return 'Basic';
          default: return 'Basic';
        }
      };

      return {
        id: uo.organizations.id,
        name: uo.organizations.name,
        slug: uo.organizations.slug,
        domain: uo.organizations.domain,
        organization_type: uo.organizations.organization_type,
        subscription_plan: uo.organizations.subscription_plan || 'free_trial',
        permission_template: uo.organizations.permission_template || 'basic_client',
        status: uo.organizations.status,
        user_role: uo.role,
        client_level: getClientLevel(uo.organizations.subscription_plan || 'free_trial'), // ADD MISSING FIELD
        capabilities: {
          can_have_white_labeling: uo.organizations.subscription_plan === 'enterprise',
          can_have_custom_domain: uo.organizations.subscription_plan === 'enterprise',
          can_have_custom_branding: ['professional', 'enterprise'].includes(uo.organizations.subscription_plan || 'free_trial'),
          has_white_labeling: false,
          has_custom_domain: false,
          has_custom_branding: false,
        }
      };
    }) || [];

    console.log(`Found ${organizationsWithCapabilities.length} organizations for user`);
    
    return NextResponse.json({
      success: true,
      organizations: organizationsWithCapabilities,
      total: organizationsWithCapabilities.length,
      user_context: {
        role: user!.role,
        organization_id: user!.organization_id,
        is_super_admin: user!.is_super_admin
      }
    });

  } catch (error) {
    console.error('Error in GET /api/organizations/capabilities-simple:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}