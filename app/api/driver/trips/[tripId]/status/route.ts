import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
interface RouteParams {
  params: {
    tripId: string;
  };
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const supabase = createClient();
    const { tripId } = params;
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has DRIVER role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    if (profileError || !profile?.roles?.includes('DRIVER')) {
      return NextResponse.json({ error: 'Access denied. Driver role required.' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { status } = body;

    if (!status) {
      return NextResponse.json({ error: 'Status is required' }, { status: 400 });
    }

    // Verify trip belongs to this driver
    const { data: existingTrip, error: tripCheckError } = await supabase
      .from('trips')
      .select('id, driver_id, status, quote_id')
      .eq('id', tripId)
      .single();

    if (tripCheckError || !existingTrip) {
      return NextResponse.json({ error: 'Trip not found' }, { status: 404 });
    }

    if (existingTrip.driver_id !== user.id) {
      return NextResponse.json({ error: 'You can only update your own trips' }, { status: 403 });
    }

    // Update trip status
    const { data: updatedTrip, error: updateError } = await supabase
      .from('trips')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', tripId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating trip status:', updateError);
      return NextResponse.json({ error: 'Failed to update trip status' }, { status: 500 });
    }

    // Log status change in trip timeline
    const { error: timelineError } = await supabase
      .from('quote_timeline')
      .insert({
        quote_id: existingTrip.quote_id,
        event_type: 'trip_status_update',
        status: status,
        title: `Trip status updated to ${status}`,
        description: `Driver updated trip status from ${existingTrip.status} to ${status}`,
        actor_id: user.id,
        actor_type: 'driver',
        event_data: {
          trip_id: tripId,
          previous_status: existingTrip.status,
          new_status: status,
          timestamp: new Date().toISOString()
        },
        visible_to_customer: true,
        visible_to_affiliate: true
      });

    if (timelineError) {
      console.error('Error logging to timeline:', timelineError);
      // Don't fail the request for timeline errors
    }

    // Send real-time update via WebSocket (if implemented)
    // TODO: Implement WebSocket notification for real-time updates

    return NextResponse.json({ 
      success: true, 
      trip: updatedTrip,
      message: `Trip status updated to ${status}`
    });

  } catch (error) {
    console.error('Error in /api/driver/trips/[tripId]/status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}