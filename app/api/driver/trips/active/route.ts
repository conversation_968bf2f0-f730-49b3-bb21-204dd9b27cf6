import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has DRIVER role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user.id)
      .single();

    if (profileError || !profile?.roles?.includes('DRIVER')) {
      return NextResponse.json({ error: 'Access denied. Driver role required.' }, { status: 403 });
    }

    // Fetch active trips assigned to this driver
    const { data: trips, error: tripsError } = await supabase
      .from('trips')
      .select(`
        id,
        status,
        pickup_address,
        destination_address,
        pickup_time,
        passenger_name,
        passenger_phone,
        quote_id,
        estimated_duration,
        special_instructions,
        quotes!inner(
          organization_id,
          customer_name,
          customer_phone
        )
      `)
      .eq('driver_id', user.id)
      .in('status', ['assigned', 'en_route_pickup', 'arrived_pickup', 'passenger_onboard', 'en_route_destination'])
      .order('pickup_time', { ascending: true });

    if (tripsError) {
      console.error('Error fetching trips:', tripsError);
      return NextResponse.json({ error: 'Failed to fetch trips' }, { status: 500 });
    }

    // Format trips for frontend
    const formattedTrips = trips?.map(trip => ({
      id: trip.id,
      status: trip.status,
      pickup_address: trip.pickup_address,
      destination_address: trip.destination_address,
      pickup_time: trip.pickup_time,
      passenger_name: trip.passenger_name || trip.quotes.customer_name,
      passenger_phone: trip.passenger_phone || trip.quotes.customer_phone,
      quote_id: trip.quote_id,
      estimated_duration: trip.estimated_duration,
      special_instructions: trip.special_instructions,
    })) || [];

    return NextResponse.json(formattedTrips);

  } catch (error) {
    console.error('Error in /api/driver/trips/active:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}