import { NextRequest, NextResponse } from "next/server"
import { authenticateWithPermissions } from '@/lib/auth/api-authentication'
import { createSuccessResponse, ErrorResponses } from '@/app/lib/utils/api-responses'
import { createClient } from '@supabase/supabase-js'
import { z } from 'zod'
// Input validation schemas
const createPassengerSchema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Valid email is required'),
  phone_number: z.string().min(1, 'Phone number is required'),
  passenger_type: z.enum(['regular', 'vip', 'corporate']).default('regular')
});

export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/passengers - Starting request');

    // Authenticate user with proper role and permission validation
    const authResult = await authenticateWithPermissions(request, {
      allowedRoles: ['CLIENT', 'CLIENT_COORDINATOR', 'SUPER_ADMIN'],
      requiredPermissions: ['users.manage'], // Need user management permission to view passengers
      requireOrganization: true
    });

    if (!authResult.success) {
      return ErrorResponses.unauthorized(authResult.error || 'Authentication failed');
    }

    const { user, organization } = authResult;
    console.log('GET /api/passengers - User authenticated:', user!.id);

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get query parameters
    const url = new URL(request.url);
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 100);
    const passengerType = url.searchParams.get('type');

    // Build organization-scoped query
    let query = supabase
      .from('passengers')
      .select(`
        *,
        created_by_user:profiles!passengers_created_by_fkey(
          id, email, full_name
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Apply organization-based filtering for multi-tenant isolation
    if (!user!.is_super_admin) {
      if (organization) {
        query = query.eq('organization_id', organization.id);
      }
    }

    // Add passenger type filter if provided
    if (passengerType) {
      query = query.eq('passenger_type', passengerType);
    }

    const { data: passengers, error } = await query;

    if (error) {
      console.error('GET /api/passengers - Error fetching passengers:', error);
      return ErrorResponses.internalError('Failed to fetch passengers');
    }

    console.log('GET /api/passengers - Found', passengers?.length || 0, 'passengers');

    return createSuccessResponse({ passengers }, 'Passengers retrieved successfully');
  } catch (error: any) {
    console.error('GET /api/passengers - Unhandled error:', error);
    return ErrorResponses.internalError('Internal server error');
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('POST /api/passengers - Starting passenger creation request');

    // Authenticate user with proper role and permission validation
    const authResult = await authenticateWithPermissions(request, {
      allowedRoles: ['CLIENT', 'CLIENT_COORDINATOR'],
      requiredPermissions: ['users.manage'], // Need user management permission to create passengers
      requiredSubscription: 'free_trial', // Minimum subscription required
      requireOrganization: true
    });

    if (!authResult.success) {
      return ErrorResponses.unauthorized(authResult.error || 'Authentication failed');
    }

    const { user, organization } = authResult;
    console.log('POST /api/passengers - User authenticated:', user!.id);

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get and validate the request body
    const body = await request.json();
    
    // Validate input with Zod schema
    const validatedInput = createPassengerSchema.parse(body);

    // Check subscription limits for passenger creation
    if (!user!.is_super_admin) {
      // Get current passenger count for organization
      const { data: existingPassengers, error: countError } = await supabase
        .from('passengers')
        .select('id', { count: 'exact' })
        .eq('organization_id', organization?.id || user!.organization_id);

      if (countError) {
        console.error('Error checking passenger count:', countError);
        return ErrorResponses.internalError('Failed to validate passenger limits');
      }

      // Check subscription limits (this would be enhanced with actual limits)
      const maxPassengers = organization?.subscription_plan === 'enterprise' ? 1000 : 
                           organization?.subscription_plan === 'professional' ? 100 : 25;

      if ((existingPassengers?.length || 0) >= maxPassengers) {
        return ErrorResponses.subscriptionRequired('higher plan for more passengers');
      }
    }

    // Create the passenger with organization-based isolation
    const passengerData = {
      first_name: validatedInput.first_name,
      last_name: validatedInput.last_name,
      email: validatedInput.email,
      phone_number: validatedInput.phone_number,
      passenger_type: validatedInput.passenger_type,
      created_by: user!.id,
      organization_id: organization?.id || user!.organization_id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: passenger, error: passengerError } = await supabase
      .from('passengers')
      .insert([passengerData])
      .select()
      .single();

    if (passengerError) {
      console.error('POST /api/passengers - Error creating passenger:', passengerError);
      return ErrorResponses.internalError('Failed to create passenger');
    }

    console.log('POST /api/passengers - Passenger created successfully:', passenger.id);

    return createSuccessResponse({ passenger }, 'Passenger created successfully', 201);
  } catch (error: any) {
    if (error.name === 'ZodError') {
      return ErrorResponses.badRequest(`Validation error: ${error.errors.map((e: any) => e.message).join(', ')}`);
    }
    
    console.error('POST /api/passengers - Unhandled error:', error);
    return ErrorResponses.internalError('Internal server error');
  }
}
