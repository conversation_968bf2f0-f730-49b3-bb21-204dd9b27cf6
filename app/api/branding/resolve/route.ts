/**
export const dynamic = 'force-dynamic';
 * GUG-32: White-Label Engine - Organization Resolution API
 * 
 * Resolves organization branding from subdomain or custom domain
 */

import { NextRequest, NextResponse } from 'next/server';
import { SubdomainDetector } from '@/app/middleware/subdomain-detection';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const subdomain = searchParams.get('subdomain');
    const domain = searchParams.get('domain');

    if (!subdomain && !domain) {
      return NextResponse.json({
        error: 'Either subdomain or domain parameter is required'
      }, { status: 400 });
    }

    const detector = new SubdomainDetector();
    
    // Create a mock request for the detector
    const mockRequest = {
      headers: {
        get: (name: string) => {
          if (name === 'host') {
            return domain || `${subdomain}.example.com`;
          }
          return null;
        }
      }
    } as NextRequest;

    const branding = await detector.resolveOrganization(mockRequest);

    if (!branding) {
      return NextResponse.json({
        error: 'No branding configuration found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      branding,
      css_variables: detector.generateCSSVariables(branding)
    });

  } catch (error) {
    console.error('Error in branding resolution API:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}