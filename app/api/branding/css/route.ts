/**
 * GUG-32: White-Label Engine - CSS Injection API
 * 
 * Serves dynamic CSS based on organization branding
 */

import { NextRequest, NextResponse } from 'next/server';
import { SubdomainDetector } from '@/app/middleware/subdomain-detection';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('org');
    const portalType = searchParams.get('portal');
    const subdomain = searchParams.get('subdomain');

    if (!organizationId && !subdomain) {
      return NextResponse.json({
        error: 'Either organizationId or subdomain parameter is required'
      }, { status: 400 });
    }

    const detector = new SubdomainDetector();
    let branding = null;

    // Resolve branding from subdomain if provided
    if (subdomain) {
      const mockRequest = {
        headers: {
          get: (name: string) => {
            if (name === 'host') {
              return `${subdomain}.example.com`;
            }
            return null;
          }
        }
      } as NextRequest;

      branding = await detector.resolveOrganization(mockRequest);
    }

    // Generate base CSS variables
    let css = '';
    if (branding) {
      css += detector.generateCSSVariables(branding);
      
      // Add font import if custom font is specified
      if (branding.font_url) {
        css = `@import url('${branding.font_url}'); ${css}`;
      }
    }

    // Get additional CSS overrides
    const orgId = organizationId || branding?.organization_id;
    if (orgId) {
      const overrides = await detector.getCSSOverrides(orgId, portalType || undefined);
      css += ' ' + overrides.join(' ');
    }

    // Return CSS with proper content type
    return new NextResponse(css, {
      status: 200,
      headers: {
        'Content-Type': 'text/css',
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('Error in CSS injection API:', error);
    return new NextResponse('/* CSS Error */', {
      status: 500,
      headers: {
        'Content-Type': 'text/css'
      }
    });
  }
}