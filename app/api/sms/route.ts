import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * POST /api/sms
 * Send SMS notifications using Twilio
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    // Allow system calls without authentication for automated notifications
    const body = await request.json();
    const { to, message, priority = 'normal', systemCall = false } = body;

    if (!systemCall && (sessionError || !session)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!to || !message) {
      return NextResponse.json(
        { error: 'to and message are required' },
        { status: 400 }
      );
    }

    // Validate phone number format
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    if (!phoneRegex.test(to.replace(/\s+/g, ''))) {
      return NextResponse.json(
        { error: 'Invalid phone number format' },
        { status: 400 }
      );
    }

    // Check if SMS is enabled
    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
      console.warn('Twilio credentials not configured. SMS will not be sent.');
      return NextResponse.json({ 
        success: true, 
        message: 'SMS sending skipped (Twilio not configured)',
        mock: true 
      });
    }

    // SMS service not available in this environment
    return NextResponse.json({ 
      error: 'SMS service not configured' 
    }, { status: 503 });

    // Format phone number
    const formattedPhone = to.startsWith('+') ? to : `+1${to.replace(/\D/g, '')}`;

    // Send SMS
    const twilioMessage = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER || '+**********',
      to: formattedPhone,
      // Set priority-based options
      ...(priority === 'urgent' && {
        statusCallback: `${process.env.NEXT_PUBLIC_APP_URL}/api/sms/status`,
        statusCallbackMethod: 'POST'
      })
    });

    // Log SMS in database
    try {
      await supabase
        .from('sms_logs')
        .insert({
          recipient_phone: formattedPhone,
          message,
          priority,
          twilio_sid: twilioMessage.sid,
          status: twilioMessage.status,
          sent_by: session?.user?.id || 'system',
          sent_at: new Date().toISOString()
        });
    } catch (logError) {
      console.error('Error logging SMS:', logError);
      // Don't fail the request if logging fails
    }

    return NextResponse.json({
      success: true,
      message: 'SMS sent successfully',
      twilioSid: twilioMessage.sid,
      status: twilioMessage.status
    });

  } catch (error) {
    console.error('SMS sending error:', error);
    
    // Handle specific Twilio errors
    if (error.code) {
      const errorMessages: Record<string, string> = {
        '21211': 'Invalid phone number',
        '21408': 'Permission denied for this phone number',
        '21610': 'Message blocked by carrier',
        '30001': 'Message queue full',
        '30002': 'Account suspended',
        '30003': 'Unreachable destination',
        '30004': 'Message blocked',
        '30005': 'Unknown destination',
        '30006': 'Landline or unreachable carrier'
      };

      const userMessage = errorMessages[error.code] || 'SMS delivery failed';
      
      return NextResponse.json({
        success: false,
        error: userMessage,
        code: error.code
      }, { status: 400 });
    }

    return NextResponse.json(
      { error: 'Failed to send SMS' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/sms/status
 * Handle Twilio delivery status callbacks
 */
export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();
    
    const { MessageSid, MessageStatus, ErrorCode, ErrorMessage } = body;

    if (!MessageSid || !MessageStatus) {
      return NextResponse.json(
        { error: 'MessageSid and MessageStatus are required' },
        { status: 400 }
      );
    }

    // Update SMS log with delivery status
    const { error } = await supabase
      .from('sms_logs')
      .update({
        status: MessageStatus,
        error_code: ErrorCode || null,
        error_message: ErrorMessage || null,
        updated_at: new Date().toISOString()
      })
      .eq('twilio_sid', MessageSid);

    if (error) {
      console.error('Error updating SMS status:', error);
      return NextResponse.json(
        { error: 'Failed to update SMS status' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('SMS status callback error:', error);
    return NextResponse.json(
      { error: 'Failed to process status callback' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/sms/logs
 * Get SMS delivery logs (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', session.user.id)
      .single();

    const userRoles = profile?.roles || [];
    if (!userRoles.includes('SUPER_ADMIN') && !userRoles.includes('TNC_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get SMS logs
    const { data: logs, error } = await supabase
      .from('sms_logs')
      .select('*')
      .order('sent_at', { ascending: false })
      .limit(100);

    if (error) {
      console.error('Error fetching SMS logs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch SMS logs' },
        { status: 500 }
      );
    }

    return NextResponse.json({ logs });

  } catch (error) {
    console.error('Error in SMS logs API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
