import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

export const runtime = 'nodejs';

// GET handler to fetch available tenants/organizations for the user
export async function GET(request: NextRequest) {
  try {
    console.log("Tenant switch API GET - Starting request");
    
    // Use standardized auth pattern - allow any authenticated user
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN', 'TENANT_MANAGER', 'AFFILIATE_ADMIN', 'AFFILIATE_MANAGER', 'CUSTOMER']);

    if (!context.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

    // Get user's organizations with account_type
    const { data: userOrgs, error: userOrgsError } = await supabase
      .from('user_organizations')
      .select(`
        organization_id,
        role,
        status,
        organizations (
          id,
          name,
          slug,
          domain,
          account_type,
          organization_type,
          status,
          branding
        )
      `)
      .eq('user_id', context.user.id)
      .eq('status', 'active');

    if (userOrgsError) {
      console.error('Error fetching user organizations:', userOrgsError);
      return NextResponse.json(
        { error: 'Failed to fetch organizations' },
        { status: 500 }
      );
    }

    // Transform to the expected format with account_type
    const tenants = userOrgs?.map((uo) => ({
      id: uo.organizations.id,
      name: uo.organizations.name,
      slug: uo.organizations.slug,
      domain: uo.organizations.domain,
      account_type: uo.organizations.account_type || 'direct_client', // Four-tier business logic
      organization_type: uo.organizations.organization_type, // Architecture level
      status: uo.organizations.status,
      branding: uo.organizations.branding,
      user_role: uo.role,
    })) || [];

    console.log(`Found ${tenants.length} organizations for user ${context.user.id}`);
    return NextResponse.json({ tenants });

  } catch (error) {
    console.error('Error in GET /api/tenant/switch:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}



export async function POST(request: NextRequest) {
  try {
    console.log("Tenant switch API POST - Starting request");
    const { organizationId } = await request.json();

    if (!organizationId) {
      return NextResponse.json(
        { error: "Organization ID is required" },
        { status: 400 }
      );
    }

    console.log(`Switching to organization: ${organizationId}`);

    // For now, just return success to unblock the Network Switcher
    // TODO: Implement proper tenant switching logic later
    return NextResponse.json({
      success: true,
      message: `Switched to organization ${organizationId}`,
      organizationId: organizationId
    });

  } catch (error: any) {
    console.error("Tenant switch API error:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}
