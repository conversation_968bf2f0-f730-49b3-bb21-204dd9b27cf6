import { NextRequest, NextResponse } from 'next/server'
export const dynamic = 'force-dynamic';

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import crypto from 'crypto'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(
  request: NextRequest,
  { params }: { params: { clientId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const clientId = params.clientId

    // Get payment methods for the client
    const { data: paymentMethods, error } = await supabase
      .from('payment_methods')
      .select('*')
      .eq('client_id', clientId)
      .eq('created_by', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching payment methods:', error)
      return NextResponse.json({ error: 'Failed to fetch payment methods' }, { status: 500 })
    }

    // Transform data to hide sensitive information
    const safePaymentMethods = paymentMethods?.map(method => ({
      id: method.id,
      type: method.type,
      name: method.name,
      last_four: method.last_four,
      expiry_month: method.expiry_month,
      expiry_year: method.expiry_year,
      brand: method.brand,
      is_default: method.is_default,
      is_active: method.is_active,
      sharing_enabled: method.sharing_enabled,
      sharing_token: method.sharing_token,
      created_at: method.created_at
    })) || []

    return NextResponse.json({ payment_methods: safePaymentMethods })
  } catch (error) {
    console.error('Payment methods API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { clientId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const clientId = params.clientId
    const body = await request.json()

    const {
      type,
      name,
      card_number,
      expiry_month,
      expiry_year,
      cvv,
      billing_address,
      is_default
    } = body

    // Validate required fields
    if (!type || !name || !card_number) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Extract card information
    const lastFour = card_number.slice(-4)
    const brand = detectCardBrand(card_number)

    // Generate sharing token
    const sharingToken = crypto.randomBytes(32).toString('hex')

    // Hash sensitive data (in production, use proper encryption)
    const hashedCardNumber = crypto.createHash('sha256').update(card_number).digest('hex')
    const hashedCvv = crypto.createHash('sha256').update(cvv || '').digest('hex')

    // If this is set as default, unset other defaults
    if (is_default) {
      await supabase
        .from('payment_methods')
        .update({ is_default: false })
        .eq('client_id', clientId)
        .eq('created_by', user.id)
    }

    // Create payment method
    const { data: paymentMethod, error } = await supabase
      .from('payment_methods')
      .insert({
        client_id: clientId,
        type,
        name,
        card_number_hash: hashedCardNumber,
        last_four: lastFour,
        expiry_month: expiry_month ? parseInt(expiry_month) : null,
        expiry_year: expiry_year ? parseInt(expiry_year) : null,
        cvv_hash: hashedCvv,
        brand,
        billing_address,
        is_default: is_default || false,
        is_active: true,
        sharing_enabled: true,
        sharing_token: sharingToken,
        created_by: user.id,
        created_at: new Date().toISOString()
      })
      .select(`
        id,
        type,
        name,
        last_four,
        expiry_month,
        expiry_year,
        brand,
        is_default,
        is_active,
        sharing_enabled,
        sharing_token,
        created_at
      `)
      .single()

    if (error) {
      console.error('Error creating payment method:', error)
      return NextResponse.json({ error: 'Failed to create payment method' }, { status: 500 })
    }

    return NextResponse.json({ payment_method: paymentMethod })
  } catch (error) {
    console.error('Create payment method API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Helper function to detect card brand
function detectCardBrand(cardNumber: string): string {
  const number = cardNumber.replace(/\s/g, '')
  
  if (/^4/.test(number)) return 'Visa'
  if (/^5[1-5]/.test(number)) return 'Mastercard'
  if (/^3[47]/.test(number)) return 'American Express'
  if (/^6/.test(number)) return 'Discover'
  
  return 'Unknown'
}
