import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js';

// Form submission endpoint for embeddable forms
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      organizationId, 
      formId, 
      submissionData, 
      customerEmail, 
      customerPhone,
      sessionId 
    } = body;

    // Validate required fields
    if (!organizationId || !submissionData) {
      return NextResponse.json(
        { error: 'Organization ID and submission data are required' },
        { status: 400 }
      );
    }

    // Use service role for form submissions (public endpoint)
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Step 1: Create quote from form submission
    const quoteData = {
      organization_id: organizationId,
      customer_email: customerEmail,
      customer_phone: customerPhone,
      pickup_location: submissionData.pickupLocation,
      dropoff_location: submissionData.dropoffLocation,
      pickup_datetime: submissionData.pickupDateTime,
      return_datetime: submissionData.returnDateTime,
      passenger_count: submissionData.passengerCount || 1,
      service_type: submissionData.serviceType || 'point-to-point',
      special_requests: submissionData.specialRequests,
      status: 'pending',
      source: 'embeddable_form',
      total_amount: 0, // Will be calculated by affiliate responses
      created_at: new Date().toISOString()
    };

    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .insert(quoteData)
      .select()
      .single();

    if (quoteError) {
      console.error('Error creating quote:', quoteError);
      return NextResponse.json(
        { error: 'Failed to create quote' },
        { status: 500 }
      );
    }

    // Step 2: Record form submission
    const { data: submission, error: submissionError } = await supabase
      .from('embeddable_form_submissions')
      .insert({
        organization_id: organizationId,
        form_id: formId,
        quote_id: quote.id,
        submission_data: submissionData,
        customer_email: customerEmail,
        customer_phone: customerPhone,
        status: 'submitted'
      })
      .select()
      .single();

    if (submissionError) {
      console.error('Error recording submission:', submissionError);
      // Continue even if submission recording fails
    }

    // Step 3: Track analytics
    await supabase
      .from('embeddable_form_analytics')
      .insert({
        organization_id: organizationId,
        form_id: formId,
        event_type: 'submit',
        session_id: sessionId,
        user_agent: request.headers.get('user-agent'),
        referrer: request.headers.get('referer'),
        ip_address: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   '127.0.0.1'
      });

    // Step 4: Trigger quote processing workflow
    // TODO: Add real-time availability checking here
    // TODO: Send to affiliate network for pricing

    return NextResponse.json({
      success: true,
      quoteId: quote.id,
      submissionId: submission?.id,
      message: 'Quote request submitted successfully',
      estimatedResponse: '15-30 minutes'
    });

  } catch (error) {
    console.error('Form submission error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Track form analytics events
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { organizationId, formId, eventType, sessionId } = body;

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    await supabase
      .from('embeddable_form_analytics')
      .insert({
        organization_id: organizationId,
        form_id: formId,
        event_type: eventType, // 'view', 'start', 'step_complete'
        session_id: sessionId,
        user_agent: request.headers.get('user-agent'),
        referrer: request.headers.get('referer'),
        ip_address: request.headers.get('x-forwarded-for') || '127.0.0.1'
      });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Analytics tracking error:', error);
    return NextResponse.json({ success: false }, { status: 500 });
  }
}