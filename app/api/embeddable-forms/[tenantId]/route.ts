import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function GET(
  request: NextRequest,
  { params }: { params: { organizationId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organizationId = params.organizationId

    // Get form customization for the tenant
    const { data: customization, error } = await supabase
      .from('embeddable_form_customizations')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('created_by', user.id)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching form customization:', error)
      return NextResponse.json({ error: 'Failed to fetch customization' }, { status: 500 })
    }

    // Return default customization if none exists
    if (!customization) {
      return NextResponse.json({
        name: 'Default Form',
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
        backgroundColor: '#ffffff',
        textColor: '#1f2937',
        borderRadius: 8,
        fontFamily: 'Inter, sans-serif',
        companyName: 'Your Company',
        enabledTabs: ['point-to-point', 'hourly', 'airport', 'multi-day'],
        isActive: true
      })
    }

    return NextResponse.json(customization)
  } catch (error) {
    console.error('Get form customization API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { organizationId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organizationId = params.organizationId
    const body = await request.json()

    const {
      name,
      primaryColor,
      secondaryColor,
      backgroundColor,
      textColor,
      borderRadius,
      fontFamily,
      logoUrl,
      companyName,
      enabledTabs,
      customCss,
      embedDomain,
      isActive
    } = body

    // Validate required fields
    if (!name || !companyName) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Check if customization already exists
    const { data: existing } = await supabase
      .from('embeddable_form_customizations')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('created_by', user.id)
      .single()

    const customizationData = {
      organization_id: organizationId,
      name,
      primary_color: primaryColor,
      secondary_color: secondaryColor,
      background_color: backgroundColor,
      text_color: textColor,
      border_radius: borderRadius,
      font_family: fontFamily,
      logo_url: logoUrl,
      company_name: companyName,
      enabled_tabs: enabledTabs,
      custom_css: customCss,
      embed_domain: embedDomain,
      is_active: isActive,
      created_by: user.id,
      updated_at: new Date().toISOString()
    }

    let result
    if (existing) {
      // Update existing customization
      const { data, error } = await supabase
        .from('embeddable_form_customizations')
        .update(customizationData)
        .eq('id', existing.id)
        .select()
        .single()

      if (error) {
        console.error('Error updating form customization:', error)
        return NextResponse.json({ error: 'Failed to update customization' }, { status: 500 })
      }
      result = data
    } else {
      // Create new customization
      const { data, error } = await supabase
        .from('embeddable_form_customizations')
        .insert({
          ...customizationData,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating form customization:', error)
        return NextResponse.json({ error: 'Failed to create customization' }, { status: 500 })
      }
      result = data
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Save form customization API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
