import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

// Get analytics data for embeddable forms
export async function GET(request: NextRequest) {
  try {
    // Require authentication for analytics access
    const context = await authenticateApiRequestWithRoles(['SUPER_ADMIN', 'ADMIN']);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');
    const formId = searchParams.get('formId');
    const timeRange = searchParams.get('timeRange') || '7d'; // 7d, 30d, 90d
    const eventType = searchParams.get('eventType'); // view, start, submit, complete

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Calculate date range
    const now = new Date();
    const daysBack = timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 7;
    const startDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));

    // Build base query
    let query = supabase
      .from('embeddable_form_analytics')
      .select('*')
      .gte('created_at', startDate.toISOString());

    if (organizationId) {
      query = query.eq('organization_id', organizationId);
    }
    if (formId) {
      query = query.eq('form_id', formId);
    }
    if (eventType) {
      query = query.eq('event_type', eventType);
    }

    const { data: analytics, error: analyticsError } = await query
      .order('created_at', { ascending: false });

    if (analyticsError) {
      console.error('Error fetching analytics:', analyticsError);
      return NextResponse.json(
        { error: 'Failed to fetch analytics' },
        { status: 500 }
      );
    }

    // Get form submissions for conversion metrics
    let submissionsQuery = supabase
      .from('embeddable_form_submissions')
      .select('*')
      .gte('created_at', startDate.toISOString());

    if (organizationId) {
      submissionsQuery = submissionsQuery.eq('organization_id', organizationId);
    }
    if (formId) {
      submissionsQuery = submissionsQuery.eq('form_id', formId);
    }

    const { data: submissions, error: submissionsError } = await submissionsQuery;

    if (submissionsError) {
      console.error('Error fetching submissions:', submissionsError);
    }

    // Calculate metrics
    const totalViews = analytics?.filter(a => a.event_type === 'view').length || 0;
    const totalStarts = analytics?.filter(a => a.event_type === 'start').length || 0;
    const totalSubmissions = submissions?.length || 0;
    const totalCompletes = analytics?.filter(a => a.event_type === 'complete').length || 0;

    // Calculate conversion rates
    const startRate = totalViews > 0 ? (totalStarts / totalViews * 100) : 0;
    const submissionRate = totalStarts > 0 ? (totalSubmissions / totalStarts * 100) : 0;
    const completionRate = totalSubmissions > 0 ? (totalCompletes / totalSubmissions * 100) : 0;

    // Group analytics by date for charts
    const dailyMetrics = analytics?.reduce((acc: any, event) => {
      const date = event.created_at.split('T')[0];
      if (!acc[date]) {
        acc[date] = { views: 0, starts: 0, submits: 0, completes: 0 };
      }
      acc[date][event.event_type === 'view' ? 'views' : 
                event.event_type === 'start' ? 'starts' :
                event.event_type === 'submit' ? 'submits' : 'completes']++;
      return acc;
    }, {}) || {};

    // Top referrers
    const referrers = analytics?.reduce((acc: any, event) => {
      if (event.referrer) {
        const domain = new URL(event.referrer).hostname;
        acc[domain] = (acc[domain] || 0) + 1;
      }
      return acc;
    }, {}) || {};

    const topReferrers = Object.entries(referrers)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 10)
      .map(([domain, count]) => ({ domain, count }));

    return NextResponse.json({
      summary: {
        totalViews,
        totalStarts,
        totalSubmissions,
        totalCompletes,
        startRate: Math.round(startRate * 100) / 100,
        submissionRate: Math.round(submissionRate * 100) / 100,
        completionRate: Math.round(completionRate * 100) / 100
      },
      dailyMetrics: Object.entries(dailyMetrics).map(([date, metrics]) => ({
        date,
        ...metrics
      })),
      topReferrers,
      timeRange,
      period: `${startDate.toISOString().split('T')[0]} to ${now.toISOString().split('T')[0]}`
    });

  } catch (error) {
    console.error('Analytics error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}