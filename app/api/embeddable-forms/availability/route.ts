import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js';

// Real-time availability checking for embeddable forms
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      organizationId,
      serviceType,
      pickupLocation,
      dropoffLocation,
      pickupDateTime,
      returnDateTime,
      passengerCount 
    } = body;

    // Validate required fields
    if (!organizationId || !serviceType || !pickupLocation || !pickupDateTime) {
      return NextResponse.json(
        { error: 'Missing required fields for availability check' },
        { status: 400 }
      );
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Step 1: Get organization's affiliate companies
    const { data: affiliates, error: affiliatesError } = await supabase
      .from('affiliate_companies')
      .select(`
        id,
        name,
        service_areas,
        vehicle_types,
        rate_cards (
          id,
          service_type,
          base_rate,
          per_mile_rate,
          per_hour_rate,
          minimum_hours,
          active
        )
      `)
      .eq('organization_id', organizationId)
      .eq('status', 'active');

    if (affiliatesError) {
      console.error('Error fetching affiliates:', affiliatesError);
      return NextResponse.json(
        { error: 'Failed to check availability' },
        { status: 500 }
      );
    }

    // Step 2: Filter affiliates by service area and availability
    const availableAffiliates = affiliates?.filter(affiliate => {
      // Check if affiliate serves the pickup location
      const serviceAreas = affiliate.service_areas || [];
      const servesArea = serviceAreas.some((area: any) => 
        pickupLocation.toLowerCase().includes(area.toLowerCase()) ||
        area.toLowerCase().includes('all') ||
        area.toLowerCase().includes('nationwide')
      );

      // Check if affiliate has rate cards for the service type
      const hasRateCard = affiliate.rate_cards?.some((card: any) => 
        card.service_type === serviceType && card.active
      );

      return servesArea && hasRateCard;
    }) || [];

    // Step 3: Calculate estimated pricing
    const estimatedPricing = availableAffiliates.map(affiliate => {
      const rateCard = affiliate.rate_cards?.find((card: any) => 
        card.service_type === serviceType && card.active
      );

      if (!rateCard) return null;

      // Basic pricing calculation (simplified)
      let estimatedPrice = rateCard.base_rate || 0;
      
      if (serviceType === 'hourly' && returnDateTime) {
        const hours = Math.ceil(
          (new Date(returnDateTime).getTime() - new Date(pickupDateTime).getTime()) / 
          (1000 * 60 * 60)
        );
        const minimumHours = rateCard.minimum_hours || 2;
        estimatedPrice += (Math.max(hours, minimumHours) * (rateCard.per_hour_rate || 0));
      } else if (serviceType === 'point-to-point') {
        // Estimate distance (simplified - in production use Google Maps API)
        const estimatedMiles = 10; // TODO: Calculate actual distance
        estimatedPrice += (estimatedMiles * (rateCard.per_mile_rate || 0));
      }

      return {
        affiliateId: affiliate.id,
        affiliateName: affiliate.name,
        estimatedPrice: Math.round(estimatedPrice * 100) / 100,
        vehicleTypes: affiliate.vehicle_types || ['sedan'],
        responseTime: '15-30 minutes'
      };
    }).filter(Boolean);

    // Step 4: Determine availability status
    const isAvailable = availableAffiliates.length > 0;
    const lowestPrice = estimatedPricing.length > 0 ? 
      Math.min(...estimatedPricing.map(p => p.estimatedPrice)) : null;
    const highestPrice = estimatedPricing.length > 0 ? 
      Math.max(...estimatedPricing.map(p => p.estimatedPrice)) : null;

    // Step 5: Track availability check
    await supabase
      .from('embeddable_form_analytics')
      .insert({
        organization_id: organizationId,
        event_type: 'availability_check',
        session_id: request.headers.get('x-session-id'),
        user_agent: request.headers.get('user-agent'),
        referrer: request.headers.get('referer'),
        ip_address: request.headers.get('x-forwarded-for') || '127.0.0.1'
      });

    return NextResponse.json({
      available: isAvailable,
      affiliateCount: availableAffiliates.length,
      estimatedPriceRange: lowestPrice && highestPrice ? {
        min: lowestPrice,
        max: highestPrice,
        currency: 'USD'
      } : null,
      estimatedResponseTime: '15-30 minutes',
      availableVehicleTypes: [
        ...new Set(estimatedPricing.flatMap(p => p.vehicleTypes))
      ],
      message: isAvailable ? 
        `${availableAffiliates.length} providers available in your area` :
        'No providers available for this route at the moment',
      providers: estimatedPricing
    });

  } catch (error) {
    console.error('Availability check error:', error);
    return NextResponse.json(
      { error: 'Failed to check availability' },
      { status: 500 }
    );
  }
}