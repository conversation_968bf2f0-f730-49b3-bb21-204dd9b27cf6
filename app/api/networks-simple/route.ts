import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { authenticateSuperAdminRequest } from '@/lib/auth/api-authentication';

export const runtime = 'nodejs';

// PRODUCTION NETWORKS API - Enterprise-grade authentication
export async function GET(request: NextRequest) {
  try {
    console.log("Production Networks API - Starting request");
    
    // Use the working authentication system
    const authResult = await authenticateSuperAdminRequest(request);
    
    if (!authResult.success) {
      console.error('Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    
    const { user } = authResult;
    console.log(`Authenticated user: ${user.email} (${user.role})`);
    
    // For super admin: Only return the main TransFlow Shared NETWORK
    // NOT client organizations - those belong in OPS dropdown
    const networks = [
      {
        id: 'cfab4e29-8917-459d-ae5b-3e438a5ba358', // TransFlow Shared ID (correct from database)
        name: 'TransFlow Shared',
        slug: 'transflow-shared',
        domain: null,
        organization_type: 'shared',
        subscription_plan: 'enterprise',
        permission_template: 'super_admin',
        status: 'active',
        user_role: 'SUPER_ADMIN',
        client_level: 'Network',
        is_network: true, // Flag to identify as network
        capabilities: {
          can_have_white_labeling: true,
          can_have_custom_domain: true,
          can_have_custom_branding: true,
          has_white_labeling: false,
          has_custom_domain: false,
          has_custom_branding: false,
        }
      }
    ];

    console.log(`Returning ${networks.length} network(s) for super admin`);
    
    return NextResponse.json({
      success: true,
      organizations: networks, // Keep same structure for compatibility
      total: networks.length,
      user_context: {
        roles: [user.role],
        userId: user.id,
        email: user.email
      }
    });

  } catch (error) {
    console.error('Error in GET /api/networks-simple:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}