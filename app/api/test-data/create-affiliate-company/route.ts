import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server'

// FOR DEVELOPMENT USE ONLY
// This endpoint creates a test affiliate company for the current user
export async function POST(req: Request) {
  // Only allow in development and test environments
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Endpoint not available in production' }, { status: 403 })
  }

  try {
    console.log('[test-data/create-affiliate-company] Starting request')
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the current user's session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      console.error('[test-data/create-affiliate-company] No session found')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log(`[test-data/create-affiliate-company] Creating test company for user: ${session.user.id}`)

    // Check if a company already exists
    const { data: existingCompanies } = await supabase
      .from('affiliate_companies')
      .select('id')
      .eq('owner_id', session.user.id)
      .limit(1)

    if (existingCompanies && existingCompanies.length > 0) {
      return NextResponse.json({ 
        message: 'Company already exists',
        companyId: existingCompanies[0].id
      })
    }

    // Create a new test company
    const now = new Date().toISOString()
    const { data: newCompany, error } = await supabase
      .from('affiliate_companies')
      .insert({
        owner_id: session.user.id,
        name: `Test Company for ${session.user.email}`,
        email: session.user.email,
        phone: '+15551234567',
        address: '123 Test Street',
        city: 'Test City',
        state: 'TS',
        zip: '12345',
        country: 'USA',
        status: 'active',
        rating: 4.5,
        completion_rate: 0.95,
        created_at: now,
        updated_at: now
      })
      .select()
      .single()

    if (error) {
      console.error('[test-data/create-affiliate-company] Error creating company:', error)
      return NextResponse.json({ error: 'Failed to create company', details: error.message }, { status: 500 })
    }

    console.log(`[test-data/create-affiliate-company] Created company with ID: ${newCompany?.id}`)
    return NextResponse.json({
      message: 'Test company created successfully',
      company: newCompany
    })
  } catch (error) {
    console.error('[test-data/create-affiliate-company] Error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
} 