import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server'

// FOR DEVELOPMENT USE ONLY
// This endpoint creates a test quote and associates it with the user's affiliate company
export async function POST(req: Request) {
  // Only allow in development and test environments
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Endpoint not available in production' }, { status: 403 })
  }

  try {
    console.log('[test-data/create-quote-data] Starting request')
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the current user's session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      console.error('[test-data/create-quote-data] No session found')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log(`[test-data/create-quote-data] User authenticated: ${session.user.id}`)

    // Get the user's affiliate companies
    const { data: companies } = await supabase
      .from('affiliate_companies')
      .select('id')
      .eq('owner_id', session.user.id)

    if (!companies || companies.length === 0) {
      console.error('[test-data/create-quote-data] No affiliate companies found for user')
      return NextResponse.json({ error: 'No affiliate companies found' }, { status: 400 })
    }

    // Use the first company
    const companyId = companies[0].id
    console.log(`[test-data/create-quote-data] Using company: ${companyId}`)

    // Create a test customer
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .insert({
        full_name: 'Test Customer',
        email: '<EMAIL>',
        phone: '+15551234567',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (customerError) {
      console.error('[test-data/create-quote-data] Error creating customer:', customerError)
      return NextResponse.json({ error: 'Failed to create customer', details: customerError.message }, { status: 500 })
    }

    // Create a test quote
    const now = new Date().toISOString()
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const tomorrowStr = tomorrow.toISOString().split('T')[0]

    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .insert({
        reference_number: `Q-${Math.floor(Math.random() * 10000)}`,
        customer_id: customer.id,
        service_type: 'Standard',
        vehicle_type: 'Sedan',
        pickup_location: '123 Main St, New York, NY',
        dropoff_location: '456 Broadway, New York, NY',
        date: tomorrowStr,
        time: '14:00',
        passenger_count: 2,
        luggage_count: 2,
        special_requests: 'Test quote for affiliate',
        status: 'pending',
        created_at: now,
        updated_at: now
      })
      .select()
      .single()

    if (quoteError) {
      console.error('[test-data/create-quote-data] Error creating quote:', quoteError)
      return NextResponse.json({ error: 'Failed to create quote', details: quoteError.message }, { status: 500 })
    }

    console.log(`[test-data/create-quote-data] Created quote: ${quote.id}`)

    // Create a quote offer that connects the quote to the affiliate company
    const { data: offer, error: offerError } = await supabase
      .from('quote_offers')
      .insert({
        quote_id: quote.id,
        company_id: companyId,
        rate_amount: 85.00,
        rate_currency: 'USD',
        status: 'pending',
        created_at: now,
        updated_at: now,
        timeout_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
      })
      .select()
      .single()

    if (offerError) {
      console.error('[test-data/create-quote-data] Error creating offer:', offerError)
      return NextResponse.json({ error: 'Failed to create offer', details: offerError.message }, { status: 500 })
    }

    console.log(`[test-data/create-quote-data] Created offer: ${offer.id}`)

    // Return the created data
    return NextResponse.json({
      message: 'Test data created successfully',
      customer: customer,
      quote: quote,
      offer: offer
    })
  } catch (error) {
    console.error('[test-data/create-quote-data] Error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
} 