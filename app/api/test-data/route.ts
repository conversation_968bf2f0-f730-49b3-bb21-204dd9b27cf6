import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabaseAdmin';

export async function GET() {
  try {
    // Test data functionality not implemented yet - quotes table doesn't exist
    return NextResponse.json({
      success: false,
      error: 'Test data functionality not implemented yet'
    }, { status: 501 });
  } catch (error) {
    console.error('Error in test data API:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ success: false, error: errorMessage }, { status: 500 });
  }
}