import { NextResponse } from "next/server"

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/lib/types/supabase'

export async function GET(req: Request) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set(name, value, options)
          },
          remove(name: string, options: any) {
            cookieStore.set(name, '', options)
          },
        },
      }
    )
    
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.user.id)
      .single()

    if (error) {
      console.error('Error fetching profile:', error)
      return NextResponse.json({ message: error.message }, { status: 500 })
    }

    return NextResponse.json({ profile }, { status: 200 })
  } catch (error: any) {
    console.error('Unexpected error in GET /api/profile:', error)
    return NextResponse.json(
      { message: error.message },
      { status: 500 }
    )
  }
}

export async function PUT(req: Request) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set(name, value, options)
          },
          remove(name: string, options: any) {
            cookieStore.set(name, '', options)
          },
        },
      }
    )
    
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    console.log('Profile update request body:', body)
    
    // Ensure we're only updating valid columns
    const validColumns = ['full_name', 'first_name', 'last_name', 'phone_number', 'avatar_url']
    const filteredBody: Record<string, any> = {}
    
    for (const key in body) {
      // Map frontend field names to database column names
      const dbKey = key === 'phone' ? 'phone_number' : 
                   key === 'email' ? null : // Don't update email directly
                   key === 'company' ? null : // company_name doesn't exist in profiles table
                   key
      
      if (dbKey && validColumns.includes(dbKey)) {
        filteredBody[dbKey] = body[key]
      }
    }
    
    // Add updated_at timestamp
    filteredBody.updated_at = new Date().toISOString()
    
    console.log('Filtered profile update data:', filteredBody)
    
    const { data, error } = await supabase
      .from('profiles')
      .update(filteredBody)
      .eq('id', session.user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating profile:', error)
      return NextResponse.json({ message: error.message }, { status: 500 })
    }

    // Map database column names back to frontend field names for consistency
    const mappedProfile = {
      ...data,
      phone: data.phone_number as unknown as string
    }

    return NextResponse.json({ profile: mappedProfile }, { status: 200 })
  } catch (error: any) {
    console.error('Unexpected error in PUT /api/profile:', error)
    return NextResponse.json(
      { message: error.message },
      { status: 500 }
    )
  }
}
