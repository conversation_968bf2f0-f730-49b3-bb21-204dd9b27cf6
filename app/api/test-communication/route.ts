import { NextRequest, NextResponse } from 'next/server';
import { CommunicationService } from '@/app/lib/communication-service';

/**
 * POST /api/test-communication
 * Test endpoint for communication system
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type = 'email', template = 'quote_submitted', recipient } = body;

    if (!recipient?.email || !recipient?.name) {
      return NextResponse.json(
        { error: 'recipient.email and recipient.name are required' },
        { status: 400 }
      );
    }

    const commService = new CommunicationService();

    // Test data for quote submission
    const testVariables = {
      customerName: recipient.name,
      quoteReference: 'TEST-' + Date.now(),
      pickupLocation: 'Test Pickup Location',
      dropoffLocation: 'Test Dropoff Location',
      tripDate: new Date().toLocaleDateString(),
      tripTime: '10:00 AM',
      passengerCount: 2,
      vehicleType: 'Sedan',
      affiliateCount: 3,
      multipleAffiliates: true,
      trackingUrl: `${process.env.NEXT_PUBLIC_APP_URL}/quotes/test-123`
    };

    const success = await commService.sendCommunication({
      type: type as 'email' | 'sms' | 'whatsapp',
      recipient,
      template,
      variables: testVariables,
      priority: 'normal',
      organizationId: 'test-tenant'
    });

    return NextResponse.json({
      success,
      message: success ? 'Communication sent successfully' : 'Communication failed',
      testData: {
        type,
        template,
        recipient,
        variables: testVariables
      }
    });

  } catch (error) {
    console.error('Test communication error:', error);
    return NextResponse.json(
      { error: 'Failed to send test communication', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET /api/test-communication
 * Get available templates and test options
 */
export async function GET() {
  return NextResponse.json({
    availableTemplates: [
      'quote_submitted',
      'affiliate_offer_received',
      'counter_offer_received',
      'quote_accepted'
    ],
    availableTypes: ['email', 'sms', 'whatsapp'],
    testEndpoint: '/api/test-communication',
    example: {
      type: 'email',
      template: 'quote_submitted',
      recipient: {
        email: '<EMAIL>',
        name: 'Test User'
      }
    }
  });
}
