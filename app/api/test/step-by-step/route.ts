import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  const results: any[] = [];
  
  try {
    console.log('=== STEP-BY-STEP ORGANIZATION CREATION TEST ===');
    
    // Use service role client
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    const timestamp = Date.now();
    
    // STEP 1: Test tenant creation
    console.log('STEP 1: Creating tenant...');
    const tenantSlug = `test-org-${timestamp}`;
    
    const { data: tenant, error: tenantError } = await supabaseAdmin
      .schema('saas_organizations')
      .from('tenants')
      .insert({
        name: `Test Organization ${timestamp}`,
        slug: tenantSlug,
        organization_type: 'segregated',
        status: 'active',
        settings: {
          industry: 'logistics',
          plan: 'professional'
        }
      })
      .select()
      .single();

    if (tenantError) {
      results.push({ step: 1, status: 'FAILED', error: tenantError.message });
      throw new Error(`Step 1 failed: ${tenantError.message}`);
    }
    
    results.push({ step: 1, status: 'SUCCESS', organization_id: tenant.id });
    console.log('✅ Step 1: Tenant created:', tenant.id);

    // STEP 2: Test organization creation
    console.log('STEP 2: Creating organization...');
    
    const { data: organization, error: orgError } = await supabaseAdmin
      .from('organizations')
      .insert({
        name: `Test Organization ${timestamp}`,
        slug: tenantSlug,
        industry: 'logistics',
        organization_id: tenant.id,
        email: `test-${timestamp}@example.com`,
        settings: {
          plan: 'professional',
          status: 'active'
        },
        status: 'active'
      })
      .select()
      .single();

    if (orgError) {
      results.push({ step: 2, status: 'FAILED', error: orgError.message });
      throw new Error(`Step 2 failed: ${orgError.message}`);
    }
    
    results.push({ step: 2, status: 'SUCCESS', organization_id: organization.id });
    console.log('✅ Step 2: Organization created:', organization.id);

    // STEP 3: Test user creation (auth)
    console.log('STEP 3: Creating auth user...');
    
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.createUser({
      email: `admin-${timestamp}@example.com`,
      password: 'TestPassword123!',
      email_confirm: true,
      user_metadata: {
        role: 'CLIENT_COORDINATOR',
        created_by: 'test'
      }
    });

    if (userError) {
      console.error('Detailed user creation error:', userError);
      results.push({ step: 3, status: 'FAILED', error: userError.message, details: userError });
      throw new Error(`Step 3 failed: ${userError.message} - Details: ${JSON.stringify(userError)}`);
    }
    
    results.push({ step: 3, status: 'SUCCESS', user_id: userData.user?.id });
    console.log('✅ Step 3: Auth user created:', userData.user?.id);

    // STEP 4: Test profile creation
    console.log('STEP 4: Creating user profile...');
    
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: userData.user!.id,
        email: `admin-${timestamp}@example.com`,
        first_name: 'Test',
        last_name: 'Admin',
        full_name: 'Test Admin',
        role: 'CLIENT_COORDINATOR',
        roles: ['CLIENT_COORDINATOR']
      })
      .select()
      .single();

    if (profileError) {
      results.push({ step: 4, status: 'FAILED', error: profileError.message });
      throw new Error(`Step 4 failed: ${profileError.message}`);
    }
    
    results.push({ step: 4, status: 'SUCCESS', profile_id: profile.id });
    console.log('✅ Step 4: Profile created:', profile.id);

    console.log('🎉 ALL STEPS COMPLETED SUCCESSFULLY');
    
    return NextResponse.json({
      success: true,
      message: 'All steps completed successfully',
      results: results,
      created: {
        tenant: tenant,
        organization: organization,
        user: userData.user,
        profile: profile
      }
    });

  } catch (error) {
    console.error('❌ TEST FAILED:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      results: results
    }, { status: 500 });
  }
}
