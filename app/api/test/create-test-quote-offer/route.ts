import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ''

export async function GET() {
  try {
    // Create a Supabase client with the service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get a random quote
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select('id, city')
      .eq('status', 'pending')
      .limit(1)
    
    if (quotesError) {
      console.error('Error fetching quotes:', quotesError)
      return NextResponse.json(
        { error: 'Error fetching quotes' },
        { status: 500 }
      )
    }

    if (!quotes || quotes.length === 0) {
      return NextResponse.json(
        { error: 'No pending quotes found' },
        { status: 404 }
      )
    }

    const quote = quotes[0]

    // Get the Boston affiliate
    const { data: bostonAffiliate, error: affiliateError } = await supabase
      .from('affiliate_companies')
      .select('id')
      .eq('city', 'Boston')
      .single()
    
    if (affiliateError) {
      console.error('Error fetching Boston affiliate:', affiliateError)
      return NextResponse.json(
        { error: 'Error fetching Boston affiliate' },
        { status: 500 }
      )
    }

    // Create a test quote offer
    const timeout = new Date()
    timeout.setHours(timeout.getHours() + 24) // 24 hours from now

    const { data: quoteOffer, error: offerError } = await supabase
      .from('quote_offers')
      .insert({
        quote_id: quote.id,
        company_id: bostonAffiliate.id,
        rate_amount: 150.00,
        rate_currency: 'USD',
        status: 'pending',
        timeout_at: timeout.toISOString()
      })
      .select()
      .single()
    
    if (offerError) {
      console.error('Error creating quote offer:', offerError)
      return NextResponse.json(
        { error: 'Error creating quote offer' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Test quote offer created successfully',
      data: quoteOffer
    })
  } catch (error) {
    console.error('Error creating test quote offer:', error)
    return NextResponse.json(
      { error: 'Error creating test quote offer' },
      { status: 500 }
    )
  }
} 