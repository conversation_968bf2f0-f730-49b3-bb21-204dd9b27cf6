import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Simulate the complete quote form submission flow
 */
export async function POST(request: NextRequest) {
  try {
    console.log('TEST: Simulating complete quote form submission flow')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Step 1: Create a quote (simulating form submission)
    const quoteData = {
      reference_number: `Q${Date.now()}`,
      customer_id: '2fee08c5-e012-4db0-842e-ebf4d7997c39',
      service_type: 'point', // This is what the form sends
      vehicle_type: 'SUV',
      passenger_count: 1,
      pickup_location: 'SSSSSSSSS',
      dropoff_location: 'TTTTTTTTTTTTT',
      city: 'Austin', // This is what the form sends
      date: '2025-01-02',
      time: '10:00:00',
      duration: '1 hour',
      distance: '25 miles',
      priority: 'medium',
      contact_name: 'Test Client',
      contact_email: '<EMAIL>',
      contact_phone: '************',
      special_requests: ['Quote form simulation test'],
      status: 'pending',
      pickup_latitude: 30.2672,
      pickup_longitude: -97.7431,
      dropoff_latitude: 30.2672,
      dropoff_longitude: -97.7431
    }

    console.log('Step 1: Creating quote with form data...')
    const { data: createdQuote, error: quoteError } = await supabase
      .from('quotes')
      .insert(quoteData)
      .select()
      .single()

    if (quoteError) {
      console.error('Error creating quote:', quoteError)
      return NextResponse.json(
        { error: 'Failed to create quote', details: quoteError.message },
        { status: 500 }
      )
    }

    console.log('Quote created successfully:', createdQuote.id)

    // Step 2: Simulate the affiliate matching API call (what the form should do)
    console.log('Step 2: Simulating affiliate matching API call...')
    
    const matchingParams = {
      city: quoteData.city,
      vehicleType: quoteData.vehicle_type,
      serviceType: quoteData.service_type,
      date: quoteData.date,
      time: quoteData.time,
      passengers: quoteData.passenger_count
    }

    console.log('Matching parameters:', matchingParams)

    // Call our test affiliate matching endpoint
    const matchingResponse = await fetch('http://localhost:3000/api/test/affiliate-matching-auth', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({})
    })

    let matchingResult = null
    if (matchingResponse.ok) {
      matchingResult = await matchingResponse.json()
      console.log(`Affiliate matching returned ${matchingResult.affiliates?.length || 0} affiliates`)
    } else {
      console.error('Affiliate matching failed:', matchingResponse.status)
      matchingResult = { error: 'Matching failed' }
    }

    // Step 3: Simulate what the quote form should display
    console.log('Step 3: Simulating quote form display logic...')
    
    const shouldShowMatching = true // This should be set to true after quote creation
    const matchingStep = matchingResult?.affiliates?.length > 0 ? 'results' : 'results' // Should show results either way
    const affiliates = matchingResult?.affiliates || []

    // Step 4: Test the actual affiliate matching API endpoint (with auth simulation)
    console.log('Step 4: Testing actual affiliate matching API endpoint...')
    
    // This simulates what the quote form does
    const actualApiResponse = await fetch('http://localhost:3000/api/event-manager/quotes/match-affiliates', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        // Note: In real scenario, this would include auth cookies
      },
      body: JSON.stringify(matchingParams)
    })

    let actualApiResult = null
    let actualApiStatus = actualApiResponse.status
    
    if (actualApiResponse.ok) {
      actualApiResult = await actualApiResponse.json()
      console.log('Actual API returned:', actualApiResult.affiliates?.length || 0, 'affiliates')
    } else {
      const errorText = await actualApiResponse.text()
      console.error('Actual API failed:', actualApiStatus, errorText)
      actualApiResult = { error: errorText }
    }

    return NextResponse.json({
      success: true,
      message: 'Quote form simulation completed',
      simulation_results: {
        step1_quote_creation: {
          success: !!createdQuote,
          quote_id: createdQuote?.id,
          reference_number: createdQuote?.reference_number,
          form_data: {
            service_type: quoteData.service_type,
            city: quoteData.city,
            vehicle_type: quoteData.vehicle_type,
            passenger_count: quoteData.passenger_count
          }
        },
        step2_affiliate_matching_test: {
          success: !!matchingResult?.affiliates,
          affiliates_found: matchingResult?.affiliates?.length || 0,
          affiliates: matchingResult?.affiliates || []
        },
        step3_form_display_logic: {
          should_show_matching: shouldShowMatching,
          matching_step: matchingStep,
          affiliates_to_display: affiliates.length
        },
        step4_actual_api_test: {
          api_status: actualApiStatus,
          success: actualApiResponse.ok,
          affiliates_found: actualApiResult?.affiliates?.length || 0,
          error: actualApiResult?.error || null,
          auth_issue: actualApiStatus === 401
        }
      },
      diagnosis: {
        quote_creation_works: !!createdQuote,
        affiliate_matching_logic_works: (matchingResult?.affiliates?.length || 0) > 0,
        actual_api_accessible: actualApiResponse.ok,
        likely_issue: actualApiStatus === 401 ? 'Authentication required for API' :
                     actualApiStatus !== 200 ? 'API endpoint issue' :
                     (actualApiResult?.affiliates?.length || 0) === 0 ? 'No affiliates returned' :
                     'Unknown - should be working'
      },
      recommendations: [
        actualApiStatus === 401 ? 'Check authentication in quote form API calls' : null,
        'Verify quote form state management for showAffiliateMatching',
        'Check browser console for JavaScript errors during form submission',
        'Verify affiliate matching API is being called after quote creation'
      ].filter(Boolean),
      test_data: {
        quote_form_parameters: matchingParams,
        expected_affiliates: affiliates.length,
        actual_api_response: actualApiResult
      }
    })

  } catch (error) {
    console.error('Error in quote form simulation:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
