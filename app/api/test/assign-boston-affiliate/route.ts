import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a Supabase client with the service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://127.0.0.1:54321',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'
)

export async function GET() {
  try {
    // Get the <NAME_EMAIL>
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .eq('email', '<EMAIL>')
      .single()
    
    if (profileError) {
      console.error('Error finding test account:', profileError)
      return NextResponse.json({ success: false, error: profileError.message }, { status: 500 })
    }
    
    if (!profile) {
      return NextResponse.json({ success: false, error: 'Test account not found' }, { status: 404 })
    }
    
    const testUserId = profile.id
    console.log('Found test account with ID:', testUserId)
    
    // Update the Boston affiliate company
    const { data: updated, error: updateError } = await supabaseAdmin
      .from('affiliate_companies')
      .update({ owner_id: testUserId })
      .eq('city', 'Boston')
      .select()
    
    if (updateError) {
      console.error('Error updating Boston affiliate:', updateError)
      return NextResponse.json({ success: false, error: updateError.message }, { status: 500 })
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Boston affiliate assigned to test account',
      data: updated
    })
  } catch (error) {
    console.error('Error in assign-boston-affiliate API:', error)
    return NextResponse.json({ success: false, error: 'Unexpected error' }, { status: 500 })
  }
} 