import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs';
import { createClient } from '@supabase/supabase-js'



export async function POST(request: NextRequest) {
  try {
    console.log('=== TESTING BOOKING FLOW FIX ===')
    
    // Create admin client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Test data simulating what the booking form would send
    const testBookingData = {
      pickup_location: "Miami, FL, United States",
      dropoff_location: "Miami International Airport, 2100 NW 42nd Ave, Miami, FL 33122, United States",
      pickup_date: "2025-06-23",
      pickup_time: "10:00",
      service_type: "point",
      passenger_count: 2,
      adults: 2,
      children: 0,
      vehicle_type: "suv",
      city: "Miami",
      selectedAffiliates: [
        { id: "954559f6-517d-43ba-aa83-f4c2b2705c5e", order: 1 } // WW MOBILITY SOLUTIONS (<EMAIL>)
      ]
    }

    console.log('Test booking data:', testBookingData)

    // Simulate the fixed NextJSWrapper logic
    let response;
    if (testBookingData.selectedAffiliates && testBookingData.selectedAffiliates.length > 0) {
      console.log('Using affiliate selection endpoint (CORRECT)')
      
      // Call the event-manager submit endpoint
      response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/event-manager/quotes/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          quoteRequest: testBookingData,
          selectedAffiliates: testBookingData.selectedAffiliates
        })
      })
    } else {
      console.log('Using basic quote endpoint')
      response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/quotes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testBookingData)
      })
    }

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API Error:', errorText)
      return NextResponse.json({ 
        error: 'API call failed', 
        details: errorText,
        status: response.status 
      }, { status: 500 })
    }

    const result = await response.json()
    console.log('Success result:', result)

    // Verify the quote was created with affiliate offers
    if (result.quoteId) {
      const { data: offers, error: offersError } = await supabase
        .from('quote_affiliate_offers')
        .select('*')
        .eq('quote_id', result.quoteId)

      console.log('Created offers:', offers)
      
      return NextResponse.json({
        success: true,
        message: 'Booking flow fix test completed',
        quote: result,
        offersCreated: offers?.length || 0,
        offers: offers
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Test completed but no quote ID returned',
      result
    })

  } catch (error) {
    console.error('Test error:', error)
    return NextResponse.json(
      { error: 'Test failed', details: error.message },
      { status: 500 }
    )
  }
}
