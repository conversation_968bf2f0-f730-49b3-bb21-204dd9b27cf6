import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Check if console issues are resolved
 */
export async function GET(request: NextRequest) {
  try {
    console.log('TEST: Checking console issues resolution')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Test 1: Check if user profile exists and can be created
    const testUserId = '2fee08c5-e012-4db0-842e-ebf4d7997c39'
    
    console.log('Step 1: Checking user profile...')
    const { data: existingProfile, error: profileError } = await supabase
      .from('user_profiles_secure')
      .select('user_id, roles')
      .eq('user_id', testUserId)
      .maybeSingle()

    let profileStatus = 'unknown'
    if (profileError && profileError.code === 'PGRST116') {
      profileStatus = 'missing'
      console.log('Profile missing, attempting to create...')
      
      // Try to create the profile
      const { data: newProfile, error: createError } = await supabase
        .from('user_profiles_secure')
        .insert({
          user_id: testUserId,
          roles: ['CLIENT']
        })
        .select()
        .single()

      if (createError) {
        console.error('Failed to create profile:', createError)
        profileStatus = 'creation_failed'
      } else {
        console.log('Profile created successfully:', newProfile)
        profileStatus = 'created'
      }
    } else if (existingProfile) {
      profileStatus = 'exists'
      console.log('Profile exists:', existingProfile)
    } else {
      profileStatus = 'error'
      console.error('Profile check error:', profileError)
    }

    // Test 2: Check affiliate matching is working
    console.log('Step 2: Testing affiliate matching...')
    
    // Skip fetch during build process
    let matchingResponse = null
    if (process.env.NODE_ENV !== 'production' || process.env.VERCEL !== '1') {
      try {
        matchingResponse = await fetch('http://localhost:3003/api/test/current-quote-issue', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}),
          signal: AbortSignal.timeout(5000) // 5 second timeout
        })
      } catch (error) {
        console.log('Fetch skipped during build or timed out:', error)
      }
    }

    let matchingStatus = 'unknown'
    let matchingResults = null
    
    if (matchingResponse && matchingResponse.ok) {
      matchingResults = await matchingResponse.json()
      const validAffiliates = matchingResults.step_by_step_results?.step5_final_matches?.valid_affiliates || 0
      matchingStatus = validAffiliates > 0 ? 'working' : 'no_matches'
      console.log(`Affiliate matching: ${validAffiliates} valid affiliates found`)
    } else {
      matchingStatus = 'api_error'
      console.error('Matching API failed:', matchingResponse.status)
    }

    // Test 3: Check WebSocket connection status
    console.log('Step 3: Checking WebSocket connection...')
    
    // This is a simple check - in a real scenario, we'd need to test the actual WebSocket
    const wsStatus = 'not_tested' // We can't easily test WebSocket from server-side

    return NextResponse.json({
      success: true,
      message: 'Console issues check completed',
      timestamp: new Date().toISOString(),
      test_results: {
        user_profile: {
          status: profileStatus,
          user_id: testUserId,
          details: profileStatus === 'exists' ? existingProfile : 
                  profileStatus === 'created' ? 'Profile created successfully' :
                  profileStatus === 'missing' ? 'Profile was missing' :
                  profileStatus === 'creation_failed' ? 'Failed to create profile' :
                  'Unknown status'
        },
        affiliate_matching: {
          status: matchingStatus,
          valid_affiliates: matchingResults?.step_by_step_results?.step5_final_matches?.valid_affiliates || 0,
          total_affiliates: matchingResults?.step_by_step_results?.step1_affiliates?.found || 0,
          total_vehicles: matchingResults?.step_by_step_results?.step2_vehicles?.found || 0,
          total_rate_cards: matchingResults?.step_by_step_results?.step3_rate_cards_filtered?.found || 0
        },
        websocket_connection: {
          status: wsStatus,
          note: 'WebSocket testing requires client-side implementation'
        }
      },
      issues_addressed: [
        {
          issue: 'React ref warning with Select component',
          status: 'fixed',
          solution: 'Added forwardRef to Select component'
        },
        {
          issue: 'Multiple Supabase client instances',
          status: 'fixed', 
          solution: 'Updated user-nav to use singleton getSupabaseClient()'
        },
        {
          issue: 'Missing user profile',
          status: profileStatus === 'exists' || profileStatus === 'created' ? 'fixed' : 'needs_attention',
          solution: 'Auto-create CLIENT profile when missing'
        },
        {
          issue: 'Cookie parsing errors',
          status: 'mitigated',
          solution: 'Middleware clears corrupted cookies, but some errors may still appear during initial load'
        },
        {
          issue: 'Affiliate matching not working',
          status: matchingStatus === 'working' ? 'fixed' : 'needs_attention',
          solution: 'Made pricing model filtering more flexible to accept multiple models'
        }
      ],
      recommendations: [
        'Monitor browser console for remaining cookie parsing errors',
        'Test quote submission flow in browser to verify all fixes',
        'Check WebSocket connection status in browser developer tools',
        'Verify user profile creation works for new users'
      ],
      next_steps: [
        'Test the quote form submission in the browser',
        'Verify affiliate matching returns results',
        'Check that console warnings are reduced',
        'Monitor for any remaining authentication issues'
      ]
    })

  } catch (error) {
    console.error('Error in console issues check:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        success: false
      },
      { status: 500 }
    )
  }
}
