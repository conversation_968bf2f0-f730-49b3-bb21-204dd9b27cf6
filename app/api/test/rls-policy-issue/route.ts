import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Verify RLS policy issue with affiliate companies
 */
export async function POST(request: NextRequest) {
  try {
    console.log('TEST: Verifying RLS policy issue with affiliate companies')

    // Test 1: Service role client (bypasses RLS)
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)
    
    console.log('Test 1: Querying with service role (bypasses RLS)...')
    const { data: adminAffiliates, error: adminError } = await supabaseAdmin
      .from('affiliate_companies')
      .select('id, name, city, status, application_status, owner_id')
      .eq('status', 'active')
      .eq('application_status', 'approved')
      .ilike('city', '%Austin%')

    console.log('Service role results:', {
      success: !adminError,
      count: adminAffiliates?.length || 0,
      affiliates: adminAffiliates?.map(a => ({ 
        id: a.id, 
        name: a.name, 
        city: a.city,
        owner_id: a.owner_id 
      }))
    })

    // Test 2: Simulate authenticated client query (with RLS)
    const supabaseClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!)
    
    // Set the auth context to simulate the CLIENT user
    const { data: authData, error: authError } = await supabaseClient.auth.admin.generateLink({
      type: 'magiclink',
      email: '<EMAIL>'
    })

    console.log('Test 2: Simulating CLIENT user query (with RLS)...')
    
    // This simulates what happens in the authenticated API
    const { data: clientAffiliates, error: clientError } = await supabaseClient
      .from('affiliate_companies')
      .select('id, name, city, status, application_status, owner_id')
      .eq('status', 'active')
      .eq('application_status', 'approved')
      .ilike('city', '%Austin%')

    console.log('CLIENT user results:', {
      success: !clientError,
      count: clientAffiliates?.length || 0,
      error: clientError?.message,
      affiliates: clientAffiliates?.map(a => ({ 
        id: a.id, 
        name: a.name, 
        city: a.city,
        owner_id: a.owner_id 
      }))
    })

    // Test 3: Check RLS policies
    console.log('Test 3: Checking RLS policies...')
    const { data: policies, error: policiesError } = await supabaseAdmin
      .from('pg_policies')
      .select('policyname, cmd, qual')
      .eq('tablename', 'affiliate_companies')

    console.log('RLS policies:', {
      success: !policiesError,
      policies: policies?.map(p => ({
        name: p.policyname,
        command: p.cmd,
        condition: p.qual
      }))
    })

    // Test 4: Check user profile using secure view
    console.log('Test 4: Checking user profile using secure view...')
    const testUserId = '2fee08c5-e012-4db0-842e-ebf4d7997c39'
    const { data: userProfile, error: profileError } = await supabaseAdmin
      .from('user_profiles_secure')
      .select('user_id, roles')
      .eq('user_id', testUserId)
      .maybeSingle()

    console.log('User profile (secure view):', {
      success: !profileError,
      user_id: userProfile?.user_id,
      roles: userProfile?.roles,
      error: profileError?.message
    })

    return NextResponse.json({
      success: true,
      message: 'RLS policy issue verification completed',
      test_results: {
        service_role_query: {
          success: !adminError,
          affiliates_found: adminAffiliates?.length || 0,
          affiliates: adminAffiliates?.map(a => ({ 
            id: a.id, 
            name: a.name, 
            city: a.city,
            owner_id: a.owner_id 
          })) || []
        },
        client_user_query: {
          success: !clientError,
          affiliates_found: clientAffiliates?.length || 0,
          error: clientError?.message,
          affiliates: clientAffiliates?.map(a => ({ 
            id: a.id, 
            name: a.name, 
            city: a.city,
            owner_id: a.owner_id 
          })) || []
        },
        rls_policies: {
          success: !policiesError,
          policies: policies?.map(p => ({
            name: p.policyname,
            command: p.cmd,
            condition: p.qual
          })) || []
        },
        user_profile: {
          success: !profileError,
          user_id: userProfile?.user_id,
          roles: userProfile?.roles,
          is_client: userProfile?.roles?.includes('CLIENT'),
          is_super_admin: userProfile?.roles?.includes('SUPER_ADMIN')
        }
      },
      diagnosis: {
        issue_confirmed: (adminAffiliates?.length || 0) > 0 && (clientAffiliates?.length || 0) === 0,
        root_cause: 'RLS policy on affiliate_companies table prevents CLIENT users from viewing affiliate companies',
        service_role_works: (adminAffiliates?.length || 0) > 0,
        client_role_blocked: (clientAffiliates?.length || 0) === 0,
        user_is_client: userProfile?.roles?.includes('CLIENT'),
        needs_policy_update: true
      },
      solution: {
        problem: 'CLIENT users cannot see affiliate companies due to RLS policy',
        current_policy: 'Only allows owners and SUPER_ADMIN to view companies',
        required_fix: 'Update RLS policy to allow CLIENT users to view active/approved companies',
        sql_fix: `
-- Update the RLS policy to allow CLIENTs to view active/approved affiliate companies
DROP POLICY IF EXISTS final_view_owned_companies ON affiliate_companies;

CREATE POLICY final_view_owned_companies ON affiliate_companies
FOR SELECT USING (
  -- Owners can see their own companies
  (owner_id = auth.uid()) 
  OR 
  -- Super admins can see all companies
  (EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_id = auth.uid() 
    AND 'SUPER_ADMIN' = ANY(roles)
  ))
  OR
  -- Clients can see active and approved companies for quote matching
  (
    status = 'active' 
    AND application_status = 'approved'
    AND EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.uid() 
      AND 'CLIENT' = ANY(roles)
    )
  )
);`
      }
    })

  } catch (error) {
    console.error('Error in RLS policy test:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
