import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs';
import { createClient } from '@supabase/supabase-js'



const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

/**
 * TEST ENDPOINT: Test the complete quote submission flow
 * This simulates what happens when a user submits a quote form
 */
export async function POST(request: NextRequest) {
  try {
    console.log('TEST: Testing complete quote submission flow')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Step 1: Create a quote (simulating form submission)
    const quoteData = {
      reference_number: `Q${Date.now()}`,
      customer_id: '7ab6f229-1250-485b-8a17-1947237b0ca3',
      service_type: 'airport',
      vehicle_type: 'SUV',
      passenger_count: 2, // Using 2 to match available vehicles
      date: '2025-01-02',
      time: '10:00:00',
      pickup_location: 'Austin Airport',
      dropoff_location: 'Downtown Austin',
      city: 'Austin',
      duration: '1 hour',
      distance: '20 miles',
      priority: 'medium',
      contact_name: 'Test Client',
      contact_email: '<EMAIL>',
      contact_phone: '************',
      special_requests: ['Test quote for Austin affiliate matching'],
      status: 'pending'
    }

    console.log('Step 1: Creating quote...')
    const { data: createdQuote, error: quoteError } = await supabase
      .from('quotes')
      .insert(quoteData)
      .select()
      .single()

    if (quoteError) {
      console.error('Error creating quote:', quoteError)
      return NextResponse.json(
        { error: 'Failed to create quote', details: quoteError.message },
        { status: 500 }
      )
    }

    console.log('Quote created successfully:', createdQuote.id)

    // Step 2: Find matching affiliates (simulating the matching API call)
    console.log('Step 2: Finding matching affiliates...')
    
    const matchingResponse = await fetch('http://localhost:3000/api/test/event-manager-matching', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({})
    })

    let matchingResult = null
    if (matchingResponse.ok) {
      matchingResult = await matchingResponse.json()
      console.log(`Found ${matchingResult.affiliates?.length || 0} matching affiliates`)
    } else {
      console.error('Matching API failed:', matchingResponse.status)
      matchingResult = { error: 'Matching API failed' }
    }

    // Step 3: Test the database function directly
    console.log('Step 3: Testing database function...')
    
    const { data: dbFunctionResult, error: dbFunctionError } = await supabase
      .rpc('find_matching_affiliates_for_quote', {
        p_pickup_lat: 30.2672,
        p_pickup_lng: -97.7431,
        p_pickup_city: 'Austin',
        p_vehicle_type: 'SUV',
        p_pickup_date: '2025-01-02',
        p_service_type: 'airport'
      })

    if (dbFunctionError) {
      console.error('Database function error:', dbFunctionError)
    } else {
      console.log(`Database function returned ${dbFunctionResult?.length || 0} results`)
    }

    // Step 4: Simulate quote distribution to affiliates
    console.log('Step 4: Simulating quote distribution...')
    
    const distributionResults = []
    if (matchingResult?.affiliates?.length > 0) {
      for (const affiliate of matchingResult.affiliates) {
        // In real implementation, this would send notifications to affiliates
        distributionResults.push({
          affiliate_company_id: affiliate.id,
          company_name: affiliate.company_name,
          estimated_price: affiliate.total_price,
          status: 'notified',
          notification_sent: true
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Quote submission flow test completed',
      results: {
        step1_quote_creation: {
          success: !!createdQuote,
          quote_id: createdQuote?.id,
          reference_number: createdQuote?.reference_number
        },
        step2_affiliate_matching: {
          success: !!matchingResult?.affiliates,
          affiliates_found: matchingResult?.affiliates?.length || 0,
          affiliates: matchingResult?.affiliates || []
        },
        step3_database_function: {
          success: !dbFunctionError,
          results_count: dbFunctionResult?.length || 0,
          results: dbFunctionResult || []
        },
        step4_distribution: {
          affiliates_notified: distributionResults.length,
          distribution_details: distributionResults
        }
      },
      summary: {
        quote_created: !!createdQuote,
        affiliates_matched: matchingResult?.affiliates?.length || 0,
        database_function_works: !dbFunctionError,
        ready_for_production: !!createdQuote && (matchingResult?.affiliates?.length > 0) && !dbFunctionError
      }
    })

  } catch (error) {
    console.error('Error in quote submission flow test:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
