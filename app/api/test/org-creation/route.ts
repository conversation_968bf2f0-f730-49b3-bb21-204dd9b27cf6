import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    console.log('=== AUTOMATED ORG CREATION TEST START ===');
    
    // Test data
    const testData = {
      name: `Test Org ${Date.now()}`,
      email: `test-${Date.now()}@example.com`,
      industry: 'logistics',
      plan: 'professional',
      admin_user: {
        email: `admin-${Date.now()}@example.com`,
        first_name: 'Test',
        last_name: 'Admin',
        password: 'TestPassword123',
        role: 'CLIENT_COORDINATOR',
        permissions_template: 'full_admin_access',
        send_invitation: false
      }
    };

    console.log('Test data:', JSON.stringify(testData, null, 2));

    // Call the actual organizations API
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3003'}/api/super-admin/organizations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('Cookie') || ''
      },
      body: JSON.stringify(testData)
    });

    const responseText = await response.text();
    console.log('API Response Status:', response.status);
    console.log('API Response Text:', responseText);

    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      responseData = { raw: responseText };
    }

    if (response.ok) {
      console.log('✅ SUCCESS: Organization created successfully');
      console.log('Created organization:', responseData.organization?.id);
      console.log('Created tenant:', responseData.tenant?.id);
      console.log('Created admin user:', responseData.admin_user?.id);
      
      return NextResponse.json({
        success: true,
        message: 'Organization creation test PASSED',
        data: responseData
      });
    } else {
      console.log('❌ FAILED: Organization creation failed');
      console.log('Error details:', responseData);
      
      return NextResponse.json({
        success: false,
        message: 'Organization creation test FAILED',
        error: responseData.error || 'Unknown error',
        details: responseData,
        status: response.status
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ TEST ERROR:', error);
    return NextResponse.json({
      success: false,
      message: 'Test execution failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
