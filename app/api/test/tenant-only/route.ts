import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    console.log('=== TESTING TENANT CREATION ONLY ===');
    
    // Use service role client
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Generate unique slug
    const baseSlug = `test-tenant-${Date.now()}`;
    
    console.log('Creating tenant with slug:', baseSlug);
    
    // Test tenant creation
    const { data: tenant, error: tenantError } = await supabaseAdmin
      .schema('saas_organizations')
      .from('tenants')
      .insert({
        name: `Test Tenant ${Date.now()}`,
        slug: baseSlug,
        organization_type: 'segregated',
        status: 'active',
        settings: {
          industry: 'logistics',
          plan: 'professional',
          created_via: 'test'
        }
      })
      .select()
      .single();

    if (tenantError) {
      console.error('❌ Tenant creation failed:', tenantError);
      return NextResponse.json({
        success: false,
        error: `Tenant creation failed: ${tenantError.message}`,
        details: tenantError
      }, { status: 500 });
    }

    console.log('✅ Tenant created successfully:', tenant.id);
    
    return NextResponse.json({
      success: true,
      message: 'Tenant creation test PASSED',
      tenant: tenant
    });

  } catch (error) {
    console.error('❌ TEST ERROR:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
