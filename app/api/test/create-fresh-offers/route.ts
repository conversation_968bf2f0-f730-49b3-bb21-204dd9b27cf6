import { createServerClient, type CookieOptions } from '@supabase/ssr';

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/test/create-fresh-offers
 * Create fresh test offers with future expiration dates
 */
export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  try {
    const { companyId } = await request.json();
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    // Get existing quotes to create offers for
    const { data: quotes, error: quotesError } = await supabase
      .from('quotes')
      .select('id, reference_number')
      .eq('status', 'pending')
      .limit(3);

    if (quotesError || !quotes || quotes.length === 0) {
      return NextResponse.json({ error: 'No pending quotes found' }, { status: 404 });
    }

    // Create fresh offers with future expiration dates
    const newOffers = [];
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 48); // 48 hours from now

    for (const quote of quotes) {
      const offerData = {
        quote_id: quote.id,
        company_id: companyId,
        rate_amount: 150,
        status: 'PENDING',
        notes: 'Fresh test offer - Priority 1',
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: '00000000-0000-0000-0000-000000000000',
        updated_by: '00000000-0000-0000-0000-000000000000'
      };

      const { data: newOffer, error: offerError } = await supabase
        .from('quote_affiliate_offers')
        .insert(offerData)
        .select()
        .single();

      if (offerError) {
        console.error('Error creating offer:', offerError);
        continue;
      }

      newOffers.push(newOffer);
    }

    return NextResponse.json({
      message: `Created ${newOffers.length} fresh offers`,
      offers: newOffers,
      expiresAt: expiresAt.toISOString()
    });
  } catch (error) {
    console.error('Error in POST /api/test/create-fresh-offers:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
