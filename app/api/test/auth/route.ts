import { NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createAuthenticatedSupabaseClient } from '@/lib/auth/server'
import { cookies } from 'next/headers'

export async function GET() {
  try {
    console.log('TEST AUTH API: Starting simple auth test')
    
    // Get all cookies for debugging
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    console.log('TEST AUTH API: All cookies:', allCookies.map(c => ({ name: c.name, hasValue: !!c.value })))
    
    // Create Supabase client using centralized auth
    const supabase = await createAuthenticatedSupabaseClient()
    console.log('TEST AUTH API: Supabase client created')
    
    // Test getSession
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    console.log('TEST AUTH API: getSession result:', { 
      hasSession: !!session, 
      userId: session?.user?.id,
      email: session?.user?.email,
      error: sessionError 
    })
    
    // Test getUser 
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    console.log('TEST AUTH API: getUser result:', { 
      hasUser: !!user, 
      userId: user?.id,
      email: user?.email,
      error: userError 
    })
    
    return NextResponse.json({
      success: true,
      cookieCount: allCookies.length,
      sessionResult: {
        hasSession: !!session,
        userId: session?.user?.id,
        email: session?.user?.email,
        error: sessionError?.message
      },
      userResult: {
        hasUser: !!user,
        userId: user?.id,
        email: user?.email,
        error: userError?.message
      }
    })
    
  } catch (error) {
    console.error('TEST AUTH API: Unexpected error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}