import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
/**
 * Enterprise Client Registration API
 * Handles new enterprise client applications
 */

interface RegistrationData {
  // Company Information
  company_name: string;
  company_website?: string;
  company_size?: string;
  industry?: string;
  headquarters_location?: string;
  
  // Contact Information
  primary_contact_name: string;
  primary_contact_email: string;
  primary_contact_phone?: string;
  primary_contact_title?: string;
  
  // Technical Information
  technical_contact_name?: string;
  technical_contact_email?: string;
  expected_api_volume: string;
  integration_timeline?: string;
  
  // Use Case Information
  use_case_description: string;
  current_solution?: string;
  integration_requirements: string[];
  
  // Pricing & Billing
  preferred_pricing_tier: string;
  billing_contact_email?: string;
  
  // Legal & Compliance
  data_residency_requirements?: string;
  compliance_requirements: string[];
  terms_accepted: boolean;
  privacy_policy_accepted: boolean;
}

export async function POST(request: NextRequest) {
  try {
    console.log('Enterprise registration API - Processing new application');

    // Parse request body
    const registrationData: RegistrationData = await request.json();

    // Validate required fields
    const requiredFields = [
      'company_name',
      'primary_contact_name',
      'primary_contact_email',
      'expected_api_volume',
      'use_case_description',
      'preferred_pricing_tier',
      'terms_accepted',
      'privacy_policy_accepted'
    ];

    const missingFields = requiredFields.filter(field => {
      const value = registrationData[field as keyof RegistrationData];
      return value === undefined || value === null || value === '' || value === false;
    });

    if (missingFields.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Missing required fields: ${missingFields.join(', ')}` 
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(registrationData.primary_contact_email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Check for duplicate applications
    const { data: existingApplication, error: checkError } = await supabase
      .from('enterprise_applications')
      .select('id, status')
      .eq('primary_contact_email', registrationData.primary_contact_email)
      .eq('company_name', registrationData.company_name)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('Error checking for existing application:', checkError);
      return NextResponse.json(
        { success: false, error: 'Database error during validation' },
        { status: 500 }
      );
    }

    if (existingApplication) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'An application already exists for this company and contact email',
          existing_status: existingApplication.status
        },
        { status: 409 }
      );
    }

    // Generate application reference number
    const applicationRef = `ENT-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    // Insert application into database
    const { data: application, error: insertError } = await supabase
      .from('enterprise_applications')
      .insert({
        reference_number: applicationRef,
        status: 'pending_review',
        
        // Company Information
        company_name: registrationData.company_name,
        company_website: registrationData.company_website,
        company_size: registrationData.company_size,
        industry: registrationData.industry,
        headquarters_location: registrationData.headquarters_location,
        
        // Contact Information
        primary_contact_name: registrationData.primary_contact_name,
        primary_contact_email: registrationData.primary_contact_email,
        primary_contact_phone: registrationData.primary_contact_phone,
        primary_contact_title: registrationData.primary_contact_title,
        
        // Technical Information
        technical_contact_name: registrationData.technical_contact_name,
        technical_contact_email: registrationData.technical_contact_email,
        expected_api_volume: registrationData.expected_api_volume,
        integration_timeline: registrationData.integration_timeline,
        
        // Use Case Information
        use_case_description: registrationData.use_case_description,
        current_solution: registrationData.current_solution,
        integration_requirements: registrationData.integration_requirements,
        
        // Pricing & Billing
        preferred_pricing_tier: registrationData.preferred_pricing_tier,
        billing_contact_email: registrationData.billing_contact_email,
        
        // Legal & Compliance
        data_residency_requirements: registrationData.data_residency_requirements,
        compliance_requirements: registrationData.compliance_requirements,
        
        // Metadata
        application_source: 'web_form',
        submitted_at: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting application:', insertError);
      return NextResponse.json(
        { success: false, error: 'Failed to submit application' },
        { status: 500 }
      );
    }

    console.log('Enterprise application submitted successfully:', applicationRef);

    // Send notification emails (in background)
    try {
      await Promise.all([
        sendApplicationConfirmationEmail(registrationData),
        sendInternalNotificationEmail(application)
      ]);
    } catch (emailError) {
      console.error('Error sending notification emails:', emailError);
      // Don't fail the request if email fails
    }

    // Return success response
    return NextResponse.json({
      success: true,
      data: {
        application_id: application.id,
        reference_number: applicationRef,
        status: 'pending_review',
        estimated_review_time: '24 hours',
        next_steps: [
          'Application review by our team',
          'Technical consultation call',
          'Custom pricing proposal',
          'API key provisioning',
          'Integration support'
        ]
      }
    });

  } catch (error) {
    console.error('Enterprise registration API error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

/**
 * Send confirmation email to applicant
 */
async function sendApplicationConfirmationEmail(data: RegistrationData) {
  // Implementation would depend on your email service
  // This is a placeholder for the email sending logic
  console.log('Sending confirmation email to:', data.primary_contact_email);
  
  // Example email content:
  const emailContent = {
    to: data.primary_contact_email,
    subject: 'TransFlow Enterprise API Application Received',
    template: 'enterprise_application_confirmation',
    data: {
      company_name: data.company_name,
      contact_name: data.primary_contact_name,
      next_steps: [
        'Application review (24 hours)',
        'Technical consultation call',
        'Custom pricing proposal',
        'API key provisioning'
      ]
    }
  };

  // Send email using your preferred service (SendGrid, AWS SES, etc.)
  // await emailService.send(emailContent);
}

/**
 * Send internal notification to sales team
 */
async function sendInternalNotificationEmail(application: any) {
  console.log('Sending internal notification for application:', application.reference_number);
  
  // Example internal notification:
  const emailContent = {
    to: process.env.ENTERPRISE_SALES_EMAIL || '<EMAIL>',
    subject: `New Enterprise API Application: ${application.company_name}`,
    template: 'enterprise_application_internal',
    data: {
      application_id: application.id,
      reference_number: application.reference_number,
      company_name: application.company_name,
      contact_name: application.primary_contact_name,
      contact_email: application.primary_contact_email,
      expected_volume: application.expected_api_volume,
      preferred_tier: application.preferred_pricing_tier,
      use_case: application.use_case_description,
      admin_url: `${process.env.NEXT_PUBLIC_APP_URL}/super-admin/enterprise/applications/${application.id}`
    }
  };

  // Send email using your preferred service
  // await emailService.send(emailContent);
}

/**
 * GET endpoint to retrieve application status
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const email = url.searchParams.get('email');
    const ref = url.searchParams.get('ref');

    if (!email && !ref) {
      return NextResponse.json(
        { success: false, error: 'Email or reference number required' },
        { status: 400 }
      );
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    let query = supabase
      .from('enterprise_applications')
      .select('reference_number, status, company_name, submitted_at, reviewed_at');

    if (ref) {
      query = query.eq('reference_number', ref);
    } else {
      query = query.eq('primary_contact_email', email);
    }

    const { data: applications, error } = await query;

    if (error) {
      console.error('Error fetching application status:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch application status' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: applications || []
    });

  } catch (error) {
    console.error('Application status API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
