import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
/**
 * Enterprise Client Analytics API
 * GET: Retrieve usage analytics for a client
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { clientId: string } }
) {
  try {
    // Create Supabase client with cookies for authentication
    const cookieStore = cookies()
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const range = url.searchParams.get('range') || '7d';

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (range) {
      case '1d':
        startDate.setDate(endDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    // Use service role for admin operations
    const adminSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Verify client exists and user has access
    const { data: client, error: clientError } = await adminSupabase
      .from('enterprise_clients')
      .select('id, name, rate_limit')
      .eq('id', params.clientId)
      .single();

    if (clientError || !client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }

    // Get overall usage statistics
    const { data: overallStats } = await adminSupabase.rpc('get_api_usage_stats', {
      client_id_param: params.clientId,
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString()
    });

    // Get today's requests
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    
    const { data: todayRequests, count: todayCount } = await supabase
      .from('api_usage_logs')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', params.clientId)
      .gte('timestamp', todayStart.toISOString());

    // Get rate limit violations today
    const { data: rateLimitViolations, count: violationCount } = await supabase
      .from('api_usage_logs')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', params.clientId)
      .eq('status_code', 429)
      .gte('timestamp', todayStart.toISOString());

    // Get unique IPs
    const { data: uniqueIPs } = await supabase
      .from('api_usage_logs')
      .select('ip_address')
      .eq('client_id', params.clientId)
      .gte('timestamp', startDate.toISOString())
      .lte('timestamp', endDate.toISOString());

    const uniqueIPCount = new Set(uniqueIPs?.map(log => log.ip_address).filter(Boolean)).size;

    // Get time series data
    const { data: timeSeriesData } = await supabase
      .from('api_usage_logs')
      .select(`
        timestamp,
        status_code,
        response_time_ms
      `)
      .eq('client_id', params.clientId)
      .gte('timestamp', startDate.toISOString())
      .lte('timestamp', endDate.toISOString())
      .order('timestamp');

    // Process time series data
    const timeSeriesMap = new Map();
    timeSeriesData?.forEach(log => {
      const date = new Date(log.timestamp).toISOString().split('T')[0];
      if (!timeSeriesMap.has(date)) {
        timeSeriesMap.set(date, {
          date,
          requests: 0,
          errors: 0,
          response_times: []
        });
      }
      const dayData = timeSeriesMap.get(date);
      dayData.requests++;
      if (log.status_code >= 400) {
        dayData.errors++;
      }
      if (log.response_time_ms) {
        dayData.response_times.push(log.response_time_ms);
      }
    });

    const timeSeries = Array.from(timeSeriesMap.values()).map(day => ({
      date: day.date,
      requests: day.requests,
      errors: day.errors,
      avg_response_time: day.response_times.length > 0 
        ? day.response_times.reduce((a, b) => a + b, 0) / day.response_times.length 
        : 0
    }));

    // Get endpoint statistics
    const { data: endpointData } = await supabase
      .from('api_usage_logs')
      .select(`
        endpoint,
        status_code,
        response_time_ms
      `)
      .eq('client_id', params.clientId)
      .gte('timestamp', startDate.toISOString())
      .lte('timestamp', endDate.toISOString());

    const endpointMap = new Map();
    endpointData?.forEach(log => {
      if (!endpointMap.has(log.endpoint)) {
        endpointMap.set(log.endpoint, {
          endpoint: log.endpoint,
          requests: 0,
          successful_requests: 0,
          response_times: []
        });
      }
      const endpointStats = endpointMap.get(log.endpoint);
      endpointStats.requests++;
      if (log.status_code < 400) {
        endpointStats.successful_requests++;
      }
      if (log.response_time_ms) {
        endpointStats.response_times.push(log.response_time_ms);
      }
    });

    const endpoints = Array.from(endpointMap.values())
      .map(endpoint => ({
        endpoint: endpoint.endpoint,
        requests: endpoint.requests,
        success_rate: endpoint.requests > 0 ? (endpoint.successful_requests / endpoint.requests) * 100 : 100,
        avg_response_time: endpoint.response_times.length > 0 
          ? endpoint.response_times.reduce((a, b) => a + b, 0) / endpoint.response_times.length 
          : 0
      }))
      .sort((a, b) => b.requests - a.requests);

    // Get status code distribution
    const statusCodeMap = new Map();
    timeSeriesData?.forEach(log => {
      const code = log.status_code;
      statusCodeMap.set(code, (statusCodeMap.get(code) || 0) + 1);
    });

    const totalRequests = timeSeriesData?.length || 0;
    const statusCodes = Array.from(statusCodeMap.entries())
      .map(([code, count]) => ({
        code,
        count,
        percentage: totalRequests > 0 ? (count / totalRequests) * 100 : 0
      }))
      .sort((a, b) => b.count - a.count);

    // Get geographic distribution (mock data for now)
    const geographic = [
      { country: 'United States', requests: Math.floor(totalRequests * 0.6), percentage: 60 },
      { country: 'Canada', requests: Math.floor(totalRequests * 0.2), percentage: 20 },
      { country: 'United Kingdom', requests: Math.floor(totalRequests * 0.1), percentage: 10 },
      { country: 'Germany', requests: Math.floor(totalRequests * 0.05), percentage: 5 },
      { country: 'France', requests: Math.floor(totalRequests * 0.05), percentage: 5 }
    ].filter(geo => geo.requests > 0);

    // Get current rate limit usage (last hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const { data: recentRequests, count: recentCount } = await supabase
      .from('api_usage_logs')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', params.clientId)
      .gte('timestamp', oneHourAgo.toISOString());

    const stats = {
      overview: {
        total_requests: overallStats?.[0]?.total_requests || 0,
        successful_requests: overallStats?.[0]?.successful_requests || 0,
        error_requests: overallStats?.[0]?.error_requests || 0,
        avg_response_time: overallStats?.[0]?.avg_response_time || 0,
        success_rate: overallStats?.[0]?.total_requests > 0 
          ? ((overallStats[0].successful_requests || 0) / overallStats[0].total_requests) * 100 
          : 100,
        requests_today: todayCount || 0,
        rate_limit_hits: violationCount || 0,
        unique_ips: uniqueIPCount
      },
      time_series: timeSeries,
      endpoints,
      status_codes: statusCodes,
      geographic,
      rate_limiting: {
        current_usage: recentCount || 0,
        limit: client.rate_limit,
        reset_time: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
        violations_today: violationCount || 0
      }
    };

    return NextResponse.json({
      success: true,
      stats,
      meta: {
        client_id: params.clientId,
        client_name: client.name,
        date_range: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
          range
        },
        generated_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
