import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs'
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
/**
 * API Key Management Endpoints
 * GET: List API keys for a client
 * POST: Create new API key
 */

interface CreateAPIKeyRequest {
  name: string;
  rate_limit: number;
  webhook_url?: string;
  allowed_ips?: string[];
}

export async function GET(
  request: NextRequest,
  { params }: { params: { clientId: string } }
) {
  try {
    // Create Supabase client with cookies for authentication
    const cookieStore = cookies()
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Use service role for admin operations
    const adminSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Verify user has access to this client
    const { data: client, error: clientError } = await adminSupabase
      .from('enterprise_clients')
      .select('id, name, organization_id')
      .eq('id', params.clientId)
      .single();

    if (clientError || !client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }

    // Get API keys for this client
    const { data: apiKeys, error: keysError } = await supabase
      .from('enterprise_clients')
      .select(`
        id,
        name,
        api_key,
        status,
        rate_limit,
        webhook_url,
        webhook_secret,
        allowed_ips,
        created_at,
        updated_at
      `)
      .eq('id', params.clientId);

    if (keysError) {
      console.error('Error fetching API keys:', keysError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch API keys' },
        { status: 500 }
      );
    }

    // Get usage statistics for each key
    const keysWithStats = await Promise.all(
      (apiKeys || []).map(async (key) => {
        try {
          const { data: stats } = await adminSupabase.rpc('get_api_usage_stats', {
            client_id_param: key.id,
            start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
            end_date: new Date().toISOString()
          });

          const { data: todayStats } = await supabase
            .from('api_usage_logs')
            .select('*', { count: 'exact', head: true })
            .eq('client_id', key.id)
            .gte('timestamp', new Date().toISOString().split('T')[0] + 'T00:00:00Z');

          return {
            ...key,
            usage_stats: {
              total_requests: stats?.[0]?.total_requests || 0,
              requests_today: todayStats?.count || 0,
              success_rate: stats?.[0]?.successful_requests && stats?.[0]?.total_requests
                ? (stats[0].successful_requests / stats[0].total_requests) * 100
                : 100
            }
          };
        } catch (error) {
          console.error('Error fetching usage stats for key:', key.id, error);
          return {
            ...key,
            usage_stats: {
              total_requests: 0,
              requests_today: 0,
              success_rate: 100
            }
          };
        }
      })
    );

    return NextResponse.json({
      success: true,
      api_keys: keysWithStats
    });

  } catch (error) {
    console.error('API keys GET error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { clientId: string } }
) {
  try {
    // Create Supabase client with cookies for authentication
    const cookieStore = cookies()
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body: CreateAPIKeyRequest = await request.json();

    // Validate required fields
    if (!body.name || !body.rate_limit) {
      return NextResponse.json(
        { success: false, error: 'Name and rate limit are required' },
        { status: 400 }
      );
    }

    // Validate rate limit
    if (body.rate_limit < 1 || body.rate_limit > 10000) {
      return NextResponse.json(
        { success: false, error: 'Rate limit must be between 1 and 10,000' },
        { status: 400 }
      );
    }

    // Use service role for admin operations
    const adminSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Verify client exists
    const { data: client, error: clientError } = await adminSupabase
      .from('enterprise_clients')
      .select('id, name, organization_id')
      .eq('id', params.clientId)
      .single();

    if (clientError || !client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }

    // Generate API key (simple implementation)
    const apiKey = `tf_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`

    // Generate webhook secret if webhook URL is provided
    let webhookSecret = null;
    if (body.webhook_url) {
      webhookSecret = `wh_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`
    }

    // Update the existing client record with new API key
    const { data: newKey, error: createError } = await adminSupabase
      .from('enterprise_clients')
      .update({
        api_key: apiKey,
        rate_limit: body.rate_limit,
        webhook_url: body.webhook_url || null,
        webhook_secret: webhookSecret,
        allowed_ips: body.allowed_ips || null,
        status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('id', params.clientId)
      .select()
      .single();

    if (createError) {
      console.error('Error creating API key:', createError);
      return NextResponse.json(
        { success: false, error: 'Failed to create API key' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      api_key: {
        id: newKey.id,
        name: newKey.name,
        api_key: newKey.api_key,
        status: newKey.status,
        rate_limit: newKey.rate_limit,
        webhook_url: newKey.webhook_url,
        allowed_ips: newKey.allowed_ips,
        created_at: newKey.created_at
      }
    });

  } catch (error) {
    console.error('API keys POST error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
