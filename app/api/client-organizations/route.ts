import { NextRequest, NextResponse } from "next/server";
export const dynamic = 'force-dynamic';
import { createClient } from '@supabase/supabase-js';

export const runtime = 'nodejs';

// CLIENT ORGANIZATIONS API - Returns only CLIENT ORGS for OPS filtering
export async function GET(request: NextRequest) {
  try {
    console.log("Client Organizations API - Starting request");
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!, 
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get only CLIENT ORGANIZATIONS (segregated and white_label)
    // NOT the main network (shared) - that belongs in Network Switcher
    const { data: clientOrgs, error: clientOrgsError } = await supabase
      .from('organizations')
      .select(`
        id,
        name,
        slug,
        domain,
        organization_type,
        subscription_plan,
        permission_template,
        status,
        industry,
        business_type
      `)
      .in('organization_type', ['segregated', 'white_label']) // ONLY client orgs
      .eq('status', 'active')
      .order('name');

    if (clientOrgsError) {
      console.error('Error fetching client organizations:', clientOrgsError);
      return NextResponse.json(
        { error: 'Failed to fetch client organizations' },
        { status: 500 }
      );
    }

    // Transform for OPS dropdown
    const formattedClientOrgs = clientOrgs?.map((org) => ({
      id: org.id,
      name: org.name,
      slug: org.slug,
      domain: org.domain,
      organization_type: org.organization_type,
      subscription_plan: org.subscription_plan || 'basic',
      status: org.status,
      industry: org.industry,
      business_type: org.business_type,
      is_client_org: true, // Flag to identify as client organization
    })) || [];

    console.log(`Found ${formattedClientOrgs.length} client organizations for OPS filtering`);
    
    return NextResponse.json({
      success: true,
      organizations: formattedClientOrgs,
      total: formattedClientOrgs.length
    });

  } catch (error) {
    console.error('Error in GET /api/client-organizations:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}