import { create<PERSON>out<PERSON><PERSON><PERSON><PERSON><PERSON>lient } from "@supabase/auth-helpers-nextjs";

export const runtime = 'nodejs'
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { Database } from '@/lib/types/supabase'

/*
<PERSON><PERSON> to create the necessary tables (to be run by an admin):

CREATE TABLE IF NOT EXISTS billing_addresses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  street TEXT NOT NULL,
  city TEXT NOT NULL,
  state TEXT NOT NULL,
  zip_code TEXT NOT NULL,
  country TEXT NOT NULL,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS payment_methods (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  last4 TEXT NOT NULL,
  exp_month TEXT NOT NULL,
  exp_year TEXT NOT NULL,
  is_default BOOLEAN DEFAULT false,
  billing_address_id UUID REFERENCES billing_addresses(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
*/

// Fallback mock data in case the tables don't exist yet
const mockPaymentMethods = [
  {
    id: 'payment-1',
    type: 'visa',
    last4: '4242',
    exp_month: '04',
    exp_year: '25',
    is_default: true,
    billing_address_id: 'address-1',
    billing_addresses: {
      id: 'address-1',
      street: '123 Main St',
      city: 'San Francisco',
      state: 'CA',
      zip_code: '94105',
      country: 'US'
    }
  }
]

export async function GET(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    // Try to fetch payment methods from the database
    const { data: paymentMethods, error } = await supabase
      .from('payment_methods')
      .select(`
        *,
        billing_addresses (*)
      `)
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Error fetching payment methods:', error)
      // If there's an error (like table doesn't exist), return mock data
      return NextResponse.json({ paymentMethods: mockPaymentMethods }, { status: 200 })
    }
    
    return NextResponse.json({ paymentMethods: paymentMethods || [] }, { status: 200 })
  } catch (error: any) {
    console.error('Error fetching payment methods:', error)
    return NextResponse.json(
      { message: error.message },
      { status: 500 }
    )
  }
}

export async function POST(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    
    // Validate required fields
    const requiredFields = ['type', 'last4', 'exp_month', 'exp_year', 'billing_address_id']
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json({ message: `Missing required field: ${field}` }, { status: 400 })
      }
    }
    
    // If this is the first payment method or is_default is true, set it as default
    let isDefault = body.is_default || false
    
    if (isDefault) {
      // If this payment method is set as default, unset any existing default
      await supabase
        .from('payment_methods')
        .update({ is_default: false })
        .eq('user_id', session.user.id)
        .eq('is_default', true)
    } else {
      // Check if this is the first payment method
      const { count, error: countError } = await supabase
        .from('payment_methods')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', session.user.id)
      
      if (!countError && count === 0) {
        isDefault = true
      }
    }
    
    // Insert the payment method
    const { data: paymentMethod, error } = await supabase
      .from('payment_methods')
      .insert({
        user_id: session.user.id,
        type: body.type,
        last4: body.last4,
        exp_month: body.exp_month,
        exp_year: body.exp_year,
        is_default: isDefault,
        billing_address_id: body.billing_address_id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select(`
        *,
        billing_addresses (*)
      `)
      .single()
    
    if (error) {
      console.error('Error creating payment method:', error)
      return NextResponse.json({ message: error.message }, { status: 500 })
    }
    
    return NextResponse.json({ paymentMethod }, { status: 201 })
  } catch (error: any) {
    console.error('Error creating payment method:', error)
    return NextResponse.json(
      { message: error.message },
      { status: 500 }
    )
  }
}

export async function PATCH(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    
    if (!body.id) {
      return NextResponse.json({ message: "Payment method ID is required" }, { status: 400 })
    }
    
    // If setting as default, unset any existing default
    if (body.is_default) {
      await supabase
        .from('payment_methods')
        .update({ is_default: false })
        .eq('user_id', session.user.id)
        .eq('is_default', true)
        .neq('id', body.id)
    }
    
    // Update the payment method
    const { data: paymentMethod, error } = await supabase
      .from('payment_methods')
      .update({
        ...body,
        updated_at: new Date().toISOString()
      })
      .eq('id', body.id)
      .eq('user_id', session.user.id)
      .select(`
        *,
        billing_addresses (*)
      `)
      .single()
    
    if (error) {
      console.error('Error updating payment method:', error)
      return NextResponse.json({ message: error.message }, { status: 500 })
    }
    
    return NextResponse.json({ paymentMethod }, { status: 200 })
  } catch (error: any) {
    console.error('Error updating payment method:', error)
    return NextResponse.json(
      { message: error.message },
      { status: 500 }
    )
  }
}

export async function DELETE(req: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const url = new URL(req.url)
    const id = url.searchParams.get('id')
    
    if (!id) {
      return NextResponse.json({ message: "Payment method ID is required" }, { status: 400 })
    }
    
    // Check if this is the default payment method
    const { data: paymentMethod, error: fetchError } = await supabase
      .from('payment_methods')
      .select('is_default')
      .eq('id', id)
      .eq('user_id', session.user.id)
      .single()
    
    if (fetchError) {
      console.error('Error fetching payment method:', fetchError)
      return NextResponse.json({ message: fetchError.message }, { status: 500 })
    }
    
    // Delete the payment method
    const { error: deleteError } = await supabase
      .from('payment_methods')
      .delete()
      .eq('id', id)
      .eq('user_id', session.user.id)
    
    if (deleteError) {
      console.error('Error deleting payment method:', deleteError)
      return NextResponse.json({ message: deleteError.message }, { status: 500 })
    }
    
    // If this was the default payment method, set another one as default
    if (paymentMethod?.is_default) {
      const { data: otherPaymentMethods, error: listError } = await supabase
        .from('payment_methods')
        .select('id')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false })
        .limit(1)
      
      if (!listError && otherPaymentMethods && otherPaymentMethods.length > 0) {
        await supabase
          .from('payment_methods')
          .update({ is_default: true })
          .eq('id', otherPaymentMethods[0].id)
      }
    }
    
    return NextResponse.json({ success: true }, { status: 200 })
  } catch (error: any) {
    console.error('Error deleting payment method:', error)
    return NextResponse.json(
      { message: error.message },
      { status: 500 }
    )
  }
} 