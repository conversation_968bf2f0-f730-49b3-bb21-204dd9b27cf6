import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'nodejs'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';
export async function POST(
  request: NextRequest,
  { params }: { params: { methodId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const methodId = params.methodId
    const body = await request.json()

    const {
      affiliate_company_id,
      quote_id,
      access_level,
      max_amount,
      expires_in_hours,
      notes
    } = body

    // Validate required fields
    if (!affiliate_company_id || !access_level) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Verify payment method exists and user has access
    const { data: paymentMethod, error: methodError } = await supabase
      .from('payment_methods')
      .select('id, name, client_id')
      .eq('id', methodId)
      .eq('created_by', user.id)
      .single()

    if (methodError || !paymentMethod) {
      return NextResponse.json({ error: 'Payment method not found or access denied' }, { status: 404 })
    }

    // Get affiliate information
    const { data: affiliate, error: affiliateError } = await supabase
      .from('affiliates')
      .select('id, company_name')
      .eq('id', affiliate_company_id)
      .single()

    if (affiliateError || !affiliate) {
      return NextResponse.json({ error: 'Affiliate not found' }, { status: 404 })
    }

    // Calculate expiry date
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + (parseInt(expires_in_hours) || 24))

    // Create payment share
    const { data: share, error: shareError } = await supabase
      .from('payment_shares')
      .insert({
        payment_method_id: methodId,
        affiliate_company_id,
        affiliate_name: affiliate.company_name,
        quote_id: quote_id || null,
        access_level,
        max_amount: max_amount ? parseFloat(max_amount) : null,
        expires_at: expiresAt.toISOString(),
        notes,
        is_active: true,
        created_by: user.id,
        created_at: new Date().toISOString()
      })
      .select(`
        id,
        payment_method_id,
        affiliate_company_id,
        affiliate_name,
        quote_id,
        access_level,
        max_amount,
        expires_at,
        notes,
        is_active,
        created_at
      `)
      .single()

    if (shareError) {
      console.error('Error creating payment share:', shareError)
      return NextResponse.json({ error: 'Failed to create payment share' }, { status: 500 })
    }

    // TODO: Send notification to affiliate about payment access
    // This could be an email, SMS, or in-app notification

    return NextResponse.json({ share })
  } catch (error) {
    console.error('Payment share API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
