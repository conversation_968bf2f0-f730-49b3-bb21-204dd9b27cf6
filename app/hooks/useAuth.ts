"use client"

import { useState, useEffect, useCallback, useMemo } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { User } from '@supabase/supabase-js';
import { 
  UserRole, 
  Permission, 
  hasRole, 
  hasAnyRole, 
  hasAllRoles, 
  hasPermission, 
  hasAnyPermission, 
  hasAllPermissions,
  getUserPermissions,
  isAdmin,
  isSuperAdmin,
  isClientCoordinator,
  isClient,
  isAffiliate,
  getHighestRole,
  canAccessPortal,
  getAllowedPortals,
  validateRoles
} from '@/app/lib/auth/roles';

interface Profile {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  roles: string[];
  organization_id?: string;
  created_at: string;
  updated_at: string;
}

interface AuthState {
  user: User | null;
  profile: Profile | null;
  roles: UserRole[];
  permissions: Permission[];
  isLoading: boolean;
  isAuthenticated: boolean;
}

/**
 * Comprehensive authentication hook with role and permission checking
 */
export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    profile: null,
    roles: [],
    permissions: [],
    isLoading: true,
    isAuthenticated: false,
  });

  const supabase = useMemo(() => createClientComponentClient(), []);

  // Clear corrupted cookies on client side
  const clearCorruptedCookies = useCallback(() => {
    try {
      // Get all cookies
      const cookies = document.cookie.split(';');

      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');

        // Check for Supabase auth cookies
        if (name && name.match(/^sb-.*-auth-token$/)) {
          try {
            // Try to parse the cookie value
            if (value && value.startsWith('base64-')) {
              const base64Part = value.substring(7);
              const decoded = atob(base64Part);
              JSON.parse(decoded);
            }
          } catch (error) {
            console.warn(`Clearing corrupted cookie: ${name}`, error);
            // Clear the corrupted cookie
            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
          }
        }
      }
    } catch (error) {
      console.warn('Error checking cookies:', error);
    }
  }, []);

  // Clear corrupted cookies on mount
  useEffect(() => {
    clearCorruptedCookies();
  }, [clearCorruptedCookies]);

  const fetchProfile = useCallback(async (userId: string) => {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle();

      if (error) {
        console.error('Error fetching profile:', error);
        return null;
      }

      return profile;
    } catch (error) {
      console.error('Error in fetchProfile:', error);
      return null;
    }
  }, [supabase]);

  const updateAuthState = useCallback(async (user: User | null) => {
    if (!user) {
      setAuthState({
        user: null,
        profile: null,
        roles: [],
        permissions: [],
        isLoading: false,
        isAuthenticated: false,
      });
      return;
    }

    const profile = await fetchProfile(user.id);
    
    if (profile) {
      const validatedRoles = validateRoles(profile.roles || []);
      const permissions = getUserPermissions(validatedRoles);

      setAuthState({
        user,
        profile,
        roles: validatedRoles,
        permissions,
        isLoading: false,
        isAuthenticated: true,
      });
    } else {
      setAuthState({
        user,
        profile: null,
        roles: [],
        permissions: [],
        isLoading: false,
        isAuthenticated: true,
      });
    }
  }, [fetchProfile]);

  useEffect(() => {
    // Get initial session with error handling
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          console.warn('Error getting initial session:', error);
          // Clear potentially corrupted session
          await supabase.auth.signOut();
          await updateAuthState(null);
          return;
        }
        await updateAuthState(session?.user || null);
      } catch (error) {
        console.error('Failed to get initial session:', error);
        // Clear potentially corrupted session
        try {
          await supabase.auth.signOut();
        } catch (signOutError) {
          console.warn('Failed to sign out after session error:', signOutError);
        }
        await updateAuthState(null);
      }
    };

    getInitialSession();

    // Listen for auth changes with error handling
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        try {
          await updateAuthState(session?.user || null);
        } catch (error) {
          console.error('Error in auth state change:', error);
          await updateAuthState(null);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [supabase.auth, updateAuthState]);

  // Role checking methods
  const checkRole = useCallback((requiredRole: UserRole): boolean => {
    return hasRole(authState.roles, requiredRole);
  }, [authState.roles]);

  const checkAnyRole = useCallback((requiredRoles: UserRole[]): boolean => {
    return hasAnyRole(authState.roles, requiredRoles);
  }, [authState.roles]);

  const checkAllRoles = useCallback((requiredRoles: UserRole[]): boolean => {
    return hasAllRoles(authState.roles, requiredRoles);
  }, [authState.roles]);

  // Permission checking methods
  const checkPermission = useCallback((permission: Permission): boolean => {
    return hasPermission(authState.roles, permission);
  }, [authState.roles]);

  const checkAnyPermission = useCallback((permissions: Permission[]): boolean => {
    return hasAnyPermission(authState.roles, permissions);
  }, [authState.roles]);

  const checkAllPermissions = useCallback((permissions: Permission[]): boolean => {
    return hasAllPermissions(authState.roles, permissions);
  }, [authState.roles]);

  // Convenience methods
  const checkIsAdmin = useCallback((): boolean => {
    return isAdmin(authState.roles);
  }, [authState.roles]);

  const checkIsSuperAdmin = useCallback((): boolean => {
    return isSuperAdmin(authState.roles);
  }, [authState.roles]);

  const checkIsClientCoordinator = useCallback((): boolean => {
    return isClientCoordinator(authState.roles);
  }, [authState.roles]);

  const checkIsClient = useCallback((): boolean => {
    return isClient(authState.roles);
  }, [authState.roles]);

  const checkIsAffiliate = useCallback((): boolean => {
    return isAffiliate(authState.roles);
  }, [authState.roles]);

  const checkCanAccessPortal = useCallback((portal: 'super-admin' | 'tnc-admin' | 'client' | 'affiliate'): boolean => {
    return canAccessPortal(authState.roles, portal);
  }, [authState.roles]);

  const getPortals = useCallback((): string[] => {
    return getAllowedPortals(authState.roles);
  }, [authState.roles]);

  const getHighestUserRole = useCallback((): UserRole | null => {
    return getHighestRole(authState.roles);
  }, [authState.roles]);

  // Refresh profile data
  const refreshProfile = useCallback(async () => {
    if (authState.user) {
      await updateAuthState(authState.user);
    }
  }, [authState.user, updateAuthState]);

  // Sign out
  const signOut = useCallback(async () => {
    await supabase.auth.signOut();
  }, [supabase.auth]);

  return {
    // Auth state
    ...authState,
    
    // Role checking
    hasRole: checkRole,
    hasAnyRole: checkAnyRole,
    hasAllRoles: checkAllRoles,
    
    // Permission checking
    hasPermission: checkPermission,
    hasAnyPermission: checkAnyPermission,
    hasAllPermissions: checkAllPermissions,
    
    // Convenience methods
    isAdmin: checkIsAdmin,
    isSuperAdmin: checkIsSuperAdmin,
    isClientCoordinator: checkIsClientCoordinator,
    isClient: checkIsClient,
    isAffiliate: checkIsAffiliate,
    
    // Portal access
    canAccessPortal: checkCanAccessPortal,
    allowedPortals: getPortals(),
    
    // Utility
    highestRole: getHighestUserRole(),
    
    // Actions
    refreshProfile,
    signOut,
  };
}

/**
 * Hook for role-based conditional rendering
 */
export function useRoleGuard(requiredRoles: UserRole | UserRole[], options?: {
  requireAll?: boolean;
  fallback?: React.ReactNode;
}) {
  const { roles } = useAuth();
  const rolesArray = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  
  const hasAccess = options?.requireAll 
    ? hasAllRoles(roles, rolesArray)
    : hasAnyRole(roles, rolesArray);

  return {
    hasAccess,
    roles,
    fallback: options?.fallback || null,
  };
}

/**
 * Hook for permission-based conditional rendering
 */
export function usePermissionGuard(requiredPermissions: Permission | Permission[], options?: {
  requireAll?: boolean;
  fallback?: React.ReactNode;
}) {
  const { roles } = useAuth();
  const permissionsArray = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
  
  const hasAccess = options?.requireAll 
    ? hasAllPermissions(roles, permissionsArray)
    : hasAnyPermission(roles, permissionsArray);

  return {
    hasAccess,
    permissions: getUserPermissions(roles),
    fallback: options?.fallback || null,
  };
}

/**
 * Hook for portal access checking
 */
export function usePortalGuard(portal: 'super-admin' | 'tnc-admin' | 'client' | 'affiliate') {
  const { roles, isLoading } = useAuth();
  
  const hasAccess = canAccessPortal(roles, portal);

  return {
    hasAccess,
    isLoading,
    roles,
  };
}
