import { Inter } from 'next/font/google'
import { Toaster } from "@/app/components/ui/toaster"
import Link from 'next/link'

const inter = Inter({ subsets: ['latin'] })

export default function PublicLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className={inter.className}>
      {/* Simple header for public pages */}
      <header className="border-b bg-white">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">TF</span>
              </div>
              <span className="text-xl font-bold">TransFlow</span>
            </Link>
            
            <nav className="flex items-center space-x-6">
              <Link href="/enterprise/register" className="text-sm font-medium hover:text-blue-600">
                Enterprise API
              </Link>
              <Link href="/login" className="text-sm font-medium hover:text-blue-600">
                Login
              </Link>
              <Link href="/register" className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                Get Started
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="min-h-screen">
        {children}
      </main>

      {/* Simple footer */}
      <footer className="border-t bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-sm text-gray-600">
            <p>&copy; 2024 TransFlow. All rights reserved.</p>
          </div>
        </div>
      </footer>

      <Toaster />
    </div>
  )
}
