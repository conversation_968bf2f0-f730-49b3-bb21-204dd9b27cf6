import { createServerClient } from '@supabase/ssr'
import { cookies } from "next/headers"
import { NextResponse } from "next/server"

export async function GET(request: Request) {
  console.log('\n=== Auth Callback Start ===')
  const requestUrl = new URL(request.url)
  console.log('Request URL:', request.url)
  
  const code = requestUrl.searchParams.get("code")
  const error = requestUrl.searchParams.get("error")
  const errorDescription = requestUrl.searchParams.get("error_description")
  
  console.log('URL Parameters:', { code: !!code, error, errorDescription })

  // If there's an error in the URL, redirect to login with the error
  if (error) {
    console.log('Error found in URL, redirecting to login with error')
    const searchParams = new URLSearchParams()
    searchParams.set("error", error)
    searchParams.set("error_description", errorDescription || "Authentication failed")
    return NextResponse.redirect(new URL(`/login?${searchParams.toString()}`, requestUrl.origin))
  }

  if (code) {
    console.log('Auth code found, exchanging for session')
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: Record<string, unknown>) {
            cookieStore.set(name, value, options)
          },
          remove(name: string, options: Record<string, unknown>) {
            cookieStore.set(name, '', options)
          },
        },
      }
    )
    
    try {
      // Exchange the code for a session
      const { data: { session }, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code)
      console.log('Session exchange result:', { session: !!session, error: !!exchangeError })
      
      if (exchangeError) {
        console.error("Session exchange error:", exchangeError)
        const searchParams = new URLSearchParams()
        searchParams.set("error", "auth_error")
        searchParams.set("error_description", exchangeError.message)
        return NextResponse.redirect(new URL(`/login?${searchParams.toString()}`, requestUrl.origin))
      }

      // Redirect based on user role
      if (session) {
        console.log('Getting user profile for role-based redirect');
        // Get user profile to determine roles
        const { data: _profile, error: profileError } = await supabase
          .from('profiles')
          .select('roles')
          .eq('id', session.user.id)
          .single();

        if (profileError) {
          console.error('Error fetching profile:', profileError);
          if (profileError.code === 'PGRST116') {
            // Get the roles array from app_metadata (fallback to ['CLIENT'])
            const appMetaRoles = session.user.app_metadata?.roles as string[] | undefined;
            const userRolesToCreate = appMetaRoles && appMetaRoles.length > 0 ? appMetaRoles : ['CLIENT'];
            // For the singular 'role' column, we can take the first role from the array.
            const primaryRole = userRolesToCreate[0];

            console.log('Creating new profile. Primary role:', primaryRole, 'All roles:', userRolesToCreate);
            
            const { error: createError } = await supabase
              .from('profiles')
              .insert({
                id: session.user.id,
                email: session.user.email,
                full_name: session.user.user_metadata?.full_name || session.user.email, // Fallback for full_name
                avatar_url: session.user.user_metadata?.avatar_url,
                role: primaryRole, // Populate the singular 'role' column
                roles: userRolesToCreate, // Populate the 'roles' array column
              });

            if (createError) {
              console.error('Failed to create profile:', createError);
              return NextResponse.redirect(new URL('/login', requestUrl.origin));
            }

            // After profile creation, redirect to root. Middleware will handle dashboard.
            console.log('Redirecting new user to root (/) for middleware to handle dashboard.');
            return NextResponse.redirect(new URL('/', requestUrl.origin));
          }
          // If profile fetch had an error but not PGRST116 (profile not found)
          return NextResponse.redirect(new URL('/login', requestUrl.origin));
        }

        // If profile exists, also redirect to root. Middleware will handle dashboard.
        console.log('Redirecting existing user to root (/) for middleware to handle dashboard.');
        return NextResponse.redirect(new URL('/', requestUrl.origin));
      }
    } catch (error) {
      console.error("Auth callback error:", error)
      // Redirect to login with error
      const searchParams = new URLSearchParams()
      searchParams.set("error", "auth_error")
      searchParams.set("error_description", error instanceof Error ? error.message : "Failed to authenticate")
      return NextResponse.redirect(new URL(`/login?${searchParams.toString()}`, requestUrl.origin))
    }
  }

  console.log('No code found, redirecting to login')
  // If no code or session, redirect to login
  const searchParams = new URLSearchParams()
  searchParams.set("error", "invalid_request")
  searchParams.set("error_description", "No authentication code provided")
  console.log('=== Auth Callback End ===\n')
  return NextResponse.redirect(new URL(`/login?${searchParams.toString()}`, requestUrl.origin))
}