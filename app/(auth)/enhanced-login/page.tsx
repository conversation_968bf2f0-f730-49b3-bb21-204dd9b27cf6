/**
 * 10.2 Complete Authentication and Authorization - Enhanced Login
 * 
 * Comprehensive login with MFA, session management, and security features
 */

"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { useToast } from '@/app/components/ui/use-toast';
import { 
  Eye, 
  EyeOff, 
  Shield, 
  AlertCircle,
  CheckCircle,
  Loader2,
  Smartphone
} from 'lucide-react';
import { createClient } from '@supabase/supabase-js';
import { MultiFactorAuth } from '@/app/lib/auth/multi-factor';

interface LoginState {
  email: string;
  password: string;
  mfaToken: string;
  showPassword: boolean;
  isLoading: boolean;
  requiresMFA: boolean;
  loginAttempts: number;
  isLocked: boolean;
  lockoutTime?: number;
}

export default function EnhancedLoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const redirectTo = searchParams.get('redirect') || '/dashboard';

  const [state, setState] = useState<LoginState>({
    email: '',
    password: '',
    mfaToken: '',
    showPassword: false,
    isLoading: false,
    requiresMFA: false,
    loginAttempts: 0,
    isLocked: false
  });

  const [errors, setErrors] = useState<{
    email?: string;
    password?: string;
    mfa?: string;
    general?: string;
  }>({});

  useEffect(() => {
    // Check for account lockout
    const lockoutData = localStorage.getItem('login_lockout');
    if (lockoutData) {
      const { attempts, timestamp } = JSON.parse(lockoutData);
      const lockoutDuration = 15 * 60 * 1000; // 15 minutes
      
      if (attempts >= 5 && Date.now() - timestamp < lockoutDuration) {
        setState(prev => ({
          ...prev,
          isLocked: true,
          lockoutTime: timestamp + lockoutDuration
        }));
      } else if (Date.now() - timestamp >= lockoutDuration) {
        localStorage.removeItem('login_lockout');
      }
    }
  }, []);

  const validateForm = (): boolean => {
    const newErrors: typeof errors = {};

    if (!state.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(state.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!state.password) {
      newErrors.password = 'Password is required';
    }

    if (state.requiresMFA && !state.mfaToken) {
      newErrors.mfa = 'MFA code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLoginAttempt = (success: boolean) => {
    if (success) {
      localStorage.removeItem('login_lockout');
      return;
    }

    const lockoutData = localStorage.getItem('login_lockout');
    let attempts = 1;
    
    if (lockoutData) {
      const data = JSON.parse(lockoutData);
      attempts = data.attempts + 1;
    }

    localStorage.setItem('login_lockout', JSON.stringify({
      attempts,
      timestamp: Date.now()
    }));

    setState(prev => ({ ...prev, loginAttempts: attempts }));

    if (attempts >= 5) {
      setState(prev => ({
        ...prev,
        isLocked: true,
        lockoutTime: Date.now() + (15 * 60 * 1000)
      }));
      
      toast({
        title: "Account Temporarily Locked",
        description: "Too many failed attempts. Please try again in 15 minutes.",
        variant: "destructive",
      });
    }
  };

  const handleInitialLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (state.isLocked) {
      toast({
        title: "Account Locked",
        description: "Please wait before trying again.",
        variant: "destructive",
      });
      return;
    }

    if (!validateForm()) return;

    setState(prev => ({ ...prev, isLoading: true }));
    setErrors({});

    try {
      const supabase = createClient();
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: state.email,
        password: state.password,
      });

      if (error) {
        handleLoginAttempt(false);
        setErrors({ general: error.message });
        return;
      }

      if (!data.user) {
        handleLoginAttempt(false);
        setErrors({ general: 'Login failed. Please try again.' });
        return;
      }

      // Check if user has MFA enabled
      const requiresMFA = await MultiFactorAuth.requiresMFA(data.user.id);
      
      if (requiresMFA) {
        setState(prev => ({ ...prev, requiresMFA: true }));
        toast({
          title: "MFA Required",
          description: "Please enter your authentication code to continue.",
        });
      } else {
        handleLoginAttempt(true);
        toast({
          title: "Login Successful",
          description: "Welcome back!",
        });
        router.push(redirectTo);
      }

    } catch (error) {
      console.error('Login error:', error);
      handleLoginAttempt(false);
      setErrors({ 
        general: error instanceof Error ? error.message : 'An unexpected error occurred' 
      });
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleMFAVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setState(prev => ({ ...prev, isLoading: true }));
    setErrors({});

    try {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setErrors({ general: 'Session expired. Please login again.' });
        setState(prev => ({ ...prev, requiresMFA: false }));
        return;
      }

      const isValidMFA = await MultiFactorAuth.verifyMFAToken(user.id, state.mfaToken);
      
      if (!isValidMFA) {
        handleLoginAttempt(false);
        setErrors({ mfa: 'Invalid authentication code. Please try again.' });
        return;
      }

      handleLoginAttempt(true);
      toast({
        title: "Login Successful",
        description: "Welcome back!",
      });
      router.push(redirectTo);

    } catch (error) {
      console.error('MFA verification error:', error);
      handleLoginAttempt(false);
      setErrors({ 
        mfa: error instanceof Error ? error.message : 'MFA verification failed' 
      });
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const getRemainingLockoutTime = (): string => {
    if (!state.lockoutTime) return '';
    
    const remaining = Math.max(0, state.lockoutTime - Date.now());
    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (state.isLocked) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <Shield className="w-6 h-6 text-red-600" />
            </div>
            <CardTitle className="text-red-600">Account Temporarily Locked</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">
              Too many failed login attempts. Please try again in:
            </p>
            <div className="text-2xl font-mono font-bold text-red-600 mb-4">
              {getRemainingLockoutTime()}
            </div>
            <p className="text-sm text-gray-500">
              For security reasons, your account has been temporarily locked.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">
            {state.requiresMFA ? 'Two-Factor Authentication' : 'Sign In'}
          </CardTitle>
          <p className="text-gray-600">
            {state.requiresMFA 
              ? 'Enter the code from your authenticator app'
              : 'Enter your credentials to access your account'
            }
          </p>
        </CardHeader>
        <CardContent>
          {!state.requiresMFA ? (
            <form onSubmit={handleInitialLogin} className="space-y-4">
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={state.email}
                  onChange={(e) => setState(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="Enter your email"
                  disabled={state.isLoading}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.email}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={state.showPassword ? 'text' : 'password'}
                    value={state.password}
                    onChange={(e) => setState(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="Enter your password"
                    disabled={state.isLoading}
                    className={errors.password ? 'border-red-500' : ''}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setState(prev => ({ ...prev, showPassword: !prev.showPassword }))}
                    disabled={state.isLoading}
                  >
                    {state.showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.password}
                  </p>
                )}
              </div>

              {errors.general && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.general}
                  </p>
                </div>
              )}

              {state.loginAttempts > 0 && state.loginAttempts < 5 && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-sm text-yellow-600">
                    {5 - state.loginAttempts} attempts remaining before account lockout
                  </p>
                </div>
              )}

              <Button 
                type="submit" 
                className="w-full" 
                disabled={state.isLoading}
              >
                {state.isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Signing In...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
            </form>
          ) : (
            <form onSubmit={handleMFAVerification} className="space-y-4">
              <div className="text-center mb-4">
                <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                  <Smartphone className="w-6 h-6 text-blue-600" />
                </div>
                <p className="text-sm text-gray-600">
                  Signed in as: <strong>{state.email}</strong>
                </p>
              </div>

              <div>
                <Label htmlFor="mfaToken">Authentication Code</Label>
                <Input
                  id="mfaToken"
                  type="text"
                  value={state.mfaToken}
                  onChange={(e) => setState(prev => ({ ...prev, mfaToken: e.target.value }))}
                  placeholder="Enter 6-digit code"
                  maxLength={8}
                  disabled={state.isLoading}
                  className={errors.mfa ? 'border-red-500' : ''}
                  autoComplete="one-time-code"
                />
                {errors.mfa && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.mfa}
                  </p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Enter the code from your authenticator app or use a backup code
                </p>
              </div>

              {errors.general && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.general}
                  </p>
                </div>
              )}

              <div className="space-y-2">
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={state.isLoading}
                >
                  {state.isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    'Verify & Sign In'
                  )}
                </Button>

                <Button 
                  type="button" 
                  variant="outline" 
                  className="w-full" 
                  onClick={() => setState(prev => ({ ...prev, requiresMFA: false }))}
                  disabled={state.isLoading}
                >
                  Back to Login
                </Button>
              </div>
            </form>
          )}

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <a href="/register" className="text-blue-600 hover:underline">
                Sign up
              </a>
            </p>
            <p className="text-sm text-gray-600 mt-2">
              <a href="/forgot-password" className="text-blue-600 hover:underline">
                Forgot your password?
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}