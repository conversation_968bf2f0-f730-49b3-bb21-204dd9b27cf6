'use client';

import { useState, useEffect } from 'react';
// // Removed useUser import - using alternative auth pattern // Commented out - hook doesn't exist
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Badge } from '@/app/components/ui/badge';
import { Button } from '@/app/components/ui/button';
import { MapPin, Clock, Phone, MessageCircle, Navigation } from 'lucide-react';

interface Trip {
  id: string;
  status: 'assigned' | 'en_route_pickup' | 'arrived_pickup' | 'passenger_onboard' | 'en_route_destination' | 'completed';
  pickup_address: string;
  destination_address: string;
  pickup_time: string;
  passenger_name: string;
  passenger_phone: string;
  quote_id: string;
  estimated_duration: number;
  special_instructions?: string;
}

export default function DriverDashboard() {
  // Temporary fix - replace with proper auth hook later
  const user = null;
  const loading = false;
  const [activeTrips, setActiveTrips] = useState<Trip[]>([]);
  const [loadingTrips, setLoadingTrips] = useState(true);

  useEffect(() => {
    if (user) {
      fetchActiveTrips();
    }
  }, [user]);

  const fetchActiveTrips = async () => {
    try {
      setLoadingTrips(true);
      const response = await fetch('/api/driver/trips/active');
      if (response.ok) {
        const trips = await response.json();
        setActiveTrips(trips);
      }
    } catch (error) {
      console.error('Error fetching active trips:', error);
    } finally {
      setLoadingTrips(false);
    }
  };

  const updateTripStatus = async (tripId: string, status: Trip['status']) => {
    try {
      const response = await fetch(`/api/driver/trips/${tripId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        await fetchActiveTrips(); // Refresh trips
      }
    } catch (error) {
      console.error('Error updating trip status:', error);
    }
  };

  const getStatusColor = (status: Trip['status']) => {
    const colors = {
      assigned: 'bg-blue-100 text-blue-800',
      en_route_pickup: 'bg-yellow-100 text-yellow-800',
      arrived_pickup: 'bg-orange-100 text-orange-800',
      passenger_onboard: 'bg-green-100 text-green-800',
      en_route_destination: 'bg-purple-100 text-purple-800',
      completed: 'bg-gray-100 text-gray-800',
    };
    return colors[status];
  };

  const getStatusLabel = (status: Trip['status']) => {
    const labels = {
      assigned: 'Assigned',
      en_route_pickup: 'En Route to Pickup',
      arrived_pickup: 'Arrived at Pickup',
      passenger_onboard: 'Passenger Onboard',
      en_route_destination: 'En Route to Destination',
      completed: 'Completed',
    };
    return labels[status];
  };

  const getNextAction = (status: Trip['status']) => {
    const actions = {
      assigned: { label: 'Start Trip', next: 'en_route_pickup' as Trip['status'] },
      en_route_pickup: { label: 'Arrived at Pickup', next: 'arrived_pickup' as Trip['status'] },
      arrived_pickup: { label: 'Passenger Onboard', next: 'passenger_onboard' as Trip['status'] },
      passenger_onboard: { label: 'En Route to Destination', next: 'en_route_destination' as Trip['status'] },
      en_route_destination: { label: 'Trip Complete', next: 'completed' as Trip['status'] },
      completed: null,
    };
    return actions[status];
  };

  if (loading || loadingTrips) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">Please log in to access the driver portal.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Driver Dashboard</h1>
        <div className="text-sm text-gray-600">
          Welcome back, {user.full_name || user.email}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Trips</CardTitle>
            <Navigation className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeTrips.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Trips</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {activeTrips.filter(trip => 
                new Date(trip.pickup_time).toDateString() === new Date().toDateString()
              ).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <div className="h-4 w-4 bg-green-500 rounded-full"></div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Online</div>
          </CardContent>
        </Card>
      </div>

      {/* Active Trips */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold text-gray-900">Active Trips</h2>
        
        {activeTrips.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Navigation className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Trips</h3>
              <p className="text-gray-600">You don't have any active trips assigned at the moment.</p>
            </CardContent>
          </Card>
        ) : (
          activeTrips.map((trip) => (
            <Card key={trip.id} className="border-l-4 border-l-blue-500">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">Trip #{trip.quote_id.slice(0, 8)}</CardTitle>
                    <Badge className={getStatusColor(trip.status)}>
                      {getStatusLabel(trip.status)}
                    </Badge>
                  </div>
                  <div className="text-right text-sm text-gray-600">
                    <div>Pickup: {new Date(trip.pickup_time).toLocaleTimeString()}</div>
                    <div>Duration: {trip.estimated_duration} min</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Trip Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-start space-x-2">
                      <MapPin className="h-4 w-4 text-green-600 mt-1" />
                      <div>
                        <div className="text-sm font-medium">Pickup</div>
                        <div className="text-sm text-gray-600">{trip.pickup_address}</div>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <MapPin className="h-4 w-4 text-red-600 mt-1" />
                      <div>
                        <div className="text-sm font-medium">Destination</div>
                        <div className="text-sm text-gray-600">{trip.destination_address}</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">Passenger:</span> {trip.passenger_name}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Phone:</span> {trip.passenger_phone}
                    </div>
                    {trip.special_instructions && (
                      <div className="text-sm">
                        <span className="font-medium">Special Instructions:</span>
                        <div className="text-gray-600 mt-1">{trip.special_instructions}</div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-between items-center pt-4 border-t">
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`tel:${trip.passenger_phone}`)}
                    >
                      <Phone className="h-4 w-4 mr-1" />
                      Call
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`sms:${trip.passenger_phone}`)}
                    >
                      <MessageCircle className="h-4 w-4 mr-1" />
                      Message
                    </Button>
                  </div>
                  
                  {getNextAction(trip.status) && (
                    <Button
                      onClick={() => updateTripStatus(trip.id, getNextAction(trip.status)!.next)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {getNextAction(trip.status)!.label}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}