"use client"

import { Card } from "@/app/components/ui/card";
import { Label } from "@/app/components/ui/label";
import { Input } from "@/app/components/ui/input";
import { Button } from "@/app/components/ui/button";
import { Textarea } from "@/app/components/ui/textarea";
import { Separator } from "@/app/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs";
import { Badge } from "@/app/components/ui/badge";
import { Building2, Camera, Code, Globe, Mail, MapPin, Phone, Save, Shield, Truck, Type, Upload, Users } from "lucide-react";

export default function CompanyProfilePage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Company Profile</h2>
        <Button>Save Changes</Button>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="fleet">Fleet</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-4">
                <div className="h-20 w-20 rounded-lg bg-muted flex items-center justify-center">
                  <Building2 className="h-10 w-10 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">Company Logo</h3>
                  <p className="text-sm text-muted-foreground">
                    Upload your company logo. Max size 2MB.
                  </p>
                </div>
              </div>
              <Button size="sm" variant="outline">
                <Camera className="h-4 w-4 mr-2" />
                Upload
              </Button>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="companyName">Company Name</Label>
                <Input id="companyName" defaultValue="Elite Fleet Services" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  defaultValue="Premier transportation services for corporate events and special occasions."
                  className="min-h-[100px]"
                />
              </div>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="grid gap-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input id="phone" type="tel" defaultValue="+****************" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" defaultValue="<EMAIL>" />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="website">Website</Label>
                <Input id="website" type="url" defaultValue="https://elitefleet.com" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Location</h3>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="address">Street Address</Label>
                <Input id="address" defaultValue="123 Transport Ave" />
              </div>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="grid gap-2">
                  <Label htmlFor="city">City</Label>
                  <Input id="city" defaultValue="New York" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="state">State</Label>
                  <Input id="state" defaultValue="NY" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="zip">ZIP Code</Label>
                  <Input id="zip" defaultValue="10001" />
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="fleet" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Fleet Overview</h3>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="flex items-center space-x-4">
                <Truck className="h-8 w-8 text-muted-foreground" />
                <div>
                  <p className="text-2xl font-bold">24</p>
                  <p className="text-sm text-muted-foreground">Total Vehicles</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Users className="h-8 w-8 text-muted-foreground" />
                <div>
                  <p className="text-2xl font-bold">32</p>
                  <p className="text-sm text-muted-foreground">Drivers</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Shield className="h-8 w-8 text-muted-foreground" />
                <div>
                  <p className="text-2xl font-bold">100%</p>
                  <p className="text-sm text-muted-foreground">Compliance</p>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Vehicle Types</h3>
              <Button variant="outline" size="sm">Add Vehicle Type</Button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <Truck className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Luxury Sedan</p>
                    <p className="text-sm text-muted-foreground">4 passengers max</p>
                  </div>
                </div>
                <Badge>8 vehicles</Badge>
              </div>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <Truck className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Executive SUV</p>
                    <p className="text-sm text-muted-foreground">6 passengers max</p>
                  </div>
                </div>
                <Badge>6 vehicles</Badge>
              </div>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <Truck className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Passenger Van</p>
                    <p className="text-sm text-muted-foreground">12 passengers max</p>
                  </div>
                </div>
                <Badge>4 vehicles</Badge>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Required Documents</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">Business License</p>
                  <p className="text-sm text-muted-foreground">Expires in 8 months</p>
                </div>
                <Button variant="outline" size="sm">Update</Button>
              </div>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">Insurance Certificate</p>
                  <p className="text-sm text-muted-foreground">Expires in 3 months</p>
                </div>
                <Button variant="outline" size="sm">Update</Button>
              </div>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">DOT Number</p>
                  <p className="text-sm text-muted-foreground">Valid</p>
                </div>
                <Button variant="outline" size="sm">View</Button>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Payment Information</h3>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="taxId">Tax ID</Label>
                <Input id="taxId" defaultValue="12-3456789" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="bankAccount">Bank Account</Label>
                <Input id="bankAccount" defaultValue="**** **** **** 4567" type="password" />
              </div>
              <Button variant="outline">Update Payment Method</Button>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Billing Address</h3>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="billingAddress">Street Address</Label>
                <Input id="billingAddress" defaultValue="123 Transport Ave" />
              </div>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="grid gap-2">
                  <Label htmlFor="billingCity">City</Label>
                  <Input id="billingCity" defaultValue="New York" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="billingState">State</Label>
                  <Input id="billingState" defaultValue="NY" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="billingZip">ZIP Code</Label>
                  <Input id="billingZip" defaultValue="10001" />
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 