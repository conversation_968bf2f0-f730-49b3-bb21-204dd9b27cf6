"use client"

import React, { useState, useEffect, Fragment } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/app/components/ui/tabs";
import Link from "next/link"
import { useParams } from "next/navigation";
import { Input } from "@/app/components/ui/input";
import { Checkbox } from "@/app/components/ui/checkbox";
import Map, { Marker, Source, Layer } from 'react-map-gl';
import 'mapbox-gl/dist/mapbox-gl.css'
import { useToast } from "@/app/components/ui/use-toast";
import { ToastAction } from "@/app/components/ui/toast";
import { Users, Car, MapPin, Search, Filter, X, ArrowLeft, MoreVertical, Calendar, Phone, List, Square, Link as LinkIcon, as <PERSON><PERSON><PERSON>, <PERSON>f<PERSON><PERSON><PERSON><PERSON>, Map } from "lucide-react";
import { useState, useEffect } from "react";
// Types
type ServiceData = {
  id: string
  title: string
  date: string
  location: string
  status: "scheduled" | "in_progress" | "completed"
  trips: Array<{
    id: string
    vehicle: string
    passengerCount: number
    pickupLocation: string
    dropoffLocation: string
    time: string
    status: "scheduled" | "in_progress" | "completed"
    type: "arrival" | "departure" | "transfer"
    passengers: Array<{
      id: string
      name: string
    }>
    driver?: {
      name: string
      phone: string
    }
    currentLocation?: Coordinates
  }>
}

type Coordinates = {
  lat: number
  lng: number
}

type TripWithCoordinates = ServiceData['trips'][0] & {
  pickupCoordinates?: Coordinates
  dropoffCoordinates?: Coordinates
}

// Mock data function
const getServiceDetails = async (id: string): Promise<ServiceData> => {
  return {
    id,
    title: "Tech Conference 2024",
    date: "March 15, 2024",
    location: "San Francisco, CA",
    status: "scheduled",
    trips: [
      {
        id: "T1",
        vehicle: "Tesla Model Y",
        passengerCount: 4,
        passengers: [
          { id: "P1", name: "John Anderson" },
          { id: "P2", name: "Sarah Miller" },
          { id: "P3", name: "David Chen" },
          { id: "P4", name: "Emma Wilson" }
        ],
        pickupLocation: "San Francisco International Airport (SFO)",
        dropoffLocation: "Moscone Center",
        time: "09:00 AM",
        status: "in_progress",
        type: "arrival",
        driver: {
          name: "John Smith",
          phone: "+****************"
        },
        currentLocation: { lat: 37.7045, lng: -122.4015 }
      },
      {
        id: "T2",
        vehicle: "Mercedes Sprinter",
        passengerCount: 6,
        passengers: [],
        pickupLocation: "Union Square",
        dropoffLocation: "Golden Gate Bridge Welcome Center",
        time: "02:00 PM",
        status: "scheduled",
        type: "transfer"
      }
    ]
  }
}

// Mock coordinates function (replace with actual geocoding service)
const getCoordinates = async (location: string): Promise<Coordinates> => {
  const mockCoordinates: Record<string, Coordinates> = {
    "San Francisco International Airport (SFO)": { lat: 37.6213, lng: -122.3790 },
    "Moscone Center": { lat: 37.7845, lng: -122.4008 },
    "Union Square": { lat: 37.7879, lng: -122.4075 },
    "Golden Gate Bridge Welcome Center": { lat: 37.8197, lng: -122.4786 }
  }
  return mockCoordinates[location] || { lat: 37.7749, lng: -122.4194 }
}

export default function ServicePage() {
  const params = useParams()
  const serviceId = params.serviceId as string
  const [service, setService] = useState<ServiceData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedTrips, setSelectedTrips] = useState<string[]>([])
  const [selectedTripType, setSelectedTripType] = useState<string>("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [mapViewport, setMapViewport] = useState({
    latitude: 37.7749,
    longitude: -122.4194,
    zoom: 11,
    bearing: 0,
    pitch: 0
  })
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [tripsWithCoordinates, setTripsWithCoordinates] = useState<TripWithCoordinates[]>([])
  const [selectedStatus, setSelectedStatus] = useState<ServiceData['trips'][0]['status']>('scheduled')
  const [isBulkUpdating, setIsBulkUpdating] = useState(false)
  const [selectedRoutes, setSelectedRoutes] = useState<string[]>([])
  const [passengerFilter, setPassengerFilter] = useState("")
  const { toast } = useToast()

  const selectedTrip = service?.trips.find(trip => trip.id === selectedTripId)

  useEffect(() => {
    const fetchService = async () => {
      try {
        const data = await getServiceDetails(serviceId)
        setService(data)
      } catch (error) {
        console.error("Failed to fetch service:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchService()
  }, [serviceId])

  // Fetch coordinates for trips
  useEffect(() => {
    const fetchCoordinates = async () => {
      if (!service) return

      const updatedTrips = await Promise.all(
        service.trips.map(async (trip) => {
          const pickupCoords = await getCoordinates(trip.pickupLocation)
          const dropoffCoords = await getCoordinates(trip.dropoffLocation)

          return {
            ...trip,
            pickupCoordinates: pickupCoords,
            dropoffCoordinates: dropoffCoords
          }
        })
      )

      setTripsWithCoordinates(updatedTrips)

      // Center map on first trip's pickup location
      if (updatedTrips[0]?.pickupCoordinates) {
        setMapViewport(prev => ({
          ...prev,
          latitude: updatedTrips[0].pickupCoordinates!.lat,
          longitude: updatedTrips[0].pickupCoordinates!.lng
        }))
      }
    }

    fetchCoordinates()
  }, [service])

  // Update trip status
  const updateTripStatus = async (tripId: string, newStatus: ServiceData['trips'][0]['status']) => {
    if (!service) return

    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500))

      const updatedTrips = service.trips.map(trip =>
        trip.id === tripId ? { ...trip, status: newStatus } : trip
      )

      setService(prev => prev ? { ...prev, trips: updatedTrips } : null)

      toast({
        title: "Status Updated",
        description: `Trip status has been updated to ${newStatus.replace('_', ' ').toUpperCase()}` })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update trip status",
        action: <ToastAction altText="Try again">Try again</ToastAction> })
    }
  }

  // Bulk update trip status
  const updateBulkStatus = async () => {
    if (!service || selectedTrips.length === 0) return

    setIsBulkUpdating(true)
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updatedTrips = service.trips.map(trip =>
        selectedTrips.includes(trip.id) ? { ...trip, status: selectedStatus } : trip
      )

      setService(prev => prev ? { ...prev, trips: updatedTrips } : null)
      setSelectedTrips([])

      toast({
        title: "Bulk Update Complete",
        description: `Updated ${selectedTrips.length} trips to ${selectedStatus.replace('_', ' ').toUpperCase()}` })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update trip statuses",
        action: <ToastAction altText="Try again">Try again</ToastAction> })
    } finally {
      setIsBulkUpdating(false)
    }
  }

  // Filter trips based on all criteria
  const filteredTrips = service?.trips.filter(trip => {
    const matchesType = selectedTripType === "all" || trip.type === selectedTripType
    const matchesSearch = trip.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trip.pickupLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trip.dropoffLocation.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesPassenger = !passengerFilter ||
      trip.passengers.some(p => p.name.toLowerCase().includes(passengerFilter.toLowerCase()))

    return matchesType && matchesSearch && matchesPassenger
  })

  if (isLoading) {
    return (
      <div className="container space-y-6 p-6">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/transportation">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div className="h-6 w-48 animate-pulse bg-muted rounded" />
        </div>
        <div className="h-[600px] animate-pulse bg-muted rounded-lg" />
      </div>
    )
  }

  if (!service) {
    return (
      <div className="container space-y-6 p-6">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/transportation">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Service Not Found</h2>
        </div>
        <Card>
          <CardContent className="p-6">
            <p className="text-muted-foreground">The requested service could not be found.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/transportation">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <div className="flex items-center gap-x-2">
              <h2 className="text-2xl font-bold tracking-tight">{service.title}</h2>
              <Badge variant="outline" className="capitalize">
                {service.status}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Service Details */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm">{service.date}</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-x-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm">{service.location}</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm">{service.trips.reduce((acc, trip) => acc + trip.passengerCount, 0)} total passengers</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main content grid */}
      <div className="grid grid-cols-2 gap-6">
        {/* Left column - Trip List */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle>Live Trips</CardTitle>
              <div className="flex items-center gap-x-2">
                <Button variant="outline" size="sm">
                  <RefreshCcw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-x-2">
                <Button
                  variant={selectedTripType === "all" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setSelectedTripType("all")}
                >
                  All Rides
                </Button>
                <Button
                  variant={selectedTripType === "arrival" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setSelectedTripType("arrival")}
                >
                  Arrivals
                </Button>
                <Button
                  variant={selectedTripType === "transfer" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setSelectedTripType("transfer")}
                >
                  Transfers
                </Button>
                <Button
                  variant={selectedTripType === "departure" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setSelectedTripType("departure")}
                >
                  Departures
                </Button>
              </div>
            </div>

            <div className="flex items-center gap-4 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search trips..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <div className="relative flex-1">
                <Users className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Filter by passenger..."
                  value={passengerFilter}
                  onChange={(e) => setPassengerFilter(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              {filteredTrips?.map((trip) => (
                <div
                  key={trip.id}
                  className={`flex items-center justify-between p-4 border rounded-lg ${selectedRoutes.includes(trip.id) ? 'border-primary' : ''
                    }`}
                >
                  <div className="flex items-center gap-x-4">
                    <Checkbox
                      checked={selectedRoutes.includes(trip.id)}
                      onCheckedChange={(checked) => {
                        setSelectedRoutes(prev =>
                          checked
                            ? [...prev, trip.id]
                            : prev.filter(id => id !== trip.id)
                        )
                      }}
                    />
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{trip.vehicle}</span>
                        <Badge variant="outline" className="capitalize">
                          {trip.type}
                        </Badge>
                        <span className="text-sm">• {trip.passengerCount} passengers</span>
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        {trip.pickupLocation} → {trip.dropoffLocation}
                      </div>
                      {trip.passengers?.length > 0 && (
                        <div className="text-sm text-muted-foreground mt-1">
                          Passengers: {trip.passengers.map(p => p.name).join(", ")}
                        </div>
                      )}
                      {trip.driver && (
                        <div className="text-sm text-muted-foreground mt-1">
                          Driver: {trip.driver.name} • {trip.driver.phone}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-x-4">
                    <div className="text-sm">{trip.time}</div>
                    <Badge
                      variant={
                        trip.status === "completed"
                          ? "default"
                          : trip.status === "in_progress"
                            ? "secondary"
                            : "outline"
                      }
                    >
                      {trip.status.replace("_", " ").toUpperCase()}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedTripId(trip.id)
                        setIsDialogOpen(true)
                      }}
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Right column - Map */}
        <Card>
          <CardContent className="p-4">
            <Map
              {...mapViewport}
              onMove={evt => setMapViewport(evt.viewState)}
              style={{ width: '100%', height: '600px' }}
              mapStyle="mapbox://styles/mapbox/streets-v11"
              mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_TOKEN}
            >
              {tripsWithCoordinates
                .filter(trip => selectedRoutes.includes(trip.id))
                .map((trip) => (
                  trip.pickupCoordinates && trip.dropoffCoordinates && (
                    <Fragment key={trip.id}>
                      <Marker
                        longitude={trip.pickupCoordinates.lng}
                        latitude={trip.pickupCoordinates.lat}
                        color="#10b981"
                      />
                      <Marker
                        longitude={trip.dropoffCoordinates.lng}
                        latitude={trip.dropoffCoordinates.lat}
                        color="#ef4444"
                      />
                      {trip.currentLocation && (
                        <Marker
                          longitude={trip.currentLocation.lng}
                          latitude={trip.currentLocation.lat}
                        >
                          <div className="bg-blue-500 p-2 rounded-full animate-pulse">
                            <Car className="h-4 w-4 text-white" />
                          </div>
                        </Marker>
                      )}
                      <Source
                        type="geojson"
                        data={{
                          type: 'Feature',
                          properties: {},
                          geometry: {
                            type: 'LineString',
                            coordinates: [
                              [trip.pickupCoordinates.lng, trip.pickupCoordinates.lat],
                              [trip.dropoffCoordinates.lng, trip.dropoffCoordinates.lat]
                            ]
                          }
                        }}
                      >
                        <Layer
                          id={`route-${trip.id}`}
                          type="line"
                          paint={{
                            'line-color': '#10b981',
                            'line-width': 2
                          }}
                        />
                      </Source>
                    </Fragment>
                  )
                ))}
            </Map>
          </CardContent>
        </Card>
      </div>

      {/* Add Bulk Actions */}
      {selectedTrips.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium">
                  {selectedTrips.length} trips selected
                </span>
                <Select
                  value={selectedStatus}
                  onValueChange={(value) =>
                    setSelectedStatus(value as ServiceData['trips'][0]['status'])
                  }
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button
                onClick={updateBulkStatus}
                disabled={isBulkUpdating}
              >
                {isBulkUpdating ? "Updating..." : "Update Status"}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Update Trip Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle>Trip Details</DialogTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsDialogOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          {selectedTrip && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Vehicle</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4" />
                      <span>{selectedTrip.vehicle}</span>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Badge
                      variant={
                        selectedTrip.status === "completed"
                          ? "default"
                          : selectedTrip.status === "in_progress"
                            ? "secondary"
                            : "outline"
                      }
                    >
                      {selectedTrip.status.replace("_", " ").toUpperCase()}
                    </Badge>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Trip Information</h4>
                  <div className="space-y-2">
                    <div className="text-sm">
                      <span className="text-muted-foreground">Pickup:</span> {selectedTrip.pickupLocation}
                    </div>
                    <div className="text-sm">
                      <span className="text-muted-foreground">Dropoff:</span> {selectedTrip.dropoffLocation}
                    </div>
                    <div className="text-sm">
                      <span className="text-muted-foreground">Time:</span> {selectedTrip.time}
                    </div>
                    <div className="text-sm">
                      <span className="text-muted-foreground">Passengers:</span> {selectedTrip.passengerCount}
                    </div>
                  </div>
                </div>

                {selectedTrip.driver && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Driver Information</h4>
                    <div className="space-y-2">
                      <div className="text-sm">
                        <span className="text-muted-foreground">Name:</span> {selectedTrip.driver.name}
                      </div>
                      <div className="text-sm flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        {selectedTrip.driver.phone}
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Close
                  </Button>
                  <Select
                    value={selectedTrip.status}
                    onValueChange={(value) => {
                      updateTripStatus(selectedTrip.id, value as ServiceData['trips'][0]['status'])
                    }}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Update status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
} 