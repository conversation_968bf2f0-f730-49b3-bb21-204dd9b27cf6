"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card } from "@/app/components/ui/card";
import { ArrowLeft, Plus, Type, Link as LinkIcon, as LinkIcon } from "lucide-react";
import Link from "next/link"
import { DataTable } from "@/app/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/app/components/ui/badge";
type PassengerGroup = {
  id: number
  name: string
  count: number
  type: string
  event: string
}

const columns: ColumnDef<PassengerGroup>[] = [
  {
    header: "Group Name",
    accessorKey: "name" },
  {
    header: "Passengers",
    accessorKey: "count",
    cell: ({ row }) => (
      <Badge variant="secondary">
        {row.getValue("count")} passengers
      </Badge>
    ) },
  {
    header: "Type",
    accessorKey: "type" },
  {
    header: "Event",
    accessorKey: "event" },
]

export default function PassengerGroupsPage() {
  const groups = [
    {
      id: 1,
      name: "VIP Guests",
      count: 12,
      type: "VIP",
      event: "Annual Conference 2024" },
    {
      id: 2,
      name: "Speakers",
      count: 5,
      type: "Special",
      event: "Annual Conference 2024" },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-3">
          <Link href="/customer/passengers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Passenger Groups</h2>
        </div>
        <Link href="/customer/passengers/groups/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Group
          </Button>
        </Link>
      </div>
      <Card className="p-4">
        <DataTable
          columns={columns}
          data={groups}
        />
      </Card>
    </div>
  )
} 