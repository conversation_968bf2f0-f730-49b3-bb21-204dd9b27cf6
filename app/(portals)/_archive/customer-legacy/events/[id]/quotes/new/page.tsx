"use client"

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { ArrowLeft, Link as LinkIcon, as LinkIcon } from "lucide-react";
import Link from "next/link"
import { QuoteForm } from "@/app/components/features/quotes/quote-form";
export default function NewQuotePage() {
  const params = useParams()
  const eventId = params.id as string

  return (
    <main className="flex-1">
      <div className="container max-w-screen-xl mx-auto py-8">
        <div className="flex items-center gap-4 mb-6">
          <Link href={`/customer/events/${eventId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Event
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Request New Quote</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Transportation Quote Request</CardTitle>
          </CardHeader>
          <CardContent>
            <QuoteForm
              userRole="event_manager"
              defaultValues={{
              }}
            />
          </CardContent>
        </Card>
      </div>
    </main>
  )
} 