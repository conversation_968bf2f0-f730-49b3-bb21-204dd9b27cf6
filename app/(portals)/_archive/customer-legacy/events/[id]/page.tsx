"use client"

import React, { useState, useEffect, Fragment } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/app/components/ui/avatar";
import { cn } from "@/lib/utils";
import Link from "next/link"
import { useParams, useRouter } from "next/navigation";
import { formatDate } from "@/lib/utils";
import { Input } from "@/app/components/ui/input";
import { Checkbox } from "@/app/components/ui/checkbox";
import Map, { Marker, Source, Layer, NavigationControl, FullscreenControl, ScaleControl } from 'react-map-gl';
import 'mapbox-gl/dist/mapbox-gl.css'
import { Separator } from "@/app/components/ui/separator";
import { Progress } from "@/app/components/ui/progress";
import { ScrollArea } from "@/app/components/ui/scroll-area";
import { Users, Shield, Car, MapPin, Bell, Activity, FileText, Plus, Search, Filter, Download, Check, X, AlertTriangle, Info, ArrowLeft, ArrowRight, MoreVertical, Calendar, Clock, Copy, Mail, Phone, MessageSquare, Save, Printer, Type, List, Grid, Square, ChevronRight, AlertCircle, Sort, UserPlus, Link as LinkIcon, as LinkIcon, Map, CloudRain } from "lucide-react";
import { useState, useEffect } from "react";
type EventData = {
  id: string
  title: string
  description: string
  startDate: Date
  endDate: Date
  location: string
  status: "draft" | "published" | "cancelled"
  totalPassengers: number
  trips: Array<{
    id: string
    vehicle: string
    passengerCount: number
    passengers?: Array<{
      id: string
      name: string
      group?: string
      phone?: string
      email?: string
      isVIP?: boolean
    }>
    pickupLocation: string
    dropoffLocation: string
    time: string
    status: "scheduled" | "in_progress" | "completed" | "cancelled"
    type: "arrival" | "departure" | "transfer"
    notes?: string
    driver?: {
      name: string
      phone: string
    }
    stage?: TripStage
    tripType?: string
    startTime?: string
  }>
  quotes: Array<{
    id: string
    vehicle: string
    passengerCount: number
    pickupLocation: string
    dropoffLocation: string
    date: string
    time: string
    status: "pending" | "rate_requested" | "quote_ready" | "changes_requested" | "accepted" | "rejected"
    price: {
      baseRate: number
      gratuity: number
      adminFee: number
      total: number
    }
    notes?: string
    affiliateRate?: number
    markup?: number
    vehicleDetails?: {
      type: string
      capacity: number
    }
    amount?: number
    pickupTime?: string
    guestGroup?: string
    eventActivity?: string
    eventRelation?: string
    relatedQuotes?: Array<{
      type: string
      vehicle: string
      passengerCount: number
      pickupLocation: string
      dropoffLocation: string
      date: string
      time: string
      status: "pending" | "rate_requested" | "quote_ready" | "changes_requested" | "accepted" | "rejected"
      price: {
        baseRate: number
        gratuity: number
        adminFee: number
        total: number
      }
      notes?: string
      affiliateRate?: number
      markup?: number
      vehicleDetails?: {
        type: string
        capacity: number
      }
      amount?: number
      pickupTime?: string
    }>
  }>
}

// Add new types for map data
interface Coordinates {
  lat: number
  lng: number
}

interface RouteData {
  type: string
  properties: {}
  geometry: {
    type: string
    coordinates: [number, number][]
  }
}

// Add new state and types at the top
type Driver = {
  id: string
  name: string
  phone: string
  available: boolean
  vehicle?: string
}

type Vehicle = {
  id: string
  name: string
  type: string
  capacity: number
  available: boolean
}

// Add mock data
const availableDrivers: Driver[] = [
  { id: "D1", name: "John Smith", phone: "+****************", available: true, vehicle: "Tesla Model Y" },
  { id: "D2", name: "Sarah Johnson", phone: "+****************", available: true, vehicle: "BMW 7 Series" },
  { id: "D3", name: "Mike Wilson", phone: "+****************", available: true, vehicle: "Mercedes Sprinter" }
]

const availableVehicles: Vehicle[] = [
  { id: "V1", name: "Tesla Model Y", type: "Sedan", capacity: 4, available: true },
  { id: "V2", name: "Mercedes Sprinter", type: "Van", capacity: 12, available: true },
  { id: "V3", name: "BMW 7 Series", type: "Luxury", capacity: 3, available: true }
]

// Add new trip status type
type TripStage = "on_the_way" | "waiting_for_passenger" | "passenger_on_board" | "dropped_off"

// Add a function to get stage color
const getTripStageAbbr = (stage: TripStage) => {
  switch (stage) {
    case "on_the_way": return "OTW"
    case "waiting_for_passenger": return "OLC"
    case "passenger_on_board": return "POB"
    case "dropped_off": return "DONE"
    default: return ""
  }
}

const getTripStageColor = (stage: TripStage) => {
  switch (stage) {
    case "on_the_way": return "bg-blue-500"
    case "waiting_for_passenger": return "bg-yellow-500"
    case "passenger_on_board": return "bg-green-500"
    case "dropped_off": return "bg-purple-500"
    default: return "bg-gray-500"
  }
}

// Update getCoordinates to be synchronous
const getCoordinates = (location: string): Coordinates => {
  const mockCoordinates: { [key: string]: Coordinates } = {
    "San Francisco International Airport (SFO)": { lat: 37.6213, lng: -122.3790 },
    "Moscone Center": { lat: 37.7845, lng: -122.4008 },
    "Union Square": { lat: 37.7879, lng: -122.4075 },
    "Golden Gate Bridge Welcome Center": { lat: 37.8077, lng: -122.4750 },
    "Fisherman's Wharf": { lat: 37.8080, lng: -122.4177 },
    "Palace of Fine Arts": { lat: 37.8029, lng: -122.4484 },
    "Pier 39": { lat: 37.8087, lng: -122.4098 },
    "Chinatown Gate": { lat: 37.7905, lng: -122.4067 },
    "Oracle Park": { lat: 37.7786, lng: -122.3893 },
    "California Academy of Sciences": { lat: 37.7699, lng: -122.4661 }
  }
  return mockCoordinates[location] || { lat: 37.7749, lng: -122.4194 }
}

// Mock function - replace with actual API call
const getEventDetails = async (id: string): Promise<EventData> => {
  return {
    id,
    title: "Tech Conference 2024",
    description: "Annual Technology Conference in San Francisco",
    startDate: new Date("2024-03-15"),
    endDate: new Date("2024-03-17"),
    location: "Moscone Center, San Francisco",
    status: "published",
    totalPassengers: 150,
    trips: [
      {
        id: "T1",
        vehicle: "Tesla Model Y",
        passengerCount: 4,
        passengers: [
          { id: "P1", name: "John Doe", group: "VIP" },
          { id: "P2", name: "Jane Smith", group: "VIP" },
          { id: "P3", name: "Bob Wilson", group: "VIP" },
          { id: "P4", name: "Alice Brown", group: "VIP" }
        ],
        pickupLocation: "San Francisco International Airport (SFO)",
        dropoffLocation: "Moscone Center",
        time: "09:00 AM",
        status: "in_progress",
        stage: "on_the_way",
        type: "arrival",
        notes: "VIP pickup - Meet at Terminal 2",
        driver: {
          name: "John Smith",
          phone: "+****************"
        }
      },
      {
        id: "T2",
        vehicle: "Mercedes Sprinter",
        passengerCount: 8,
        pickupLocation: "Union Square",
        dropoffLocation: "Golden Gate Bridge Welcome Center",
        time: "02:00 PM",
        status: "in_progress",
        stage: "waiting_for_passenger",
        type: "transfer",
        notes: "Group tour transfer",
        driver: {
          name: "Mike Wilson",
          phone: "+****************"
        }
      },
      {
        id: "T3",
        vehicle: "BMW 7 Series",
        passengerCount: 3,
        pickupLocation: "Moscone Center",
        dropoffLocation: "Fisherman's Wharf",
        time: "01:30 PM",
        status: "in_progress",
        stage: "passenger_on_board",
        type: "transfer",
        driver: {
          name: "Sarah Johnson",
          phone: "+****************"
        }
      },
      {
        id: "T4",
        vehicle: "Cadillac Escalade",
        passengerCount: 5,
        pickupLocation: "Palace of Fine Arts",
        dropoffLocation: "Chinatown Gate",
        time: "02:30 PM",
        status: "in_progress",
        stage: "dropped_off",
        type: "transfer",
        driver: {
          name: "David Lee",
          phone: "+****************"
        }
      },
      {
        id: "T5",
        vehicle: "Lincoln Navigator",
        passengerCount: 6,
        pickupLocation: "Oracle Park",
        dropoffLocation: "California Academy of Sciences",
        time: "03:00 PM",
        status: "in_progress",
        type: "transfer",
        driver: {
          name: "Emily Chen",
          phone: "+****************"
        }
      },
      // Keep existing scheduled and completed trips
      {
        id: "T6",
        vehicle: "BMW 7 Series",
        passengerCount: 3,
        pickupLocation: "Moscone Center",
        dropoffLocation: "San Francisco International Airport (SFO)",
        time: "05:00 PM",
        status: "scheduled",
        type: "departure",
        driver: {
          name: "Sarah Johnson",
          phone: "+****************"
        }
      }
    ],
    quotes: [
      {
        id: "Q1",
        vehicle: "Mercedes Sprinter",
        passengerCount: 8,
        pickupLocation: "San Francisco International Airport (SFO)",
        dropoffLocation: "Moscone Center",
        date: "2024-03-15",
        time: "10:00 AM",
        status: "pending",
        price: {
          baseRate: 180,
          gratuity: 36,
          adminFee: 15,
          total: 231
        },
        notes: "WiFi required for all vehicles"
      },
      {
        id: "Q2",
        vehicle: "Tesla Model Y",
        passengerCount: 4,
        pickupLocation: "Union Square",
        dropoffLocation: "Golden Gate Bridge Welcome Center",
        date: "2024-03-16",
        time: "02:00 PM",
        status: "rate_requested",
        price: {
          baseRate: 120,
          gratuity: 24,
          adminFee: 15,
          total: 159
        }
      },
      {
        id: "Q3",
        vehicle: "Motor Coach",
        passengerCount: 45,
        pickupLocation: "Moscone Center",
        dropoffLocation: "SFO Airport",
        date: "2024-03-17",
        time: "03:00 PM",
        status: "quote_ready",
        price: {
          baseRate: 800,
          gratuity: 160,
          adminFee: 50,
          total: 1010
        },
        affiliateRate: 700,
        markup: 15
      },
      {
        id: "Q4",
        vehicle: "Executive Van",
        passengerCount: 12,
        pickupLocation: "Fisherman's Wharf",
        dropoffLocation: "Oracle Park",
        date: "2024-03-16",
        time: "05:00 PM",
        status: "changes_requested",
        price: {
          baseRate: 250,
          gratuity: 50,
          adminFee: 25,
          total: 325
        },
        notes: "Client requested vehicle upgrade"
      }
    ]
  }
}

// Add sorting type
type SortOption = {
  field: 'time' | 'passengerCount' | 'status' | 'type'
  direction: 'asc' | 'desc'
}

interface QuoteCardProps {
  quote: EventData['quotes'][0]
  onAction: (action: string, quoteId: string) => void
}

function QuoteCard({ quote, onAction }: QuoteCardProps) {
  return (
    <Card className="p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <div>
            <div className="font-medium">{quote.vehicle}</div>
            <div className="text-sm text-muted-foreground">{quote.passengerCount} passengers</div>
          </div>
          <Badge variant={
            quote.status === 'pending' ? 'secondary' :
              quote.status === 'rate_requested' ? 'outline' :
                quote.status === 'quote_ready' ? 'default' :
                  quote.status === 'changes_requested' ? 'secondary' :
                    quote.status === 'accepted' ? 'default' :
                      'destructive'
          }>
            {quote.status.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
        <div className="text-lg font-bold">
          ${quote.price.total.toFixed(2)}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div>
          <div className="text-muted-foreground">Pickup</div>
          <div>{quote.pickupLocation}</div>
        </div>
        <div>
          <div className="text-muted-foreground">Dropoff</div>
          <div>{quote.dropoffLocation}</div>
        </div>
        <div>
          <div className="text-muted-foreground">Date</div>
          <div>{quote.date}</div>
        </div>
        <div>
          <div className="text-muted-foreground">Time</div>
          <div>{quote.time}</div>
        </div>
      </div>

      {quote.status === 'quote_ready' && (
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={() => onAction('request_changes', quote.id)}
          >
            Request Changes
          </Button>
          <Button
            variant="outline"
            onClick={() => onAction('reject', quote.id)}
          >
            Reject
          </Button>
          <Button
            onClick={() => onAction('accept', quote.id)}
          >
            Accept
          </Button>
        </div>
      )}

      {quote.notes && (
        <div className="mt-4 text-sm text-muted-foreground">
          <div className="font-medium">Notes</div>
          <div>{quote.notes}</div>
        </div>
      )}
    </Card>
  )
}

interface TripCardProps {
  trip: EventData['trips'][0]
  onClick: () => void
  isSelected: boolean
}

function TripCard({ trip, onClick, isSelected }: TripCardProps) {
  return (
    <Card
      className={cn(
        "p-4 cursor-pointer transition-all hover:shadow-md",
        isSelected && "ring-2 ring-primary"
      )}
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <div>
            <div className="font-medium">{trip.vehicle}</div>
            <div className="text-sm text-muted-foreground">{trip.passengerCount} passengers</div>
          </div>
          <Badge variant={
            trip.status === 'scheduled' ? 'secondary' :
              trip.status === 'in_progress' ? 'default' :
                trip.status === 'completed' ? 'outline' :
                  'destructive'
          }>
            {trip.status.replace('_', ' ').toUpperCase()}
          </Badge>
          <Badge variant="outline">
            {trip.type.toUpperCase()}
          </Badge>
        </div>
        <div className="text-sm text-muted-foreground">
          {trip.time}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div>
          <div className="text-muted-foreground">Pickup</div>
          <div>{trip.pickupLocation}</div>
        </div>
        <div>
          <div className="text-muted-foreground">Dropoff</div>
          <div>{trip.dropoffLocation}</div>
        </div>
      </div>

      {trip.driver && (
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarFallback>{trip.driver.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{trip.driver.name}</div>
              <div className="text-muted-foreground">{trip.driver.phone}</div>
            </div>
          </div>
          <Button variant="ghost" size="sm">
            <Phone className="h-4 w-4 mr-2" />
            Contact
          </Button>
        </div>
      )}

      {trip.notes && (
        <div className="mt-4 text-sm text-muted-foreground">
          <div className="font-medium">Notes</div>
          <div>{trip.notes}</div>
        </div>
      )}

      {trip.passengers && trip.passengers.length > 0 && (
        <div className="mt-4">
          <div className="text-sm font-medium mb-2">Passengers</div>
          <div className="flex -space-x-2">
            {trip.passengers.map((passenger) => (
              <Avatar key={passenger.id} className="h-6 w-6 border-2 border-background">
                <AvatarFallback>{passenger.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
            ))}
          </div>
        </div>
      )}
    </Card>
  )
}

// Add new types
interface EventCoordinator {
  id: string
  name: string
  email: string
  phone: string
  role: "primary" | "secondary"
  assignedTrips: string[]
  lastActive?: string
  isOnline?: boolean
}

interface EventAlert {
  id: string
  type: "high" | "medium" | "low"
  message: string
  timestamp: string
  category: "vip" | "schedule" | "traffic" | "weather"
  relatedTrips?: string[]
}

// Add mock data
const mockCoordinators: EventCoordinator[] = [
  {
    id: "c1",
    name: "John Smith",
    email: "<EMAIL>",
    phone: "+****************",
    role: "primary",
    assignedTrips: ["T1", "T2", "T3"],
    lastActive: new Date().toISOString(),
    isOnline: true
  },
  {
    id: "c2",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+****************",
    role: "secondary",
    assignedTrips: ["T4", "T5"],
    lastActive: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 mins ago
    isOnline: false
  }
]

const mockAlerts: EventAlert[] = [
  {
    id: "a1",
    type: "high",
    message: "VIP arrival delayed - John Smith's flight ETA updated to 10:30 AM",
    timestamp: new Date().toISOString(),
    category: "vip",
    relatedTrips: ["T1"]
  },
  {
    id: "a2",
    type: "medium",
    message: "Heavy traffic reported on route to Moscone Center",
    timestamp: new Date().toISOString(),
    category: "traffic",
    relatedTrips: ["T2", "T3"]
  },
  {
    id: "a3",
    type: "low",
    message: "Light rain expected around venue area",
    timestamp: new Date().toISOString(),
    category: "weather"
  }
]

export default function EventDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const eventId = params.id as string
  const [event, setEvent] = useState<EventData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedTrips, setSelectedTrips] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState("scheduled")
  const [mapViewport, setMapViewport] = useState({
    latitude: 37.7749,
    longitude: -122.4194,
    zoom: 11,
    bearing: 0,
    pitch: 0,
    padding: { top: 0, bottom: 0, left: 0, right: 0 }
  })
  const [routeData, setRouteData] = useState<RouteData | null>(null)
  const [selectedTrip, setSelectedTrip] = useState<EventData['trips'][0] | null>(null)
  const [tripDetailsOpen, setTripDetailsOpen] = useState(false)
  const [selectedTripType, setSelectedTripType] = useState<string>("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [isMapLoading, setIsMapLoading] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [tripFilters, setTripFilters] = useState({
    timeRange: "all", // all, today, tomorrow, thisWeek
    passengerCount: "all", // all, 1-4, 5-8, 9+
    hasDriver: "all" // all, assigned, unassigned
  })
  const [sortConfig, setSortConfig] = useState<SortOption>({ field: 'time', direction: 'asc' })
  const [hoveredTripId, setHoveredTripId] = useState<string | null>(null)
  const [quoteSearchTerm, setQuoteSearchTerm] = useState("")
  const [selectedLiveTrips, setSelectedLiveTrips] = useState<string[]>([])
  const [liveSearchTerm, setLiveSearchTerm] = useState("")
  const [pinnedTrips, setPinnedTrips] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<"list" | "map">("map")
  const [showPassengerDetails, setShowPassengerDetails] = useState<string | null>(null)
  const [timelineView, setTimelineView] = useState<"day" | "week" | "month">("week")
  const [expandedEvents, setExpandedEvents] = useState<string[]>([])
  const [expandedQuoteId, setExpandedQuoteId] = useState<string | null>(null);
  const [searchPassenger, setSearchPassenger] = useState("")
  const [coordinators] = useState<EventCoordinator[]>(mockCoordinators)
  const [alerts] = useState<EventAlert[]>(mockAlerts)
  const [showAlerts, setShowAlerts] = useState(true)
  const [messageDialogOpen, setMessageDialogOpen] = useState(false)
  const [selectedCoordinator, setSelectedCoordinator] = useState<EventCoordinator | null>(null)

  // Add this near the top of the component
  const getQuoteAction = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Review Quote';
      case 'rate_requested':
        return 'View Rate Request';
      case 'quote_ready':
        return 'Review & Accept';
      case 'changes_requested':
        return 'View Requested Changes';
      default:
        return 'View Details';
    }
  }

  // Add the modal component
  const QuoteDetailsModal = ({ quote, isOpen }: { quote: EventData['quotes'][0], isOpen: boolean }) => {
    if (!quote) return null;

    return (
      <Dialog open={isOpen} onOpenChange={() => setExpandedQuoteId(null)}>
        <DialogContent className="max-w-3xl">
          {/* Header with Event Name and Date */}
          <div className="flex items-center gap-2 mb-6">
            <div>
              <h2 className="text-xl font-semibold flex items-center gap-2">
                {event?.title} <span className="text-muted-foreground">•</span>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>{quote.date}</span>
                </div>
              </h2>
            </div>
            <div className="ml-auto flex items-center gap-2">
              <Badge variant="secondary" className="text-yellow-600 bg-yellow-100">Pending Quote</Badge>
              <Badge variant="outline">VIP</Badge>
              <Badge variant="secondary" className="bg-red-100 text-red-800">high priority</Badge>
            </div>
          </div>

          {/* Passenger Section */}
          <div className="mb-6">
            <div className="text-sm text-muted-foreground">Passenger</div>
            <div className="font-medium">Emma Wilson</div>
          </div>

          {/* Vehicle and Passenger Count */}
          <div className="flex items-center gap-2 mb-4">
            <CarFront className="h-4 w-4 text-muted-foreground" />
            <span>{quote.vehicleDetails?.type || quote.vehicle}</span>
            <span className="text-muted-foreground">•</span>
            <Users className="h-4 w-4 text-muted-foreground" />
            <span>{quote.passengerCount} passengers</span>
          </div>

          {/* Route */}
          <div className="flex items-center gap-2 text-muted-foreground mb-8">
            <MapPin className="h-4 w-4" />
            <span>{quote.pickupLocation} → {quote.dropoffLocation}</span>
          </div>

          <div className="grid grid-cols-2 gap-8">
            {/* Left Column */}
            <div>
              {/* Timeline */}
              <div className="mb-8">
                <h3 className="font-medium mb-4">Timeline</h3>
                <div className="space-y-6">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-primary mt-2" />
                    <div>
                      <div className="font-medium">Quote Created</div>
                      <div className="text-sm text-muted-foreground">
                        {quote.date} by Emma Wilson
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Initial quote request submitted
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-primary mt-2" />
                    <div>
                      <div className="font-medium">Rate Requested</div>
                      <div className="text-sm text-muted-foreground">
                        {quote.date} by System Manager
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Rate request sent to preferred affiliates
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div>
              {/* Communications */}
              <div className="mb-8">
                <h3 className="font-medium mb-4">Communications</h3>
                <div className="space-y-4">
                  <div>
                    <div className="font-medium">System Manager</div>
                    <div className="text-sm text-muted-foreground">
                      {quote.date}
                    </div>
                    <div className="text-sm mt-1">
                      Quote received. Processing your request.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Cancellation Policy and Terms */}
          <div className="mb-8 p-4 border rounded-lg bg-muted/30">
            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">Cancellation Policy</h3>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>Free cancellation up to 48 hours before pickup</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    <span>50% charge for cancellations within 48 hours</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <X className="h-4 w-4" />
                    <span>100% charge for no-shows or same-day cancellations</span>
                  </div>
                </div>
              </div>
              <Separator />
              <div className="text-sm">
                By accepting this quote, you agree to our{" "}
                <Link href="/terms" className="text-primary hover:underline">
                  Terms & Conditions
                </Link>
              </div>
            </div>
          </div>

          {/* Total Amount */}
          <div>
            <h3 className="font-medium mb-4">Total Amount</h3>
            <div className="flex justify-between items-start">
              <div>
                <div className="text-2xl font-bold mb-2">$231</div>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <div>Base Fare: $180</div>
                  <div>Additional Services: $15</div>
                  <div>Gratuity: $36</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline">Request Changes</Button>
                <Button variant="destructive">Reject</Button>
                <Button>Accept Quote</Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        const data = await getEventDetails(eventId)
        setEvent(data)
      } catch (error) {
        console.error("Failed to fetch event:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchEvent()
  }, [eventId])

  // Mock live trips data - in real app this would be updated via websocket/polling
  const liveTrips = event?.trips.filter(trip => trip.status === "in_progress") || []

  const filteredLiveTrips = liveTrips.filter(trip => {
    const matchesSearch =
      trip.pickupLocation.toLowerCase().includes(liveSearchTerm.toLowerCase()) ||
      trip.dropoffLocation.toLowerCase().includes(liveSearchTerm.toLowerCase()) ||
      trip.vehicle.toLowerCase().includes(liveSearchTerm.toLowerCase()) ||
      (trip.driver?.name.toLowerCase() || "").includes(liveSearchTerm.toLowerCase())
    return matchesSearch
  })

  const handleLiveTripSelect = (tripId: string) => {
    setSelectedLiveTrips(prev => {
      if (prev.includes(tripId)) {
        return prev.filter(id => id !== tripId)
      }
      return [...prev, tripId]
    })
  }

  // Enhanced trip filtering
  const getFilteredTrips = () => {
    if (!event?.trips) return []

    return event.trips.filter(trip => {
      const matchesSearch =
        trip.pickupLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trip.dropoffLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trip.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trip.driver?.name.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesType = selectedTripType === "all" || trip.type === selectedTripType
      const matchesStatus = statusFilter === "all" || trip.status === statusFilter

      // Time range filtering
      const tripDate = new Date(trip.time)
      const today = new Date()
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)
      const weekEnd = new Date(today)
      weekEnd.setDate(weekEnd.getDate() + 7)

      const matchesTimeRange = tripFilters.timeRange === "all" ||
        (tripFilters.timeRange === "today" && tripDate.toDateString() === today.toDateString()) ||
        (tripFilters.timeRange === "tomorrow" && tripDate.toDateString() === tomorrow.toDateString()) ||
        (tripFilters.timeRange === "thisWeek" && tripDate <= weekEnd)

      // Passenger count filtering
      const matchesPassengerCount = tripFilters.passengerCount === "all" ||
        (tripFilters.passengerCount === "1-4" && trip.passengerCount <= 4) ||
        (tripFilters.passengerCount === "5-8" && trip.passengerCount > 4 && trip.passengerCount <= 8) ||
        (tripFilters.passengerCount === "9+" && trip.passengerCount > 8)

      // Driver assignment filtering
      const matchesDriverStatus = tripFilters.hasDriver === "all" ||
        (tripFilters.hasDriver === "assigned" && trip.driver) ||
        (tripFilters.hasDriver === "unassigned" && !trip.driver)

      return matchesSearch && matchesType && matchesStatus &&
        matchesTimeRange && matchesPassengerCount && matchesDriverStatus
    })
  }

  // Add sorting function
  const getSortedTrips = (trips: EventData['trips']) => {
    return [...trips].sort((a, b) => {
      switch (sortConfig.field) {
        case 'time':
          return sortConfig.direction === 'asc'
            ? new Date(a.time).getTime() - new Date(b.time).getTime()
            : new Date(b.time).getTime() - new Date(a.time).getTime()
        case 'passengerCount':
          return sortConfig.direction === 'asc'
            ? a.passengerCount - b.passengerCount
            : b.passengerCount - a.passengerCount
        case 'status':
          return sortConfig.direction === 'asc'
            ? a.status.localeCompare(b.status)
            : b.status.localeCompare(a.status)
        case 'type':
          return sortConfig.direction === 'asc'
            ? a.type.localeCompare(b.type)
            : b.type.localeCompare(a.type)
        default:
          return 0
      }
    })
  }

  // Update filtered trips to include sorting
  const filteredAndSortedTrips = getSortedTrips(getFilteredTrips())

  // Handle map marker click
  const handleMarkerClick = (tripId: string) => {
    const trip = event?.trips.find(t => t.id === tripId)
    if (trip) {
      setSelectedTrip(trip)
      setTripDetailsOpen(true)
    }
  }

  // Update map effect with loading state
  useEffect(() => {
    if (selectedTrip) {
      setIsMapLoading(true)
      const pickup = getCoordinates(selectedTrip.pickupLocation)
      const dropoff = getCoordinates(selectedTrip.dropoffLocation)

      // Center the map between pickup and dropoff
      setMapViewport(prev => ({
        ...prev,
        latitude: (pickup.lat + dropoff.lat) / 2,
        longitude: (pickup.lng + dropoff.lng) / 2,
        zoom: 11
      }))

      // Create a route line between points
      setRouteData({
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates: [
            [pickup.lng, pickup.lat],
            [dropoff.lng, dropoff.lat]
          ]
        }
      })
      setIsMapLoading(false)
    }
  }, [selectedTrip])

  // Bulk actions
  const handleBulkAction = (action: string) => {
    switch (action) {
      case "export":
        console.log("Exporting selected trips:", selectedTrips)
        break
      case "print":
        console.log("Printing selected trips:", selectedTrips)
        break
      case "email":
        console.log("Emailing selected trips:", selectedTrips)
        break
      case "clone":
        console.log("Cloning selected trips:", selectedTrips)
        break
      default:
        break
    }
  }

  // Update the TabsContent for pending quotes
  const handleQuoteAction = (action: string, quoteId?: string) => {
    // Implementation of handleQuoteAction
  }

  const fitMapToSelectedTrips = () => {
    if (selectedLiveTrips.length === 0) return;

    const selectedTripsData = filteredLiveTrips.filter(trip => selectedLiveTrips.includes(trip.id));
    const coordinates: [number, number][] = [];

    selectedTripsData.forEach(trip => {
      const pickup = getCoordinates(trip.pickupLocation);
      const dropoff = getCoordinates(trip.dropoffLocation);
      coordinates.push([pickup.lng, pickup.lat]);
      coordinates.push([dropoff.lng, dropoff.lat]);
    });

    // Calculate bounds
    const lngs = coordinates.map(coord => coord[0]);
    const lats = coordinates.map(coord => coord[1]);
    const minLng = Math.min(...lngs);
    const maxLng = Math.max(...lngs);
    const minLat = Math.min(...lats);
    const maxLat = Math.max(...lats);

    // Add padding
    const padding = 0.1;
    const newViewport = {
      ...mapViewport,
      latitude: (minLat + maxLat) / 2,
      longitude: (minLng + maxLng) / 2,
      zoom: Math.min(
        Math.log2(360 / (maxLng - minLng + padding)) - 1,
        Math.log2(180 / (maxLat - minLat + padding)) - 1,
        11 // max zoom level
      )
    };

    setMapViewport(newViewport);
  };

  // Update useEffect to call fitMapToSelectedTrips when selected trips change
  useEffect(() => {
    fitMapToSelectedTrips();
  }, [selectedLiveTrips]);

  const handlePinTrip = (tripId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    setPinnedTrips(prev => {
      if (prev.includes(tripId)) {
        return prev.filter(id => id !== tripId)
      }
      return [...prev, tripId]
    })
  }

  const sortedLiveTrips = [...filteredLiveTrips].sort((a, b) => {
    const aIsPinned = pinnedTrips.includes(a.id)
    const bIsPinned = pinnedTrips.includes(b.id)
    if (aIsPinned && !bIsPinned) return -1
    if (!aIsPinned && bIsPinned) return 1
    return 0
  })

  if (isLoading) {
    return (
      <div className="container space-y-6 p-6">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/events">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div className="h-6 w-48 animate-pulse bg-muted rounded" />
        </div>
        <div className="h-[600px] animate-pulse bg-muted rounded-lg" />
      </div>
    )
  }

  if (!event) {
    return (
      <div className="container space-y-6 p-6">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/events">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Event Not Found</h2>
        </div>
        <Card>
          <CardContent className="p-6">
            <p className="text-muted-foreground">The requested event could not be found.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container space-y-4 p-6">
      {/* Header - More compact with event info inline */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold">{event?.title || "Tech Conference 2024"}</h1>
          <div className="flex items-center gap-2 text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>3/15/2024 - 3/17/2024</span>
            <span>•</span>
            <MapPin className="h-4 w-4" />
            <span>Moscone Center, San Francisco</span>
            <span>•</span>
            <Users className="h-4 w-4" />
            <span>150 passengers</span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Link href={`/customer/events/${event?.id}/quotes`}>
            <Button variant="outline" size="sm">
              <FileText className="h-4 w-4 mr-2" />
              View Event Quotes
            </Button>
          </Link>
          <Link href={`/customer/events/${event?.id}/quotes/new`}>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Request New Quote
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Row */}
      <div className="grid grid-cols-5 gap-4 mb-6">
        <Card className="p-4">
          <CardTitle className="text-sm font-medium mb-2">Transportation Coverage</CardTitle>
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold">85%</div>
            <div className="text-sm text-muted-foreground">120/140 guests</div>
          </div>
          <Progress value={85} className="mt-2" />
        </Card>

        <Card className="p-4">
          <CardTitle className="text-sm font-medium mb-2">Arrival Transfers</CardTitle>
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold">8/10</div>
            <div className="text-sm text-green-500">Confirmed</div>
          </div>
          <Progress value={80} className="mt-2" />
        </Card>

        <Card className="p-4">
          <CardTitle className="text-sm font-medium mb-2">Departure Transfers</CardTitle>
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold">6/8</div>
            <div className="text-sm text-green-500">Confirmed</div>
          </div>
          <Progress value={75} className="mt-2" />
        </Card>

        <Card className="p-4">
          <CardTitle className="text-sm font-medium mb-2">Planning Gaps</CardTitle>
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold text-yellow-500">2</div>
            <div className="text-sm text-yellow-500">VIP arrivals need transportation</div>
          </div>
        </Card>

        <Card className="p-4">
          <CardTitle className="text-sm font-medium mb-2">Event Coordinators</CardTitle>
          <div className="space-y-2">
            {coordinators.map((coordinator) => (
              <div key={coordinator.id} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Avatar className="h-6 w-6">
                      <div className="bg-primary/10 h-full w-full flex items-center justify-center text-xs font-medium">
                        {coordinator.name.split(' ').map(n => n[0]).join('')}
                      </div>
                    </Avatar>
                    {coordinator.isOnline && (
                      <div className="absolute -bottom-0.5 -right-0.5 h-2 w-2 rounded-full bg-green-500 ring-1 ring-white" />
                    )}
                  </div>
                  <Link
                    href={`/customer/events/${event?.id}/coordinators/${coordinator.id}`}
                    className="text-sm hover:underline"
                  >
                    {coordinator.name}
                  </Link>
                </div>
                <Link
                  href={`/customer/events/${event?.id}/coordinators/${coordinator.id}/trips`}
                  className="text-xs text-muted-foreground hover:underline"
                >
                  {coordinator.assignedTrips.length} trips
                </Link>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Event Timeline Section */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Event Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            {/* View Toggle */}
            <div className="bg-secondary rounded-lg p-1">
              <Button
                variant={timelineView === "day" ? "default" : "ghost"}
                size="sm"
                onClick={() => setTimelineView("day")}
                className="h-8"
              >
                Day
              </Button>
              <Button
                variant={timelineView === "week" ? "default" : "ghost"}
                size="sm"
                onClick={() => setTimelineView("week")}
                className="h-8"
              >
                Week
              </Button>
              <Button
                variant={timelineView === "month" ? "default" : "ghost"}
                size="sm"
                onClick={() => setTimelineView("month")}
                className="h-8"
              >
                Month
              </Button>
            </div>
            {/* Status Legend */}
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-green-500" />
                <span className="text-sm">Completed</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-blue-500" />
                <span className="text-sm">In Progress</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-yellow-500" />
                <span className="text-sm">Pending</span>
              </div>
            </div>
          </div>

          {/* Timeline Content */}
          <div className="space-y-4">
            <div className="relative min-h-[400px]">
              {/* Time header */}
              <div className="flex ml-[300px] mb-4">
                {Array.from({ length: 24 }).map((_, i) => (
                  <div key={i} className="flex-1 text-center text-xs text-muted-foreground">
                    {i.toString().padStart(2, '0')}:00
                  </div>
                ))}
              </div>

              {/* Grid lines */}
              <div className="absolute inset-0 ml-[300px] grid grid-cols-24 gap-0">
                {Array.from({ length: 24 }).map((_, i) => (
                  <div key={i} className="border-l border-gray-100 h-full" />
                ))}
              </div>

              {/* Trip rows */}
              <div className="relative space-y-2">
                {event?.trips.map((trip) => {
                  const startHour = parseInt(trip.time.split(':')[0])
                  const startMinute = parseInt(trip.time.split(':')[1]) || 0
                  const duration = trip.type === 'transfer' ? 1.5 : 2
                  const startPercentage = (startHour + startMinute / 60) / 24 * 100
                  const widthPercentage = (duration / 24) * 100

                  return (
                    <div key={trip.id} className="flex items-center h-16 relative">
                      {/* Trip details on the left */}
                      <div className="w-[300px] pr-4 flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className={cn(
                            "h-2 w-2 rounded-full shrink-0",
                            trip.status === 'completed' ? 'bg-green-500' :
                              trip.status === 'in_progress' ? 'bg-blue-500' :
                                trip.status === 'scheduled' ? 'bg-emerald-500' :
                                  'bg-gray-500'
                          )} />
                          <div>
                            <div className="text-sm font-medium">
                              {trip.passengers?.[0]?.name || 'No Passenger'}
                            </div>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <CarFront className="h-3 w-3" />
                              <span>{trip.vehicle}</span>
                              <span>•</span>
                              <span>{trip.time}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground text-right">
                          {trip.pickupLocation.split('(')[0]} →<br />
                          {trip.dropoffLocation.split('(')[0]}
                        </div>
                      </div>

                      {/* Trip block on the timeline */}
                      <div
                        className={cn(
                          "absolute h-12 rounded-lg border flex items-center px-3 cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]",
                          trip.status === 'completed' ? 'bg-green-500/10 border-green-500' :
                            trip.status === 'in_progress' ? 'bg-blue-500/10 border-blue-500' :
                              trip.status === 'scheduled' ? 'bg-emerald-500/10 border-emerald-500' :
                                'bg-gray-500/10 border-gray-500'
                        )}
                        style={{
                          left: `calc(${startPercentage}% + 300px)`,
                          width: `${widthPercentage}%`
                        }}
                        onClick={() => {
                          setSelectedTrip(trip)
                          setTripDetailsOpen(true)
                        }}
                      >
                        <div className="flex items-center gap-2 z-10 w-full min-w-0">
                          <div className="truncate text-sm">
                            {trip.passengers?.[0]?.name || 'No Passenger'} • {trip.passengerCount} passengers
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Keep only the Alerts Panel */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
            <Button variant="ghost" size="sm" onClick={() => setShowAlerts(!showAlerts)}>
              <Bell className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        {showAlerts && (
          <CardContent>
            <ScrollArea className="h-[120px]">
              <div className="space-y-3">
                {alerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`p-3 rounded-lg border ${alert.type === "high"
                      ? "bg-red-50/50 border-red-200"
                      : alert.type === "medium"
                        ? "bg-yellow-50/50 border-yellow-200"
                        : "bg-blue-50/50 border-blue-200"
                      }`}
                  >
                    <div className="flex items-start gap-2">
                      {alert.category === "vip" ? (
                        <Shield className="h-4 w-4 text-red-500 mt-0.5" />
                      ) : alert.category === "traffic" ? (
                        <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5" />
                      ) : alert.category === "weather" ? (
                        <AlertTriangle className="h-4 w-4 text-blue-500 mt-0.5" />
                      ) : (
                        <Clock4 className="h-4 w-4 text-gray-500 mt-0.5" />
                      )}
                      <div>
                        <p className="text-sm">{alert.message}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-muted-foreground">
                            {new Date(alert.timestamp).toLocaleTimeString()}
                          </span>
                          {alert.relatedTrips && (
                            <Badge variant="secondary" className="text-xs">
                              {alert.relatedTrips.length} affected trips
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        )}
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="live" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="live">Live Trips</TabsTrigger>
          <TabsTrigger value="quotes">Quotes</TabsTrigger>
          <TabsTrigger value="trips">Scheduled Trips</TabsTrigger>
        </TabsList>

        {/* Live Trips Tab Content */}
        <TabsContent value="live">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Active Live Trips</CardTitle>
                <div className="flex items-center gap-2">
                  <div className="bg-secondary p-1 rounded-lg flex items-center gap-1">
                    <Button
                      variant={viewMode === "map" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("map")}
                      className="h-8"
                    >
                      <MapIcon className="h-4 w-4 mr-2" />
                      Map
                    </Button>
                    <Button
                      variant={viewMode === "list" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("list")}
                      className="h-8"
                    >
                      <LayoutGrid className="h-4 w-4 mr-2" />
                      List
                    </Button>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {viewMode === "list" ? (
                // List View
                <div className="space-y-4">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search live trips..."
                      className="pl-8"
                      value={liveSearchTerm}
                      onChange={(e) => setLiveSearchTerm(e.target.value)}
                    />
                  </div>
                  <div className="space-y-4">
                    {sortedLiveTrips.map((trip) => (
                      <Card
                        key={trip.id}
                        className={cn(
                          "transition-colors",
                          selectedLiveTrips.includes(trip.id) ? "bg-muted/50 ring-2 ring-primary" : "hover:bg-muted/50"
                        )}
                      >
                        <CardContent className="p-4">
                          <div className="space-y-4">
                            {/* Header */}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4">
                                <Checkbox
                                  checked={selectedLiveTrips.includes(trip.id)}
                                  onCheckedChange={() => handleLiveTripSelect(trip.id)}
                                />
                                <div>
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">{trip.passengers?.[0]?.name.split(' ')[1] || 'No'} {trip.passengers?.[0]?.name.split(' ')[0]?.[0] || 'P'}.</span>
                                    <span className="text-sm text-muted-foreground">• {trip.vehicle}</span>
                                    {trip.passengerCount > 1 && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="relative"
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          setShowPassengerDetails(showPassengerDetails === trip.id ? null : trip.id)
                                        }}
                                      >
                                        <Users className="h-4 w-4 mr-1" />
                                        +{trip.passengerCount - 1}
                                        {showPassengerDetails === trip.id && (
                                          <div className="absolute top-full left-0 mt-2 w-64 p-3 bg-popover rounded-lg shadow-lg border z-50">
                                            <div className="space-y-2">
                                              <h4 className="font-medium text-sm">All Passengers</h4>
                                              {trip.passengers?.map((passenger, index) => (
                                                <div key={passenger.id} className="flex items-center gap-2 text-sm">
                                                  <Avatar className="h-6 w-6">
                                                    <AvatarFallback>{passenger.name.split(' ')[0][0]}{passenger.name.split(' ')[1][0]}</AvatarFallback>
                                                  </Avatar>
                                                  <div>
                                                    <div className="font-medium">{passenger.name}</div>
                                                    {passenger.group && (
                                                      <div className="text-xs text-muted-foreground">{passenger.group}</div>
                                                    )}
                                                  </div>
                                                </div>
                                              ))}
                                            </div>
                                          </div>
                                        )}
                                      </Button>
                                    )}
                                  </div>
                                  {trip.driver && (
                                    <div className="text-sm text-muted-foreground">
                                      Driver: {trip.driver.name} • {trip.driver.phone}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge
                                  variant={
                                    trip.stage === "on_the_way" ? "default" :
                                      trip.stage === "waiting_for_passenger" ? "secondary" :
                                        trip.stage === "passenger_on_board" ? "default" : "outline"
                                  }
                                >
                                  {trip.stage?.replace(/_/g, " ").toUpperCase()}
                                </Badge>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className={pinnedTrips.includes(trip.id) ? "text-primary" : ""}
                                  onClick={(e) => handlePinTrip(trip.id, e)}
                                >
                                  <Pin className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>

                            {/* Progress Bar */}
                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <div className="flex items-center gap-2">
                                  <div className="flex items-center gap-1">
                                    <span className="font-medium">A</span>
                                    <span className="text-muted-foreground truncate">
                                      {trip.pickupLocation} • {trip.passengers?.[0]?.name.split(' ')[1] || 'No'} {trip.passengers?.[0]?.name.split(' ')[0]?.[0] || 'P'}.
                                    </span>
                                  </div>
                                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                                  <div className="flex items-center gap-1">
                                    <span className="font-medium">B</span>
                                    <span className="text-muted-foreground truncate">
                                      {trip.dropoffLocation} • {trip.passengers?.[0]?.name.split(' ')[1] || 'No'} {trip.passengers?.[0]?.name.split(' ')[0]?.[0] || 'P'}.
                                    </span>
                                  </div>
                                </div>
                                <div className="text-muted-foreground">
                                  ETA: {trip.stage === "on_the_way" ? "10 mins" :
                                    trip.stage === "waiting_for_passenger" ? "Arrived" :
                                      trip.stage === "passenger_on_board" ? "15 mins" : "Completed"}
                                </div>
                              </div>
                              <div className="h-2 bg-secondary rounded-full overflow-hidden">
                                <div
                                  className={cn(
                                    "h-full rounded-full transition-all",
                                    trip.stage === "on_the_way" ? "bg-blue-500 w-[30%]" :
                                      trip.stage === "waiting_for_passenger" ? "bg-yellow-500 w-[50%]" :
                                        trip.stage === "passenger_on_board" ? "bg-green-500 w-[75%]" :
                                          "bg-purple-500 w-full"
                                  )}
                                />
                              </div>
                            </div>

                            {/* Quick Actions */}
                            <div className="flex items-center justify-between">
                              <div className="text-sm text-muted-foreground">
                                Started at {trip.time}
                              </div>
                              <div className="flex items-center gap-2">
                                <Button variant="outline" size="sm">
                                  <Phone className="h-4 w-4 mr-2" />
                                  Call Driver
                                </Button>
                                <Button variant="outline" size="sm">
                                  <Mail className="h-4 w-4 mr-2" />
                                  Message
                                </Button>
                                <Button variant="outline" size="sm">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ) : (
                // Map View (restored original layout)
                <div className="grid gap-4 md:grid-cols-[350px,1fr]">
                  {/* Live Trip List */}
                  <div className="space-y-4">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search live trips..."
                        className="pl-8"
                        value={liveSearchTerm}
                        onChange={(e) => setLiveSearchTerm(e.target.value)}
                      />
                    </div>
                    <div className="space-y-1 max-h-[500px] overflow-y-auto pr-2">
                      {sortedLiveTrips.map((trip) => (
                        <Card
                          key={trip.id}
                          className={`${selectedLiveTrips.includes(trip.id)
                            ? 'bg-muted/50 ring-2 ring-primary'
                            : 'hover:bg-muted/50'
                            } cursor-pointer transition-colors`}
                          onClick={() => handleLiveTripSelect(trip.id)}
                        >
                          <CardContent className="p-2">
                            <div className="flex items-center gap-2">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-2">
                                  <div className="flex items-center gap-2">
                                    <div className="relative group">
                                      <div className={cn(
                                        "px-2 py-0.5 rounded text-xs font-medium text-white",
                                        getTripStageColor(trip.stage || "on_the_way")
                                      )}>
                                        {getTripStageAbbr(trip.stage || "on_the_way")}
                                      </div>
                                      <div className="absolute left-0 -bottom-8 hidden group-hover:block bg-popover text-popover-foreground text-xs p-1 rounded shadow-lg whitespace-nowrap">
                                        {trip.stage?.replace(/_/g, ' ').toUpperCase()}
                                      </div>
                                    </div>
                                    <div>
                                      <div className="font-medium text-sm">
                                        {trip.vehicle}
                                      </div>
                                      <div className="text-xs text-muted-foreground truncate">
                                        {trip.pickupLocation.split('(')[0]} → {trip.dropoffLocation.split('(')[0]}
                                      </div>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    {trip.driver && (
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-6 w-6"
                                        onClick={(e) => {
                                          e.stopPropagation()
                                        }}
                                      >
                                        <Phone className="h-3 w-3" />
                                      </Button>
                                    )}
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className={`h-6 w-6 ${pinnedTrips.includes(trip.id) ? 'text-primary' : ''}`}
                                      onClick={(e) => handlePinTrip(trip.id, e)}
                                    >
                                      <Pin className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                                <div className="grid gap-1 text-xs leading-snug">
                                  <div className="flex items-center gap-1.5">
                                    <span className="font-medium text-green-600">A</span>
                                    <span className="text-muted-foreground truncate">
                                      {trip.pickupLocation} • {trip.passengers?.[0]?.name.split(' ')[1] || 'No'} {trip.passengers?.[0]?.name.split(' ')[0]?.[0] || 'P'}.
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-1.5">
                                    <span className="font-medium text-red-600">B</span>
                                    <span className="text-muted-foreground truncate">
                                      {trip.dropoffLocation} • {trip.passengers?.[0]?.name.split(' ')[1] || 'No'} {trip.passengers?.[0]?.name.split(' ')[0]?.[0] || 'P'}.
                                    </span>
                                  </div>
                                  <div className="flex justify-end text-xs text-muted-foreground">
                                    ETA: {trip.stage === 'on_the_way' ? '10 mins' :
                                      trip.stage === 'waiting_for_passenger' ? 'Arrived' :
                                        trip.stage === 'passenger_on_board' ? '15 mins' : 'Completed'}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  {/* Live Map */}
                  <div className="h-[500px] rounded-lg overflow-hidden">
                    <Map
                      {...mapViewport}
                      onMove={evt => setMapViewport(evt.viewState)}
                      style={{ width: '100%', height: '100%' }}
                      mapStyle="mapbox://styles/mapbox/light-v11"
                      mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_TOKEN}
                    >
                      {/* Map Controls */}
                      <NavigationControl position="top-right" />
                      <FullscreenControl position="top-right" />
                      <ScaleControl position="bottom-right" />

                      {/* Route Line */}
                      {routeData && (
                        <Source type="geojson" data={routeData}>
                          <Layer
                            id="route"
                            type="line"
                            paint={{
                              'line-color': '#4B83E8',
                              'line-width': 3,
                              'line-dasharray': [2, 1]
                            }}
                          />
                        </Source>
                      )}

                      {/* Trip Markers */}
                      {sortedLiveTrips.map((trip) => {
                        const pickup = getCoordinates(trip.pickupLocation)
                        const dropoff = getCoordinates(trip.dropoffLocation)

                        // Calculate vehicle position based on trip stage
                        const progress = trip.stage === 'on_the_way' ? 0.3 :
                          trip.stage === 'waiting_for_passenger' ? 0 :
                            trip.stage === 'passenger_on_board' ? 0.7 : 1

                        const vehiclePosition = {
                          lat: pickup.lat + (dropoff.lat - pickup.lat) * progress,
                          lng: pickup.lng + (dropoff.lng - pickup.lng) * progress
                        }

                        return (
                          <Fragment key={trip.id}>
                            {/* Vehicle Position */}
                            <Marker
                              longitude={vehiclePosition.lng}
                              latitude={vehiclePosition.lat}
                            >
                              <div className="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-lg" />
                            </Marker>

                            {/* Pickup Point */}
                            <Marker longitude={pickup.lng} latitude={pickup.lat}>
                              <div className="flex items-center gap-2">
                                <div className="bg-green-500 text-white px-2 py-1 rounded shadow-lg text-sm font-medium">
                                  A
                                </div>
                                <div className="bg-white px-2 py-1 rounded shadow-lg text-sm">
                                  {trip.passengers?.[0]?.name.split(' ')[1] || 'No'} {trip.passengers?.[0]?.name.split(' ')[0]?.[0] || 'P'}.
                                </div>
                              </div>
                            </Marker>

                            {/* Dropoff Point */}
                            <Marker longitude={dropoff.lng} latitude={dropoff.lat}>
                              <div className="flex items-center gap-2">
                                <div className="bg-red-500 text-white px-2 py-1 rounded shadow-lg text-sm font-medium">
                                  B
                                </div>
                                <div className="bg-white px-2 py-1 rounded shadow-lg text-sm">
                                  {trip.passengers?.[0]?.name.split(' ')[1] || 'No'} {trip.passengers?.[0]?.name.split(' ')[0]?.[0] || 'P'}.
                                </div>
                              </div>
                            </Marker>
                          </Fragment>
                        )
                      })}
                    </Map>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Quotes Tab */}
        <TabsContent value="quotes">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle>Transportation Overview</CardTitle>
                  <p className="text-sm text-muted-foreground">Plan and track transportation for your event schedule</p>
                </div>
                <div className="mt-4 p-4 bg-muted/50 rounded-lg flex items-start gap-4">
                  <AlertCircle className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">
                      This view is for planning purposes. To manage quotes (approve, reject, or request changes), please visit the
                      <Link href="/quotes" className="font-medium text-primary hover:underline ml-1">
                        Quotes Management page
                      </Link>
                    </p>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Enhanced Filters Section */}
              <div className="flex flex-wrap gap-4 mb-8">
                {/* Search by Passenger Name */}
                <div className="w-[250px] relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search passenger name..."
                    className="pl-8"
                    value={searchPassenger}
                    onChange={(e) => setSearchPassenger(e.target.value)}
                  />
                </div>

                {/* Date Filter */}
                <div className="flex items-center space-x-2">
                  <Select defaultValue="all-days">
                    <SelectTrigger className="w-[200px]">
                      <Calendar className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="View Schedule By Day" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-days">Full Event Schedule</SelectItem>
                      <SelectItem value="day-1">Day 1 - Guest Arrivals</SelectItem>
                      <SelectItem value="day-2">Day 2 - Event Day</SelectItem>
                      <SelectItem value="day-3">Day 3 - Departures</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Guest Groups Filter */}
                <div className="flex items-center space-x-2">
                  <Select defaultValue="all-groups">
                    <SelectTrigger className="w-[160px]">
                      <Users className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="Guest Groups" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-groups">All Groups</SelectItem>
                      <SelectItem value="vip">VIP Guests (6)</SelectItem>
                      <SelectItem value="staff">Staff (12)</SelectItem>
                      <SelectItem value="attendees">Attendees (122)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Status Filter */}
                <div className="flex items-center space-x-2">
                  <Select defaultValue="all-statuses">
                    <SelectTrigger className="w-[160px]">
                      <AlertCircle className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="Quote Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-statuses">All Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="accepted">Accepted</SelectItem>
                      <SelectItem value="rate_requested">Rate Requested</SelectItem>
                      <SelectItem value="changes_requested">Changes Requested</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Sort Options */}
                <div className="flex items-center space-x-2">
                  <Select defaultValue="pickup-time">
                    <SelectTrigger className="w-[160px]">
                      <ArrowUpDown className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="Sort By" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pickup-time">Pickup Time</SelectItem>
                      <SelectItem value="passenger-name">Passenger Name</SelectItem>
                      <SelectItem value="price">Price</SelectItem>
                      <SelectItem value="status">Status</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Timeline View with Event Context */}
              <div className="space-y-8">
                {/* Day Section */}
                <div>
                  <div className="flex items-center gap-3 mb-6">
                    <div className="bg-primary/10 text-primary font-medium px-4 py-2 rounded-full">
                      March 15, 2024
                    </div>
                    <Badge variant="outline" className="text-base px-3 py-1">Guest Arrivals</Badge>
                    <div className="text-sm text-muted-foreground">8 transfers scheduled</div>
                  </div>

                  <div className="space-y-4 pl-4 border-l-2 border-gray-100">
                    {event.quotes.map(quote => (
                      <div key={quote.id} className="relative">
                        <div className={`absolute -left-[25px] top-3 w-4 h-4 rounded-full border-2 ${quote.status === 'accepted' ? 'border-green-500 bg-green-100' :
                          quote.status === 'pending' ? 'border-yellow-500 bg-yellow-100' :
                            quote.status === 'rate_requested' ? 'border-blue-500 bg-blue-100' :
                              quote.status === 'changes_requested' ? 'border-orange-500 bg-orange-100' :
                                quote.status === 'rejected' ? 'border-red-500 bg-red-100' :
                                  'border-gray-500 bg-gray-100'
                          }`} />
                        <Card className={cn(
                          "ml-6",
                          quote.status === 'accepted' ? 'bg-green-50' :
                            quote.status === 'pending' ? 'bg-yellow-50' :
                              quote.status === 'rate_requested' ? 'bg-blue-50' :
                                quote.status === 'changes_requested' ? 'bg-orange-50' :
                                  quote.status === 'rejected' ? 'bg-red-50' :
                                    'bg-gray-50'
                        )}>
                          <div className="p-4">
                            {/* Rest of the quote card content remains unchanged */}
                            <div className="flex items-center justify-between mb-4">
                              <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                  <div className="text-lg font-semibold">John Smith</div>
                                  <Badge>VIP</Badge>
                                </div>
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <CarFront className="h-4 w-4" />
                                  <span>{quote.vehicleDetails?.type || quote.vehicle}</span>
                                  <span>•</span>
                                  <Users className="h-4 w-4" />
                                  <span>{quote.passengerCount} passengers</span>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-lg font-bold">${quote.amount || quote.price?.total || 0}</div>
                                <Badge
                                  variant="outline"
                                  className={cn(
                                    "cursor-pointer transition-colors hover:bg-secondary",
                                    quote.status === 'accepted' ? 'text-green-600 hover:text-green-700' :
                                      quote.status === 'pending' ? 'text-yellow-600 hover:text-yellow-700' :
                                        'text-gray-600 hover:text-gray-700'
                                  )}
                                >
                                  {quote.status === 'accepted' ? 'Confirmed' :
                                    quote.status === 'pending' ? 'Pending Confirmation' :
                                      quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
                                </Badge>
                              </div>
                            </div>

                            {/* Schedule and Route Details */}
                            <div className="grid grid-cols-[auto,1fr] gap-x-6 gap-y-3 text-sm">
                              <div className="text-muted-foreground">Schedule</div>
                              <div className="space-y-1">
                                <div className="font-medium">{quote.pickupTime ?
                                  new Date(quote.pickupTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) :
                                  quote.time
                                }</div>
                                <div className="text-xs text-muted-foreground">
                                  {quote.eventRelation || '2 hours before Opening Ceremony'}
                                </div>
                              </div>

                              <div className="text-muted-foreground">Route</div>
                              <div className="font-medium">
                                {quote.pickupLocation} → {quote.dropoffLocation}
                              </div>

                              <div className="text-muted-foreground">Related</div>
                              <div className="flex items-center gap-2">
                                {quote.relatedQuotes?.map((related, index) => (
                                  <Button key={index} variant="outline" size="sm" className="h-7">
                                    View {related.type || 'return'} transfer
                                  </Button>
                                )) || (
                                    <Button variant="outline" size="sm" className="h-7">
                                      View return transfer
                                    </Button>
                                  )}
                              </div>
                            </div>

                            {/* Action Button */}
                            <div className="mt-4 flex justify-end">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-primary hover:text-primary/80 hover:bg-primary/10 transition-colors"
                                onClick={() => setExpandedQuoteId(quote.id)}
                              >
                                {getQuoteAction(quote.status)}
                                <ArrowRight className="ml-2 h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </Card>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scheduled Trips Tab */}
        <TabsContent value="trips">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle>Scheduled Trips</CardTitle>
                  <p className="text-sm text-muted-foreground">View and manage all scheduled trips for this event</p>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Search and Filter Row */}
                <div className="flex items-center gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Search trips..." className="pl-8" />
                  </div>
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Trip Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="arrival">Airport Arrivals</SelectItem>
                      <SelectItem value="departure">Airport Departures</SelectItem>
                      <SelectItem value="transfer">Hotel Transfers</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Trip List */}
                <div className="space-y-4">
                  {event?.trips?.map((trip) => (
                    <div key={trip.id} className="border rounded-lg">
                      {/* Main Row - Always Visible */}
                      <div className="p-4">
                        {/* City, Date, Event Row */}
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="text-lg font-semibold">New York City</h3>
                              <span className="text-sm text-muted-foreground">2024-03-15</span>
                            </div>
                            <div className="text-sm text-muted-foreground">Annual Conference 2024</div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setExpandedEvents(prev =>
                              prev.includes(trip.id)
                                ? prev.filter(id => id !== trip.id)
                                : [...prev, trip.id]
                            )}
                          >
                            <ChevronRight className={cn(
                              "h-4 w-4 transition-transform",
                              expandedEvents.includes(trip.id) && "transform rotate-90"
                            )} />
                          </Button>
                        </div>

                        {/* Passenger Info Row */}
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">Alice Johnson</span>
                            <Crown className="h-4 w-4 text-yellow-500" />
                            <Badge variant="outline" className="ml-2">VVIP</Badge>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <Car className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm text-muted-foreground">Luxury SUV</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm text-muted-foreground">3 passengers</span>
                            </div>
                          </div>
                        </div>

                        {/* Time and Location Row */}
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">14:00</span>
                          </div>
                          <div className="flex items-center gap-2 flex-1">
                            <div className="h-6 w-6 rounded-full bg-green-100 flex items-center justify-center">
                              <span className="text-green-700 text-sm font-medium">A</span>
                            </div>
                            <span className="text-sm text-muted-foreground">JFK Airport Terminal 4</span>
                            <div className="w-12 h-px bg-muted-foreground/30 mx-2" />
                            <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                              <span className="text-blue-700 text-sm font-medium">B</span>
                            </div>
                            <span className="text-sm text-muted-foreground">Marriott Hotel Manhattan</span>
                          </div>
                        </div>

                        {/* Status and Actions Row */}
                        <div className="flex items-center justify-between mt-4">
                          <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-0">Scheduled</Badge>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="sm">
                              <Phone className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MessageSquare className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MapPin className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Expandable Area */}
                      {expandedEvents.includes(trip.id) && (
                        <div className="border-t bg-muted/30">
                          <div className="p-4">
                            <Tabs defaultValue="timeline">
                              <TabsList>
                                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                                <TabsTrigger value="passengers">Passengers</TabsTrigger>
                                <TabsTrigger value="driver">Driver & Vehicle</TabsTrigger>
                                <TabsTrigger value="live-map">Live Map</TabsTrigger>
                                <TabsTrigger value="security">Security</TabsTrigger>
                                <TabsTrigger value="admin">Admin</TabsTrigger>
                              </TabsList>

                              {/* Timeline Tab Content */}
                              <TabsContent value="timeline" className="mt-4">
                                <div className="space-y-4">
                                  <div className="flex items-center gap-2">
                                    <div className="h-2 w-2 rounded-full bg-green-500" />
                                    <span className="text-sm font-medium">Pickup</span>
                                    <span className="text-sm text-muted-foreground">JFK Airport Terminal 4</span>
                                    <span className="text-sm font-medium ml-auto">14:00</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <div className="h-2 w-2 rounded-full bg-blue-500" />
                                    <span className="text-sm font-medium">Dropoff</span>
                                    <span className="text-sm text-muted-foreground">Marriott Hotel Manhattan</span>
                                    <span className="text-sm font-medium ml-auto">15:30</span>
                                  </div>
                                </div>
                                <div className="mt-4 grid grid-cols-4 gap-4">
                                  <div>
                                    <div className="text-sm font-medium">Duration</div>
                                    <div className="text-sm text-muted-foreground">90 min</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">Distance</div>
                                    <div className="text-sm text-muted-foreground">12.5 km</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">Status</div>
                                    <div className="text-sm text-green-600">On Time</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">Weather</div>
                                    <div className="text-sm text-muted-foreground">Clear Sky</div>
                                  </div>
                                </div>
                                <div className="mt-4">
                                  <div className="text-sm font-medium mb-2">Active Alerts</div>
                                  <div className="space-y-2">
                                    <div className="flex items-center gap-2 text-sm text-blue-600">
                                      <AlertCircle className="h-4 w-4" />
                                      <span>Driver en route to pickup location</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm text-blue-600">
                                      <CloudRain className="h-4 w-4" />
                                      <span>Light rain expected</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm text-orange-600">
                                      <AlertTriangle className="h-4 w-4" />
                                      <span>Moderate traffic on route</span>
                                    </div>
                                  </div>
                                </div>
                              </TabsContent>

                              {/* Passengers Tab Content */}
                              <TabsContent value="passengers" className="mt-4">
                                <div className="space-y-6">
                                  <div className="flex items-center justify-between">
                                    <div className="space-y-1">
                                      <h4 className="text-sm font-medium">Passenger List</h4>
                                      <p className="text-sm text-muted-foreground">Total: 3 passengers</p>
                                    </div>
                                    <Button variant="outline" size="sm">
                                      <UserPlus className="h-4 w-4 mr-2" />
                                      Add Passenger
                                    </Button>
                                  </div>
                                  <div className="space-y-4">
                                    {trip.passengers?.map((passenger) => (
                                      <div key={passenger.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex items-center gap-4">
                                          <Avatar>
                                            <AvatarFallback>{passenger.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                                          </Avatar>
                                          <div>
                                            <div className="flex items-center gap-2">
                                              <span className="font-medium">{passenger.name}</span>
                                              {passenger.isVIP && (
                                                <Badge variant="secondary" className="bg-yellow-100 text-yellow-700 border-0">VIP</Badge>
                                              )}
                                            </div>
                                            <div className="text-sm text-muted-foreground">{passenger.group || 'No group assigned'}</div>
                                          </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          {passenger.phone && (
                                            <Button variant="ghost" size="sm">
                                              <Phone className="h-4 w-4" />
                                            </Button>
                                          )}
                                          {passenger.email && (
                                            <Button variant="ghost" size="sm">
                                              <Mail className="h-4 w-4" />
                                            </Button>
                                          )}
                                          <Button variant="ghost" size="sm">
                                            <MoreVertical className="h-4 w-4" />
                                          </Button>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </TabsContent>

                              {/* Driver & Vehicle Tab Content */}
                              <TabsContent value="driver" className="mt-4">
                                <div className="space-y-6">
                                  <div className="grid grid-cols-2 gap-6">
                                    {/* Driver Information */}
                                    <div className="space-y-4">
                                      <h4 className="text-sm font-medium">Driver Information</h4>
                                      {trip.driver ? (
                                        <div className="p-4 border rounded-lg">
                                          <div className="flex items-center gap-4 mb-4">
                                            <Avatar>
                                              <AvatarFallback>{trip.driver.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                                            </Avatar>
                                            <div>
                                              <div className="font-medium">{trip.driver.name}</div>
                                              <div className="text-sm text-muted-foreground">{trip.driver.phone}</div>
                                            </div>
                                          </div>
                                          <div className="space-y-2">
                                            <div className="flex items-center gap-2">
                                              <Badge variant="outline" className="bg-green-100 text-green-700 border-0">Available</Badge>
                                              <Badge variant="outline">4.9 ★</Badge>
                                            </div>
                                            <div className="text-sm text-muted-foreground">Last active: 5 minutes ago</div>
                                          </div>
                                        </div>
                                      ) : (
                                        <div className="p-4 border rounded-lg">
                                          <div className="text-sm text-muted-foreground">No driver assigned</div>
                                          <Button variant="outline" size="sm" className="mt-2">
                                            <UserPlus className="h-4 w-4 mr-2" />
                                            Assign Driver
                                          </Button>
                                        </div>
                                      )}
                                    </div>

                                    {/* Vehicle Information */}
                                    <div className="space-y-4">
                                      <h4 className="text-sm font-medium">Vehicle Information</h4>
                                      <div className="p-4 border rounded-lg">
                                        <div className="flex items-center gap-4 mb-4">
                                          <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                                            <Car className="h-6 w-6 text-primary" />
                                          </div>
                                          <div>
                                            <div className="font-medium">{trip.vehicle}</div>
                                            <div className="text-sm text-muted-foreground">License: NYC-123</div>
                                          </div>
                                        </div>
                                        <div className="space-y-2">
                                          <div className="grid grid-cols-2 gap-2 text-sm">
                                            <div>
                                              <span className="text-muted-foreground">Capacity:</span>
                                              <span className="ml-2">4 passengers</span>
                                            </div>
                                            <div>
                                              <span className="text-muted-foreground">Type:</span>
                                              <span className="ml-2">Luxury SUV</span>
                                            </div>
                                            <div>
                                              <span className="text-muted-foreground">Color:</span>
                                              <span className="ml-2">Black</span>
                                            </div>
                                            <div>
                                              <span className="text-muted-foreground">Year:</span>
                                              <span className="ml-2">2023</span>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </TabsContent>

                              {/* Live Map Tab Content */}
                              <TabsContent value="live-map" className="mt-4">
                                <div className="space-y-4">
                                  <div className="h-[400px] rounded-lg border relative">
                                    <Map
                                      initialViewState={{
                                        latitude: 40.7128,
                                        longitude: -74.0060,
                                        zoom: 12
                                      }}
                                      style={{ width: '100%', height: '100%', borderRadius: '8px' }}
                                      mapStyle="mapbox://styles/mapbox/streets-v11"
                                    >
                                      <NavigationControl position="top-right" />
                                      <Marker latitude={40.7128} longitude={-74.0060}>
                                        <div className="h-4 w-4 bg-primary rounded-full ring-4 ring-primary/20" />
                                      </Marker>
                                      {/* Add route line and other markers here */}
                                    </Map>
                                    <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg">
                                      <div className="text-sm font-medium">ETA: 15 minutes</div>
                                      <div className="text-xs text-muted-foreground">Current speed: 35 mph</div>
                                    </div>
                                  </div>
                                </div>
                              </TabsContent>

                              {/* Security Tab Content */}
                              <TabsContent value="security" className="mt-4">
                                <div className="space-y-6">
                                  <div className="grid grid-cols-2 gap-6">
                                    {/* Security Requirements */}
                                    <div className="space-y-4">
                                      <h4 className="text-sm font-medium">Security Requirements</h4>
                                      <div className="p-4 border rounded-lg space-y-4">
                                        <div className="flex items-center gap-2">
                                          <Shield className="h-4 w-4 text-green-500" />
                                          <span className="text-sm">Background Check Verified</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <Shield className="h-4 w-4 text-green-500" />
                                          <span className="text-sm">Insurance Documentation Complete</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <Shield className="h-4 w-4 text-green-500" />
                                          <span className="text-sm">Vehicle Safety Inspection Passed</span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Security Protocols */}
                                    <div className="space-y-4">
                                      <h4 className="text-sm font-medium">Security Protocols</h4>
                                      <div className="p-4 border rounded-lg space-y-4">
                                        <div className="flex items-center gap-2">
                                          <Badge variant="outline">VIP Protocol Active</Badge>
                                        </div>
                                        <div className="text-sm text-muted-foreground">
                                          <ul className="list-disc list-inside space-y-2">
                                            <li>Secure route planning completed</li>
                                            <li>Backup vehicle on standby</li>
                                            <li>Direct communication line established</li>
                                          </ul>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </TabsContent>

                              {/* Admin Tab Content */}
                              <TabsContent value="admin" className="mt-4">
                                <div className="space-y-6">
                                  {/* Trip Details */}
                                  <div className="space-y-4">
                                    <h4 className="text-sm font-medium">Administrative Details</h4>
                                    <div className="grid grid-cols-3 gap-4">
                                      <div className="p-4 border rounded-lg">
                                        <div className="text-sm font-medium mb-2">Trip ID</div>
                                        <div className="text-sm text-muted-foreground">#{trip.id}</div>
                                      </div>
                                      <div className="p-4 border rounded-lg">
                                        <div className="text-sm font-medium mb-2">Created By</div>
                                        <div className="text-sm text-muted-foreground">John Admin</div>
                                      </div>
                                      <div className="p-4 border rounded-lg">
                                        <div className="text-sm font-medium mb-2">Last Modified</div>
                                        <div className="text-sm text-muted-foreground">2024-03-14 09:30 AM</div>
                                      </div>
                                    </div>
                                  </div>

                                  {/* Activity Log */}
                                  <div className="space-y-4">
                                    <h4 className="text-sm font-medium">Activity Log</h4>
                                    <div className="border rounded-lg divide-y">
                                      <div className="p-3">
                                        <div className="flex items-center justify-between">
                                          <div className="text-sm">Trip created</div>
                                          <div className="text-xs text-muted-foreground">2024-03-14 09:00 AM</div>
                                        </div>
                                      </div>
                                      <div className="p-3">
                                        <div className="flex items-center justify-between">
                                          <div className="text-sm">Driver assigned</div>
                                          <div className="text-xs text-muted-foreground">2024-03-14 09:15 AM</div>
                                        </div>
                                      </div>
                                      <div className="p-3">
                                        <div className="flex items-center justify-between">
                                          <div className="text-sm">Vehicle details updated</div>
                                          <div className="text-xs text-muted-foreground">2024-03-14 09:30 AM</div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  {/* Administrative Actions */}
                                  <div className="flex items-center justify-end gap-2">
                                    <Button variant="outline" size="sm">
                                      <Printer className="h-4 w-4 mr-2" />
                                      Print Details
                                    </Button>
                                    <Button variant="outline" size="sm">
                                      <Copy className="h-4 w-4 mr-2" />
                                      Duplicate Trip
                                    </Button>
                                    <Button variant="destructive" size="sm">
                                      <X className="h-4 w-4 mr-2" />
                                      Cancel Trip
                                    </Button>
                                  </div>
                                </div>
                              </TabsContent>
                            </Tabs>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Trip Details Dialog */}
      <Dialog open={tripDetailsOpen} onOpenChange={setTripDetailsOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Trip Details</DialogTitle>
            <DialogDescription>
              View and manage trip information
            </DialogDescription>
          </DialogHeader>
          {selectedTrip && (
            <div className="space-y-6">
              {/* Status Section */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Current Status</p>
                  <Badge
                    variant={
                      selectedTrip?.status === "completed"
                        ? "default"
                        : selectedTrip?.status === "in_progress"
                          ? "secondary"
                          : selectedTrip?.status === "cancelled"
                            ? "destructive"
                            : "outline"
                    }
                  >
                    {selectedTrip?.status?.replace("_", " ").toUpperCase()}
                  </Badge>
                </div>
                <Select defaultValue={selectedTrip?.status}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Update status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Tabs defaultValue="details" className="w-full">
                <TabsList>
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="assignment">Assignment</TabsTrigger>
                  <TabsTrigger value="passengers">Passengers</TabsTrigger>
                  <TabsTrigger value="pricing">Pricing</TabsTrigger>
                </TabsList>

                <TabsContent value="details" className="space-y-4">
                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <h4 className="font-medium">Route Information</h4>
                      <div className="grid gap-2">
                        <div className="flex items-center text-sm">
                          <MapPin className="h-4 w-4 mr-2 text-green-500" />
                          <span>Pickup: {selectedTrip?.pickupLocation}</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <MapPin className="h-4 w-4 mr-2 text-red-500" />
                          <span>Dropoff: {selectedTrip?.dropoffLocation}</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Clock className="h-4 w-4 mr-2" />
                          <span>Time: {selectedTrip?.time}</span>
                        </div>
                      </div>
                    </div>
                    {selectedTrip?.notes && (
                      <div className="space-y-2">
                        <h4 className="font-medium">Notes</h4>
                        <p className="text-sm text-muted-foreground">{selectedTrip?.notes}</p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="assignment" className="space-y-4">
                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <h4 className="font-medium">Vehicle Assignment</h4>
                      <Select defaultValue={selectedTrip?.vehicle}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select vehicle" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableVehicles.map(vehicle => (
                            <SelectItem key={vehicle.id} value={vehicle.name}>
                              {vehicle.name} ({vehicle.capacity} passengers)
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium">Driver Assignment</h4>
                      <Select defaultValue={selectedTrip?.driver?.name}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select driver" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableDrivers.map(driver => (
                            <SelectItem key={driver.id} value={driver.name}>
                              {driver.name} - {driver.vehicle}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="passengers" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Assigned Passengers ({selectedTrip?.passengerCount || 0})</h4>
                    <Button variant="outline" size="sm">
                      <UserPlus className="h-4 w-4 mr-2" />
                      Add Passengers
                    </Button>
                  </div>
                  <div className="border rounded-lg divide-y">
                    {selectedTrip?.passengers ? (
                      selectedTrip.passengers.map((passenger, i) => (
                        <div key={passenger.id} className="p-3 flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback>{passenger.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="text-sm font-medium">{passenger.name}</p>
                              {passenger.group && (
                                <p className="text-xs text-muted-foreground">{passenger.group}</p>
                              )}
                            </div>
                          </div>
                          <Button variant="ghost" size="sm">
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-sm text-muted-foreground">
                        No passengers assigned yet
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="pricing" className="space-y-4">
                  <div className="space-y-4">
                    <div className="grid gap-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Base Rate</span>
                        <span>$180.00</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Gratuity (20%)</span>
                        <span>$36.00</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Admin Fee</span>
                        <span>$15.00</span>
                      </div>
                      <Separator />
                      <div className="flex items-center justify-between font-medium">
                        <span>Total</span>
                        <span>$231.00</span>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex items-center justify-end gap-2">
                <Button variant="outline" onClick={() => setTripDetailsOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setTripDetailsOpen(false)}>
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Render the modal */}
      {expandedQuoteId && event && (
        <QuoteDetailsModal
          quote={event.quotes.find(q => q.id === expandedQuoteId)!}
          isOpen={!!expandedQuoteId}
        />
      )}
    </div>
  )
} 