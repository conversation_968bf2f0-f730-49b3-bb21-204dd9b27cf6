"use client"

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/app/components/ui/tabs";
import { But<PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { CalendarDays, Car, CheckCircle2, Filter, FilterX, List, Search, Map } from "lucide-react";
import { Badge } from "@/app/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Card, CardHeader, CardTitle, CardContent } from "@/app/components/ui/card";
import { TripRow, TripRowData } from "@/app/components/shared/rows";
import { useToast } from "@/app/components/ui/use-toast";
import { useRouter } from "next/navigation";

export default function CustomerTripsPage() {
  const [trips, setTrips] = useState<TripRowData[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTrip, setSelectedTrip] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredTrips, setFilteredTrips] = useState<{
    all: TripRowData[];
    upcoming: TripRowData[];
    active: TripRowData[];
    completed: TripRowData[];
    cancelled: TripRowData[];
  }>({
    all: [],
    upcoming: [],
    active: [],
    completed: [],
    cancelled: []
  })
  
  const { toast } = useToast()
  const router = useRouter()
  
  // Fetch trips data
  useEffect(() => {
    const fetchTrips = async () => {
      try {
        setLoading(true)
        
        const response = await fetch('/api/customer/trips')
        
        if (!response.ok) {
          throw new Error(`Error: ${response.statusText}`)
        }
        
        const data = await response.json()
        
        if (data && Array.isArray(data.trips)) {
          // Map the API data to TripRowData format
          const mappedTrips: TripRowData[] = data.trips.map((trip: any) => ({
            id: trip.id,
            reference_number: trip.reference_number,
            pickup_location: trip.pickup_location || 'N/A',
            dropoff_location: trip.dropoff_location || 'N/A',
            date: trip.date,
            time: trip.time,
            vehicle_type: trip.vehicle_type || 'Standard',
            status: trip.status,
            price: trip.total_amount,
            passenger_count: trip.passenger_count,
            luggage_count: trip.luggage_count,
            special_requests: trip.special_requests,
            priority: trip.priority || 'medium',
            city: trip.city,
            created_at: trip.created_at,
            driver: trip.driver ? {
              name: trip.driver.name,
              phone: trip.driver.phone,
              rating: trip.driver.rating
            } : undefined,
            eta: trip.eta,
            progress_percentage: trip.progress_percentage || 0,
            last_updated: trip.updated_at
          }))
          
          setTrips(mappedTrips)
        } else {
          setTrips([])
        }
      } catch (err) {
        console.error('Error fetching trips:', err)
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }
    
    fetchTrips()
  }, [])
  
  // Filter trips
  useEffect(() => {
    const now = new Date()
    
    const filtered = {
      all: trips.filter(trip => filterBySearch(trip)),
      upcoming: trips.filter(trip => 
        filterBySearch(trip) && 
        ['scheduled', 'confirmed'].includes(trip.status.toLowerCase()) &&
        new Date(trip.date) > now
      ),
      active: trips.filter(trip => 
        filterBySearch(trip) && 
        ['in_progress', 'on_the_way', 'en_route', 'on_location', 'pax_on_board'].includes(trip.status.toLowerCase())
      ),
      completed: trips.filter(trip => 
        filterBySearch(trip) && 
        trip.status.toLowerCase() === 'completed'
      ),
      cancelled: trips.filter(trip => 
        filterBySearch(trip) && 
        trip.status.toLowerCase() === 'cancelled'
      )
    }
    
    setFilteredTrips(filtered)
  }, [trips, searchTerm])
  
  // Search filter function
  const filterBySearch = (trip: TripRowData) => {
    if (!searchTerm) return true
    
    const search = searchTerm.toLowerCase()
    return (
      (trip.pickup_location.toLowerCase().includes(search)) ||
      (trip.dropoff_location.toLowerCase().includes(search)) ||
      (trip.vehicle_type.toLowerCase().includes(search)) ||
      (trip.reference_number && trip.reference_number.toLowerCase().includes(search)) ||
      (trip.driver?.name && trip.driver.name.toLowerCase().includes(search))
    )
  }
  
  // Trip action handlers
  const handleTripSelection = (trip: TripRowData) => {
    setSelectedTrip(trip.id === selectedTrip ? null : trip.id)
    router.push(`/customer/trips/${trip.id}`)
  }
  
  const handleCallDriver = (tripId: string) => {
    const trip = trips.find(t => t.id === tripId)
    if (!trip?.driver?.phone) {
      toast({
        title: "Cannot call driver",
        description: "Driver phone number not available",
        variant: "destructive"
      })
      return
    }
    
    // In a real app, this would initiate a call or redirect to the phone app
    window.location.href = `tel:${trip.driver.phone}`
  }
  
  const handleMessageDriver = (tripId: string) => {
    router.push(`/customer/trips/${tripId}/message`)
  }
  
  const handleTrackTrip = (tripId: string) => {
    router.push(`/customer/trips/${tripId}/track`)
  }
  
  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }
  
  // Error state
  if (error) {
    return (
      <div className="p-8">
        <div className="bg-destructive/10 p-4 rounded-md text-destructive">
          <p>Error loading trips: {error}</p>
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()} 
            className="mt-2"
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }
  
  return (
    <div className="p-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">My Trips</h1>
          <p className="text-muted-foreground">
            View and manage your upcoming and past trips
          </p>
        </div>
      </div>
      
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Trips</CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredTrips.upcoming.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Trips</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredTrips.active.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredTrips.completed.length}</div>
          </CardContent>
        </Card>
      </div>
      
      {/* Search & Filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search trips..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Date Filter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Dates</SelectItem>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
          </SelectContent>
        </Select>
        
        {searchTerm && (
          <Button variant="outline" onClick={() => setSearchTerm('')}>
            <FilterX className="mr-2 h-4 w-4" />
            Clear
          </Button>
        )}
      </div>
      
      {/* Trips List */}
      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">
            All <Badge variant="secondary" className="ml-2">{filteredTrips.all.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="upcoming">
            Upcoming <Badge variant="secondary" className="ml-2">{filteredTrips.upcoming.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="active">
            Active <Badge variant="secondary" className="ml-2">{filteredTrips.active.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="completed">
            Completed <Badge variant="secondary" className="ml-2">{filteredTrips.completed.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="cancelled">
            Cancelled <Badge variant="secondary" className="ml-2">{filteredTrips.cancelled.length}</Badge>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="space-y-2 mt-6">
          {filteredTrips.all.length === 0 ? (
            <div className="text-center p-4 border rounded-md">
              <p className="text-muted-foreground">No trips found</p>
            </div>
          ) : (
            filteredTrips.all.map((trip) => (
              <TripRow
                key={trip.id}
                trip={trip}
                isSelected={selectedTrip === trip.id}
                onClick={() => handleTripSelection(trip)}
                onCall={() => handleCallDriver(trip.id)}
                onMessage={() => handleMessageDriver(trip.id)}
                onTrack={() => handleTrackTrip(trip.id)}
                userType="customer"
                expandable={true}
              />
            ))
          )}
        </TabsContent>
        
        <TabsContent value="upcoming" className="space-y-2 mt-6">
          {filteredTrips.upcoming.length === 0 ? (
            <div className="text-center p-4 border rounded-md">
              <p className="text-muted-foreground">No upcoming trips found</p>
            </div>
          ) : (
            filteredTrips.upcoming.map((trip) => (
              <TripRow
                key={trip.id}
                trip={trip}
                isSelected={selectedTrip === trip.id}
                onClick={() => handleTripSelection(trip)}
                onCall={() => handleCallDriver(trip.id)}
                onMessage={() => handleMessageDriver(trip.id)}
                onTrack={() => handleTrackTrip(trip.id)}
                userType="customer"
                expandable={true}
              />
            ))
          )}
        </TabsContent>
        
        <TabsContent value="active" className="space-y-2 mt-6">
          {filteredTrips.active.length === 0 ? (
            <div className="text-center p-4 border rounded-md">
              <p className="text-muted-foreground">No active trips found</p>
            </div>
          ) : (
            filteredTrips.active.map((trip) => (
              <TripRow
                key={trip.id}
                trip={trip}
                isSelected={selectedTrip === trip.id}
                onClick={() => handleTripSelection(trip)}
                onCall={() => handleCallDriver(trip.id)}
                onMessage={() => handleMessageDriver(trip.id)}
                onTrack={() => handleTrackTrip(trip.id)}
                userType="customer"
                expandable={true}
              />
            ))
          )}
        </TabsContent>
        
        <TabsContent value="completed" className="space-y-2 mt-6">
          {filteredTrips.completed.length === 0 ? (
            <div className="text-center p-4 border rounded-md">
              <p className="text-muted-foreground">No completed trips found</p>
            </div>
          ) : (
            filteredTrips.completed.map((trip) => (
              <TripRow
                key={trip.id}
                trip={trip}
                isSelected={selectedTrip === trip.id}
                onClick={() => handleTripSelection(trip)}
                userType="customer"
                expandable={true}
              />
            ))
          )}
        </TabsContent>
        
        <TabsContent value="cancelled" className="space-y-2 mt-6">
          {filteredTrips.cancelled.length === 0 ? (
            <div className="text-center p-4 border rounded-md">
              <p className="text-muted-foreground">No cancelled trips found</p>
            </div>
          ) : (
            filteredTrips.cancelled.map((trip) => (
              <TripRow
                key={trip.id}
                trip={trip}
                isSelected={selectedTrip === trip.id}
                onClick={() => handleTripSelection(trip)}
                userType="customer"
                expandable={true}
              />
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
} 