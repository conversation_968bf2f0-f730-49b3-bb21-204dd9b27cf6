"use client"

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/auth/context';
import { UserRole } from '@/app/lib/auth/roles';
import { Button } from '@/app/components/ui/button';
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/app/components/ui/card';
import { Badge } from '@/app/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/app/components/ui/avatar';
import { Progress } from '@/app/components/ui/progress';
import { Separator } from '@/app/components/ui/separator';
import { getQuotes } from '@/lib/api/quotes';
import { format } from 'date-fns';
import { ToggleGroup, ToggleGroupItem } from '@/app/components/ui/toggle-group';
import { Input } from '@/app/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { useRouter, useSearchParams } from "next/navigation";
import { getSupabaseClient } from '@/lib/supabase';
import { SupabaseClient } from '@supabase/supabase-js';
import { formatDate } from "@/lib/utils";
import { toast } from "@/app/components/ui/use-toast";
import { QuoteCard } from "@/app/components/quotes/cards/quote-card";
import { Link as LinkIcon, as LinkIcon } from "lucide-react";
