"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card } from "@/app/components/ui/card";
import { ArrowLeft, Link as LinkIcon, as <PERSON>Icon } from "lucide-react";
import Link from "next/link"
import { QuoteForm } from "@/app/components/features/quotes/quote-form";
import { useAuth } from '@/lib/auth/context';
import { UserRole } from '@/app/lib/auth/roles';
export default function NewQuotePage() {
  const { user } = useAuth()
  const userRole = user?.roles?.includes('EVENT_MANAGER' as UserRole) ? 'event_manager' : 'direct_passenger'
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/quotes">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">New Quote</h2>
        </div>
      </div>
      <Card className="p-6">
        <QuoteForm userRole={userRole} />
      </Card>
    </div>
  )
} 