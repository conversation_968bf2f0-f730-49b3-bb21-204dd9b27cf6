"use client"

import React from 'react'
import { useParams } from "next/navigation";
import { mockQuotes } from '@/app/components/features/quotes/mock-data';
import { mockAffiliates } from '@/app/components/features/quotes/mock-affiliates';
import { QuoteActionPanel } from '@/app/components/features/quotes/panels/quote-action-panel';
import { AffiliateSelectionPanel } from '@/app/components/features/quotes/panels/affiliate-selection-panel';
import { FixedOfferPanel } from '@/app/components/features/quotes/panels/fixed-offer-panel';
import { RateRequestPanel } from '@/app/components/features/quotes/panels/rate-request-panel';
import { QuoteRow } from '@/app/components/features/quotes/quote-row';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { toast } from '@/app/components/ui/use-toast';

// Import the shared Quote type correctly
import { type Quote, type Affiliate, type PanelType, type AdminQuoteTab } from '@/app/components/features/quotes/types';
import { AlertCircle, CheckCircle2, DollarSign, Filter, ListFilter, Plus, Timer, Type } from "lucide-react";
import { Badge } from '@/app/components/ui/badge';
import { Button } from '@/app/components/ui/button';
import { QuoteFilters, type QuoteRowData } from "@/app/components/features/quotes/quote-filters";
import { RateRequestsPanel } from "@/app/components/features/quotes/panels/rate-requests-panel";
import { FixedOffersPanel } from "@/app/components/features/quotes/panels/fixed-offers-panel";
import { InProgressPanel } from "@/app/components/features/quotes/panels/in-progress-panel";
import { ConfirmedPanel } from "@/app/components/features/quotes/panels/confirmed-panel";
import { DeclinedPanel } from "@/app/components/features/quotes/panels/declined-panel";
import { useState } from "react";


export default function QuotesPage() {
  const params = useParams()
  const eventId = params.id as string
  const [selectedQuote, setSelectedQuote] = React.useState<Quote | null>(null) // State already correctly typed
  const [activeTab, setActiveTab] = React.useState<AdminQuoteTab>('pending')
  const [activePanel, setActivePanel] = React.useState<PanelType | null>(null)
  const [selectedAffiliate, setSelectedAffiliate] = React.useState<Affiliate | null>(null)
  const [selectedAffiliates, setSelectedAffiliates] = React.useState<string[]>([])

  // Filter quotes for this event, assuming mockQuotes contains Quote-like objects
  const eventQuotes = mockQuotes.filter((quote: Quote) => { // Use Quote type for filtering
    const [eventName, eventNumber] = eventId.split('-')
    // Use optional chaining for safety if special_requests might be missing
    return quote.special_requests?.includes(eventName)
  })

  // Type eventQuotes explicitly if mockQuotes isn't typed
  const typedEventQuotes: Quote[] = eventQuotes as Quote[];

  // Convert Quote[] to QuoteRowData[] using the typed array
  const quotesData: QuoteRowData[] = typedEventQuotes.map((quote) => ({
    id: quote.id,
    pickup_location: quote.pickup_location,
    dropoff_location: quote.dropoff_location,
    // Derive date and time from trip_date
    date: quote.trip_date ? new Date(quote.trip_date).toLocaleDateString() : 'N/A',
    time: quote.trip_date ? new Date(quote.trip_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'N/A',
    vehicle_type: quote.vehicle_type ?? 'N/A', // Use vehicle_type, provide fallback
    status: quote.status,
    price: quote.total_amount
  }))

  // Convert selected quote to QuoteRowData without 'any'
  const selectedQuoteData = selectedQuote ? {
    id: selectedQuote.id,
    pickup_location: selectedQuote.pickup_location,
    dropoff_location: selectedQuote.dropoff_location,
    // Derive date and time from trip_date
    date: selectedQuote.trip_date ? new Date(selectedQuote.trip_date).toLocaleDateString() : 'N/A',
    time: selectedQuote.trip_date ? new Date(selectedQuote.trip_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'N/A',
    vehicle_type: selectedQuote.vehicle_type ?? 'N/A',
    status: selectedQuote.status,
    price: selectedQuote.total_amount
  } as QuoteRowData : null;

  const selectedAffiliatesWithDetails = selectedAffiliates.map(id => 
    mockAffiliates.find(a => a.id === id)
  );

  const handleQuoteClick = (quoteData: QuoteRowData) => {
    // Find the full quote object from the typed list based on the ID
    const fullQuote = typedEventQuotes.find(q => q.id === quoteData.id); // Use typedEventQuotes
    if (fullQuote) {
      setSelectedQuote(fullQuote);
      setActivePanel('action');
    } else {
      console.error("Could not find full quote data for ID:", quoteData.id);
      // Optionally show an error toast to the user
      toast({
        title: 'Error',
        description: 'Could not load quote details.',
        variant: 'destructive' });
    }
  };

  // Removed duplicate handleQuoteClick and @ts-ignore

  const handleClosePanel = () => {
    setSelectedQuote(null);
    setActivePanel(null);
  };

  const handleAffiliateSelect = (affiliateId: string) => {
    const affiliate = mockAffiliates.find(a => a.id === affiliateId);
    if (affiliate) {
      setSelectedAffiliate(affiliate);
      setActivePanel('fixed_offer');
    }
  };

  const handleFixedOfferSubmit = (data: {
    affiliate_id: string;
    base_rate: number;
    markup_percentage: number;
    expiry_hours: number;
  }) => {
    toast({
      title: 'Fixed Offer Submitted',
      description: 'The fixed offer has been sent to the affiliate.'
    });
    setActivePanel(null);
  };

  const handleRateRequestSubmit = (data: any) => {
    toast({
      title: 'Rate Request Submitted',
      description: 'The rate request has been sent to selected affiliates.'
    });
    setActivePanel(null);
  };

  const handleQuoteAction = async (action: string, data?: any) => {
    switch (action) {
      case 'select_affiliate':
        handleAffiliateSelect(data.affiliateId);
        break;

      case 'submit_fixed_offer':
        handleFixedOfferSubmit(data);
        break;

      case 'submit_rate_request':
        handleRateRequestSubmit(data);
        break;

      case 'send_rate_request':
        setActivePanel('rate_request');
        setSelectedAffiliates([]);
        break;

      case 'send_fixed_offer':
        setActivePanel('affiliate');
        break;

      case 'toggle_affiliate':
        if (selectedAffiliates.includes(data.affiliateId)) {
          setSelectedAffiliates(prev => prev.filter(id => id !== data.affiliateId));
        } else {
          setSelectedAffiliates(prev => [...prev, data.affiliateId]);
        }
        break;

      case 'continue_rate_request':
        setActivePanel('rate_request');
        break;

      default:
        console.log('Unhandled action:', action, data);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Event Quotes</h1>
          <p className="text-muted-foreground">Event ID: {params.id}</p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" /> New Quote
        </Button>
      </div>

      <QuoteFilters
        quotes={quotesData} // Add required quotes prop
        selectedQuote={selectedQuoteData} // Add required selectedQuote prop
        onQuoteClick={handleQuoteClick}
        // Remove incorrect props:
        // activeTab={activeTab}
        // onTabChange={setActiveTab}
      />

      {selectedQuote && (
        <>
          {activePanel === 'action' && (
            <QuoteActionPanel
              quote={selectedQuote}
              onClose={handleClosePanel}
              onAction={handleQuoteAction}
              activePanel={activePanel}
            />
          )}
          {activePanel === 'affiliate' && (
            <AffiliateSelectionPanel
              affiliates={mockAffiliates}
              selectedAffiliates={selectedAffiliates.map((id, index) => ({ id, order: index + 1 }))}
              onAffiliateSelect={(affiliateId) => handleQuoteAction('toggle_affiliate', { affiliateId })} // Pass the correct handler
              onClose={handleClosePanel}
              onSelect={(affiliateId) => {
                handleQuoteAction('select_affiliate', { affiliateId });
              }}
            />
          )}
          {activePanel === 'fixed_offer' && selectedAffiliate && (
            <FixedOfferPanel
              selectedAffiliate={selectedAffiliate}
              onClose={handleClosePanel}
              onSubmit={(data) => {
                handleQuoteAction('submit_fixed_offer', data);
              }}
            />
          )}
          {activePanel === 'rate_request' && selectedAffiliates.length > 0 && (
            <RateRequestPanel
              selectedAffiliates={selectedAffiliatesWithDetails.filter(Boolean) as Affiliate[]}
              onClose={handleClosePanel}
              onSubmit={(data) => {
                handleQuoteAction('submit_rate_request', data);
              }}
            />
          )}
          {activeTab === 'rate_requests' && selectedQuote && (
            <RateRequestsPanel
              quote={selectedQuote}
              onClose={handleClosePanel}
              onAction={handleQuoteAction}
            />
          )}
          {activeTab === 'fixed_offers' && selectedQuote && (
            <FixedOffersPanel
              quote={selectedQuote}
              onClose={handleClosePanel}
              onAction={handleQuoteAction}
            />
          )}
          {activeTab === 'in_progress' && selectedQuote && (
            <InProgressPanel
              quote={selectedQuote}
              onClose={handleClosePanel}
              onAction={handleQuoteAction}
            />
          )}
          {activeTab === 'confirmed' && selectedQuote && (
            <ConfirmedPanel
              quote={selectedQuote}
              onClose={handleClosePanel}
              onAction={handleQuoteAction}
            />
          )}
          {activeTab === 'declined' && selectedQuote && (
            <DeclinedPanel
              quote={selectedQuote}
              onClose={handleClosePanel}
              onAction={handleQuoteAction}
            />
          )}
        </>
      )}
    </div>
  )
}