"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card, CardContent } from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { ArrowLeft, Calendar, ChevronDown, FileText, Filter, MapPin, Phone, Plus, Search, Truck, Type, Upload, Users, X, Link as LinkIcon, as LinkIcon } from "lucide-react";
import Link from "next/link"
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod"
import { Textarea } from "@/app/components/ui/textarea";
import { DatePicker } from "@/app/components/ui/date-picker";
import { Badge } from "@/app/components/ui/badge";
import { Avatar, AvatarFallback } from "@/app/components/ui/avatar";
import { useState } from "react";
import { cn } from "@/lib/utils";
const formSchema = z.object({
  name: z.string().min(2, "Event name must be at least 2 characters"),
  type: z.string().min(1, "Event type is required"),
  startDate: z.date({
    required_error: "Start date is required" }),
  endDate: z.date().optional(),
  location: z.string().min(2, "Location must be at least 2 characters"),
  description: z.string().optional(),
  expectedPassengers: z.string().transform((val) => parseInt(val, 10)).refine((val) => val > 0, {
    message: "Number of passengers must be greater than 0" }),
  vehicleTypes: z.array(z.string()).min(1, "At least one vehicle type is required"),
  additionalNotes: z.string().optional() })

const vehicleOptions = [
  { id: "luxury-sedan", label: "Luxury Sedan", icon: "🚗" },
  { id: "suv", label: "SUV", icon: "🚙" },
  { id: "van", label: "Van", icon: "🚐" },
  { id: "mini-bus", label: "Mini Bus", icon: "🚌" },
  { id: "coach-bus", label: "Coach Bus", icon: "🚍" },
  { id: "electric", label: "Electric Vehicle", icon: "⚡" },
]

// Add new interfaces
interface Passenger {
  id: string
  name: string
  email: string
  phone?: string
  company?: string
  group: 'VIP' | 'Staff' | 'Guest'
  dietaryRestrictions?: string
  specialRequirements?: string
}

interface Coordinator {
  id: string
  name: string
  email: string
  phone?: string
  role?: string
}

export default function NewEventPage() {
  const router = useRouter()
  const [selectedPassengers, setSelectedPassengers] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedVehicles, setSelectedVehicles] = useState<string[]>([])
  const [showNewPassengerForm, setShowNewPassengerForm] = useState(false)
  const [showNewCoordinatorForm, setShowNewCoordinatorForm] = useState(false)
  const [searchBy, setSearchBy] = useState("name")
  const [filterBy, setFilterBy] = useState("all")

  // New passenger form
  const passengerForm = useForm({
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      company: "",
      group: "Guest",
      dietaryRestrictions: "",
      specialRequirements: "" }
  })

  // New coordinator form
  const coordinatorForm = useForm({
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      role: "" }
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      type: "",
      location: "",
      expectedPassengers: 1,
      vehicleTypes: [],
      description: "",
      additionalNotes: "" } })

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values)
    router.push("/customer/events")
  }

  const toggleVehicleSelection = (vehicleId: string) => {
    setSelectedVehicles(prev =>
      prev.includes(vehicleId)
        ? prev.filter(id => id !== vehicleId)
        : [...prev, vehicleId]
    )
  }

  return (
    <div className="container space-y-6 p-6 bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <Link
            href="/customer/events"
            className="flex items-center text-sm text-muted-foreground hover:text-primary"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Events
          </Link>
          <h1 className="text-2xl font-semibold">Create Event</h1>
          <p className="text-sm text-muted-foreground">Let's create a new event for your organization</p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-6 md:grid-cols-[2fr,1fr]">
          {/* Left Column - Fixed Content */}
          <div className="space-y-6">
            {/* Event Information Card */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6 space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <div className="p-2 rounded-full bg-blue-50">
                    <FileText className="h-5 w-5 text-blue-500" />
                  </div>
                  <h2 className="text-lg font-medium text-blue-900">Event Information</h2>
                </div>

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Event Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter event name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Event Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select event type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="wedding">Wedding</SelectItem>
                          <SelectItem value="corporate">Corporate Event</SelectItem>
                          <SelectItem value="conference">Conference</SelectItem>
                          <SelectItem value="tour">Tour</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="startDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Date</FormLabel>
                        <FormControl>
                          <DatePicker
                            date={field.value}
                            onSelect={field.onChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="endDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>End Date (Optional)</FormLabel>
                        <FormControl>
                          <DatePicker
                            date={field.value}
                            onSelect={field.onChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Primary Location</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <MapPin className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input placeholder="Search for a location" className="pl-8" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Provide a detailed description of your event"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <FormLabel>Event Files</FormLabel>
                  <div className="border-2 border-dashed rounded-lg p-6 text-center space-y-2">
                    <Upload className="h-8 w-8 mx-auto text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">
                      Drag and drop files here, or click to select files
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Supported formats: Images, PDF, DOC, DOCX, XLS, XLSX
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Transportation Requirements */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6 space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <div className="p-2 rounded-full bg-purple-50">
                    <Truck className="h-5 w-5 text-purple-500" />
                  </div>
                  <h2 className="text-lg font-medium text-purple-900">Transportation Requirements</h2>
                </div>

                <FormField
                  control={form.control}
                  name="expectedPassengers"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expected Number of Passengers</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter expected number of passengers"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vehicleTypes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Vehicle Types Required</FormLabel>
                      <div className="grid grid-cols-2 gap-2">
                        {vehicleOptions.map((vehicle) => (
                          <Button
                            key={vehicle.id}
                            type="button"
                            variant={selectedVehicles.includes(vehicle.id) ? "default" : "outline"}
                            className="justify-start"
                            onClick={() => {
                              toggleVehicleSelection(vehicle.id);
                              field.onChange([...selectedVehicles, vehicle.id]);
                            }}
                          >
                            <span className="mr-2">{vehicle.icon}</span>
                            {vehicle.label}
                          </Button>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="additionalNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Add any special requirements or notes"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <div className="flex justify-end gap-2">
              <Button variant="outline" type="button" onClick={() => router.back()}>
                Cancel
              </Button>
              <Button type="submit">Create Event</Button>
            </div>
          </div>

          {/* Right Column - Scrollable Passenger Management */}
          <div className="relative">
            <div className="space-y-6 sticky top-6 max-h-[calc(100vh-8rem)] overflow-y-auto">
              {/* Passenger Management */}
              <Card className="shadow-sm hover:shadow-md transition-shadow">
                <CardContent className="p-6 space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Passengers</h3>
                    <p className="text-sm text-gray-500 mb-4">
                      Search and select from your existing passenger list
                    </p>

                    <div className="space-y-4">
                      <div className="relative">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Search passengers..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-8 border-gray-200 focus:ring-blue-500"
                        />
                      </div>

                      <div className="flex gap-2">
                        <Select value={searchBy} onValueChange={setSearchBy}>
                          <SelectTrigger className="w-[140px] border-gray-200">
                            <SelectValue placeholder="Search by" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="name">By Name</SelectItem>
                            <SelectItem value="email">By Email</SelectItem>
                            <SelectItem value="phone">By Phone</SelectItem>
                          </SelectContent>
                        </Select>

                        <Select value={filterBy} onValueChange={setFilterBy}>
                          <SelectTrigger className="w-[140px] border-gray-200">
                            <SelectValue placeholder="Filter by" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Groups</SelectItem>
                            <SelectItem value="vip">VIP</SelectItem>
                            <SelectItem value="staff">Staff</SelectItem>
                            <SelectItem value="guest">Guest</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Create New Passenger */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900">Create New Passenger</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowNewPassengerForm(!showNewPassengerForm)}
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        {showNewPassengerForm ? <X className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                      </Button>
                    </div>

                    {showNewPassengerForm && (
                      <Form {...passengerForm}>
                        <form className="space-y-4 p-4 bg-gray-50 rounded-lg border border-gray-100">
                          <div className="grid gap-4">
                            <div className="grid grid-cols-2 gap-4">
                              <Input placeholder="First Name" className="border-gray-200" />
                              <Input placeholder="Last Name" className="border-gray-200" />
                            </div>
                            <Input placeholder="Email" type="email" className="border-gray-200" />
                            <Input placeholder="Phone" type="tel" className="border-gray-200" />
                            <Input placeholder="Company" className="border-gray-200" />
                            <Select defaultValue="guest">
                              <SelectTrigger className="border-gray-200">
                                <SelectValue placeholder="Select group" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="guest">Guest</SelectItem>
                                <SelectItem value="vip">VIP</SelectItem>
                                <SelectItem value="staff">Staff</SelectItem>
                              </SelectContent>
                            </Select>
                            <Textarea placeholder="Dietary Restrictions" className="border-gray-200" />
                            <Textarea placeholder="Special Requirements" className="border-gray-200" />
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setShowNewPassengerForm(false)}
                            >
                              Cancel
                            </Button>
                            <Button type="submit" size="sm">Add Passenger</Button>
                          </div>
                        </form>
                      </Form>
                    )}
                  </div>

                  {/* Selected Passengers */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-gray-900">Selected Passengers</h4>
                    <div className="space-y-2">
                      <div className="text-xs font-medium text-gray-500">STAFF</div>
                      <Card className="border-gray-200">
                        <CardContent className="p-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-8 w-8 bg-blue-100">
                                <AvatarFallback className="text-blue-600">JS</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="text-sm font-medium text-gray-900">Jane Smith</div>
                                <div className="text-xs text-gray-500"><EMAIL></div>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-400 hover:text-gray-500"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  {/* Event Coordinators */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900">Event Coordinators</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowNewCoordinatorForm(!showNewCoordinatorForm)}
                        className="text-green-600 border-green-200 hover:bg-green-50"
                      >
                        {showNewCoordinatorForm ? <X className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500">
                      Add one or more coordinators to manage this event
                    </p>

                    {showNewCoordinatorForm && (
                      <Form {...coordinatorForm}>
                        <form className="space-y-4 p-4 bg-gray-50 rounded-lg border border-gray-100">
                          <div className="grid gap-4">
                            <div className="grid grid-cols-2 gap-4">
                              <Input placeholder="First Name" className="border-gray-200" />
                              <Input placeholder="Last Name" className="border-gray-200" />
                            </div>
                            <Input placeholder="Email" type="email" className="border-gray-200" />
                            <Input placeholder="Phone" type="tel" className="border-gray-200" />
                            <Input placeholder="Role" className="border-gray-200" />
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setShowNewCoordinatorForm(false)}
                            >
                              Cancel
                            </Button>
                            <Button type="submit" size="sm">Add Coordinator</Button>
                          </div>
                        </form>
                      </Form>
                    )}

                    <div className="space-y-2">
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8 bg-green-100">
                            <AvatarFallback className="text-green-600">MD</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="text-sm text-gray-900">Michael Davis</div>
                            <div className="text-xs text-gray-500"><EMAIL></div>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-green-600 border-green-200 hover:bg-green-50"
                        >
                          Add as Passenger
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
} 