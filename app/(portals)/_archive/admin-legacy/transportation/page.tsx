"use client"

import { useState } from "react";
import { Card } from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { But<PERSON> } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/app/components/ui/tabs";
import { Calendar, Clock, Filter, MapPin, Search, Users, Link as LinkIcon, as LinkIcon } from "lucide-react";
import Link from "next/link"
interface Trip {
  id: string
  eventName: string
  date: string
  pickupLocation: string
  dropoffLocation: string
  passengerCount: number
  status: "scheduled" | "in_progress" | "completed" | "cancelled"
  startTime: string
  endTime: string
}

const mockTrips: Trip[] = [
  {
    id: "1",
    eventName: "Annual Conference 2024",
    date: "2024-03-15",
    pickupLocation: "JFK Airport",
    dropoffLocation: "Marriott Hotel Manhattan",
    passengerCount: 25,
    status: "scheduled",
    startTime: "14:00",
    endTime: "15:30"
  },
  {
    id: "2",
    eventName: "Tech Summit 2024",
    date: "2024-03-20",
    pickupLocation: "Grand Central Station",
    dropoffLocation: "Javits Center",
    passengerCount: 40,
    status: "in_progress",
    startTime: "09:00",
    endTime: "10:00"
  }
]

function TripCard({ trip }: { trip: Trip }) {
  const statusColors = {
    scheduled: "bg-blue-100 text-blue-800",
    in_progress: "bg-green-100 text-green-800",
    completed: "bg-gray-100 text-gray-800",
    cancelled: "bg-red-100 text-red-800"
  }

  const href = `/customer/transportation/service/${trip.id}`
  console.log('Transportation link:', href)

  return (
    <Link href={href}>
      <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-lg">{trip.eventName}</h3>
            <Badge className={statusColors[trip.status]}>
              {trip.status.replace("_", " ")}
            </Badge>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center text-sm text-muted-foreground">
                <Calendar className="h-4 w-4 mr-2" />
                {new Date(trip.date).toLocaleDateString()}
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <Clock className="h-4 w-4 mr-2" />
                {trip.startTime} - {trip.endTime}
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <Users className="h-4 w-4 mr-2" />
                {trip.passengerCount} passengers
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <MapPin className="h-4 w-4 mr-2 text-green-600" />
                <span className="text-muted-foreground">From:</span>
                <span className="ml-1">{trip.pickupLocation}</span>
              </div>
              <div className="flex items-center text-sm">
                <MapPin className="h-4 w-4 mr-2 text-red-600" />
                <span className="text-muted-foreground">To:</span>
                <span className="ml-1">{trip.dropoffLocation}</span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </Link>
  )
}

export default function TransportationPage() {
  const [searchQuery, setSearchQuery] = useState("")

  const filteredTrips = mockTrips.filter(trip =>
    trip.eventName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    trip.pickupLocation.toLowerCase().includes(searchQuery.toLowerCase()) ||
    trip.dropoffLocation.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const activeTrips = filteredTrips.filter(trip => trip.status === "in_progress")
  const upcomingTrips = filteredTrips.filter(trip => trip.status === "scheduled")
  const completedTrips = filteredTrips.filter(trip => trip.status === "completed")

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Transportation</h2>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search trips..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button>
          Filter
        </Button>
      </div>

      <Tabs defaultValue="active" className="space-y-4">
        <TabsList>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          {activeTrips.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2">
              {activeTrips.map((trip) => (
                <TripCard key={trip.id} trip={trip} />
              ))}
            </div>
          ) : (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground">No active trips</p>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4">
          {upcomingTrips.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2">
              {upcomingTrips.map((trip) => (
                <TripCard key={trip.id} trip={trip} />
              ))}
            </div>
          ) : (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground">No upcoming trips</p>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          {completedTrips.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2">
              {completedTrips.map((trip) => (
                <TripCard key={trip.id} trip={trip} />
              ))}
            </div>
          ) : (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground">No completed trips</p>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
} 