"use client"

import { useEffect, useState } from "react";
import { format } from "date-fns";
import { But<PERSON> } from "@/app/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { Badge } from "@/app/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs";
import { ToggleGroup, ToggleGroupItem } from "@/app/components/ui/toggle-group";
import { AlertTriangle, Calendar, Car, Clock, Filter, List, Map as MapIcon, Monitor, Plus, Search, Shield } from "lucide-react";
import { TripRow, TripRowData } from "@/app/components/shared/rows";

// Update Trip interface to match our API
interface Trip {
  id: string
  quote_id: string
  status: "scheduled" | "in_progress" | "completed" | "cancelled"
  created_at: string
  updated_at: string
  pickup_location: string
  dropoff_location: string
  date: string
  time: string
  vehicle_type: string
  passenger_count: number
  luggage_count: number
  special_requests?: string[]
  total_amount: number
  service_type: string
  reference_number: string
  priority: string
  // Optional fields
  driver_name?: string | null
  driver_rating?: number | null
  driver_trips?: number | null
  estimated_arrival?: string | null
  completed_at?: string | null
  is_rated?: boolean
}

// Convert API Trip to TripRowData
const mapTripToRowData = (trip: any): TripRowData => {
  return {
    id: trip.id,
    reference_number: trip.reference_number,
    pickup_location: trip.pickup_location,
    dropoff_location: trip.dropoff_location,
    date: trip.date,
    time: trip.time,
    vehicle_type: trip.vehicle_type,
    status: trip.status,
    customer: {
      name: `${trip.customer?.first_name || ''} ${trip.customer?.last_name || ''}`.trim() || 'No customer',
      email: trip.customer?.email,
      phone: trip.customer?.phone,
      is_vip: trip.customer?.is_vip || false,
      company: trip.customer?.company,
      event: trip.event_name
    },
    passenger_count: trip.passenger_count,
    special_requests: trip.special_instructions,
    priority: trip.priority || 'medium',
    city: trip.city,
    driver: trip.driver ? {
      name: trip.driver.name,
      phone: trip.driver.phone,
      rating: trip.driver.rating,
      photo: trip.driver.photo
    } : undefined,
    eta: trip.eta,
    progress_percentage: trip.progress_percentage || 0,
    price: trip.price,
    base_fare: trip.base_fare,
    gratuity: trip.gratuity,
    admin_fee: trip.admin_fee,
    additional_services: trip.additional_services,
    payment_status: trip.payment_status,
    payment_method: trip.payment_method,
    affiliate_name: trip.affiliate?.name,
    tracking_url: trip.tracking_url,
    estimated_distance: trip.estimated_distance,
    estimated_duration: trip.estimated_duration,
    actual_distance: trip.actual_distance,
    actual_duration: trip.actual_duration,
    last_updated: trip.updated_at
  };
};

export default function TripsPage() {
  const [view, setView] = useState<"list" | "calendar" | "map">("list")
  const [searchTerm, setSearchTerm] = useState("")
  const [trips, setTrips] = useState<Trip[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null)
  const [filters, setFilters] = useState({
    tripType: [] as string[],
    priority: [] as string[],
    status: [] as string[],
    dateRange: {
      start: "",
      end: ""
    }
  })

  // Fetch trips from the API
  useEffect(() => {
    const fetchTrips = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch('/api/admin/trips')
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
        }
        
        const data = await response.json()
        console.log('API Response:', data) // Debug log
        
        if (!data || !data.trips) {
          throw new Error('No trips data found in response')
        }
        
        setTrips(data.trips)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while fetching trips')
        console.error('Error fetching trips:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchTrips()
  }, [])

  // Handle communication with driver
  const handleCallDriver = (tripId: string) => {
    // Implement call functionality
    console.log('Calling driver for trip:', tripId);
    // In a real app, this would initiate a call
  };

  // Handle sending message to driver
  const handleMessageDriver = (tripId: string) => {
    // Implement messaging functionality
    console.log('Messaging driver for trip:', tripId);
    // In a real app, this would open a messaging interface
  };

  // Handle trip tracking
  const handleTrackTrip = (tripId: string) => {
    // Implement tracking functionality
    console.log('Tracking trip:', tripId);
    // In a real app, this would open a map or tracking view
  };

  // Filter trips based on status
  const scheduledTrips = trips.filter(trip => trip.status === "scheduled")
  const liveTrips = trips.filter(trip => trip.status === "in_progress")
  const completedTrips = trips.filter(trip => trip.status === "completed")

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading trips...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 text-destructive mx-auto" />
          <p className="mt-4 text-destructive">{error}</p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8 p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Trip Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage all scheduled and live trips
          </p>
        </div>
        <div className="flex items-center gap-4">
          {/* View Toggle */}
          <ToggleGroup type="single" value={view} onValueChange={(v) => v && setView(v as any)}>
            <ToggleGroupItem value="list" aria-label="List view">
              <List className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="calendar" aria-label="Calendar view">
              <Calendar className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="map" aria-label="Map view">
              <MapIcon className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>

          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Trip
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Trips</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{trips.length}</div>
            <p className="text-xs text-muted-foreground">
              {scheduledTrips.length} scheduled
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Live Trips</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{liveTrips.length}</div>
            <p className="text-xs text-muted-foreground">
              {liveTrips.length > 0 ? `${Math.round((liveTrips.length / trips.length) * 100)}% of total` : 'No live trips'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Today</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {completedTrips.filter(t => new Date(t.completed_at || '').toDateString() === new Date().toDateString()).length}
            </div>
            <p className="text-xs text-muted-foreground">
              {completedTrips.length} total completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Airport Transfers</CardTitle>
            <PlaneTakeoff className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {trips.filter(trip => 
                trip.service_type?.toLowerCase().includes('airport') || 
                trip.pickup_location?.toLowerCase().includes('airport') || 
                trip.dropoff_location?.toLowerCase().includes('airport')
              ).length}
            </div>
            <p className="text-xs text-muted-foreground">
              {Math.round((trips.filter(trip => trip.service_type?.toLowerCase().includes('airport')).length / trips.length) * 100)}% of total trips
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="relative w-[300px]">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search trips..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </Button>
        </div>
        <Select defaultValue="today">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select date range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="custom">Custom Range</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Trips List */}
      <Tabs defaultValue="scheduled">
        <TabsList>
          <TabsTrigger value="scheduled" className="gap-2">
            <Clock className="h-4 w-4" />
            Scheduled ({scheduledTrips.length})
          </TabsTrigger>
          <TabsTrigger value="live" className="gap-2">
            <Shield className="h-4 w-4" />
            Live Trips ({liveTrips.length})
          </TabsTrigger>
          <TabsTrigger value="completed" className="gap-2">
            <CheckCircle2 className="h-4 w-4" />
            Completed ({completedTrips.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="scheduled" className="mt-4">
          <div className="space-y-2">
            {scheduledTrips.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No scheduled trips found
              </div>
            ) : (
              scheduledTrips.map((trip) => (
                <TripRow 
                  key={trip.id} 
                  trip={mapTripToRowData(trip)}
                  isSelected={selectedTripId === trip.id}
                  onClick={() => setSelectedTripId(trip.id === selectedTripId ? null : trip.id)}
                  onCall={() => handleCallDriver(trip.id)}
                  onMessage={() => handleMessageDriver(trip.id)}
                  onTrack={() => handleTrackTrip(trip.id)}
                />
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="live" className="mt-4">
          <div className="space-y-2">
            {liveTrips.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No live trips found
              </div>
            ) : (
              liveTrips.map((trip) => (
                <TripRow 
                  key={trip.id} 
                  trip={mapTripToRowData(trip)}
                  isSelected={selectedTripId === trip.id}
                  onClick={() => setSelectedTripId(trip.id === selectedTripId ? null : trip.id)}
                  onCall={() => handleCallDriver(trip.id)}
                  onMessage={() => handleMessageDriver(trip.id)}
                  onTrack={() => handleTrackTrip(trip.id)}
                />
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="completed" className="mt-4">
          <div className="space-y-2">
            {completedTrips.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No completed trips found
              </div>
            ) : (
              completedTrips.map((trip) => (
                <TripRow 
                  key={trip.id} 
                  trip={mapTripToRowData(trip)}
                  isSelected={selectedTripId === trip.id}
                  onClick={() => setSelectedTripId(trip.id === selectedTripId ? null : trip.id)}
                  onCall={() => handleCallDriver(trip.id)}
                  onMessage={() => handleMessageDriver(trip.id)}
                  onTrack={() => handleTrackTrip(trip.id)}
                />
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 