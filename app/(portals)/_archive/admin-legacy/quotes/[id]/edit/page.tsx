"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card } from "@/app/components/ui/card";
import { ArrowLeft, Edit, Link as LinkIcon, as LinkIcon } from "lucide-react";
import Link from "next/link"
import { QuoteForm } from "@/app/components/features/quotes/quote-form";
import { useParams } from "next/navigation";
import { z } from "zod";
// Define the type based on the form schema
type QuoteFormValues = {
  service_type: "airport" | "hourly" | "point"
  vehicle_type: string
  pickup_location: string
  dropoff_location: string
  pickup_date: string
  pickup_time: string
  passenger_count: string
  luggage_count: string
  special_requests?: string
}

export default function EditQuotePage() {
  const params = useParams()
  const quoteId = params.id

  // Mock data for the quote - replace with actual data fetching
  const quote: Partial<QuoteFormValues> = {
    service_type: "airport",
    vehicle_type: "Executive Sedan",
    pickup_location: "JFK Airport",
    dropoff_location: "Manhattan Hotel",
    pickup_date: "2024-04-15",
    pickup_time: "14:00",
    passenger_count: "3",
    luggage_count: "4",
    special_requests: "Meet and greet service"
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/quotes">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Edit Quote</h2>
        </div>
      </div>
      <Card className="p-6">
        <QuoteForm userRole="system_manager" defaultValues={quote} />
      </Card>
    </div>
  )
} 