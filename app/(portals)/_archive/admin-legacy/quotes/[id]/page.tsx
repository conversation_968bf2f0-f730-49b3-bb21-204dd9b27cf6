"use client"

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Progress } from "@/app/components/ui/progress";
import { Alert, AlertDescription } from "@/app/components/ui/alert";
import { Switch } from "@/app/components/ui/switch";
import { Label } from "@/app/components/ui/label";
import { useToast } from "@/app/components/ui/use-toast";
import { getSupabaseClient } from '@/lib/supabase';
import { getAffiliatesForQuote } from "@/lib/api/affiliates";
import { Building2, Briefcase, ArrowRight, Clock, Star, Type, AlertCircle } from "lucide-react";


interface Quote {
  id: string;
  customer_name?: string;
  service_type?: string;
  pickup_location?: string;
  dropoff_location?: string;
  date?: string;
  time?: string;
  passenger_count?: number;
  luggage_count?: number;
  vehicle_type?: string;
  special_requests?: string;
  status: string;
  city?: string;
  affiliate_id?: string;
  company_id?: string;
  // UI specific fields
  marketAnalysis?: {
    avgMarketRate: number;
    demandLevel: string;
    suggestedMarkup: number;
    peakPricing: boolean;
    similarTripsLastMonth: number;
  };
  urgency?: 'high' | 'medium' | 'low';
  smartAssignmentSettings?: {
    enabled: boolean;
    responseTimer: string;
    nextAssignment?: string;
  };
  responses?: number;
  timeLeft?: string;
  confidenceScore?: number;
}

interface Affiliate {
  id: string;
  name: string;
  city?: string;
  state?: string;
  coverage?: string;
  rating?: number;
  baseRate?: number;
  totalRate?: number;
  confidenceScore?: number;
  avgResponseTime?: string;
  completedSimilar?: number;
  onTimeRate?: number;
  historicalPricing?: {
    avg: number;
    min: number;
    max: number;
  };
}

export default function QuoteDetailPage() {
  const params = useParams()
  const quoteId = params.id as string
  const [quote, setQuote] = useState<Quote | null>(null)
  const [affiliates, setAffiliates] = useState<Affiliate[]>([])
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()
  const [smartAssignmentEnabled, setSmartAssignmentEnabled] = useState(false)
  const router = useRouter()
  const [timelineEntries, setTimelineEntries] = useState<any[]>([])
  const [selectedAffiliate, setSelectedAffiliate] = useState<string>("")
  const [isAssigning, setIsAssigning] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [selectedAffiliates, setSelectedAffiliates] = useState<string[]>([])

  useEffect(() => {
    const fetchQuoteData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/admin/quotes/${params.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch quote');
        }
        const data = await response.json();
        setQuote(data);
        
        // Also fetch timeline entries
        const timelineResponse = await fetch(`/api/admin/quotes/${params.id}/timeline`);
        if (timelineResponse.ok) {
          const timelineData = await timelineResponse.json();
          setTimelineEntries(timelineData);
        }
        
        // Fetch affiliates if city is available
        if (data.city) {
          fetchAffiliatesForCity(data.city);
        } else {
          // If no city specified, fetch all affiliates
          fetchAllAffiliates();
        }
      } catch (error) {
        console.error('Error fetching quote:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch quote details',
          variant: 'destructive' });
      } finally {
        setLoading(false);
      }
    };

    fetchQuoteData();
    
    // Set up periodic refresh every 15 seconds to catch status changes
    const refreshInterval = setInterval(fetchQuoteData, 15000);
    
    // Set up visibility change listener to refresh when tab becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchQuoteData();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      clearInterval(refreshInterval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [params.id, toast]);
  
  const fetchAffiliatesForCity = async (city: string) => {
    try {
      console.log(`[QuoteDetailPage] Fetching affiliates for city: ${city}`);
      
      // Use the getAffiliatesForQuote function directly with the city parameter
      const affiliatesWithStats = await getAffiliatesForQuote(city);
      
      if (!affiliatesWithStats || affiliatesWithStats.length === 0) {
        console.log('[QuoteDetailPage] No affiliates found for this quote');
        setAffiliates([]);
        return;
      }
      
      console.log(`[QuoteDetailPage] Found ${affiliatesWithStats.length} affiliates`);
      
      // Transform to Affiliate interface if needed
      const transformedAffiliates: Affiliate[] = affiliatesWithStats.map((affiliate: any) => ({
        id: affiliate.id,
        name: affiliate.name,
        city: affiliate.city,
        state: affiliate.state,
        coverage: `${affiliate.city || ''}, ${affiliate.state || ''}`,
        rating: affiliate.rating || 4.5,
        baseRate: affiliate.baseRate || 150,
        totalRate: affiliate.totalRate || 180,
        confidenceScore: 90,
        avgResponseTime: affiliate.avgResponse || "25m",
        completedSimilar: affiliate.similarTrips || 120,
        onTimeRate: affiliate.on_time_percentage || 95,
        historicalPricing: {
          avg: affiliate.priceRange ? (affiliate.priceRange.min + affiliate.priceRange.max) / 2 : 220,
          min: affiliate.priceRange ? affiliate.priceRange.min : 200,
          max: affiliate.priceRange ? affiliate.priceRange.max : 240
        }
      }));
      
      console.log('[QuoteDetailPage] Setting affiliates:', transformedAffiliates);
      setAffiliates(transformedAffiliates);
    } catch (error) {
      console.error('[QuoteDetailPage] Error in fetchAffiliatesForCity:', error);
      toast({
        title: "Error",
        description: "Failed to load available affiliates",
        variant: "destructive"
      });
    }
  }
  
  const fetchAllAffiliates = async () => {
    try {
      console.log('[QuoteDetailPage] Fetching all affiliates');
      
      // For all affiliates, we'll use the getAffiliatesForQuote function with an empty string
      // The function will handle the case when no city is specified
      const affiliatesWithStats = await getAffiliatesForQuote('');
      
      if (!affiliatesWithStats || affiliatesWithStats.length === 0) {
        console.log('[QuoteDetailPage] No affiliates found');
        setAffiliates([]);
        return;
      }
      
      console.log(`[QuoteDetailPage] Found ${affiliatesWithStats.length} total affiliates`);
      
      // Transform to Affiliate interface if needed
      const transformedAffiliates: Affiliate[] = affiliatesWithStats.map((affiliate: any) => ({
        id: affiliate.id,
        name: affiliate.name,
        city: affiliate.city,
        state: affiliate.state,
        coverage: `${affiliate.city || ''}, ${affiliate.state || ''}`,
        rating: affiliate.rating || 4.5,
        baseRate: affiliate.baseRate || 150,
        totalRate: affiliate.totalRate || 180,
        confidenceScore: 90,
        avgResponseTime: affiliate.avgResponse || "25m",
        completedSimilar: affiliate.similarTrips || 120,
        onTimeRate: affiliate.on_time_percentage || 95,
        historicalPricing: {
          avg: affiliate.priceRange ? (affiliate.priceRange.min + affiliate.priceRange.max) / 2 : 220,
          min: affiliate.priceRange ? affiliate.priceRange.min : 200,
          max: affiliate.priceRange ? affiliate.priceRange.max : 240
        }
      }));
      
      console.log('[QuoteDetailPage] Setting all affiliates:', transformedAffiliates);
      setAffiliates(transformedAffiliates);
    } catch (error) {
      console.error('[QuoteDetailPage] Error in fetchAllAffiliates:', error);
      toast({
        title: "Error",
        description: "Failed to load available affiliates",
        variant: "destructive"
      });
    }
  }

  const handleSmartAssign = () => {
    if (!quote) return
    
    setQuote(prev => {
      if (!prev) return prev
      return {
        ...prev,
        smartAssignmentSettings: {
          ...prev.smartAssignmentSettings!,
          enabled: true
        }
      }
    })
    
    toast({
      title: "Smart Assignment Enabled",
      description: "The system will automatically assign the best matching affiliate."
    })
  }

  const handleManualAssign = async () => {
    if (!selectedAffiliate) {
      toast({
        title: 'Error',
        description: 'Please select an affiliate',
        variant: 'destructive' });
      return;
    }

    if (!quote) {
      toast({
        title: 'Error',
        description: 'Quote data not available',
        variant: 'destructive' });
      return;
    }

    try {
      setIsAssigning(true);
      const response = await fetch('/api/admin/quotes/assign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json' },
        body: JSON.stringify({
          quoteId: quote.id,
          affiliateId: selectedAffiliate }) });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to assign quote');
      }

      // Get the updated quote data from the response
      const updatedQuote = await response.json();
      
      // Update the local state with the new quote data
      setQuote(prevQuote => {
        if (!prevQuote) return updatedQuote;
        return {
          ...prevQuote,
          ...updatedQuote,
          company_id: updatedQuote.company_id || prevQuote.company_id,
          affiliate_id: updatedQuote.affiliate_id || selectedAffiliate,
          status: updatedQuote.status || 'quote_assigned' };
      });

      toast({
        title: 'Success',
        description: 'Quote assigned successfully' });
      
      // Refresh the page to show updated status
      router.refresh();
      
      // Also fetch timeline entries to show the assignment
      if (quote) {
        const timelineResponse = await fetch(`/api/admin/quotes/${quote.id}/timeline`);
        if (timelineResponse.ok) {
          const timelineData = await timelineResponse.json();
          setTimelineEntries(timelineData);
        }
      }
    } catch (error) {
      console.error('Error assigning quote:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to assign quote',
        variant: 'destructive' });
    } finally {
      setIsAssigning(false);
      setShowAssignDialog(false);
    }
  };

  const handleSendQuote = async () => {
    if (selectedAffiliates.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select at least one affiliate',
        variant: 'destructive' });
      return;
    }

    if (!quote) {
      toast({
        title: 'Error',
        description: 'Quote data not available',
        variant: 'destructive' });
      return;
    }

    try {
      setIsSending(true);
      const response = await fetch('/api/admin/quotes/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json' },
        body: JSON.stringify({
          quoteId: quote.id,
          affiliateIds: selectedAffiliates }) });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send quote');
      }

      // Get the updated quote data from the response
      const updatedQuote = await response.json();
      
      // Update the local state with the new quote data
      setQuote(prevQuote => {
        if (!prevQuote) return updatedQuote;
        return {
          ...prevQuote,
          ...updatedQuote,
          status: updatedQuote.status || 'quote_sent' };
      });

      toast({
        title: 'Success',
        description: 'Quote sent successfully' });
      
      // Refresh the page to show updated status
      router.refresh();
      
      // Also fetch timeline entries to show the send action
      const timelineResponse = await fetch(`/api/admin/quotes/${quote.id}/timeline`);
      if (timelineResponse.ok) {
        const timelineData = await timelineResponse.json();
        setTimelineEntries(timelineData);
      }
    } catch (error) {
      console.error('Error sending quote:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to send quote',
        variant: 'destructive' });
    } finally {
      setIsSending(false);
      setShowSendDialog(false);
      setSelectedAffiliates([]);
    }
  };

  if (loading) {
    return (
      <div className="container p-6 flex items-center justify-center h-screen">
        <div className="text-center">
          <Clock className="h-10 w-10 mb-4 mx-auto animate-spin text-primary" />
          <p>Loading quote details...</p>
        </div>
      </div>
    )
  }

  if (!quote) {
    return (
      <div className="container p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Quote not found or you don't have permission to view it.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Quote Details</h1>
          <p className="text-muted-foreground">
            Manage quote assignment and workflow
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardContent className="p-6">
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold mb-4">Service Details</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Service Type</h3>
                    <p>{quote.service_type || 'Not specified'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Vehicle Type</h3>
                    <p>{quote.vehicle_type || 'Not specified'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Pickup Location</h3>
                    <p>{quote.pickup_location || 'Not specified'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Dropoff Location</h3>
                    <p>{quote.dropoff_location || 'Not specified'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Date & Time</h3>
                    <p>{quote.date ? `${quote.date} ${quote.time || ''}` : 'Not specified'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">City</h3>
                    <p>{quote.city || 'Not specified'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Passengers</h3>
                    <p>{quote.passenger_count || 0}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Luggage</h3>
                    <p>{quote.luggage_count || 0}</p>
                  </div>
                </div>
              </div>

              <div>
                <h2 className="text-xl font-semibold mb-4">Special Requests</h2>
                <p>{quote.special_requests || 'No special requests'}</p>
              </div>

              <div>
                <h2 className="text-xl font-semibold mb-4">Assignment Options</h2>
                <div className="flex flex-col space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="smart-assignment">Smart Assignment</Label>
                      <p className="text-sm text-muted-foreground">
                        Let the system automatically assign the best affiliate
                      </p>
                    </div>
                    <Switch
                      id="smart-assignment"
                      checked={quote.smartAssignmentSettings?.enabled}
                      onCheckedChange={handleSmartAssign}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Dialog open={showAnalysisDialog} onOpenChange={setShowAnalysisDialog}>
                        <DialogTrigger asChild>
                          <Button variant="outline">View Market Analysis</Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Market Analysis</DialogTitle>
                            <DialogDescription>
                              Insights to help with pricing and affiliate selection
                            </DialogDescription>
                          </DialogHeader>
                          {/* Analysis content */}
                        </DialogContent>
                      </Dialog>

                      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
                        <DialogTrigger asChild>
                          <Button>Assign Manually</Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Manual Assignment</DialogTitle>
                            <DialogDescription>
                              Select an affiliate to assign this quote
                            </DialogDescription>
                          </DialogHeader>

                          <div className="mt-4 space-y-4">
                            {affiliates.length > 0 ? (
                              affiliates.map((affiliate) => (
                                <Card
                                  key={affiliate.id}
                                  className="cursor-pointer hover:bg-accent transition-colors"
                                  onClick={() => {
                                    setSelectedAffiliate(affiliate.id);
                                    handleManualAssign();
                                  }}
                                >
                                  <CardContent className="p-4">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        <Building2 className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                          <span className="font-medium">{affiliate.name}</span>
                                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                            <Star className="h-3 w-3 fill-yellow-400" />
                                            <span>{affiliate.rating}</span>
                                            <span className="mx-1">•</span>
                                            <span>{affiliate.coverage}</span>
                                          </div>
                                        </div>
                                      </div>
                                      <div className="text-right">
                                        <div className="text-sm font-medium">${affiliate.totalRate}</div>
                                        <div className="text-xs text-muted-foreground">
                                          Confidence: {affiliate.confidenceScore}%
                                        </div>
                                      </div>
                                    </div>

                                    <div className="grid grid-cols-3 gap-4 mt-3 text-sm">
                                      <div className="flex items-center gap-1">
                                        <Clock className="h-3 w-3 text-muted-foreground" />
                                        <span>{affiliate.avgResponseTime} avg response</span>
                                      </div>
                                      <div className="flex items-center gap-1">
                                        <CheckCircle2 className="h-3 w-3 text-muted-foreground" />
                                        <span>{affiliate.onTimeRate}% on-time</span>
                                      </div>
                                      <div className="flex items-center gap-1">
                                        <Briefcase className="h-3 w-3 text-muted-foreground" />
                                        <span>{affiliate.completedSimilar} similar trips</span>
                                      </div>
                                    </div>

                                    <div className="mt-3">
                                      <div className="h-1.5 w-full bg-secondary rounded-full overflow-hidden">
                                        <div
                                          className="h-full bg-primary rounded-full transition-all duration-300"
                                          style={{ width: `${affiliate.confidenceScore}%` }}
                                        />
                                      </div>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))
                            ) : (
                              <div className="text-center p-4">
                                <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                                <p>No affiliates available for this quote's location.</p>
                                <p className="text-sm text-muted-foreground mt-1">Try updating the quote's city or adding affiliates for this area.</p>
                              </div>
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="font-semibold">Workflow Status</h3>
                <div className="flex items-center gap-2">
                  <Clock4 className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Time Left: {quote.timeLeft}</span>
                </div>
              </div>

              <div className="relative">
                <div className="flex justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">affiliate_selection</Badge>
                    <ArrowRight className="h-4 w-4" />
                    <Badge variant="outline">customer_approval</Badge>
                  </div>
                  <Badge>{quote.responses || 0} / {affiliates.length} responses</Badge>
                </div>
                <Progress value={40} className="h-2" />
              </div>

              <div className="pt-4">
                <h3 className="font-semibold mb-2">Quote Status</h3>
                <Badge className="capitalize">{quote.status}</Badge>
                
                {(quote.affiliate_id || quote.company_id) && (
                  <div className="mt-4">
                    <h3 className="font-semibold mb-2">Assigned Affiliate</h3>
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span>
                        {affiliates.find(a => a.id === quote.affiliate_id || a.id === quote.company_id)?.name || 'Unknown Affiliate'}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}