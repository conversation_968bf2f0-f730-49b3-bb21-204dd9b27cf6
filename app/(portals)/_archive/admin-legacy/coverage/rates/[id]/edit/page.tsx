'use client'

import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { ArrowLeft, Edit, Type } from "lucide-react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { PostgrestError } from "@supabase/supabase-js";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Switch } from "@/app/components/ui/switch";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/app/components/ui/form";

interface RateCard {
  id: string
  name: string
  serviceType: string
  baseRate: number
  hourlyRate: number
  minimumHours: number
  status: 'active' | 'inactive'
  passengerCapacity: {
    min: number
    max: number
  }
  specialRequirements: string[]
  peakPricing: {
    enabled: boolean
    multiplier: number
    conditions: string[]
  }
  cancellationPolicy: string
  createdAt: string
  updatedAt: string
}

const serviceTypeLabels: Record<string, string> = {
  airport_transfer: "Airport Transfer",
  venue_transfer: "Venue Transfer",
  city_tour: "City Tour",
  vip_service: "VIP Service",
  group_shuttle: "Group Shuttle" }

const specialRequirementLabels: Record<string, string> = {
  wheelchair_accessible: "Wheelchair Accessible",
  luggage_space: "Extra Luggage Space",
  child_seats: "Child Seats",
  luxury_vehicle: "Luxury Vehicle" }

const peakConditionLabels: Record<string, string> = {
  weekends: "Weekends",
  holidays: "Holidays",
  events: "Major Events",
  seasonal: "Seasonal Peak" }

const cancellationPolicyLabels: Record<string, string> = {
  "24h_notice": "24 Hours Notice",
  "48h_notice": "48 Hours Notice",
  "72h_notice": "72 Hours Notice" }

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  serviceType: z.string().min(1, "Service type is required"),
  baseRate: z.number().min(0, "Base rate must be positive"),
  hourlyRate: z.number().min(0, "Hourly rate must be positive"),
  minimumHours: z.number().min(1, "Minimum hours must be at least 1"),
  status: z.enum(["active", "inactive"]),
  passengerCapacity: z.object({
    min: z.number().min(1, "Minimum capacity must be at least 1"),
    max: z.number().min(1, "Maximum capacity must be at least 1") }),
  specialRequirements: z.array(z.string()),
  peakPricing: z.object({
    enabled: z.boolean(),
    multiplier: z.number().min(1, "Multiplier must be at least 1"),
    conditions: z.array(z.string()) }),
  cancellationPolicy: z.string().min(1, "Cancellation policy is required") })

async function getRateCard(id: string): Promise<RateCard> {
  // TODO: Implement rate card fetching
  return {
    id,
    name: "Standard Airport Transfer",
    serviceType: "airport_transfer",
    baseRate: 100,
    hourlyRate: 75,
    minimumHours: 3,
    status: "active",
    passengerCapacity: {
      min: 1,
      max: 50 },
    specialRequirements: ["wheelchair_accessible", "luggage_space"],
    peakPricing: {
      enabled: true,
      multiplier: 1.25,
      conditions: ["weekends", "holidays"] },
    cancellationPolicy: "48h_notice",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString() }
}

export default function RateCardEditPage() {
  const params = useParams()
  const router = useRouter()
  const rateCardId = params.id as string
  const isNew = rateCardId === "new"

  const { data: rateCard, isLoading, error } = useQuery<RateCard, PostgrestError>({
    queryKey: ["rateCard", rateCardId],
    queryFn: () => getRateCard(rateCardId),
    enabled: !isNew })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: isNew
      ? {
        name: "",
        serviceType: "",
        baseRate: 0,
        hourlyRate: 0,
        minimumHours: 1,
        status: "active",
        passengerCapacity: {
          min: 1,
          max: 1 },
        specialRequirements: [],
        peakPricing: {
          enabled: false,
          multiplier: 1,
          conditions: [] },
        cancellationPolicy: "" }
      : rateCard
        ? {
          name: rateCard.name,
          serviceType: rateCard.serviceType,
          baseRate: rateCard.baseRate,
          hourlyRate: rateCard.hourlyRate,
          minimumHours: rateCard.minimumHours,
          status: rateCard.status,
          passengerCapacity: rateCard.passengerCapacity,
          specialRequirements: rateCard.specialRequirements,
          peakPricing: rateCard.peakPricing,
          cancellationPolicy: rateCard.cancellationPolicy }
        : undefined })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      // TODO: Implement rate card creation/update
      console.log(values)
      toast.success(isNew ? "Rate card created" : "Rate card updated")
      router.push("/admin/coverage/rates")
    } catch (error) {
      toast.error("Failed to save rate card")
    }
  }

  if (!isNew && isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    )
  }

  if (!isNew && error) {
    toast.error(error.message || "Failed to load rate card")
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-red-500">Error loading rate card</p>
        <Button
          variant="outline"
          onClick={() => router.push("/admin/coverage/rates")}
          className="mt-4"
        >
          Go Back
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center gap-x-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => router.push("/admin/coverage/rates")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold">
          {isNew ? "New Rate Card" : "Edit Rate Card"}
        </h1>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter rate card name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="serviceType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Service Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a service type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(serviceTypeLabels).map(([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="baseRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Base Rate</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="hourlyRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hourly Rate</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="minimumHours"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum Hours</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          step="1"
                          placeholder="1"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <FormDescription>
                        Determine if this rate card is active and can be used
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value === "active"}
                        onCheckedChange={(checked) =>
                          field.onChange(checked ? "active" : "inactive")
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Capacity & Requirements</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="passengerCapacity.min"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum Capacity</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          step="1"
                          placeholder="1"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="passengerCapacity.max"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Maximum Capacity</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          step="1"
                          placeholder="1"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="specialRequirements"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Special Requirements</FormLabel>
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(specialRequirementLabels).map(([value, label]) => (
                        <FormField
                          key={value}
                          control={form.control}
                          name="specialRequirements"
                          render={({ field }) => (
                            <FormItem
                              key={value}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Switch
                                  checked={field.value?.includes(value)}
                                  onCheckedChange={(checked) => {
                                    const updated = checked
                                      ? [...field.value, value]
                                      : field.value?.filter((item) => item !== value)
                                    field.onChange(updated)
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {label}
                              </FormLabel>
                            </FormItem>
                          )}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Peak Pricing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="peakPricing.enabled"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Enable Peak Pricing</FormLabel>
                      <FormDescription>
                        Apply different rates during peak periods
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {form.watch("peakPricing.enabled") && (
                <>
                  <FormField
                    control={form.control}
                    name="peakPricing.multiplier"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price Multiplier</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            step="0.01"
                            placeholder="1.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value))}
                          />
                        </FormControl>
                        <FormDescription>
                          Multiply the base rate by this amount during peak periods
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="peakPricing.conditions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Peak Conditions</FormLabel>
                        <div className="grid grid-cols-2 gap-4">
                          {Object.entries(peakConditionLabels).map(([value, label]) => (
                            <FormField
                              key={value}
                              control={form.control}
                              name="peakPricing.conditions"
                              render={({ field }) => (
                                <FormItem
                                  key={value}
                                  className="flex flex-row items-start space-x-3 space-y-0"
                                >
                                  <FormControl>
                                    <Switch
                                      checked={field.value?.includes(value)}
                                      onCheckedChange={(checked) => {
                                        const updated = checked
                                          ? [...field.value, value]
                                          : field.value?.filter((item) => item !== value)
                                        field.onChange(updated)
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    {label}
                                  </FormLabel>
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Cancellation Policy</CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="cancellationPolicy"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Policy</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a cancellation policy" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(cancellationPolicyLabels).map(([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end gap-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/admin/coverage/rates")}
            >
              Cancel
            </Button>
            <Button type="submit">
              {isNew ? "Create Rate Card" : "Update Rate Card"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
} 