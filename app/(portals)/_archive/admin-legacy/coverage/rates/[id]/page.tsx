'use client'

import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { ArrowLeft, Edit, Pencil, Type, Link as LinkIcon, as LinkIcon } from "lucide-react";
import Link from "next/link"
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Badge } from "@/app/components/ui/badge";
import { PostgrestError } from "@supabase/supabase-js";
interface RateCard {
  id: string
  name: string
  serviceType: string
  baseRate: number
  hourlyRate: number
  minimumHours: number
  status: 'active' | 'inactive'
  passengerCapacity: {
    min: number
    max: number
  }
  specialRequirements: string[]
  peakPricing: {
    enabled: boolean
    multiplier: number
    conditions: string[]
  }
  cancellationPolicy: string
  createdAt: string
  updatedAt: string
}

const serviceTypeLabels: Record<string, string> = {
  airport_transfer: "Airport Transfer",
  venue_transfer: "Venue Transfer",
  city_tour: "City Tour",
  vip_service: "VIP Service",
  group_shuttle: "Group Shuttle" }

const specialRequirementLabels: Record<string, string> = {
  wheelchair_accessible: "Wheelchair Accessible",
  luggage_space: "Extra Luggage Space",
  child_seats: "Child Seats",
  luxury_vehicle: "Luxury Vehicle" }

const peakConditionLabels: Record<string, string> = {
  weekends: "Weekends",
  holidays: "Holidays",
  events: "Major Events",
  seasonal: "Seasonal Peak" }

const cancellationPolicyLabels: Record<string, string> = {
  "24h_notice": "24 Hours Notice",
  "48h_notice": "48 Hours Notice",
  "72h_notice": "72 Hours Notice" }

async function getRateCard(id: string): Promise<RateCard> {
  // TODO: Implement rate card fetching
  return {
    id,
    name: "Standard Airport Transfer",
    serviceType: "airport_transfer",
    baseRate: 100,
    hourlyRate: 75,
    minimumHours: 3,
    status: "active",
    passengerCapacity: {
      min: 1,
      max: 50 },
    specialRequirements: ["wheelchair_accessible", "luggage_space"],
    peakPricing: {
      enabled: true,
      multiplier: 1.25,
      conditions: ["weekends", "holidays"] },
    cancellationPolicy: "48h_notice",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString() }
}

export default function RateCardDetailPage() {
  const params = useParams()
  const router = useRouter()
  const rateCardId = params.id as string

  const { data: rateCard, isLoading, error } = useQuery<RateCard, PostgrestError>({
    queryKey: ["rateCard", rateCardId],
    queryFn: () => getRateCard(rateCardId) })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    )
  }

  if (error) {
    toast.error(error.message || "Failed to load rate card")
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-red-500">Error loading rate card</p>
        <Button
          variant="outline"
          onClick={() => router.push("/admin/coverage/rates")}
          className="mt-4"
        >
          Go Back
        </Button>
      </div>
    )
  }

  if (!rateCard) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p>Rate card not found</p>
        <Button
          variant="outline"
          onClick={() => router.push("/admin/coverage/rates")}
          className="mt-4"
        >
          Go Back
        </Button>
      </div>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD' }).format(amount)
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push("/admin/coverage/rates")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">{rateCard.name}</h1>
          <Badge variant={rateCard.status === "active" ? "default" : "secondary"}>
            {rateCard.status === "active" ? "Active" : "Inactive"}
          </Badge>
        </div>
        <Button onClick={() => router.push(`/admin/coverage/rates/${rateCardId}/edit`)}>
          <Pencil className="mr-2 h-4 w-4" />
          Edit Rate Card
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Service Type</p>
              <p>{serviceTypeLabels[rateCard.serviceType]}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Base Rate</p>
              <p>{formatCurrency(rateCard.baseRate)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Hourly Rate</p>
              <p>{formatCurrency(rateCard.hourlyRate)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Minimum Hours</p>
              <p>{rateCard.minimumHours} hours</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Capacity & Requirements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Passenger Capacity</p>
              <p>{rateCard.passengerCapacity.min} - {rateCard.passengerCapacity.max} passengers</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Special Requirements</p>
              <div className="flex flex-wrap gap-2 mt-1">
                {rateCard.specialRequirements.map((req) => (
                  <Badge key={req} variant="outline">
                    {specialRequirementLabels[req]}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Peak Pricing</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <Badge variant={rateCard.peakPricing.enabled ? "default" : "secondary"}>
                {rateCard.peakPricing.enabled ? "Enabled" : "Disabled"}
              </Badge>
            </div>
            {rateCard.peakPricing.enabled && (
              <>
                <div>
                  <p className="text-sm font-medium text-gray-500">Multiplier</p>
                  <p>{rateCard.peakPricing.multiplier}x</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Conditions</p>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {rateCard.peakPricing.conditions.map((condition) => (
                      <Badge key={condition} variant="outline">
                        {peakConditionLabels[condition]}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Policies & Timestamps</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Cancellation Policy</p>
              <p>{cancellationPolicyLabels[rateCard.cancellationPolicy]}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Created At</p>
              <p>{new Date(rateCard.createdAt).toLocaleDateString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Last Updated</p>
              <p>{new Date(rateCard.updatedAt).toLocaleDateString()}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 