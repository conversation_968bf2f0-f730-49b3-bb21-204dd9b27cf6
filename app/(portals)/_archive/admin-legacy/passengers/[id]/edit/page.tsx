"use client"

import { useState } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Checkbox } from "@/app/components/ui/checkbox";
import { Textarea } from "@/app/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Alert, AlertDescription } from "@/app/components/ui/alert";
import { AlertCircle, ArrowLeft, Car, Edit, Save, Share, Type, User, X, Link as LinkIcon, as LinkIcon } from "lucide-react";
import Link from "next/link"
import { useParams, useRouter } from "next/navigation";
import { Badge } from '@/app/components/ui/badge';
const carTypes = [
  "Economy",
  "Luxury",
  "SUV",
  "Mercedes Benz",
  "Van",
  "VIP Sedan",
  "Stretch Limo"
]

export default function EditPassengerPage() {
  const params = useParams()
  const router = useRouter()
  const passengerId = params.id as string

  const [formData, setFormData] = useState({
    name: "Lamar Latrelle",
    email: "<EMAIL>",
    phone: "+12124564326",
    jobTitle: "designer",
    notes: "",
    allowedCars: ["Economy", "Luxury", "SUV", "Mercedes Benz", "Van"],
    preferredCarType: "Luxury",
    travelArrangers: ["Matt Sasso"],
    canChargeGlobal: true,
    creditCards: []
  })

  const [editArrangersOpen, setEditArrangersOpen] = useState(false)
  const [newArranger, setNewArranger] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log("Form submitted:", formData)
    router.push(`/customer/passengers/${passengerId}`)
  }

  return (
    <div className="container space-y-6 p-6">
      <div className="flex items-center gap-x-4">
        <Link href={`/customer/passengers/${passengerId}`}>
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h2 className="text-2xl font-bold tracking-tight">Edit Passenger</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone">Mobile</Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="jobTitle">Job Title</Label>
              <Input
                id="jobTitle"
                value={formData.jobTitle}
                onChange={(e) => setFormData(prev => ({ ...prev, jobTitle: e.target.value }))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="notes">Notes/Instructions</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Assigned Travel Arrangers</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                {formData.travelArrangers.map((arranger, index) => (
                  <div key={index} className="flex items-center gap-x-2">
                    <span>{arranger}</span>
                    <Badge variant="secondary">Accepted</Badge>
                  </div>
                ))}
              </div>
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditArrangersOpen(true)}
              >
                Edit
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Ride Policy / Preferences</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="warning">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please note that your company has set default ride policy for this account and make sure that you have the privileges to edit it.
              </AlertDescription>
            </Alert>
            <div className="space-y-4">
              <Label>Allowed Car Types</Label>
              <div className="grid grid-cols-2 gap-4">
                {carTypes.map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={type}
                      checked={formData.allowedCars.includes(type)}
                      onCheckedChange={(checked) => {
                        setFormData(prev => ({
                          ...prev,
                          allowedCars: checked
                            ? [...prev.allowedCars, type]
                            : prev.allowedCars.filter(t => t !== type)
                        }))
                      }}
                    />
                    <label
                      htmlFor={type}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {type}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div className="grid gap-2">
              <Label>Preferred Car Type (Default)</Label>
              <Select
                value={formData.preferredCarType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, preferredCarType: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select preferred car type" />
                </SelectTrigger>
                <SelectContent>
                  {formData.allowedCars.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Payment Options</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="globalCharge"
                checked={formData.canChargeGlobal}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({ ...prev, canChargeGlobal: !!checked }))
                }
              />
              <label
                htmlFor="globalCharge"
                className="text-sm font-medium leading-none"
              >
                May Charge against Global Account Payment Methods
              </label>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-2">Passenger's Individual Corporate Credit Cards</h4>
              {formData.creditCards.length === 0 ? (
                <p className="text-sm text-muted-foreground">No credit cards added</p>
              ) : (
                <div className="space-y-2">
                  {/* Credit cards list would go here */}
                </div>
              )}
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="mt-4"
              >
                + Add Credit Card
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Earnings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-x-2">
                <span className="text-sm font-medium">GroundLink Credits:</span>
                <span className="text-sm">$0</span>
              </div>
              <Button variant="link" className="h-auto p-0">
                Share&Earn
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/customer/passengers/${passengerId}`)}
          >
            Cancel
          </Button>
          <Button type="submit">Save Changes</Button>
        </div>
      </form>

      {/* Travel Arrangers Edit Dialog */}
      <Dialog open={editArrangersOpen} onOpenChange={setEditArrangersOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Assigned Travel Arrangers</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {formData.travelArrangers.map((arranger, index) => (
              <div key={index} className="flex items-center justify-between">
                <span>{arranger}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setFormData(prev => ({
                      ...prev,
                      travelArrangers: prev.travelArrangers.filter((_, i) => i !== index)
                    }))
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Select
              value={newArranger}
              onValueChange={setNewArranger}
            >
              <SelectTrigger>
                <SelectValue placeholder="+ Add a Travel Arranger" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="new-arranger">New Arranger</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex justify-end gap-x-2">
              <Button variant="outline" onClick={() => setEditArrangersOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => {
                if (newArranger) {
                  setFormData(prev => ({
                    ...prev,
                    travelArrangers: [...prev.travelArrangers, newArranger]
                  }))
                  setNewArranger("")
                }
                setEditArrangersOpen(false)
              }}>
                Save
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
} 