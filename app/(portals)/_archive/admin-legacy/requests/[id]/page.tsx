"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import Link from "next/link"
import { formatDate } from "@/lib/utils";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs";
import { QuoteList } from "@/app/components/features/quotes/quote-list";
import { Building2, Users, MapPin, FileText, Edit, ArrowLeft, Calendar, Clock, Link as LinkIcon, as LinkIcon } from "lucide-react";
export default function TripRequestDetailsPage({
  params }: {
  params: { id: string }
}) {
  // TODO: Fetch trip request details from API
  const tripRequest = {
    id: parseInt(params.id),
    eventName: "Annual Conference 2024",
    pickupLocation: "Airport Terminal 1",
    dropoffLocation: "Grand Hotel",
    date: new Date("2024-02-15"),
    pickupTime: "09:00",
    dropoffTime: "10:00",
    passengers: 25,
    status: "pending",
    specialRequirements: "Luggage space needed for all passengers",
    event: {
      id: 1,
      name: "Annual Conference 2024" },
    company: {
      id: 1,
      name: "Acme Corporation" } }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-3">
          <Link href="/customer/requests">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Trip Request Details</h2>
          <Badge
            variant={
              tripRequest.status === "approved"
                ? "default"
                : tripRequest.status === "pending"
                  ? "secondary"
                  : tripRequest.status === "completed"
                    ? "outline"
                    : "destructive"
            }
          >
            {tripRequest.status}
          </Badge>
        </div>
        <div className="flex items-center gap-x-2">
          <Link href={`/customer/requests/${tripRequest.id}/edit`}>
            <Button variant="outline">Edit Request</Button>
          </Link>
          <Button variant="destructive">Cancel Request</Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Trip Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>{formatDate(tripRequest.date)}</span>
            </div>
            <div className="flex items-center gap-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>
                Pickup: {tripRequest.pickupTime} - Dropoff: {tripRequest.dropoffTime}
              </span>
            </div>
            <div className="flex items-center gap-x-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <div className="space-y-1">
                <div>From: {tripRequest.pickupLocation}</div>
                <div>To: {tripRequest.dropoffLocation}</div>
              </div>
            </div>
            <div className="flex items-center gap-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span>{tripRequest.passengers} passengers</span>
            </div>
            <div className="flex items-center gap-x-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <Link
                href={`/customer/companies/${tripRequest.company.id}`}
                className="hover:underline"
              >
                {tripRequest.company.name}
              </Link>
            </div>
            {tripRequest.specialRequirements && (
              <div className="flex items-start gap-x-2 pt-4 border-t">
                <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
                <p className="text-sm text-muted-foreground">
                  {tripRequest.specialRequirements}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quote Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Total Quotes
                </div>
                <div className="text-2xl font-bold">2</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Average Quote
                </div>
                <div className="text-2xl font-bold">$750</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Lowest Quote
                </div>
                <div className="text-2xl font-bold">$500</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Highest Quote
                </div>
                <div className="text-2xl font-bold">$1,000</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quotes</CardTitle>
        </CardHeader>
        <CardContent>
          <QuoteList />
        </CardContent>
      </Card>
    </div>
  )
} 