"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth/context'
import { EventList } from '@/app/components/events/event-list'
import { Button } from '@/app/components/ui/button'
import { CalendarPlus } from 'lucide-react'
import Link from 'next/link'
import { Spinner } from '@/app/components/ui/spinner'
import { UserRole } from '@/app/lib/auth/roles'

export default function EventsPage() {
  const router = useRouter()
  const { user, loading } = useAuth()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    
    // If the user is not an event manager, redirect to dashboard
    if (!loading && mounted && !user?.roles.includes('EVENT_MANAGER' as UserRole)) {
      router.push('/customer/dashboard')
    }
  }, [loading, mounted, user?.roles, router])

  // Show loading state while checking roles
  if (loading || !mounted) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    )
  }

  // If not an event manager, don't render anything (will be redirected)
  if (!user?.roles.includes('EVENT_MANAGER' as UserRole)) {
    return null
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Events</h1>
        <Link href="/customer/events/new">
          <Button className="flex items-center gap-2">
            <CalendarPlus className="h-4 w-4" />
            Create Event
          </Button>
        </Link>
      </div>
      
      <EventList userRole="event_manager" />
    </div>
  )
} 