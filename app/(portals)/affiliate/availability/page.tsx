"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { Badge } from '@/app/components/ui/badge';
import { Textarea } from '@/app/components/ui/textarea';
import { useToast } from '@/app/components/ui/use-toast';
import { 
  Calendar, 
  Plus, 
  Trash2, 
  Save, 
  AlertCircle, 
  CheckCircle2,
  Clock,
  Ban,
  Repeat
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/app/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import { Switch } from '@/app/components/ui/switch';
import { useAffiliateCompany } from '@/app/contexts/AffiliateCompanyContext';

/**
 * GUG-26: Advanced Affiliate Features - Availability Calendar
 * 
 * This component allows affiliates to:
 * - Block out dates when unavailable
 * - Set recurring availability patterns
 * - Manage service-specific availability
 * - View and edit existing date blocks
 */

interface DateBlock {
  id: string;
  company_id: string;
  block_type: 'full_day' | 'partial_day' | 'service_specific';
  start_date: string;
  end_date: string;
  start_time?: string;
  end_time?: string;
  reason: string;
  is_recurring: boolean;
  recurring_pattern?: {
    frequency: 'weekly' | 'monthly' | 'yearly';
    interval: number;
    days_of_week?: number[];
    end_date?: string;
  };
  affected_services: string[];
  created_by: string;
  created_at: string;
  updated_at: string;
}

const serviceTypes = [
  'airport',
  'hourly',
  'point_to_point',
  'event',
  'corporate'
];

const blockTypes = [
  { value: 'full_day', label: 'Full Day Block' },
  { value: 'partial_day', label: 'Partial Day Block' },
  { value: 'service_specific', label: 'Service-Specific Block' }
];

export default function AvailabilityPage() {
  const { selectedCompany } = useAffiliateCompany();
  const { toast } = useToast();
  
  const [dateBlocks, setDateBlocks] = useState<DateBlock[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    block_type: 'full_day',
    start_date: '',
    end_date: '',
    start_time: '',
    end_time: '',
    reason: '',
    is_recurring: false,
    recurring_frequency: 'weekly',
    recurring_interval: 1,
    recurring_days: [] as number[],
    recurring_end_date: '',
    affected_services: [] as string[]
  });

  // Fetch date blocks
  const fetchDateBlocks = async () => {
    if (!selectedCompany?.id) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/affiliate/date-blocks?company_id=${selectedCompany.id}`);
      const data = await response.json();

      if (data.success) {
        setDateBlocks(data.dateBlocks);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch date blocks",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error fetching date blocks:', error);
      toast({
        title: "Error",
        description: "Failed to fetch date blocks",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDateBlocks();
  }, [selectedCompany?.id]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedCompany?.id) return;

    try {
      setIsSubmitting(true);

      const payload = {
        company_id: selectedCompany.id,
        block_type: formData.block_type,
        start_date: formData.start_date,
        end_date: formData.end_date,
        start_time: formData.start_time || null,
        end_time: formData.end_time || null,
        reason: formData.reason,
        is_recurring: formData.is_recurring,
        recurring_pattern: formData.is_recurring ? {
          frequency: formData.recurring_frequency,
          interval: formData.recurring_interval,
          days_of_week: formData.recurring_days,
          end_date: formData.recurring_end_date || null
        } : null,
        affected_services: formData.affected_services
      };

      const response = await fetch('/api/affiliate/date-blocks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: "Date block created successfully",
        });
        setIsCreateDialogOpen(false);
        resetForm();
        fetchDateBlocks();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to create date block",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error creating date block:', error);
      toast({
        title: "Error",
        description: "Failed to create date block",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      block_type: 'full_day',
      start_date: '',
      end_date: '',
      start_time: '',
      end_time: '',
      reason: '',
      is_recurring: false,
      recurring_frequency: 'weekly',
      recurring_interval: 1,
      recurring_days: [],
      recurring_end_date: '',
      affected_services: []
    });
  };

  // Delete date block
  const handleDelete = async (blockId: string) => {
    try {
      const response = await fetch(`/api/affiliate/date-blocks/${blockId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: "Date block deleted successfully",
        });
        fetchDateBlocks();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to delete date block",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error deleting date block:', error);
      toast({
        title: "Error",
        description: "Failed to delete date block",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getBlockTypeLabel = (type: string) => {
    const blockType = blockTypes.find(bt => bt.value === type);
    return blockType?.label || type;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading availability calendar...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Availability Calendar</h1>
          <p className="text-gray-600">Manage your company's availability and date blocks</p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Date Block
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Date Block</DialogTitle>
              <DialogDescription>
                Block out dates when your company is unavailable for service
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="block_type">Block Type</Label>
                  <Select
                    value={formData.block_type}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, block_type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {blockTypes.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="reason">Reason</Label>
                  <Input
                    id="reason"
                    value={formData.reason}
                    onChange={(e) => setFormData(prev => ({ ...prev, reason: e?.target?.value }))}
                    placeholder="e.g., Holiday, Maintenance, Personal"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="start_date">Start Date</Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, start_date: e?.target?.value }))}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="end_date">End Date</Label>
                  <Input
                    id="end_date"
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, end_date: e?.target?.value }))}
                    required
                  />
                </div>
              </div>

              {formData.block_type === 'partial_day' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start_time">Start Time</Label>
                    <Input
                      id="start_time"
                      type="time"
                      value={formData.start_time}
                      onChange={(e) => setFormData(prev => ({ ...prev, start_time: e?.target?.value }))}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="end_time">End Time</Label>
                    <Input
                      id="end_time"
                      type="time"
                      value={formData.end_time}
                      onChange={(e) => setFormData(prev => ({ ...prev, end_time: e?.target?.value }))}
                    />
                  </div>
                </div>
              )}

              {formData.block_type === 'service_specific' && (
                <div>
                  <Label>Affected Services</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {serviceTypes.map(service => (
                      <label key={service} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData?.affected_services?.includes(service)}
                          onChange={(e) => {
                            if (e?.target?.checked) {
                              setFormData(prev => ({
                                ...prev,
                                affected_services: [...prev.affected_services, service]
                              }));
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                affected_services: prev?.affected_services?.filter(s => s !== service)
                              }));
                            }
                          }}
                          className="rounded"
                        />
                        <span className="text-sm capitalize">{service.replace('_', ' ')}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_recurring"
                  checked={formData.is_recurring}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_recurring: checked }))}
                />
                <Label htmlFor="is_recurring">Recurring Block</Label>
              </div>

              {formData.is_recurring && (
                <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="recurring_frequency">Frequency</Label>
                      <Select
                        value={formData.recurring_frequency}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, recurring_frequency: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="yearly">Yearly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="recurring_interval">Every</Label>
                      <Input
                        id="recurring_interval"
                        type="number"
                        min="1"
                        value={formData.recurring_interval}
                        onChange={(e) => setFormData(prev => ({ ...prev, recurring_interval: parseInt(e?.target?.value) }))}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="recurring_end_date">Recurring Until (Optional)</Label>
                    <Input
                      id="recurring_end_date"
                      type="date"
                      value={formData.recurring_end_date}
                      onChange={(e) => setFormData(prev => ({ ...prev, recurring_end_date: e?.target?.value }))}
                    />
                  </div>
                </div>
              )}

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Creating...' : 'Create Date Block'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Date Blocks List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Current Date Blocks
          </CardTitle>
        </CardHeader>
        <CardContent>
          {dateBlocks.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No date blocks configured</p>
              <p className="text-sm text-gray-500">Add date blocks to manage your availability</p>
            </div>
          ) : (
            <div className="space-y-4">
              {dateBlocks.map((block) => (
                <div key={block.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline">
                        {getBlockTypeLabel(block.block_type)}
                      </Badge>
                      {block.is_recurring && (
                        <Badge variant="secondary">
                          <Repeat className="h-3 w-3 mr-1" />
                          Recurring
                        </Badge>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Dates:</span> {formatDate(block.start_date)} - {formatDate(block.end_date)}
                      </div>
                      {block.start_time && block.end_time && (
                        <div>
                          <span className="font-medium">Time:</span> {block.start_time} - {block.end_time}
                        </div>
                      )}
                      <div>
                        <span className="font-medium">Reason:</span> {block.reason}
                      </div>
                      {block?.affected_services?.length > 0 && (
                        <div>
                          <span className="font-medium">Services:</span> {block.affected_services.join(', ')}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(block.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}