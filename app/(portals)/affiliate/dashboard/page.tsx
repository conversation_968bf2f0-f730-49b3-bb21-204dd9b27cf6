"use client"

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAffiliateCompany, AffiliateUserCompany } from '@/app/contexts/AffiliateCompanyContext';
import { Card } from '@/app/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/app/components/ui/table';
import { Button } from '@/app/components/ui/button';
import {
  ArrowUpRight,
  TrendingUp,
  FileText,
  Car,
  Bell,
  AlertCircle,
  Clock,
  CheckCircle2,
  ChevronRight,
  Timer
} from 'lucide-react';
import { ScrollArea } from '@/app/components/ui/scroll-area';
import { Badge } from '@/app/components/ui/badge';
import { useAffiliateAccess } from '@/app/lib/hooks/useAffiliateAccess';
import { AffiliateOnboardingBanner } from '@/app/components/ui/AffiliateOnboardingBanner';
import { OnboardingProgress } from '@/app/components/affiliate/OnboardingProgress';
import { useAffiliateMetrics } from '@/app/lib/hooks/useAffiliateMetrics';
import { TierBadge, TierProgressCard } from '@/app/components/affiliate/TierBadge';
import { useQuery } from '@tanstack/react-query';

// Mock data for demonstration
const mockTrips = [
  {
    id: 1,
    customer: 'John Doe',
    pickup: 'JFK Airport Terminal 4',
    dropoff: 'Marriott Hotel Manhattan',
    status: 'Active',
    amount: '$150',
    urgent: true,
    time: '10:30 AM',
    date: '2024-03-20',
    vehicle: 'Luxury Sedan',
    driver: 'Michael Smith'
  },
  {
    id: 2,
    customer: 'Jane Smith',
    pickup: 'Hilton Hotel',
    dropoff: 'Convention Center',
    status: 'Completed',
    amount: '$75',
    urgent: false,
    time: '2:15 PM',
    date: '2024-03-19',
    vehicle: 'SUV',
    driver: 'David Johnson'
  },
  {
    id: 3,
    customer: 'Mike Johnson',
    pickup: 'Le Bernardin Restaurant',
    dropoff: 'LaGuardia Airport',
    status: 'Active',
    amount: '$200',
    urgent: true,
    time: '4:45 PM',
    date: '2024-03-20',
    vehicle: 'Executive Sedan',
    driver: 'Robert Wilson'
  },
  {
    id: 4,
    customer: 'Sarah Williams',
    pickup: 'Javits Center',
    dropoff: 'Four Seasons Hotel',
    status: 'Scheduled',
    amount: '$120',
    urgent: false,
    time: '9:00 AM',
    date: '2024-03-21',
    vehicle: 'Luxury SUV',
    driver: 'James Brown'
  },
  {
    id: 5,
    customer: 'Robert Brown',
    pickup: 'The Plaza Hotel',
    dropoff: 'Hudson Yards',
    status: 'Completed',
    amount: '$90',
    urgent: false,
    time: '11:30 AM',
    date: '2024-03-19',
    vehicle: 'Sedan',
    driver: 'William Davis'
  }
];

const mockQuotes = [
  {
    id: 1,
    customer: 'Emily Davis',
    serviceType: 'Airport Transfer',
    dateNeeded: '2024-03-25',
    status: 'Urgent',
    pickupLocation: 'JFK Airport Terminal 5',
    dropoffLocation: 'Manhattan Hotel',
    passengers: 2,
    luggage: 'Medium',
    notes: 'Early morning flight arrival at 6:30 AM',
    requestedVehicle: 'Luxury Sedan',
    estimatedDuration: '45 mins',
    specialRequests: 'Meet & Greet service requested'
  },
  {
    id: 2,
    customer: 'Tom Wilson',
    serviceType: 'Wedding Service',
    dateNeeded: '2024-04-15',
    status: 'Pending',
    pickupLocation: 'The Plaza Hotel',
    dropoffLocation: 'St. Patrick\'s Cathedral',
    passengers: 6,
    luggage: 'Light',
    notes: 'Wedding party transfer, bride and family',
    requestedVehicle: 'Luxury SUV',
    estimatedDuration: '2 hours',
    specialRequests: 'White ribbon decoration, champagne service'
  },
  {
    id: 3,
    customer: 'Alice Cooper',
    serviceType: 'Corporate Event',
    dateNeeded: '2024-03-30',
    status: 'Urgent',
    pickupLocation: 'Hilton Midtown',
    dropoffLocation: 'World Trade Center',
    passengers: 4,
    luggage: 'Heavy',
    notes: 'VIP clients for annual board meeting',
    requestedVehicle: 'Executive Van',
    estimatedDuration: '3 hours',
    specialRequests: 'Professional driver in suit, water and refreshments'
  },
  {
    id: 4,
    customer: 'David Lee',
    serviceType: 'Hourly Service',
    dateNeeded: '2024-04-02',
    status: 'New',
    pickupLocation: 'Goldman Sachs HQ',
    dropoffLocation: 'Multiple Manhattan Locations',
    passengers: 2,
    luggage: 'Light',
    notes: 'City tour for potential investors',
    requestedVehicle: 'Mercedes S-Class',
    estimatedDuration: '6 hours',
    specialRequests: 'WiFi needed, stops at multiple financial institutions'
  },
  {
    id: 5,
    customer: 'Maria Rodriguez',
    serviceType: 'Airport Transfer',
    dateNeeded: '2024-03-28',
    status: 'Pending',
    pickupLocation: 'Newark Airport',
    dropoffLocation: 'Wall Street',
    passengers: 3,
    luggage: 'Heavy',
    notes: 'International arrival, customs clearance needed',
    requestedVehicle: 'Executive SUV',
    estimatedDuration: '1 hour',
    specialRequests: 'Flight tracking, meet & greet with name sign'
  }
];

const mockAnalytics = {
  revenueByMonth: [
    { month: 'Jan', amount: 10500, tripCount: 85, growth: '+12%' },
    { month: 'Feb', amount: 12000, tripCount: 92, growth: '+14%' },
    { month: 'Mar', amount: 12450, tripCount: 98, growth: '+4%' }
  ],
  tripsByType: [
    { type: 'Airport Transfer', count: 45, revenue: 6750, growth: '+15%' },
    { type: 'Corporate', count: 30, revenue: 4500, growth: '+10%' },
    { type: 'Wedding', count: 15, revenue: 3000, growth: '+5%' },
    { type: 'Hourly', count: 25, revenue: 3750, growth: '+8%' }
  ],
  customerSatisfaction: {
    overall: 4.8,
    categories: {
      'Driver Professionalism': 4.9,
      'Vehicle Quality': 4.8,
      'On-time Performance': 4.7,
      'Booking Experience': 4.8
    },
    recentReviews: [
      { customer: 'John D.', rating: 5, comment: 'Excellent service, very professional driver' },
      { customer: 'Sarah W.', rating: 4.5, comment: 'Great experience, clean vehicle' }
    ]
  },
  completionRate: 98.5,
  topDestinations: [
    { location: 'JFK Airport', count: 125, revenue: 18750, trend: 'up' },
    { location: 'Manhattan Hotels', count: 98, revenue: 14700, trend: 'up' },
    { location: 'Convention Centers', count: 45, revenue: 6750, trend: 'stable' },
    { location: 'Wedding Venues', count: 28, revenue: 5600, trend: 'up' }
  ],
  peakHours: [
    { hour: '6-9 AM', trips: 45, utilization: '85%' },
    { hour: '9-12 PM', trips: 30, utilization: '65%' },
    { hour: '12-3 PM', trips: 25, utilization: '55%' },
    { hour: '3-6 PM', trips: 40, utilization: '75%' }
  ]
};

const mockNotifications = [
  { id: 1, type: 'urgent', message: 'New quote request needs immediate attention', time: '5m ago' },
  { id: 2, type: 'update', message: 'Driver John arrived at pickup location', time: '10m ago' },
  { id: 3, type: 'info', message: 'Monthly report is ready for review', time: '1h ago' },
  { id: 4, type: 'urgent', message: 'Wedding service quote requires review', time: '30m ago' },
  { id: 5, type: 'update', message: 'Corporate event booking confirmed', time: '2h ago' }
];

export default function AffiliateDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const router = useRouter();
  const pathname = usePathname();
  const { userCompanies, isLoadingCompanies, selectedCompany, selectCompany, companyError } = useAffiliateCompany();
  const { showOnboardingBanner } = useAffiliateAccess();
  const {
    data: metricsData,
    loading: metricsLoading,
    error: metricsError,
    isApproved,
    metrics,
    recentActivity,
    notifications: realNotifications,
    summary
  } = useAffiliateMetrics();

  useEffect(() => {
    if (!isLoadingCompanies) {
      if (!userCompanies || userCompanies.length === 0) {
        if (pathname !== '/affiliate/onboarding/create-company') {
          router.push('/affiliate/onboarding/create-company');
        }
      } else if (userCompanies.length > 0 && !selectedCompany && selectCompany) {
        // If companies exist but none is selected, select the first one.
        selectCompany(userCompanies[0].company.id);
      }
    }
  }, [isLoadingCompanies, userCompanies, router, pathname, selectedCompany, selectCompany]);

  // Display a loading indicator or null while checking for companies and redirecting
  if (isLoadingCompanies || (!userCompanies || userCompanies.length === 0) && pathname !== '/affiliate/onboarding/create-company') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading affiliate data...</p>
      </div>
    );
  }

  // If user is on create-company page already and has no companies, let that page render
  if ((!userCompanies || userCompanies.length === 0) && pathname === '/affiliate/onboarding/create-company') {
    return null;
  }

  // Handle company error state after loading and if not redirecting
  if (!isLoadingCompanies && companyError) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-red-500">
        <p>Error loading company information:</p>
        <p>{companyError}</p>
      </div>
    );
  }

  // If still no companies after loading (and no error, and not on create page), implies something is wrong or user genuinely has none.
  // The redirect should have handled this, but as a fallback UI:
  if (!isLoadingCompanies && (!userCompanies || userCompanies.length === 0) && pathname !== '/affiliate/onboarding/create-company') {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p>No companies found. You might need to create one.</p>
        <Button onClick={() => router.push('/affiliate/onboarding/create-company')} className="mt-4">
          Create Company
        </Button>
      </div>
    );
  }

  // Use real data from metrics API
  const urgentNotifications = realNotifications.filter(n => n.type === 'urgent').length;
  const pendingOffersCount = metrics?.pendingOffers || 0;
  const pendingActionsCount = urgentNotifications + pendingOffersCount;

  // Check if company is approved using real data
  const isCompanyApproved = isApproved;
  const isCompanyPending = selectedCompany?.application_status === 'Draft' || selectedCompany?.application_status === 'Pending';

  // Get current status from selected company
  const currentStatus = selectedCompany?.status || 'pending';
  const verificationStatus = (selectedCompany as any)?.verification_status || 'pending';

  // Show loading state while fetching metrics
  if (metricsLoading && selectedCompany) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Loading dashboard data...</p>
      </div>
    );
  }

  if (showOnboardingBanner) {
    return (
      <div className="p-8">
        <AffiliateOnboardingBanner>
          <div className="mt-2 text-yellow-800">You must complete onboarding to access the dashboard.</div>
        </AffiliateOnboardingBanner>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-6">
      {/* Notification Banner for Urgent Items */}
      {(urgentNotifications > 0 || pendingOffersCount > 0) && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-r-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-yellow-400 mr-2" />
            <div className="flex-1">
              <p className="text-sm text-yellow-700">
                {urgentNotifications > 0 && (
                  <span>You have {urgentNotifications} urgent {urgentNotifications === 1 ? 'notification' : 'notifications'} that need attention. </span>
                )}
                {pendingOffersCount > 0 && (
                  <span>You have {pendingOffersCount} pending {pendingOffersCount === 1 ? 'offer' : 'offers'} to review.</span>
                )}
              </p>
            </div>
            <Button
              variant="link"
              className="ml-4 text-yellow-700 whitespace-nowrap"
              onClick={() => router.push('/affiliate/offers')}
            >
              View Now
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Status Notification Banner */}
      {currentStatus === 'active' && verificationStatus === 'verified' && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6 rounded-r-lg">
          <div className="flex items-center">
            <CheckCircle2 className="h-5 w-5 text-green-400 mr-2" />
            <div className="flex-1">
              <p className="text-sm text-green-700">
                <strong>Congratulations!</strong> Your affiliate application has been approved. You can now receive and respond to quote requests.
              </p>
            </div>
          </div>
        </div>
      )}

      {currentStatus === 'inactive' && verificationStatus === 'rejected' && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6 rounded-r-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
            <div className="flex-1">
              <p className="text-sm text-red-700">
                Your application requires updates before approval. Please review the requirements and update your information.
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="ml-4 border-red-300 text-red-700 hover:bg-red-100"
              onClick={() => router.push('/affiliate/fleet-rates')}
            >
              Update Information
            </Button>
          </div>
        </div>
      )}

      {verificationStatus === 'in_progress' && (
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6 rounded-r-lg">
          <div className="flex items-center">
            <Clock className="h-5 w-5 text-blue-400 mr-2" />
            <div className="flex-1">
              <p className="text-sm text-blue-700">
                We've requested some updates to your application. Please review and provide the additional information.
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="ml-4 border-blue-300 text-blue-700 hover:bg-blue-100"
              onClick={() => router.push('/affiliate/fleet-rates')}
            >
              Review Updates
            </Button>
          </div>
        </div>
      )}

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-2xl font-bold">Affiliate Dashboard</h1>
            {isCompanyApproved && metrics?.tierInfo && (
              <TierBadge
                tier={metrics.tierInfo.currentTier}
                score={metrics.tierInfo.tierScore}
                size="md"
              />
            )}
          </div>
          <p className="text-muted-foreground mt-1">Welcome back! Here's what needs your attention</p>
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Button variant="outline" className="flex-1 sm:flex-none justify-center">
            <Bell className="mr-2 h-4 w-4" />
            <Badge variant="secondary" className="mr-2">{pendingActionsCount}</Badge>
            Actions
          </Button>
          <Button variant="outline" className="flex-1 sm:flex-none justify-center">
            Download Report
            <ArrowUpRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Onboarding Progress */}
      <div className="mb-6">
        <OnboardingProgress />
      </div>

      {/* Stats Cards - Only show if company is approved */}
      {isCompanyApproved ? (
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="font-semibold text-muted-foreground">Active Vehicles</h2>
                <p className="text-3xl font-bold">{metrics?.activeVehicles || 0}</p>
              </div>
              <div className="relative">
                {metrics?.activeVehicles === 0 && metrics?.totalVehicles > 0 && (
                  <Badge variant="destructive" className="absolute -top-2 -right-2">!</Badge>
                )}
                <Car className="h-8 w-8 text-primary" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <span>{metrics?.totalVehicles || 0} total vehicles</span>
            </div>
            <Button
              variant="ghost"
              className="w-full mt-4 text-primary"
              onClick={() => router.push('/affiliate/fleet-rates')}
            >
              Manage Fleet
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="font-semibold text-muted-foreground">Pending Offers</h2>
                <p className="text-3xl font-bold">{metrics?.pendingOffers || 0}</p>
              </div>
              <div className="relative">
                {pendingOffersCount > 0 && (
                  <Badge variant="destructive" className="absolute -top-2 -right-2">{pendingOffersCount}</Badge>
                )}
                <FileText className="h-8 w-8 text-primary" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <span>{metrics?.totalOffers || 0} total offers</span>
            </div>
            <Button
              variant="ghost"
              className="w-full mt-4 text-primary"
              onClick={() => router.push('/affiliate/offers')}
            >
              Review Offers
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="font-semibold text-muted-foreground">Revenue</h2>
                <p className="text-3xl font-bold">${metrics?.totalRevenue?.toLocaleString() || '0'}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-primary" />
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <span>{metrics?.acceptedOffers || 0} accepted offers</span>
            </div>
            <Button
              variant="ghost"
              className="w-full mt-4 text-primary"
              onClick={() => setActiveTab('analytics')}
            >
              View Analytics
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="font-semibold text-muted-foreground">Compliance Score</h2>
                <p className="text-3xl font-bold">{metrics?.complianceScore?.toFixed(1) || '0'}%</p>
              </div>
              <div className="relative">
                {metrics?.complianceScore && metrics.complianceScore < 75 && (
                  <Badge variant="destructive" className="absolute -top-2 -right-2">!</Badge>
                )}
                <CheckCircle2 className={`h-8 w-8 ${
                  (metrics?.complianceScore ?? 0) >= 90 ? 'text-green-600' :
                  (metrics?.complianceScore ?? 0) >= 75 ? 'text-blue-600' :
                  (metrics?.complianceScore ?? 0) >= 60 ? 'text-yellow-600' : 'text-red-600'
                }`} />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <span>{metrics?.tierInfo?.currentTier || 'Standard'} tier status</span>
            </div>
            <Button
              variant="ghost"
              className="w-full mt-4 text-primary"
              onClick={() => router.push('/affiliate/duty-of-care')}
            >
              View Details
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </Card>
        </div>
      ) : (
        /* Pending Approval Message */
        <Card className="mb-6 p-6 border-amber-200 bg-amber-50">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-amber-100 rounded-full">
              <Clock className="h-6 w-6 text-amber-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-amber-800">Application Under Review</h3>
              <p className="text-amber-700 mt-1">
                Your company profile is being reviewed by our team. Complete your onboarding steps below to speed up the approval process.
              </p>
            </div>
          </div>
        </Card>
      )}

      {/* Recent Notifications - Only show if company is approved */}
      {isCompanyApproved && realNotifications.length > 0 && (
        <Card className="mb-6 p-4">
          <h3 className="text-lg font-semibold mb-4">Recent Notifications</h3>
          <ScrollArea className="h-[120px]">
            {realNotifications.map((notification) => (
              <div key={notification.id} className="flex items-start gap-3 mb-3 last:mb-0">
                {notification.type === 'urgent' && <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />}
                {notification.type === 'warning' && <Clock className="h-5 w-5 text-yellow-500 mt-0.5" />}
                {notification.type === 'info' && <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5" />}
                <div>
                  <p className="text-sm">{notification.message}</p>
                  <p className="text-xs text-muted-foreground">{notification.time}</p>
                </div>
              </div>
            ))}
          </ScrollArea>
        </Card>
      )}

      {/* Tabs - Only show if company is approved */}
      {isCompanyApproved && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="w-full justify-start">
            <TabsTrigger value="overview" className="flex-1 sm:flex-none">Overview</TabsTrigger>
            <TabsTrigger value="offers" className="flex-1 sm:flex-none">
              Offers
              {pendingOffersCount > 0 && (
                <Badge variant="destructive" className="ml-2">{pendingOffersCount}</Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex-1 sm:flex-none">
              Activity
              {recentActivity.length > 0 && (
                <Badge variant="secondary" className="ml-2">{recentActivity.length}</Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex-1 sm:flex-none">Analytics</TabsTrigger>
          </TabsList>

        <TabsContent value="overview">
          <Card className="p-4 sm:p-6">
            <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
            {recentActivity.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Customer</TableHead>
                      <TableHead className="hidden sm:table-cell">Service</TableHead>
                      <TableHead className="hidden sm:table-cell">Reference</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentActivity.map((activity) => (
                      <TableRow key={activity.id}>
                        <TableCell>{activity.customer}</TableCell>
                        <TableCell className="hidden sm:table-cell">{activity.service}</TableCell>
                        <TableCell className="hidden sm:table-cell">{activity.reference || 'N/A'}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            activity?.status?.toLowerCase() === 'accepted'
                              ? 'bg-green-100 text-green-800'
                              : activity?.status?.toLowerCase() === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : activity?.status?.toLowerCase() === 'declined'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-gray-100 text-gray-800'
                          }`}>
                            {activity.status}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">${activity.amount?.toLocaleString() || '0'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No recent activity to display</p>
                <p className="text-sm">Activity will appear here once you start receiving offers</p>
              </div>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="offers">
          <Card className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
              <h3 className="text-lg font-semibold">Recent Offers</h3>
              <Button
                variant="outline"
                onClick={() => router.push('/affiliate/offers')}
              >
                View All Offers
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </div>
            {recentActivity.length > 0 ? (
              <div className="space-y-4">
                {recentActivity.slice(0, 3).map((activity) => (
                  <div key={activity.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{activity.customer}</span>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          activity?.status?.toLowerCase() === 'accepted'
                            ? 'bg-green-100 text-green-800'
                            : activity?.status?.toLowerCase() === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : activity?.status?.toLowerCase() === 'declined'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                        }`}>
                          {activity.status}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">{activity.service}</p>
                      <p className="text-xs text-muted-foreground">{activity.reference}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${activity.amount?.toLocaleString() || '0'}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No offers yet</p>
                <p className="text-sm">Offers will appear here once your company is approved and you receive quote requests</p>
              </div>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="activity">
          <Card className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
              <h3 className="text-lg font-semibold">All Activity</h3>
              <div className="flex gap-2 w-full sm:w-auto">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 sm:flex-none"
                  onClick={() => router.push('/affiliate/offers')}
                >
                  View Offers
                </Button>
              </div>
            </div>
            {recentActivity.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Customer</TableHead>
                      <TableHead className="hidden sm:table-cell">Service Type</TableHead>
                      <TableHead className="hidden sm:table-cell">Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentActivity.map((activity) => (
                      <TableRow key={activity.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{activity.customer}</p>
                            <p className="text-xs text-muted-foreground">{activity.reference}</p>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <p className="font-medium">{activity.service}</p>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <p className="text-sm">{new Date(activity.date).toLocaleDateString()}</p>
                        </TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            activity?.status?.toLowerCase() === 'accepted'
                              ? 'bg-green-100 text-green-800'
                              : activity?.status?.toLowerCase() === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : activity?.status?.toLowerCase() === 'declined'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-gray-100 text-gray-800'
                          }`}>
                            {activity.status}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">${activity.amount?.toLocaleString() || '0'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No activity yet</p>
                <p className="text-sm">Your offer activity will appear here</p>
              </div>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
            <Card className="p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4">Performance Summary</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Total Offers</span>
                  <span className="font-medium">{metrics?.totalOffers || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Accepted Offers</span>
                  <span className="font-medium">{metrics?.acceptedOffers || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Pending Offers</span>
                  <span className="font-medium">{metrics?.pendingOffers || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Total Revenue</span>
                  <span className="font-medium">${metrics?.totalRevenue?.toLocaleString() || '0'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">This Month Revenue</span>
                  <span className="font-medium">${summary?.thisMonth?.revenue?.toLocaleString() || '0'}</span>
                </div>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4">Fleet Status</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Active Vehicles</span>
                  <span className="font-medium">{metrics?.activeVehicles || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Total Vehicles</span>
                  <span className="font-medium">{metrics?.totalVehicles || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Rate Cards</span>
                  <span className="font-medium">{metrics?.activeRateCards || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Active Drivers</span>
                  <span className="font-medium">{metrics?.activeDrivers || 0}</span>
                </div>
                {(metrics?.totalVehicles ?? 0) > 0 && (
                  <div className="pt-2 border-t">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Vehicle Utilization</span>
                      <span className="text-sm font-medium">
                        {Math.round(((metrics?.activeVehicles || 0) / (metrics?.totalVehicles || 1)) * 100)}%
                      </span>
                    </div>
                    <div className="h-2 bg-secondary rounded-full overflow-hidden mt-2">
                      <div
                        className="h-full bg-primary"
                        style={{ width: `${((metrics?.activeVehicles || 0) / (metrics?.totalVehicles || 1)) * 100}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => router.push('/affiliate/fleet-rates')}
                >
                  <Car className="mr-2 h-4 w-4" />
                  Manage Fleet & Rates
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => router.push('/affiliate/drivers')}
                >
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  Manage Drivers
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => router.push('/affiliate/offers')}
                >
                  <FileText className="mr-2 h-4 w-4" />
                  View All Offers
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => router.push('/affiliate/company')}
                >
                  <AlertCircle className="mr-2 h-4 w-4" />
                  Company Settings
                </Button>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <h3 className="text-lg font-semibold mb-4">Getting Started</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-primary">1</span>
                  </div>
                  <div>
                    <p className="font-medium">Complete your company profile</p>
                    <p className="text-muted-foreground">Add business details and contact information</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-primary">2</span>
                  </div>
                  <div>
                    <p className="font-medium">Add your vehicles and rate cards</p>
                    <p className="text-muted-foreground">Configure your fleet and pricing</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-primary">3</span>
                  </div>
                  <div>
                    <p className="font-medium">Wait for approval</p>
                    <p className="text-muted-foreground">Our team will review your application</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-primary">4</span>
                  </div>
                  <div>
                    <p className="font-medium">Start receiving offers</p>
                    <p className="text-muted-foreground">Accept offers and grow your business</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </TabsContent>
        </Tabs>
      )}
    </div>
  );
}