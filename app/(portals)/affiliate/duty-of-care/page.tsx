'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import { Progress } from '@/app/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { useRouter } from 'next/navigation'
import { useAffiliateCompany } from '@/app/contexts/AffiliateCompanyContext'
import { 
  Shield, 
  FileText, 
  Car, 
  Users, 
  Building2, 
  AlertTriangle, 
  CheckCircle2, 
  Clock, 
  TrendingUp,
  ChevronRight,
  Calendar,
  AlertCircle
} from 'lucide-react'

interface ComplianceMetrics {
  overallScore: number
  businessDocuments: {
    score: number
    completed: number
    total: number
    expiring: number
    expired: number
  }
  fleetCompliance: {
    score: number
    activeVehicles: number
    totalVehicles: number
    documentsComplete: number
    documentsTotal: number
  }
  driverCompliance: {
    score: number
    activeDrivers: number
    totalDrivers: number
    documentsComplete: number
    documentsTotal: number
  }
  recommendations: string[]
}

interface ComplianceItem {
  id: string
  category: 'business' | 'vehicle' | 'driver'
  name: string
  status: 'complete' | 'missing' | 'expiring' | 'expired'
  expiryDate?: string
  description: string
  actionUrl?: string
}

export default function DutyOfCarePage() {
  const router = useRouter()
  const { selectedCompany } = useAffiliateCompany()
  const [metrics, setMetrics] = useState<ComplianceMetrics | null>(null)
  const [complianceItems, setComplianceItems] = useState<ComplianceItem[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (selectedCompany?.id) {
      fetchComplianceData()
    }
  }, [selectedCompany?.id])

  const fetchComplianceData = async () => {
    try {
      setLoading(true)
      const [metricsResponse, itemsResponse] = await Promise.all([
        fetch(`/api/affiliate/compliance/metrics?companyId=${selectedCompany?.id}`),
        fetch(`/api/affiliate/compliance/items?companyId=${selectedCompany?.id}`)
      ])

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json()
        setMetrics(metricsData)
      }

      if (itemsResponse.ok) {
        const itemsData = await itemsResponse.json()
        setComplianceItems(itemsData.items || [])
      }
    } catch (error) {
      console.error('Error fetching compliance data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete':
        return <CheckCircle2 className="h-4 w-4 text-green-600" />
      case 'expiring':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'expired':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const config = {
      complete: { label: 'Complete', variant: 'default' as const, className: 'bg-green-100 text-green-800' },
      missing: { label: 'Required', variant: 'destructive' as const, className: undefined },
      expiring: { label: 'Expiring Soon', variant: 'secondary' as const, className: 'bg-yellow-100 text-yellow-800' },
      expired: { label: 'Expired', variant: 'destructive' as const, className: undefined }
    }
    
    const statusConfig = config[status as keyof typeof config] || config.missing
    
    return (
      <Badge variant={statusConfig.variant} className={statusConfig.className || ''}>
        {statusConfig.label}
      </Badge>
    )
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 75) return 'text-blue-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const filterItemsByCategory = (category: string) => {
    return complianceItems.filter(item => item.category === category)
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading compliance data...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6" />
            Duty of Care & Compliance
          </h1>
          <p className="text-muted-foreground mt-1">
            Monitor and manage all compliance requirements for your transportation services
          </p>
        </div>
        <Button onClick={() => router.push('/affiliate/fleet-rates')}>
          <FileText className="mr-2 h-4 w-4" />
          Manage Documents
        </Button>
      </div>

      {/* Overall Compliance Score */}
      {metrics && (
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-lg font-semibold">Overall Compliance Score</h2>
                <p className="text-sm text-muted-foreground">
                  Based on business documents, fleet compliance, and driver verification
                </p>
              </div>
              <div className="text-right">
                <div className={`text-3xl font-bold ${getScoreColor(metrics.overallScore)}`}>
                  {metrics?.overallScore?.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">Compliance Score</div>
              </div>
            </div>
            
            <Progress value={metrics.overallScore} className="h-3 mb-4" />
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted rounded-lg">
                <Building2 className="h-6 w-6 mx-auto mb-2 text-blue-600" />
                <div className="font-semibold">{metrics.businessDocuments?.score?.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Business Documents</div>
                <div className="text-xs mt-1">
                  {metrics.businessDocuments.completed}/{metrics.businessDocuments.total} complete
                </div>
              </div>
              
              <div className="text-center p-4 bg-muted rounded-lg">
                <Car className="h-6 w-6 mx-auto mb-2 text-green-600" />
                <div className="font-semibold">{metrics.fleetCompliance?.score?.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Fleet Compliance</div>
                <div className="text-xs mt-1">
                  {metrics.fleetCompliance.activeVehicles} active vehicles
                </div>
              </div>
              
              <div className="text-center p-4 bg-muted rounded-lg">
                <Users className="h-6 w-6 mx-auto mb-2 text-purple-600" />
                <div className="font-semibold">{metrics.driverCompliance?.score?.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Driver Compliance</div>
                <div className="text-xs mt-1">
                  {metrics.driverCompliance.activeDrivers} active drivers
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Compliance Details */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="business">Business</TabsTrigger>
          <TabsTrigger value="fleet">Fleet</TabsTrigger>
          <TabsTrigger value="drivers">Drivers</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Urgent Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                Urgent Actions Required
              </CardTitle>
            </CardHeader>
            <CardContent>
              {complianceItems.filter(item => item.status === 'expired' || item.status === 'expiring').length === 0 ? (
                <div className="text-center py-4">
                  <CheckCircle2 className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">No urgent actions required</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {complianceItems
                    .filter(item => item.status === 'expired' || item.status === 'expiring')
                    .slice(0, 5)
                    .map(item => (
                      <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {getStatusIcon(item.status)}
                          <div>
                            <div className="font-medium">{item.name}</div>
                            <div className="text-sm text-muted-foreground">{item.description}</div>
                            {item.expiryDate && (
                              <div className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                                <Calendar className="h-3 w-3" />
                                Expires: {new Date(item.expiryDate).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusBadge(item.status)}
                          {item.actionUrl && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(item.actionUrl!)}
                            >
                              Fix Now
                              <ChevronRight className="ml-1 h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recommendations */}
          {metrics?.recommendations && metrics.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                  Improvement Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {metrics?.recommendations?.map((recommendation, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="h-2 w-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                      <p className="text-sm">{recommendation}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="business" className="space-y-4">
          <ComplianceItemsList 
            items={filterItemsByCategory('business')}
            title="Business Documents"
            description="Essential business documentation for legal operation"
            icon={Building2}
            onItemAction={(item) => item.actionUrl && router.push(item.actionUrl)}
          />
        </TabsContent>

        <TabsContent value="fleet" className="space-y-4">
          <ComplianceItemsList 
            items={filterItemsByCategory('vehicle')}
            title="Fleet & Vehicle Compliance"
            description="Vehicle documentation and safety compliance"
            icon={Car}
            onItemAction={(item) => item.actionUrl && router.push(item.actionUrl)}
          />
        </TabsContent>

        <TabsContent value="drivers" className="space-y-4">
          <ComplianceItemsList 
            items={filterItemsByCategory('driver')}
            title="Driver Compliance"
            description="Driver licensing and background verification"
            icon={Users}
            onItemAction={(item) => item.actionUrl && router.push(item.actionUrl)}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface ComplianceItemsListProps {
  items: ComplianceItem[]
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  onItemAction: (item: ComplianceItem) => void
}

function ComplianceItemsList({ items, title, description, icon: Icon, onItemAction }: ComplianceItemsListProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete':
        return <CheckCircle2 className="h-4 w-4 text-green-600" />
      case 'expiring':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'expired':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const config = {
      complete: { label: 'Complete', variant: 'default' as const, className: 'bg-green-100 text-green-800' },
      missing: { label: 'Required', variant: 'destructive' as const, className: undefined },
      expiring: { label: 'Expiring Soon', variant: 'secondary' as const, className: 'bg-yellow-100 text-yellow-800' },
      expired: { label: 'Expired', variant: 'destructive' as const, className: undefined }
    }
    
    const statusConfig = config[status as keyof typeof config] || config.missing
    
    return (
      <Badge variant={statusConfig.variant} className={statusConfig.className || ''}>
        {statusConfig.label}
      </Badge>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon className="h-5 w-5" />
          {title}
        </CardTitle>
        <p className="text-sm text-muted-foreground">{description}</p>
      </CardHeader>
      <CardContent>
        {items.length === 0 ? (
          <div className="text-center py-8">
            <Icon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No compliance items found</p>
          </div>
        ) : (
          <div className="space-y-3">
            {items.map(item => (
              <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex items-center gap-3">
                  {getStatusIcon(item.status)}
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div className="text-sm text-muted-foreground">{item.description}</div>
                    {item.expiryDate && (
                      <div className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                        <Calendar className="h-3 w-3" />
                        Expires: {new Date(item.expiryDate).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(item.status)}
                  {item.actionUrl && item.status !== 'complete' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onItemAction(item)}
                    >
                      {item.status === 'missing' ? 'Upload' : 'Update'}
                      <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
