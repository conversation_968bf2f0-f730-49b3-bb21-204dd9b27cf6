/**
 * GUG-30: Affiliate Network Management - Network Discovery & Management Page
 * 
 * Allows affiliates to discover, join, and manage their network participations
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { Badge } from '@/app/components/ui/badge';
import { Textarea } from '@/app/components/ui/textarea';
import { useToast } from '@/app/components/ui/use-toast';
import { 
  Network, 
  Plus, 
  Settings, 
  Star,
  Users,
  Globe,
  Shield,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/app/components/ui/dialog';
import {
  <PERSON>,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import { Slider } from '@/app/components/ui/slider';

interface AffiliateNetwork {
  id: string;
  name: string;
  slug: string;
  description?: string;
  network_type: 'regional' | 'specialty' | 'premium' | 'global';
  coverage_area: Record<string, any>;
  auto_approval: boolean;
  min_rating_required: number;
  max_affiliates?: number;
  requires_insurance: boolean;
  requires_background_check: boolean;
  commission_rate: number;
  membership_fee: number;
  fee_frequency: 'monthly' | 'annual' | 'per_trip';
  brand_color: string;
  logo_url?: string;
  is_active: boolean;
  is_public: boolean;
}

interface NetworkParticipation {
  id: string;
  affiliate_company_id: string;
  network_id: string;
  participation_status: 'pending' | 'active' | 'suspended' | 'left';
  priority_level: number;
  auto_accept_quotes: boolean;
  max_concurrent_quotes: number;
  service_radius_km: number;
  available_hours: Record<string, any>;
  quotes_received: number;
  quotes_accepted: number;
  quotes_completed: number;
  average_response_time_minutes: number;
  network_rating: number;
  total_earnings: number;
  commission_paid: number;
  affiliate_networks?: AffiliateNetwork;
}

export default function AffiliateNetworksPage() {
  const [availableNetworks, setAvailableNetworks] = useState<AffiliateNetwork[]>([]);
  const [myParticipations, setMyParticipations] = useState<NetworkParticipation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isJoinDialogOpen, setIsJoinDialogOpen] = useState(false);
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
  const [selectedNetwork, setSelectedNetwork] = useState<AffiliateNetwork | null>(null);
  const [selectedParticipation, setSelectedParticipation] = useState<NetworkParticipation | null>(null);
  const { toast } = useToast();

  // Mock company ID - in real app, this would come from auth context
  const affiliateCompanyId = "mock-company-id";

  const [joinSettings, setJoinSettings] = useState({
    priority_level: 5,
    auto_accept_quotes: false,
    max_concurrent_quotes: 10,
    service_radius_km: 50,
    available_hours: {}
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Load available networks
      const networksResponse = await fetch('/api/networks');
      if (networksResponse.ok) {
        const networksData = await networksResponse.json();
        setAvailableNetworks(networksData.networks || []);
      }

      // Load my participations
      const participationsResponse = await fetch(`/api/affiliate/networks?company_id=${affiliateCompanyId}`);
      if (participationsResponse.ok) {
        const participationsData = await participationsResponse.json();
        setMyParticipations(participationsData.participations || []);
      }

    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        title: "Error",
        description: "Failed to load network data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinNetwork = async () => {
    if (!selectedNetwork) return;

    try {
      const response = await fetch('/api/affiliate/networks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          affiliate_company_id: affiliateCompanyId,
          network_id: selectedNetwork.id,
          ...joinSettings
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to join network');
      }

      toast({
        title: "Success",
        description: `Successfully joined ${selectedNetwork.name}`,
      });

      setIsJoinDialogOpen(false);
      setSelectedNetwork(null);
      loadData();

    } catch (error) {
      console.error('Error joining network:', error);
      toast({
        title: "Error",
        description: "Failed to join network",
        variant: "destructive",
      });
    }
  };

  const handleLeaveNetwork = async (participation: NetworkParticipation) => {
    if (!confirm('Are you sure you want to leave this network?')) {
      return;
    }

    try {
      const response = await fetch(`/api/affiliate/networks/${participation.id}?company_id=${affiliateCompanyId}&network_id=${participation.network_id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to leave network');
      }

      toast({
        title: "Success",
        description: "Successfully left network",
      });

      loadData();

    } catch (error) {
      console.error('Error leaving network:', error);
      toast({
        title: "Error",
        description: "Failed to leave network",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'suspended':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getNetworkTypeIcon = (type: string) => {
    switch (type) {
      case 'global':
        return <Globe className="w-5 h-5" />;
      case 'premium':
        return <Star className="w-5 h-5" />;
      case 'specialty':
        return <Shield className="w-5 h-5" />;
      default:
        return <Network className="w-5 h-5" />;
    }
  };

  const getNetworkTypeColor = (type: string) => {
    switch (type) {
      case 'global':
        return 'bg-blue-100 text-blue-800';
      case 'premium':
        return 'bg-purple-100 text-purple-800';
      case 'specialty':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isAlreadyJoined = (networkId: string) => {
    return myParticipations.some(p => p.network_id === networkId && p.participation_status !== 'left');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading networks...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Network Management</h1>
        <p className="text-gray-600">Discover and manage your network participations</p>
      </div>

      {/* My Networks Section */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">My Networks</h2>
        {myParticipations.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Network className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Network Participations</h3>
              <p className="text-gray-600 mb-4">Join networks to start receiving quotes and growing your business</p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {myParticipations.map((participation) => (
              <Card key={participation.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {getNetworkTypeIcon(participation.affiliate_networks?.network_type || 'regional')}
                        {participation.affiliate_networks?.name}
                      </CardTitle>
                      <div className="flex items-center gap-2 mt-2">
                        {getStatusIcon(participation.participation_status)}
                        <Badge variant={participation.participation_status === 'active' ? 'default' : 'secondary'}>
                          {participation.participation_status}
                        </Badge>
                        <Badge className={getNetworkTypeColor(participation.affiliate_networks?.network_type || 'regional')}>
                          {participation.affiliate_networks?.network_type}
                        </Badge>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedParticipation(participation);
                        setIsSettingsDialogOpen(true);
                      }}
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Priority Level:</span>
                      <span className="font-medium">{participation.priority_level}/10</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Quotes Received:</span>
                      <span className="font-medium">{participation.quotes_received}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Acceptance Rate:</span>
                      <span className="font-medium">
                        {participation.quotes_received > 0 
                          ? Math.round((participation.quotes_accepted / participation.quotes_received) * 100)
                          : 0}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Network Rating:</span>
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span className="font-medium">{participation?.network_rating?.toFixed(1)}</span>
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Earnings:</span>
                      <span className="font-medium">${participation?.total_earnings?.toFixed(2)}</span>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full text-red-600 hover:text-red-700"
                      onClick={() => handleLeaveNetwork(participation)}
                    >
                      Leave Network
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Available Networks Section */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">Available Networks</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {availableNetworks.map((network) => (
            <Card key={network.id} className={isAlreadyJoined(network.id) ? 'opacity-60' : ''}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {getNetworkTypeIcon(network.network_type)}
                      {network.name}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge className={getNetworkTypeColor(network.network_type)}>
                        {network.network_type}
                      </Badge>
                      {network.auto_approval && (
                        <Badge variant="outline" className="text-green-600">
                          Auto Approval
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4">{network.description}</p>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Min Rating:</span>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="font-medium">{network.min_rating_required}</span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Commission:</span>
                    <span className="font-medium">{network.commission_rate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Membership Fee:</span>
                    <span className="font-medium">
                      ${network.membership_fee}/{network.fee_frequency}
                    </span>
                  </div>
                  {network.requires_insurance && (
                    <div className="flex items-center gap-2 text-sm">
                      <Shield className="w-4 h-4 text-blue-600" />
                      <span>Insurance Required</span>
                    </div>
                  )}
                  {network.requires_background_check && (
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span>Background Check Required</span>
                    </div>
                  )}
                </div>

                <div className="mt-4 pt-4 border-t">
                  {isAlreadyJoined(network.id) ? (
                    <Button disabled className="w-full">
                      Already Joined
                    </Button>
                  ) : (
                    <Button
                      className="w-full"
                      onClick={() => {
                        setSelectedNetwork(network);
                        setIsJoinDialogOpen(true);
                      }}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Join Network
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Join Network Dialog */}
      <Dialog open={isJoinDialogOpen} onOpenChange={setIsJoinDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Join {selectedNetwork?.name}</DialogTitle>
            <DialogDescription>
              Configure your participation settings for this network
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="priority_level">Priority Level (1-10)</Label>
              <div className="mt-2">
                <Slider
                  value={[joinSettings.priority_level]}
                  onValueChange={(value) => setJoinSettings({...joinSettings, priority_level: value[0]})}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Highest (1)</span>
                  <span>Current: {joinSettings.priority_level}</span>
                  <span>Lowest (10)</span>
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="max_concurrent_quotes">Max Concurrent Quotes</Label>
              <Input
                id="max_concurrent_quotes"
                type="number"
                value={joinSettings.max_concurrent_quotes}
                onChange={(e) => setJoinSettings({...joinSettings, max_concurrent_quotes: parseInt(e?.target?.value)})}
                min={1}
                max={50}
              />
            </div>

            <div>
              <Label htmlFor="service_radius_km">Service Radius (km)</Label>
              <Input
                id="service_radius_km"
                type="number"
                value={joinSettings.service_radius_km}
                onChange={(e) => setJoinSettings({...joinSettings, service_radius_km: parseInt(e?.target?.value)})}
                min={1}
                max={500}
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="auto_accept_quotes"
                checked={joinSettings.auto_accept_quotes}
                onChange={(e) => setJoinSettings({...joinSettings, auto_accept_quotes: e?.target?.checked})}
                className="rounded"
              />
              <Label htmlFor="auto_accept_quotes">Auto-accept quotes</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsJoinDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleJoinNetwork}>
              Join Network
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Settings Dialog */}
      <Dialog open={isSettingsDialogOpen} onOpenChange={setIsSettingsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Network Settings</DialogTitle>
            <DialogDescription>
              Update your participation settings
            </DialogDescription>
          </DialogHeader>
          
          {selectedParticipation && (
            <div className="space-y-4">
              <div>
                <Label>Network: {selectedParticipation.affiliate_networks?.name}</Label>
              </div>
              
              <div>
                <Label>Status: {selectedParticipation.participation_status}</Label>
              </div>

              <div>
                <Label htmlFor="edit_priority_level">Priority Level (1-10)</Label>
                <div className="mt-2">
                  <Slider
                    value={[selectedParticipation.priority_level]}
                    onValueChange={(value) => setSelectedParticipation({
                      ...selectedParticipation,
                      priority_level: value[0]
                    })}
                    max={10}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Highest (1)</span>
                    <span>Current: {selectedParticipation.priority_level}</span>
                    <span>Lowest (10)</span>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="edit_max_concurrent_quotes">Max Concurrent Quotes</Label>
                <Input
                  id="edit_max_concurrent_quotes"
                  type="number"
                  value={selectedParticipation.max_concurrent_quotes}
                  onChange={(e) => setSelectedParticipation({
                    ...selectedParticipation,
                    max_concurrent_quotes: parseInt(e?.target?.value)
                  })}
                  min={1}
                  max={50}
                />
              </div>

              <div>
                <Label htmlFor="edit_service_radius_km">Service Radius (km)</Label>
                <Input
                  id="edit_service_radius_km"
                  type="number"
                  value={selectedParticipation.service_radius_km}
                  onChange={(e) => setSelectedParticipation({
                    ...selectedParticipation,
                    service_radius_km: parseInt(e?.target?.value)
                  })}
                  min={1}
                  max={500}
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="edit_auto_accept_quotes"
                  checked={selectedParticipation.auto_accept_quotes}
                  onChange={(e) => setSelectedParticipation({
                    ...selectedParticipation,
                    auto_accept_quotes: e?.target?.checked
                  })}
                  className="rounded"
                />
                <Label htmlFor="edit_auto_accept_quotes">Auto-accept quotes</Label>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSettingsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => {
              // Handle update
              setIsSettingsDialogOpen(false);
            }}>
              Update Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}