"use client";

import { useState, useEffect, useMemo } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form";
import { Badge } from "@/app/components/ui/badge";
import {
  Car,
  Plus,
  Image as ImageIcon,
  Check,
  X,
  Loader2,
  Upload,
  Trash2,
  Save,
  ChevronDown,
  ChevronUp,
  Shield,
  FileText,
  Edit,
  AlertCircle,
} from "lucide-react";
import { Checkbox } from "@/app/components/ui/checkbox";
import { ToggleGroup, ToggleGroupItem } from "@/app/components/ui/toggle-group";
import { Switch } from "@/app/components/ui/switch";
import { cn } from "@/lib/utils";
import { useToast } from "@/app/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/app/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/app/components/ui/alert-dialog";
import { useAffiliateCompany } from "@/app/contexts/AffiliateCompanyContext";
import { getSupabaseClient } from "@/lib/supabase";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/app/components/ui/tooltip";
import { useAffiliateAccess } from "@/app/lib/hooks/useAffiliateAccess";
import { AffiliateOnboardingBanner } from "@/app/components/ui/AffiliateOnboardingBanner";
import {
  uploadFile,
  getDocumentStatus,
  type DocumentMetadata,
} from "@/app/lib/utils/file-upload";
import { useAuth } from "@/lib/auth/context";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import { VipServicesConfig } from "@/app/components/affiliate/VipServicesConfig";

// Vehicle types and their base configurations - Master List
const vehicleTypes = [
  {
    id: "sedan",
    name: "sedan",
    displayName: "Luxury Sedan",
    passengers: "1–4",
    description: "Corporate transfers, airport pickups",
  },
  {
    id: "executive_sedan",
    name: "executive_sedan",
    displayName: "Executive Sedan",
    passengers: "1–4",
    description: "C-level clients, premium service",
  },
  {
    id: "suv",
    name: "suv",
    displayName: "Luxury SUV",
    passengers: "1–6",
    description: "Small groups, premium luggage capacity",
  },
  {
    id: "executive_suv",
    name: "executive_suv",
    displayName: "Executive SUV",
    passengers: "1–6",
    description: "High-end clients, VIPs",
  },
  {
    id: "sprinter",
    name: "sprinter",
    displayName: "Mercedes Sprinter",
    passengers: "8–14",
    description: "Event shuttles, group transfers",
  },
  {
    id: "shuttle_van",
    name: "shuttle_van",
    displayName: "Shuttle Van",
    passengers: "8–15",
    description: "Hotel/venue shuttles",
  },
  {
    id: "mini_coach",
    name: "mini_coach",
    displayName: "Mini Coach",
    passengers: "16–35",
    description: "Weddings, conferences, tours",
  },
  {
    id: "motor_coach",
    name: "motor_coach",
    displayName: "Motor Coach",
    passengers: "36–56",
    description: "Large groups, long-distance",
  },
  {
    id: "limousine",
    name: "limousine",
    displayName: "Stretch Limousine",
    passengers: "6–10",
    description: "Weddings, special occasions",
  },
  {
    id: "party_bus",
    name: "party_bus",
    displayName: "Party Bus",
    passengers: "15–40",
    description: "Entertainment, nightlife events",
  },
  {
    id: "electric_sedan",
    name: "electric_sedan",
    displayName: "Electric Sedan",
    passengers: "1–4",
    description: "Eco-conscious corporate travel",
  },
  {
    id: "electric_suv",
    name: "electric_suv",
    displayName: "Electric SUV",
    passengers: "1–6",
    description: "Luxury + sustainability",
  },
  {
    id: "luxury_van",
    name: "luxury_van",
    displayName: "Luxury Van",
    passengers: "6–10",
    description: "VIP transport with privacy",
  },
  {
    id: "accessible_van",
    name: "accessible_van",
    displayName: "Wheelchair-Accessible Van",
    passengers: "1–6",
    description: "Accessible transport for events",
  },
  // Legacy support for existing data
  {
    id: "SUV",
    name: "SUV",
    displayName: "SUV (Legacy)",
    passengers: "1–6",
    description: "Legacy SUV type for existing data",
  },
];

// Mock data for the fleet
const mockFleet = [
  {
    id: "V001",
    typeId: "sedan",
    type: "LUXURY SEDAN",
    make: "Mercedes-Benz",
    model: "S580",
    year: "2024",
    capacity: 4,
    status: "active",
    images: {
      exterior: "/vehicles/s-class-exterior.jpg",
      interior: "/vehicles/s-class-interior.jpg",
    },
  },
  {
    id: "V002",
    typeId: "suv",
    type: "SUV",
    make: "Cadillac",
    model: "Escalade",
    year: "2024",
    capacity: 6,
    status: "active",
    images: {
      exterior: "/vehicles/escalade-exterior.jpg",
      interior: "/vehicles/escalade-interior.jpg",
    },
  },
];

// Define interfaces for fetched data
interface Vehicle {
  id: string;
  affiliate_company_id: string | null;
  type: string | null;
  make: string | null;
  model: string | null;
  year: number | null;
  license_plate?: string | null;
  capacity: number | null;
  amenities?: string[] | null;
  status: string | null;
  images?: { exterior?: string | null; interior?: string | null } | null; // Refined type
  documents?: {
    insurance?: string | null;
    registration?: string | null;
    insurance_expiry?: string | null;
    registration_expiry?: string | null;
  } | null;
  created_at: string;
  updated_at: string;
  vin?: string | null;
}

interface RateCard {
  id: string;
  affiliate_company_id: string | null;
  vehicle_type: string | null; // Name/label, e.g., "LUXURY SEDAN"
  pricing_model_type?:
  | "P2P"
  | "DT"
  | "HOURLY_CHARTER"
  | "AIRPORT_TRANSFER"
  | "NONE"
  | null;
  p2p_point_to_point_rate?: number | null;
  p2p_extra_hour_rate?: number | null;
  dt_base_fee?: number | null;
  dt_per_mile_rate?: number | null;
  dt_per_hour_rate?: number | null;
  dt_min_miles?: number | null;
  dt_min_hours?: number | null;
  airport_transfer_flat_rate?: number | null;
  charter_hourly_rate?: number | null;
  charter_min_hours?: number | null;
  gratuity_percentage?: number | null;
  additional_fees?: any | null; // Keep as any if structure is dynamic or unknown
  seasonal_multipliers?: any | null; // Keep as any
  status: string | null;
  created_at: string;
  updated_at: string;
  base_rate?: number | null;
  per_mile_rate?: number | null;
  per_hour_rate?: number | null;
  minimum_hours?: number | null;
}

// Adjust vehicleSchema if API expects numbers for year/capacity
const vehicleSchema = z.object({
  type: z.string().min(1, "Please select a vehicle type name/label"),
  make: z.string().min(1, "Please enter the make"),
  model: z.string().min(1, "Please enter the model"),
  year: z
    .string()
    .min(4, "Please enter a valid year")
    .regex(/^\d{4}$/, "Year must be 4 digits"),
  capacity: z
    .string()
    .min(1, "Please enter the capacity")
    .regex(/^\d+$/, "Capacity must be a number"),
  license_plate: z.string().min(1, "License plate is required"),
  insurance_policy_number: z.string().optional(),
  status: z.enum(["active", "inactive", "maintenance"]).default("active"),
  images: z.array(z.string().url()).optional(),
});

// UPDATED rateSchema to reflect individual, potentially nullable, numeric inputs
// This schema might be used by a sub-form or for validating individual rate fields
const individualRateFieldSchema = z.number().nullable().optional();

// Main form schema for the entire page (not used directly by useForm for the whole page, but for parts)
// Zod schema for individual rate card data structure (used to build payload for API)
const rateCardApiSchema = z.object({
  vehicle_type: z.string().min(1),
  pricing_model_type: z
    .enum(["P2P", "DT", "HOURLY_CHARTER", "AIRPORT_TRANSFER"])
    .default("P2P"),
  p2p_point_to_point_rate: z.number().nullable().optional(),
  p2p_extra_hour_rate: z.number().nullable().optional(),
  dt_base_fee: z.number().nullable().optional(),
  dt_per_mile_rate: z.number().nullable().optional(),
  dt_per_hour_rate: z.number().nullable().optional(),
  dt_min_miles: z.number().nullable().optional(),
  dt_min_hours: z.number().int().nullable().optional(),
  airport_transfer_flat_rate: z.number().nullable().optional(),
  charter_hourly_rate: z.number().nullable().optional(),
  charter_min_hours: z.number().int().nullable().optional(),
  gratuity_percentage: z.number().min(0).max(100).nullable().optional(),
  status: z.string().default("draft"),
  // additional_fees, seasonal_multipliers, special_rates are JSONB and handled as `any` for now in API,
  // specific UI for them would be a further enhancement.
});

// Type for the rateInputs state
type RateInputValues = Partial<
  Omit<
    RateCard,
    | "id"
    | "affiliate_company_id"
    | "created_at"
    | "updated_at"
    | "additional_fees"
    | "seasonal_multipliers"
    | "status"
  >
>;

const pricingTypes = [
  { value: "P2P", label: "Point-to-Point" },
  { value: "DT", label: "Distance & Time" },
  { value: "HOURLY_CHARTER", label: "Hourly Charter" },
  { value: "AIRPORT_TRANSFER", label: "Airport Transfer" },
];

export default function FleetRatesPage() {
  const { toast } = useToast();
  const { selectedCompany } = useAffiliateCompany();
  const { user } = useAuth();
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [rateCards, setRateCards] = useState<RateCard[]>([]);
  const [rateInputs, setRateInputs] = useState<Record<string, RateInputValues>>(
    {}
  );
  const [loading, setLoading] = useState(false);
  const [imageUploading, setImageUploading] = useState<string | null>(null);
  const [showImageDialog, setShowImageDialog] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<string | null>(null);
  const [showDocumentDialog, setShowDocumentDialog] = useState(false);
  const [documentUploading, setDocumentUploading] = useState<string | null>(
    null
  );
  const [showEditVehicleDialog, setShowEditVehicleDialog] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState<Vehicle | null>(null);
  const [activePricingModel, setActivePricingModel] = useState<
    Record<
      string,
      "P2P" | "DT" | "HOURLY_CHARTER" | "AIRPORT_TRANSFER" | "NONE"
    >
  >({});
  const [focusedVehicleTypeId, setFocusedVehicleTypeId] = useState<
    string | null
  >(null);
  const [newVehicleIds, setNewVehicleIds] = useState<string[]>([]); // Track new vehicles
  const [newRateCategoryIds, setNewRateCategoryIds] = useState<string[]>([]); // Track new rate categories
  const { showOnboardingBanner } = useAffiliateAccess();
  const [activeTab, setActiveTab] = useState("fleet-rates");

  // Custom CSS to override Radix UI toggle button styles
  useEffect(() => {
    // Add custom CSS to ensure toggle items keep their background color when selected
    const style = document.createElement("style");
    style.innerHTML = `
      /* Override radix reset styles */
      [data-radix-toggle-group-item][data-state="on"] {
        background-image: none !important;
        background-color: inherit !important;
        color: inherit !important;
      }

      /* Custom title tooltip styling */
      .custom-tooltip {
        position: relative;
        cursor: help;
      }

      .custom-tooltip::before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 125%;
        left: 50%;
        transform: translateX(-50%);
        padding: 8px 12px;
        background-color: #000;
        color: #fff;
        border-radius: 4px;
        white-space: pre-line;
        min-width: 180px;
        max-width: 320px;
        text-align: left;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s, visibility 0.2s;
        z-index: 9999;
        pointer-events: none;
        font-weight: 500;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .custom-tooltip:hover::before {
        opacity: 1;
        visibility: visible;
      }

      .dark .custom-tooltip::before {
        background-color: #fff;
        color: #000;
      }

      /* Custom class for vehicle type dropdown */
      .vehicle-type-dropdown {
        width: 100%;
        max-width: 100%;
      }
      .vehicle-type-dropdown span {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        display: block !important;
      }
      /* Fixed width select items */
      .vehicle-type-select-content .radix-select-item {
        white-space: nowrap !important;
      }
      /* Toggle styles */
      .toggle-group-item[data-state="on"] {
        background-image: none !important;
        color: inherit !important;
      }
      .toggle-group-item.green-bg {
        background-color: rgb(220, 252, 231) !important;
      }
      .toggle-group-item.red-bg {
        background-color: rgb(254, 226, 226) !important;
      }
      .dark .toggle-group-item.green-bg {
        background-color: rgb(6, 78, 59) !important;
      }
      .dark .toggle-group-item.red-bg {
        background-color: rgb(127, 29, 29) !important;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const activeVehicleTypeIds = useMemo(() => {
    const idsFromVehicles = Array.isArray(vehicles)
      ? vehicles
        .map((v) => vehicleTypes.find((vt) => vt.name === v.type)?.id)
        .filter(Boolean) as string[]
      : [];
    const idsFromRateCards = Array.isArray(rateCards)
      ? rateCards
        .map((rc) => vehicleTypes.find((vt) => vt.name === rc.vehicle_type)?.id)
        .filter(Boolean) as string[]
      : [];
    return Array.from(new Set([...idsFromVehicles, ...idsFromRateCards]));
  }, [vehicles, rateCards]);

  // Vehicle form
  const vehicleForm = useForm<z.infer<typeof vehicleSchema>>({
    resolver: zodResolver(vehicleSchema),
    defaultValues: {
      status: "active",
      type: "", // Will be set by dropdown or initial load focus
      make: "",
      model: "",
      year: "",
      capacity: "",
      license_plate: "",
      insurance_policy_number: "",
    },
  });

  useEffect(() => {
    const supabase = getSupabaseClient();

    const fetchData = async () => {
      if (!selectedCompany || !supabase) {
        if (!supabase) {
          toast({
            title: "Error",
            description: "Supabase client not available.",
            variant: "destructive",
          });
        }
        return;
      }
      setLoading(true);
      try {
        const [resVehicles, resRateCards] = await Promise.all([
          supabase
            .from("vehicles")
            .select("*")
            .eq("affiliate_company_id", selectedCompany.id),
          supabase
            .from("rate_cards")
            .select("*")
            .eq("affiliate_company_id", selectedCompany.id),
        ]);

        if (resVehicles.error) throw resVehicles.error;
        if (resRateCards.error) throw resRateCards.error;

        const rawVehiclesData = resVehicles.data || [];
        const processedVehiclesData: Vehicle[] = rawVehiclesData.map(
          (v_raw: any) => {
            let processedImagesData: {
              exterior?: string | null;
              interior?: string | null;
            } | null = null;
            const rawImgData = v_raw.images;

            if (
              typeof rawImgData === "object" &&
              rawImgData !== null &&
              !Array.isArray(rawImgData)
            ) {
              processedImagesData = {}; // Initialize as an empty object
              if (
                Object.prototype.hasOwnProperty.call(rawImgData, "exterior") &&
                (typeof rawImgData.exterior === "string" ||
                  rawImgData.exterior === null)
              ) {
                processedImagesData.exterior = rawImgData.exterior;
              }
              if (
                Object.prototype.hasOwnProperty.call(rawImgData, "interior") &&
                (typeof rawImgData.interior === "string" ||
                  rawImgData.interior === null)
              ) {
                processedImagesData.interior = rawImgData.interior;
              }
            } else if (rawImgData !== null) {
              console.warn(
                `Vehicle ID ${v_raw.id} has incompatible 'images' data type: ${typeof rawImgData}. Setting to null.`
              );
              // processedImagesData remains null
            }

            return {
              ...v_raw, // Spread all properties from raw data
              type: v_raw.vehicle_type, // Map vehicle_type to type for frontend
              capacity: v_raw.passenger_capacity, // Map passenger_capacity to capacity for frontend
              images: processedImagesData, // Override with processed images data
            } as Vehicle; // Assert the final object as Vehicle type
          }
        );
        setVehicles(processedVehiclesData);

        const rateCardsData = resRateCards.data || [];
        setRateCards(rateCardsData as RateCard[]);

        // Initialize data from both rate cards and vehicles for active types
        const initialRateInputs: Record<string, RateInputValues> = {};
        const initialPricingModels: Record<
          string,
          "P2P" | "DT" | "HOURLY_CHARTER" | "AIRPORT_TRANSFER" | "NONE"
        > = {};
        const tempActiveTypeIds = new Set<string>();

        // Process rate cards
        if (rateCardsData.length > 0) {
          rateCardsData.forEach((rc: any) => {
            const vehicleTypeId = vehicleTypes.find(
              (vt) => vt.name === rc.vehicle_type
            )?.id;
            if (vehicleTypeId) {
              tempActiveTypeIds.add(vehicleTypeId);
              // Helper function to safely convert to number
              const safeNumber = (value: any): number | null => {
                if (value === null || value === undefined || value === "")
                  return null;
                const num = Number(value);
                return isNaN(num) ? null : num;
              };

              // Parse special_rates JSON if it exists
              let specialRates = {};
              if (rc.special_rates) {
                try {
                  specialRates =
                    typeof rc.special_rates === "string"
                      ? JSON.parse(rc.special_rates)
                      : rc.special_rates;
                } catch (e) {
                  console.warn(
                    "Failed to parse special_rates:",
                    rc.special_rates
                  );
                }
              }

              // Use the reverse mapping function to convert database fields to frontend fields
              const mappedData = mapDatabaseToFrontend(rc);

              // Apply safeNumber to all numeric fields
              Object.keys(mappedData).forEach(key => {
                if (typeof mappedData[key] === 'string' && !isNaN(Number(mappedData[key]))) {
                  mappedData[key] = safeNumber(mappedData[key]);
                }
              });

              initialRateInputs[vehicleTypeId] = mappedData;
              initialPricingModels[vehicleTypeId] =
                mapPricingModelFromDatabase(rc.pricing_model) || "NONE";
            }
          });
        }

        // Process vehicles to find their types and initialize if not already from rate cards
        processedVehiclesData.forEach((vehicle) => {
          if (vehicle.type) {
            const vehicleTypeId = vehicleTypes.find(
              (vt) => vt.name === vehicle.type
            )?.id;
            if (vehicleTypeId) {
              tempActiveTypeIds.add(vehicleTypeId);
              if (!initialRateInputs[vehicleTypeId]) {
                // Only if not already set by a rate card
                initialRateInputs[vehicleTypeId] = {
                  vehicle_type: vehicle.type,
                  pricing_model_type: "P2P", // Default to P2P
                };
                initialPricingModels[vehicleTypeId] = "P2P";
              }
            }
          }
        });

        // Set the states
        setRateInputs(initialRateInputs);
        setActivePricingModel(initialPricingModels);
        // activeVehicleTypeIds is derived via useMemo, so no direct setting here.
        // If there's a focused type from a previous session or deep link, you might want to restore it here.
        // For now, if there are active types, focus the first one by default.
        const derivedActiveIds = Array.from(tempActiveTypeIds);
        if (derivedActiveIds.length > 0 && !focusedVehicleTypeId) {
          const firstActiveId = derivedActiveIds[0];
          setFocusedVehicleTypeId(firstActiveId);
          vehicleForm.setValue("type", firstActiveId); // Also set the form value
        } else if (focusedVehicleTypeId) {
          // If a focusedVehicleTypeId already exists (e.g. from user interaction before data load finished or persisted state),
          // ensure the form reflects it.
          vehicleForm.setValue("type", focusedVehicleTypeId);
        }

        toast({
          title: "Data loaded",
          description: "Fleet and rates data loaded successfully.",
        });
      } catch (error: any) {
        console.error("Failed to fetch initial data:", error);
        toast({
          title: "Error loading data",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    if (selectedCompany?.id) fetchData();
  }, [toast, selectedCompany]); // Dependencies for the fetchData effect

  const isVehicleTypeInactive = (typeId: string) => {
    const vehicleTypeDefinition = vehicleTypes.find((vt) => vt.id === typeId);
    if (!vehicleTypeDefinition) return false;
    const typeVehicles = vehicles.filter(
      (v) => v.type === vehicleTypeDefinition.name
    );
    return (
      typeVehicles.length > 0 &&
      typeVehicles.every((v) => v.status !== "active")
    );
  };

  // Get document status with expiry checking
  const getVehicleDocumentStatus = (
    vehicle: Vehicle,
    docType: "insurance" | "registration"
  ) => {
    const docUrl = vehicle.documents?.[docType];
    const expiryDate =
      vehicle.documents?.[
      `${docType}_expiry` as keyof typeof vehicle.documents
      ];

    if (!docUrl) {
      return { status: "missing", color: "text-red-600", icon: X };
    }

    if (expiryDate) {
      const status = getDocumentStatus(expiryDate as string);
      if (status.status === "expired") {
        return {
          status: "expired",
          color: "text-red-600",
          icon: X,
          message: status.message,
        };
      } else if (status.status === "warning") {
        return {
          status: "warning",
          color: "text-yellow-600",
          icon: AlertCircle,
          message: status.message,
        };
      }
    }

    return { status: "valid", color: "text-green-600", icon: Check };
  };

  // Toggle vehicle status
  const toggleVehicleStatus = async (vehicleId: string) => {
    const vehicle = vehicles.find((v) => v.id === vehicleId);
    if (!vehicle) return;

    const newStatus = vehicle.status === "active" ? "inactive" : "active";

    try {
      const response = await fetch(`/api/affiliate/vehicles/${vehicleId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "X-Affiliate-Company-ID": selectedCompany?.id || "",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update vehicle status");
      }

      // Update local state only after successful API call
      setVehicles(
        vehicles.map((v) =>
          v.id === vehicleId ? { ...v, status: newStatus } : v
        )
      );

      toast({
        title: "Vehicle Updated",
        description: `Vehicle status changed to ${newStatus}.`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Handle vehicle form submission
  const onVehicleSubmit = async (values: z.infer<typeof vehicleSchema>) => {
    setLoading(true);
    try {
      const vehicleTypeDefinition = vehicleTypes.find(
        (vt) => vt.id === values.type
      );
      if (!vehicleTypeDefinition) {
        toast({
          title: "Error",
          description: "Invalid vehicle type selected.",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      const payload = {
        ...values,
        type: vehicleTypeDefinition.name,
      };

      const response = await fetch("/api/affiliate/vehicles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Affiliate-Company-ID": selectedCompany?.id || "",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to add vehicle");
      }
      const newVehicle: Vehicle = await response.json();
      setVehicles((prev) => [...prev, newVehicle]);
      setNewVehicleIds((prev) => [...prev, newVehicle.id]); // Highlight this row
      vehicleForm.reset();
      toast({
        title: "Vehicle Added",
        description: `${newVehicle.make} ${newVehicle.model} has been added.`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to handle changes in rate input fields
  const handleRateInputChange = (
    vehicleTypeId: string,
    fieldName: keyof RateInputValues,
    value: string | number | null | undefined
  ) => {
    const numericValue =
      typeof value === "string"
        ? value === ""
          ? null
          : parseFloat(value)
        : value;
    console.log(
      `🔥 Rate Cards: handleRateInputChange called - vehicleTypeId: ${vehicleTypeId}, fieldName: ${fieldName}, value: ${value}, numericValue: ${numericValue}`
    );

    setRateInputs((prev) => {
      const newState = {
        ...prev,
        [vehicleTypeId]: {
          ...prev[vehicleTypeId],
          [fieldName]: numericValue,
          vehicle_type:
            vehicleTypes.find((vt) => vt.id === vehicleTypeId)?.name || "", // Ensure vehicle_type name is included
        },
      };
      console.log(`🔥 Rate Cards: New rateInputs state:`, newState);
      return newState;
    });
  };

  // Function to handle pricing model change
  const handlePricingModelChange = (
    vehicleTypeId: string,
    newModel: "P2P" | "DT" | "HOURLY_CHARTER" | "AIRPORT_TRANSFER" | "NONE"
  ) => {
    setActivePricingModel((prev) => ({
      ...prev,
      [vehicleTypeId]: newModel,
    }));
    // Also update rateInputs to store the selected model
    setRateInputs((prev) => ({
      ...prev,
      [vehicleTypeId]: {
        // Preserve existing rates for this vehicle type if any, or initialize
        ...(prev[vehicleTypeId] || {}),
        pricing_model_type: newModel,
        vehicle_type:
          vehicleTypes.find((vt) => vt.id === vehicleTypeId)?.name || "",
      },
    }));
  };

  // Helper function to map pricing models to database values
  const mapPricingModelToDatabase = (frontendModel: string) => {
    const mapping: Record<string, string> = {
      'P2P': 'flat_rate',
      'DT': 'distance_based', // Fixed: was 'distance_time', should be 'distance_based'
      'HOURLY_CHARTER': 'hourly',
      'AIRPORT_TRANSFER': 'flat_rate' // Airport transfers are typically flat rate
    };
    return mapping[frontendModel] || 'flat_rate';
  };

  // Helper function to map frontend field names to database column names
  const mapFieldToDatabase = (fieldName: string, value: any) => {
    const fieldMapping: Record<string, string> = {
      'p2p_point_to_point_rate': 'base_rate',
      'p2p_extra_hour_rate': 'per_hour_rate',
      'dt_base_fee': 'minimum_charge',
      'dt_per_mile_rate': 'per_mile_rate',
      'dt_per_hour_rate': 'per_hour_rate',
      'airport_transfer_flat_rate': 'airport_fee',
      'charter_hourly_rate': 'per_hour_rate',
    };

    const dbFieldName = fieldMapping[fieldName] || fieldName;
    return { [dbFieldName]: value };
  };

  // Helper function to map database field names back to frontend field names
  const mapDatabaseToFrontend = (rc: any) => {
    console.log('🔥 Rate Cards: Mapping database record to frontend:', rc);

    const reverseMapping: Record<string, string> = {
      'base_rate': 'p2p_point_to_point_rate',
      'per_hour_rate': 'p2p_extra_hour_rate', // Note: this maps to multiple frontend fields
      'minimum_charge': 'dt_base_fee',
      'per_mile_rate': 'dt_per_mile_rate',
      'airport_fee': 'airport_transfer_flat_rate',
    };

    const frontendData: any = {
      vehicle_type: rc.vehicle_type,
      pricing_model_type: mapPricingModelFromDatabase(rc.pricing_model),
    };

    // Map database fields to frontend fields
    Object.keys(reverseMapping).forEach(dbField => {
      if (rc[dbField] !== undefined && rc[dbField] !== null) {
        const frontendField = reverseMapping[dbField];
        frontendData[frontendField] = rc[dbField];

        // Handle special cases where one database field maps to multiple frontend fields
        if (dbField === 'per_hour_rate') {
          frontendData['dt_per_hour_rate'] = rc[dbField];
          frontendData['charter_hourly_rate'] = rc[dbField];
        }
      }
    });

    console.log('🔥 Rate Cards: Mapped frontend data:', frontendData);
    return frontendData;
  };

  // Helper function to map database pricing model back to frontend
  const mapPricingModelFromDatabase = (dbPricingModel: string) => {
    const modelMapping: Record<string, string> = {
      'flat_rate': 'P2P',
      'distance_based': 'DT',
      'time_based': 'DT',
      'hourly': 'HOURLY_CHARTER',
      'zone_based': 'AIRPORT_TRANSFER',
    };
    return modelMapping[dbPricingModel] || 'P2P';
  };

  // Updated onRateSubmit function
  const onRateSubmit = async () => {
    setLoading(true);
    const payloads: any[] = [];

    // Process all active vehicle types
    activeVehicleTypeIds.forEach((typeId) => {
      const vehicleTypeDefinition = vehicleTypes.find((vt) => vt.id === typeId);
      if (!vehicleTypeDefinition) return;

      const currentRateInput = rateInputs[typeId] || {};
      const currentPricingModel = activePricingModel[typeId] || "P2P";

      // Map pricing model to service type
      const getServiceType = (pricingModel: string) => {
        switch (pricingModel) {
          case "P2P": return "point_to_point";
          case "HOURLY_CHARTER": return "hourly_charter";
          case "AIRPORT_TRANSFER": return "airport_transfer";
          case "DT": return "point_to_point"; // Distance/Time is still point to point
          default: return "point_to_point";
        }
      };

      // Map frontend data to database schema
      const payload: any = {
        vehicle_type: vehicleTypeDefinition.name,
        pricing_model: mapPricingModelToDatabase(currentPricingModel),
        service_type: getServiceType(currentPricingModel), // Required field - map from pricing model
        status: "active", // Use database enum value
        name: `${vehicleTypeDefinition.name} - ${currentPricingModel}`,
        description: `Rate card for ${vehicleTypeDefinition.name}`,
      };

      // Only add rate values that are actually set and valid
      Object.keys(currentRateInput).forEach((key) => {
        const rateKey = key as keyof RateInputValues;
        const value = currentRateInput[rateKey];

        // Skip undefined, null, empty string, and non-rate fields
        if (
          value !== undefined &&
          value !== null &&
          value !== "" &&
          key !== "vehicle_type" &&
          key !== "pricing_model_type" &&
          key !== "id" &&
          key !== "effective_date" &&
          key !== "expiry_date" &&
          key !== "created_at" &&
          key !== "updated_at" &&
          key !== "affiliate_company_id" &&
          key !== "organization_id" &&
          key !== "name" &&
          key !== "description"
        ) {
          const numericValue =
            typeof value === "string" ? parseFloat(value) : value;
          if (!isNaN(numericValue) && numericValue >= 0) { // Allow 0 values
            // Map frontend field names to database column names
            const mappedField = mapFieldToDatabase(key, numericValue);
            Object.assign(payload, mappedField);
          }
        }
      });

      // Only include required fields based on pricing model
      // Check for mapped database field names, not frontend field names
      switch (currentPricingModel) {
        case "P2P":
          // P2P requires at least base_rate (mapped from p2p_point_to_point_rate)
          if (!payload.base_rate || payload.base_rate <= 0) {
            return; // Skip this payload if required field is missing
          }
          break;
        case "HOURLY_CHARTER":
          // Hourly requires at least per_hour_rate (mapped from charter_hourly_rate)
          if (!payload.per_hour_rate || payload.per_hour_rate <= 0) {
            return; // Skip this payload if required field is missing
          }
          break;
        case "AIRPORT_TRANSFER":
          // Airport requires at least airport_fee (mapped from airport_transfer_flat_rate)
          if (!payload.airport_fee || payload.airport_fee <= 0) {
            return; // Skip this payload if required field is missing
          }
          break;
        case "DT":
          // DT requires at least per_mile_rate and per_hour_rate
          if (!payload.per_mile_rate || payload.per_mile_rate <= 0 ||
            !payload.per_hour_rate || payload.per_hour_rate <= 0) {
            return; // Skip this payload if required fields are missing
          }
          break;
      }

      payloads.push(payload);
    });

    if (payloads.length === 0) {
      toast({
        title: "No rates to save",
        description: "Please select vehicle types and configure their rates.",
        variant: "default",
      });
      setLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/affiliate/rate-cards", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Affiliate-Company-ID": selectedCompany?.id || "",
        },
        body: JSON.stringify(payloads),
      });

      const responseData = await response.json();
      console.log("🔥 Rate Cards: Response received:", responseData);

      if (!response.ok) {
        // If responseData is an array of results, find the first error
        const errorDetail = Array.isArray(responseData)
          ? responseData.find((r) => !r.success)?.error
          : responseData.error;
        throw new Error(errorDetail || "Failed to save rates");
      }

      console.log("🔥 Rate Cards: Processing successful response");
      // Update local rateCards state with successfully saved/updated ones
      const successfullySaved = (
        Array.isArray(responseData)
          ? responseData.filter((r) => r.success)
          : [responseData]
      ).map((r) => r.data);
      console.log(
        "🔥 Rate Cards: Successfully saved items:",
        successfullySaved
      );
      setRateCards((prev) => {
        const updated = [...prev];
        successfullySaved.forEach((savedRc) => {
          const index = updated.findIndex(
            (rc) =>
              rc.affiliate_company_id === savedRc.affiliate_company_id &&
              rc.vehicle_type === savedRc.vehicle_type
          );
          if (index !== -1) updated[index] = savedRc;
          else updated.push(savedRc);
        });
        return updated;
      });
      // Highlight these rate categories
      setNewRateCategoryIds((prev) => [
        ...prev,
        ...successfullySaved
          .map(
            (savedRc) =>
              vehicleTypes.find((vt) => vt.name === savedRc.vehicle_type)?.id
          )
          .filter((id): id is string => typeof id === "string"),
      ]);

      console.log("🔥 Rate Cards: About to show success toast");
      toast({
        title: "Rates Saved",
        description: "Rate configurations have been successfully saved.",
      });

      // Refresh rate cards data after successful save
      console.log("🔥 Rate Cards: Refreshing data after save");
      try {
        const response = await fetch("/api/affiliate/rate-cards", {
          headers: {
            "X-Affiliate-Company-ID": selectedCompany?.id || "",
          },
        });
        if (response.ok) {
          const freshData = await response.json();
          console.log("🔥 Rate Cards: Fresh data loaded:", freshData);

          // Extract the data array from the API response
          const freshRateCards = freshData?.data || freshData;

          if (Array.isArray(freshRateCards)) {
            setRateCards(freshRateCards);

            // Update rate inputs with fresh data
            const updatedRateInputs: Record<string, RateInputValues> = {};
            freshRateCards.forEach((rc: any) => {
              const vehicleTypeId = vehicleTypes.find(
                (vt) => vt.name === rc.vehicle_type
              )?.id;
              if (vehicleTypeId) {
                const safeNumber = (value: any): number | null => {
                  if (value === null || value === undefined || value === "")
                    return null;
                  const num = Number(value);
                  return isNaN(num) ? null : num;
                };

                // Parse special_rates JSON if it exists
                let specialRates = {};
                if (rc.special_rates) {
                  try {
                    specialRates =
                      typeof rc.special_rates === "string"
                        ? JSON.parse(rc.special_rates)
                        : rc.special_rates;
                  } catch (e) {
                    console.warn(
                      "Failed to parse special_rates:",
                      rc.special_rates
                    );
                  }
                }

                // Use the reverse mapping function to convert database fields to frontend fields
                const mappedData = mapDatabaseToFrontend(rc);

                // Apply safeNumber to all numeric fields
                Object.keys(mappedData).forEach(key => {
                  if (typeof mappedData[key] === 'string' && !isNaN(Number(mappedData[key]))) {
                    mappedData[key] = safeNumber(mappedData[key]);
                  }
                });

                updatedRateInputs[vehicleTypeId] = mappedData;
              }
            });

            setRateInputs(updatedRateInputs); // Replace entirely to force re-render
            console.log(
              "🔥 Rate Cards: Rate inputs replaced with fresh data:",
              updatedRateInputs
            );
          } else {
            console.error("🔥 Rate Cards: Fresh data is not an array:", freshRateCards);
            toast({
              title: "Data Refresh Error",
              description: "Unable to refresh rate card data. Please reload the page.",
              variant: "destructive",
            });
          }
        }
      } catch (refreshError) {
        console.error("🔥 Rate Cards: Error refreshing data:", refreshError);
      }
    } catch (error: any) {
      console.error("🔥 Rate Cards: Error in save process:", error);
      toast({
        title: "Error Saving Rates",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      console.log(
        "🔥 Rate Cards: Save process completed, setting loading to false"
      );
      setLoading(false);
    }
  };

  // Handle image upload
  const handleImageUpload = async (
    vehicleId: string,
    type: "exterior" | "interior"
  ) => {
    try {
      setImageUploading(vehicleId);
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setVehicles(
        vehicles.map((v) =>
          v.id === vehicleId
            ? {
              ...v,
              images: {
                ...(v.images || {}), // Ensure v.images is an object before spreading
                [type]: `/vehicles/${v.make}-${v.model}-${type}.jpg`,
              },
            }
            : v
        )
      );

      toast({
        title: "Image Uploaded",
        description: `The ${type} image has been uploaded successfully.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to upload image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setImageUploading(null);
      setShowImageDialog(false);
    }
  };

  // Handle vehicle deletion
  const handleDeleteVehicle = async (vehicleId: string) => {
    try {
      setLoading(true);
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setVehicles(vehicles.filter((v) => v.id !== vehicleId));
      toast({
        title: "Vehicle Removed",
        description: "The vehicle has been removed from your fleet.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove vehicle. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle document upload
  const handleDocumentUpload = async (
    vehicleId: string,
    type: "insurance" | "registration",
    file?: File,
    expiryDate?: string
  ) => {
    if (!file) {
      // Trigger file input
      const input = document.createElement("input");
      input.type = "file";
      input.accept = ".pdf,.jpg,.jpeg,.png";
      input.onchange = (e) => {
        const selectedFile = (e.target as HTMLInputElement).files?.[0];
        if (selectedFile) {
          handleDocumentUpload(vehicleId, type, selectedFile, expiryDate);
        }
      };
      input.click();
      return;
    }

    if (!user || !selectedCompany) {
      toast({
        title: "Error",
        description: "User or company not found.",
        variant: "destructive",
      });
      return;
    }

    try {
      setDocumentUploading(vehicleId);

      const metadata: DocumentMetadata = {
        vehicleId,
        companyId: selectedCompany.id,
        documentType: type,
        expiryDate,
        uploadedBy: user.id,
      };

      const result = await uploadFile(file, metadata);

      if (!result.success) {
        throw new Error(result.error || "Upload failed");
      }

      // Update vehicle documents in state
      setVehicles(
        vehicles.map((v) =>
          v.id === vehicleId
            ? {
              ...v,
              documents: {
                ...(v.documents || {}),
                [type]: result.url,
                [`${type}_expiry`]: expiryDate || null,
              },
            }
            : v
        )
      );

      toast({
        title: "Document Uploaded",
        description: `The ${type} document has been uploaded successfully.`,
      });
    } catch (error) {
      console.error("Document upload error:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to upload document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDocumentUploading(null);
      setShowDocumentDialog(false);
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
      {showOnboardingBanner && <AffiliateOnboardingBanner />}
      <div>
        <h3 className="text-lg sm:text-xl font-medium">
          Fleet & Rates Management
        </h3>
        <p className="text-sm text-muted-foreground">
          Manage your vehicles, rates, and VIP services
        </p>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="fleet-rates">Fleet & Rates</TabsTrigger>
          <TabsTrigger value="vip-services">VIP Services</TabsTrigger>
        </TabsList>

        <TabsContent value="fleet-rates" className="space-y-4">
          {/* Sequential flow container with connecting elements */}
          <div className="space-y-2 relative">
            {/* Section 2 (was Vehicle Details, now effectively Step 1 of vehicle/rate config) */}
            {selectedCompany && (
              <Card
                id="vehicle-details-section-card"
                className="relative z-10 bg-slate-50"
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Step 1 - Add Vehicles</CardTitle>
                      <CardDescription>
                        Add specific vehicles to your fleet. Active categories
                        will appear for rate configuration.
                      </CardDescription>
                    </div>
                    <div className="text-xs text-muted-foreground flex items-center gap-1">
                      <Badge variant="outline" className="mr-1">
                        Step 1
                      </Badge>
                      Add vehicles to activate categories for rating
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <Form {...vehicleForm}>
                      <div className="p-4 rounded-md bg-slate-100">
                        {" "}
                        {/* More pronounced background */}
                        <form
                          onSubmit={vehicleForm.handleSubmit(onVehicleSubmit)}
                          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-12 gap-4 items-end"
                        >
                          <FormField
                            control={vehicleForm.control}
                            name="type"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2 lg:col-span-2">
                                <FormLabel>Vehicle Type</FormLabel>
                                <div className="relative">
                                  <Select
                                    onValueChange={(value) => {
                                      field.onChange(value);
                                      setFocusedVehicleTypeId(value || null);
                                    }}
                                    value={field.value || ""}
                                  >
                                    <FormControl>
                                      <SelectTrigger className="relative pr-16 vehicle-type-dropdown">
                                        <SelectValue placeholder="Select vehicle category" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent className="vehicle-type-select-content min-w-[240px]">
                                      {vehicleTypes.map((type) => {
                                        return (
                                          <SelectItem
                                            key={type.id}
                                            value={type.id}
                                            className="whitespace-nowrap radix-select-item"
                                          >
                                            {type.displayName || type.name}
                                          </SelectItem>
                                        );
                                      })}
                                    </SelectContent>
                                  </Select>
                                </div>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={vehicleForm.control}
                            name="make"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-1 lg:col-span-2">
                                <FormLabel>Make</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="e.g. Mercedes-Benz"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={vehicleForm.control}
                            name="model"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-1 lg:col-span-1">
                                <FormLabel>Model</FormLabel>
                                <FormControl>
                                  <Input placeholder="e.g. S580" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={vehicleForm.control}
                            name="year"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-1 lg:col-span-1">
                                <FormLabel>Year</FormLabel>
                                <FormControl>
                                  <Input placeholder="e.g. 2024" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={vehicleForm.control}
                            name="capacity"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-1 lg:col-span-1">
                                <FormLabel>Capacity</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="e.g. 4"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={vehicleForm.control}
                            name="license_plate"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-1 lg:col-span-2">
                                <FormLabel>License Plate</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="e.g. ABC-123"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={vehicleForm.control}
                            name="insurance_policy_number"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-1 lg:col-span-2">
                                <FormLabel>Insurance Policy #</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="e.g. 123456789"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <div className="flex sm:col-span-2 lg:col-span-1 items-center">
                            <Button
                              type="submit"
                              disabled={loading}
                              className="h-10 w-full"
                            >
                              {loading ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Plus className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </form>
                      </div>

                      {/* Active Vehicle Categories section MOVED ABOVE TABLE */}
                      {activeVehicleTypeIds.length > 0 && (
                        <div className="mb-4 mt-6">
                          <h4 className="text-sm font-medium mb-2">
                            Active Vehicle Categories:
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {vehicleTypes.map((vehicleType) => {
                              const isActive = activeVehicleTypeIds.includes(
                                vehicleType.id
                              );
                              const tooltipText = isActive
                                ? "You will receive offers for this vehicle type"
                                : "You won't receive offers for this vehicle type";

                              return (
                                <Badge
                                  key={vehicleType.id}
                                  variant="outline"
                                  className={cn(
                                    "px-3 py-1 text-sm hover:bg-transparent custom-tooltip",
                                    isActive
                                      ? "bg-green-100 text-green-800 hover:bg-green-100"
                                      : "bg-red-100 text-red-800 hover:bg-red-100"
                                  )}
                                  data-tooltip={tooltipText}
                                >
                                  {vehicleType.name}
                                </Badge>
                              );
                            })}
                          </div>
                          <p className="text-xs text-muted-foreground mt-2">
                            You will receive Quote requests for these active
                            categories where you have added vehicles and set up
                            rate cards.
                          </p>
                        </div>
                      )}

                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="min-w-[120px]">
                                Vehicle Type
                              </TableHead>
                              <TableHead className="min-w-[150px]">
                                Make & Model
                              </TableHead>
                              <TableHead className="min-w-[80px]">
                                Year
                              </TableHead>
                              <TableHead className="min-w-[80px]">
                                Capacity
                              </TableHead>
                              <TableHead className="min-w-[150px]">
                                Status
                              </TableHead>
                              <TableHead className="min-w-[120px]">
                                Actions
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {vehicles.map((vehicle) => {
                              const vehicleTypeDefinition = vehicleTypes.find(
                                (vt) => vt.name === vehicle.type
                              );
                              const hasRateCard = Array.isArray(rateCards) && rateCards.some(
                                (rc) => rc.vehicle_type === vehicle.type
                              );
                              const isActive =
                                vehicle.status === "active" && hasRateCard;
                              const isDeactivated =
                                vehicle.status === "deactivated";
                              const isDisabled =
                                vehicle.status !== "active" &&
                                vehicle.status !== "deactivated";
                              const isMissingRate = !hasRateCard;

                              // Status badge type
                              let statusType = "pending";
                              let statusLabel = "Pending";
                              let tooltipMsg = "";

                              if (isActive) {
                                statusType = "active";
                                statusLabel = "Active";
                                tooltipMsg =
                                  "This vehicle is active and has rate cards configured. You will receive offers for this vehicle type.";
                              } else if (isDeactivated) {
                                statusType = "excluded";
                                statusLabel = "Excluded from Offers";
                                tooltipMsg =
                                  "This vehicle type is excluded from receiving offers. You chose not to carry this vehicle type. Activate to start receiving offers again.";
                              } else if (isDisabled && isMissingRate) {
                                statusType = "disabled";
                                statusLabel = "Disabled & Missing Rates";
                                tooltipMsg =
                                  "This vehicle is disabled and missing rate cards. Enable it and add rate cards to receive offers.";
                              } else if (isDisabled) {
                                statusType = "disabled";
                                statusLabel = "Disabled";
                                tooltipMsg =
                                  "This vehicle is disabled. Enable it to start receiving offers for this vehicle type.";
                              } else if (isMissingRate) {
                                statusType = "error";
                                statusLabel = "Missing Rates";
                                tooltipMsg =
                                  "No rate cards configured for this vehicle type. Add rate cards to receive offers.";
                              }

                              return (
                                <TableRow key={vehicle.id}>
                                  <TableCell>{vehicle.type}</TableCell>
                                  <TableCell>
                                    {vehicle.make} {vehicle.model}
                                  </TableCell>
                                  <TableCell>{vehicle.year}</TableCell>
                                  <TableCell>{vehicle.capacity}</TableCell>
                                  <TableCell>
                                    <div className="flex items-center gap-2">
                                      <Switch
                                        checked={vehicle.status === "active"}
                                        onCheckedChange={() =>
                                          toggleVehicleStatus(vehicle.id)
                                        }
                                      />
                                      <Badge
                                        variant={
                                          statusType === "active"
                                            ? "success"
                                            : statusType === "excluded"
                                              ? "destructive"
                                              : statusType === "disabled"
                                                ? "outline"
                                                : statusType === "error"
                                                  ? "destructive"
                                                  : "secondary"
                                        }
                                        className="ml-2 whitespace-nowrap custom-tooltip"
                                        data-tooltip={tooltipMsg}
                                      >
                                        {statusLabel}
                                      </Badge>
                                    </div>
                                  </TableCell>
                                  <TableCell>
                                    <div className="flex items-center gap-1">
                                      {/* Images Button */}
                                      <Dialog
                                        open={
                                          showImageDialog &&
                                          selectedVehicle === vehicle.id
                                        }
                                        onOpenChange={(open) => {
                                          setShowImageDialog(open);
                                          if (!open) setSelectedVehicle(null);
                                        }}
                                      >
                                        <DialogTrigger asChild>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                              setSelectedVehicle(vehicle.id)
                                            }
                                            title="Upload vehicle images"
                                          >
                                            <ImageIcon className="h-4 w-4" />
                                          </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                          <DialogHeader>
                                            <DialogTitle>
                                              Upload Vehicle Images
                                            </DialogTitle>
                                            <DialogDescription>
                                              Upload exterior and interior
                                              images of your vehicle
                                            </DialogDescription>
                                          </DialogHeader>
                                          <div className="grid gap-4 py-4">
                                            <div className="grid grid-cols-2 gap-4">
                                              <div>
                                                <Label>Exterior Image</Label>
                                                <div className="mt-2">
                                                  <Button
                                                    onClick={() =>
                                                      handleImageUpload(
                                                        vehicle.id,
                                                        "exterior"
                                                      )
                                                    }
                                                    disabled={
                                                      imageUploading ===
                                                      vehicle.id
                                                    }
                                                  >
                                                    {imageUploading ===
                                                      vehicle.id ? (
                                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                                    ) : (
                                                      <Upload className="h-4 w-4 mr-2" />
                                                    )}
                                                    Upload Exterior
                                                  </Button>
                                                </div>
                                              </div>
                                              <div>
                                                <Label>Interior Image</Label>
                                                <div className="mt-2">
                                                  <Button
                                                    onClick={() =>
                                                      handleImageUpload(
                                                        vehicle.id,
                                                        "interior"
                                                      )
                                                    }
                                                    disabled={
                                                      imageUploading ===
                                                      vehicle.id
                                                    }
                                                  >
                                                    {imageUploading ===
                                                      vehicle.id ? (
                                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                                    ) : (
                                                      <Upload className="h-4 w-4 mr-2" />
                                                    )}
                                                    Upload Interior
                                                  </Button>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        </DialogContent>
                                      </Dialog>

                                      {/* Insurance Document Button */}
                                      <Dialog
                                        open={
                                          showDocumentDialog &&
                                          selectedVehicle === vehicle.id
                                        }
                                        onOpenChange={(open) => {
                                          setShowDocumentDialog(open);
                                          if (!open) setSelectedVehicle(null);
                                        }}
                                      >
                                        <DialogTrigger asChild>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                              setSelectedVehicle(vehicle.id)
                                            }
                                            title={`Upload insurance documents${getVehicleDocumentStatus(vehicle, "insurance").message ? ` - ${getVehicleDocumentStatus(vehicle, "insurance").message}` : ""}`}
                                          >
                                            <Shield
                                              className={`h-4 w-4 ${getVehicleDocumentStatus(vehicle, "insurance").color}`}
                                            />
                                          </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                          <DialogHeader>
                                            <DialogTitle>
                                              Upload Insurance Documents
                                            </DialogTitle>
                                            <DialogDescription>
                                              Upload insurance certificate and
                                              set expiry date
                                            </DialogDescription>
                                          </DialogHeader>
                                          <div className="grid gap-4 py-4">
                                            <div className="space-y-2">
                                              <Label>
                                                Insurance Certificate
                                              </Label>
                                              <Button
                                                onClick={() =>
                                                  handleDocumentUpload(
                                                    vehicle.id,
                                                    "insurance"
                                                  )
                                                }
                                                disabled={
                                                  documentUploading ===
                                                  vehicle.id
                                                }
                                                className="w-full"
                                              >
                                                {documentUploading ===
                                                  vehicle.id ? (
                                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                                ) : (
                                                  <Upload className="h-4 w-4 mr-2" />
                                                )}
                                                Upload Insurance Certificate
                                              </Button>
                                            </div>
                                            <div className="space-y-2">
                                              <Label>Expiry Date</Label>
                                              <Input
                                                type="date"
                                                placeholder="Insurance expiry date"
                                              />
                                            </div>
                                          </div>
                                        </DialogContent>
                                      </Dialog>

                                      {/* Registration Document Button */}
                                      <Dialog
                                        open={
                                          showDocumentDialog &&
                                          selectedVehicle ===
                                          vehicle.id + "-registration"
                                        }
                                        onOpenChange={(open) => {
                                          setShowDocumentDialog(open);
                                          if (!open) setSelectedVehicle(null);
                                        }}
                                      >
                                        <DialogTrigger asChild>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                              setSelectedVehicle(
                                                vehicle.id + "-registration"
                                              )
                                            }
                                            title={`Upload registration documents${getVehicleDocumentStatus(vehicle, "registration").message ? ` - ${getVehicleDocumentStatus(vehicle, "registration").message}` : ""}`}
                                          >
                                            <FileText
                                              className={`h-4 w-4 ${getVehicleDocumentStatus(vehicle, "registration").color}`}
                                            />
                                          </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                          <DialogHeader>
                                            <DialogTitle>
                                              Upload Registration Documents
                                            </DialogTitle>
                                            <DialogDescription>
                                              Upload vehicle registration and
                                              set expiry date
                                            </DialogDescription>
                                          </DialogHeader>
                                          <div className="grid gap-4 py-4">
                                            <div className="space-y-2">
                                              <Label>
                                                Vehicle Registration
                                              </Label>
                                              <Button
                                                onClick={() =>
                                                  handleDocumentUpload(
                                                    vehicle.id,
                                                    "registration"
                                                  )
                                                }
                                                disabled={
                                                  documentUploading ===
                                                  vehicle.id
                                                }
                                                className="w-full"
                                              >
                                                {documentUploading ===
                                                  vehicle.id ? (
                                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                                ) : (
                                                  <Upload className="h-4 w-4 mr-2" />
                                                )}
                                                Upload Registration Document
                                              </Button>
                                            </div>
                                            <div className="space-y-2">
                                              <Label>Expiry Date</Label>
                                              <Input
                                                type="date"
                                                placeholder="Registration expiry date"
                                              />
                                            </div>
                                          </div>
                                        </DialogContent>
                                      </Dialog>

                                      {/* Edit Vehicle Button */}
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          setEditingVehicle(vehicle);
                                          setShowEditVehicleDialog(true);
                                        }}
                                        title="Edit vehicle details"
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>

                                      <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                          <Button variant="ghost" size="sm">
                                            <Trash2 className="h-4 w-4 text-destructive" />
                                          </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                          <AlertDialogHeader>
                                            <AlertDialogTitle>
                                              Remove Vehicle
                                            </AlertDialogTitle>
                                            <AlertDialogDescription>
                                              Are you sure you want to remove
                                              this vehicle? This action cannot
                                              be undone.
                                            </AlertDialogDescription>
                                          </AlertDialogHeader>
                                          <AlertDialogFooter>
                                            <AlertDialogCancel>
                                              Cancel
                                            </AlertDialogCancel>
                                            <AlertDialogAction
                                              onClick={() =>
                                                handleDeleteVehicle(vehicle.id)
                                              }
                                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                            >
                                              Remove
                                            </AlertDialogAction>
                                          </AlertDialogFooter>
                                        </AlertDialogContent>
                                      </AlertDialog>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </div>
                    </Form>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Visual connector between Vehicle Details and Rate Configuration */}
            {activeVehicleTypeIds.length > 0 && selectedCompany && (
              <div className="flex justify-center items-center h-6 relative z-0">
                <div className="h-full w-px bg-border"></div>
                <div className="absolute bottom-0 w-3 h-3 rounded-full bg-primary -mb-1.5 z-10"></div>
              </div>
            )}

            {/* Rate Configuration Card - Updated JSX (New Section 2) */}
            {activeVehicleTypeIds.length > 0 && selectedCompany && (
              <Card className="relative z-10">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>
                        Step 2 - Add your Vehicle Category rate
                      </CardTitle>
                      <CardDescription>
                        Configure pricing for each active vehicle category. One
                        rate card is created per category.
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <Badge variant="outline" className="mr-1">
                          Step 2
                        </Badge>
                        Set at least one pricing model per vehicle category
                      </div>
                      <Button onClick={onRateSubmit} disabled={loading}>
                        {loading ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4 mr-2" />
                        )}
                        Save All Rates
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="min-w-[150px]">
                            Vehicle Category
                          </TableHead>
                          <TableHead className="min-w-[250px]">
                            Pricing Model
                          </TableHead>
                          <TableHead className="min-w-[300px]">
                            Rate Details
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {activeVehicleTypeIds.map((typeId) => {
                          const vehicleType = vehicleTypes.find(
                            (vt) => vt.id === typeId
                          );
                          if (!vehicleType) return null;

                          const currentPricingModel =
                            activePricingModel[typeId] || "P2P";
                          const currentRateInput = rateInputs[typeId] || {};
                          console.log(
                            "🔥 Rate Cards: Rendering input for typeId:",
                            typeId,
                            "currentRateInput:",
                            currentRateInput
                          );
                          console.log(
                            "🔥 Rate Cards: All rateInputs:",
                            rateInputs
                          );

                          return (
                            <TableRow
                              key={typeId}
                              className={
                                newRateCategoryIds.includes(typeId)
                                  ? "bg-primary/10 border-l-4 border-primary"
                                  : ""
                              }
                            >
                              <TableCell className="font-medium">
                                {vehicleType.name}
                              </TableCell>
                              <TableCell>
                                <ToggleGroup
                                  type="single"
                                  value={currentPricingModel}
                                  onValueChange={(
                                    value:
                                      | "P2P"
                                      | "DT"
                                      | "HOURLY_CHARTER"
                                      | "AIRPORT_TRANSFER"
                                      | "NONE"
                                  ) => {
                                    if (value)
                                      handlePricingModelChange(typeId, value);
                                  }}
                                  className="flex-nowrap overflow-x-auto"
                                >
                                  {[
                                    "P2P",
                                    "DT",
                                    "HOURLY_CHARTER",
                                    "AIRPORT_TRANSFER",
                                  ].map((model) => {
                                    // Determine if this pricing model is filled for this typeId
                                    let isModelFilled = false;
                                    if (model === "P2P") {
                                      isModelFilled =
                                        !!currentRateInput.p2p_point_to_point_rate;
                                    } else if (model === "DT") {
                                      isModelFilled =
                                        !!currentRateInput.dt_base_fee ||
                                        !!currentRateInput.dt_per_mile_rate ||
                                        !!currentRateInput.dt_per_hour_rate;
                                    } else if (model === "HOURLY_CHARTER") {
                                      isModelFilled =
                                        !!currentRateInput.charter_hourly_rate;
                                    } else if (model === "AIRPORT_TRANSFER") {
                                      isModelFilled =
                                        !!currentRateInput.airport_transfer_flat_rate;
                                    }
                                    const isSelected =
                                      currentPricingModel === model;
                                    const colorClass = isModelFilled
                                      ? "green-bg"
                                      : "red-bg";
                                    const borderClass = isSelected
                                      ? "ring-2 ring-primary"
                                      : "";
                                    return (
                                      <ToggleGroupItem
                                        key={model}
                                        value={
                                          model as
                                          | "P2P"
                                          | "DT"
                                          | "HOURLY_CHARTER"
                                          | "AIRPORT_TRANSFER"
                                          | "NONE"
                                        }
                                        aria-label={model}
                                        className={`toggle-group-item ${colorClass} ${borderClass} mx-1 min-w-[80px]`}
                                        data-state={isSelected ? "on" : "off"}
                                        style={{ backgroundImage: "none" }}
                                      >
                                        {model === "P2P"
                                          ? "P2P"
                                          : model === "DT"
                                            ? "D+T"
                                            : model === "HOURLY_CHARTER"
                                              ? "Hourly"
                                              : "Airport"}
                                      </ToggleGroupItem>
                                    );
                                  })}
                                </ToggleGroup>
                              </TableCell>
                              <TableCell className="p-2 bg-slate-100 rounded-md">
                                {" "}
                                {/* More pronounced background */}
                                {/* P2P Fields */}
                                {currentPricingModel === "P2P" && (
                                  <div className="grid grid-cols-2 gap-2">
                                    <div className="space-y-1">
                                      <Label htmlFor={`p2p_rate_${typeId}`}>
                                        Point to Point ($)
                                      </Label>
                                      <Input
                                        id={`p2p_rate_${typeId}`}
                                        type="number"
                                        placeholder="e.g. 150"
                                        value={(() => {
                                          const value =
                                            currentRateInput.p2p_point_to_point_rate ??
                                            "";
                                          console.log(
                                            `🔥 Rate Cards: P2P input value for ${typeId}:`,
                                            value,
                                            "from currentRateInput:",
                                            currentRateInput.p2p_point_to_point_rate
                                          );
                                          return value;
                                        })()}
                                        onChange={(e) =>
                                          handleRateInputChange(
                                            typeId,
                                            "p2p_point_to_point_rate",
                                            e?.target?.value
                                          )
                                        }
                                      />
                                    </div>
                                    <div className="space-y-1">
                                      <Label
                                        htmlFor={`p2p_extra_hour_${typeId}`}
                                      >
                                        Extra Hour ($)
                                      </Label>
                                      <Input
                                        id={`p2p_extra_hour_${typeId}`}
                                        type="number"
                                        placeholder="e.g. 75"
                                        value={
                                          currentRateInput.p2p_extra_hour_rate ??
                                          ""
                                        }
                                        onChange={(e) =>
                                          handleRateInputChange(
                                            typeId,
                                            "p2p_extra_hour_rate",
                                            e?.target?.value
                                          )
                                        }
                                      />
                                    </div>
                                  </div>
                                )}
                                {/* Distance + Time Fields */}
                                {currentPricingModel === "DT" && (
                                  <div className="grid grid-cols-5 gap-2">
                                    <div className="space-y-1">
                                      <Label htmlFor={`dt_base_${typeId}`}>
                                        Base Rate ($)
                                      </Label>
                                      <Input
                                        id={`dt_base_${typeId}`}
                                        type="number"
                                        placeholder="e.g. 50"
                                        value={
                                          currentRateInput.dt_base_fee ?? ""
                                        }
                                        onChange={(e) =>
                                          handleRateInputChange(
                                            typeId,
                                            "dt_base_fee",
                                            e?.target?.value
                                          )
                                        }
                                      />
                                    </div>
                                    <div className="space-y-1">
                                      <Label htmlFor={`dt_per_mile_${typeId}`}>
                                        Per Mile ($)
                                      </Label>
                                      <Input
                                        id={`dt_per_mile_${typeId}`}
                                        type="number"
                                        placeholder="e.g. 3.5"
                                        step="0.01"
                                        value={
                                          currentRateInput.dt_per_mile_rate ??
                                          ""
                                        }
                                        onChange={(e) =>
                                          handleRateInputChange(
                                            typeId,
                                            "dt_per_mile_rate",
                                            e?.target?.value
                                          )
                                        }
                                      />
                                    </div>
                                    <div className="space-y-1">
                                      <Label htmlFor={`dt_per_hour_${typeId}`}>
                                        Per Hour ($)
                                      </Label>
                                      <Input
                                        id={`dt_per_hour_${typeId}`}
                                        type="number"
                                        placeholder="e.g. 60"
                                        value={
                                          currentRateInput.dt_per_hour_rate ??
                                          ""
                                        }
                                        onChange={(e) =>
                                          handleRateInputChange(
                                            typeId,
                                            "dt_per_hour_rate",
                                            e?.target?.value
                                          )
                                        }
                                      />
                                    </div>
                                    <div className="space-y-1">
                                      <Label htmlFor={`dt_min_miles_${typeId}`}>
                                        Min. Miles
                                      </Label>
                                      <Input
                                        id={`dt_min_miles_${typeId}`}
                                        type="number"
                                        placeholder="e.g. 10"
                                        value={
                                          currentRateInput.dt_min_miles ?? ""
                                        }
                                        onChange={(e) =>
                                          handleRateInputChange(
                                            typeId,
                                            "dt_min_miles",
                                            e?.target?.value
                                          )
                                        }
                                      />
                                    </div>
                                    <div className="space-y-1">
                                      <Label htmlFor={`dt_min_hours_${typeId}`}>
                                        Min. Hours
                                      </Label>
                                      <Input
                                        id={`dt_min_hours_${typeId}`}
                                        type="number"
                                        placeholder="e.g. 2"
                                        value={
                                          currentRateInput.dt_min_hours ?? ""
                                        }
                                        onChange={(e) =>
                                          handleRateInputChange(
                                            typeId,
                                            "dt_min_hours",
                                            e?.target?.value
                                          )
                                        }
                                      />
                                    </div>
                                  </div>
                                )}
                                {/* HOURLY_CHARTER Fields */}
                                {currentPricingModel === "HOURLY_CHARTER" && (
                                  <div className="grid grid-cols-2 gap-2">
                                    <div className="space-y-1">
                                      <Label
                                        htmlFor={`charter_hourly_rate_${typeId}`}
                                      >
                                        Hourly Rate ($)
                                      </Label>
                                      <Input
                                        id={`charter_hourly_rate_${typeId}`}
                                        type="number"
                                        placeholder="e.g. 90"
                                        value={
                                          currentRateInput.charter_hourly_rate ??
                                          ""
                                        }
                                        onChange={(e) =>
                                          handleRateInputChange(
                                            typeId,
                                            "charter_hourly_rate",
                                            e?.target?.value
                                          )
                                        }
                                      />
                                    </div>
                                    <div className="space-y-1">
                                      <Label
                                        htmlFor={`charter_min_hours_${typeId}`}
                                      >
                                        Min. Hours
                                      </Label>
                                      <Input
                                        id={`charter_min_hours_${typeId}`}
                                        type="number"
                                        placeholder="e.g. 3"
                                        value={
                                          currentRateInput.charter_min_hours ??
                                          ""
                                        }
                                        onChange={(e) =>
                                          handleRateInputChange(
                                            typeId,
                                            "charter_min_hours",
                                            e?.target?.value
                                          )
                                        }
                                      />
                                    </div>
                                  </div>
                                )}
                                {/* AIRPORT_TRANSFER Fields */}
                                {currentPricingModel === "AIRPORT_TRANSFER" && (
                                  <div className="grid grid-cols-1 gap-2">
                                    <div className="space-y-1">
                                      <Label
                                        htmlFor={`airport_transfer_flat_rate_${typeId}`}
                                      >
                                        Flat Rate ($)
                                      </Label>
                                      <Input
                                        id={`airport_transfer_flat_rate_${typeId}`}
                                        type="number"
                                        placeholder="e.g. 120"
                                        value={
                                          currentRateInput.airport_transfer_flat_rate ??
                                          ""
                                        }
                                        onChange={(e) =>
                                          handleRateInputChange(
                                            typeId,
                                            "airport_transfer_flat_rate",
                                            e?.target?.value
                                          )
                                        }
                                      />
                                    </div>
                                  </div>
                                )}
                                {currentPricingModel === "NONE" && (
                                  <p className="text-sm text-muted-foreground">
                                    Select a pricing model to configure rates.
                                  </p>
                                )}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Edit Vehicle Modal */}
            <Dialog
              open={showEditVehicleDialog}
              onOpenChange={setShowEditVehicleDialog}
            >
              <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Edit Vehicle</DialogTitle>
                  <DialogDescription>
                    Update vehicle information and documents
                  </DialogDescription>
                </DialogHeader>
                {editingVehicle && (
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Vehicle Type</Label>
                        <Input value={editingVehicle.type || ""} disabled />
                      </div>
                      <div className="space-y-2">
                        <Label>Status</Label>
                        <Input value={editingVehicle.status || ""} disabled />
                      </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Make</Label>
                        <Input
                          value={editingVehicle.make || ""}
                          onChange={(e) =>
                            setEditingVehicle({
                              ...editingVehicle,
                              make: e?.target?.value,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Model</Label>
                        <Input
                          value={editingVehicle.model || ""}
                          onChange={(e) =>
                            setEditingVehicle({
                              ...editingVehicle,
                              model: e?.target?.value,
                            })
                          }
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Year</Label>
                        <Input
                          value={editingVehicle.year || ""}
                          onChange={(e) =>
                            setEditingVehicle({
                              ...editingVehicle,
                              year: parseInt(e?.target?.value) || 0,
                            })
                          }
                          type="number"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Capacity</Label>
                        <Input
                          value={editingVehicle.capacity || ""}
                          onChange={(e) =>
                            setEditingVehicle({
                              ...editingVehicle,
                              capacity: parseInt(e?.target?.value) || 0,
                            })
                          }
                          type="number"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>License Plate</Label>
                      <Input
                        value={editingVehicle.license_plate || ""}
                        onChange={(e) =>
                          setEditingVehicle({
                            ...editingVehicle,
                            license_plate: e?.target?.value,
                          })
                        }
                      />
                    </div>

                    <div className="space-y-4">
                      <h4 className="text-sm font-medium">Documents</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="flex items-center gap-2">
                            <Shield
                              className={`h-4 w-4 ${editingVehicle.documents?.insurance ? "text-green-600" : "text-red-600"}`}
                            />
                            Insurance Certificate
                          </Label>
                          <Button
                            variant="outline"
                            onClick={() =>
                              handleDocumentUpload(
                                editingVehicle.id,
                                "insurance"
                              )
                            }
                            disabled={documentUploading === editingVehicle.id}
                            className="w-full"
                          >
                            {documentUploading === editingVehicle.id ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Upload className="h-4 w-4 mr-2" />
                            )}
                            {editingVehicle.documents?.insurance
                              ? "Update"
                              : "Upload"}{" "}
                            Insurance
                          </Button>
                        </div>
                        <div className="space-y-2">
                          <Label className="flex items-center gap-2">
                            <FileText
                              className={`h-4 w-4 ${editingVehicle.documents?.registration ? "text-green-600" : "text-red-600"}`}
                            />
                            Registration
                          </Label>
                          <Button
                            variant="outline"
                            onClick={() =>
                              handleDocumentUpload(
                                editingVehicle.id,
                                "registration"
                              )
                            }
                            disabled={documentUploading === editingVehicle.id}
                            className="w-full"
                          >
                            {documentUploading === editingVehicle.id ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Upload className="h-4 w-4 mr-2" />
                            )}
                            {editingVehicle.documents?.registration
                              ? "Update"
                              : "Upload"}{" "}
                            Registration
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setShowEditVehicleDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      toast({
                        title: "Vehicle Updated",
                        description: "Vehicle information has been updated.",
                      });
                      setShowEditVehicleDialog(false);
                      setEditingVehicle(null);
                    }}
                  >
                    Save Changes
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </TabsContent>

        <TabsContent value="vip-services" className="space-y-4">
          <VipServicesConfig
            companyId={selectedCompany?.id || ""}
            vehicleTypes={vehicleTypes.map((vt) => ({ ...vt, type: vt.name }))}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
