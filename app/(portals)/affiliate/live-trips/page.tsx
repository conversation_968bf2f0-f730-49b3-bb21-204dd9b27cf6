"use client"

import { useState, Fragment, useEffect } from "react"
import { format } from "date-fns"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Badge } from "@/app/components/ui/badge"
import { Button } from "@/app/components/ui/button"
import {
  MapPin,
  Users,
  Car,
  Clock,
  MessageSquare,
  AlertCircle,
  CheckCircle2,
  Info,
  Navigation,
  Phone,
  List,
  Map as MapIcon,
  Filter,
  Search,
} from "lucide-react"
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/app/components/ui/hover-card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/app/components/ui/sheet"
import { Input } from "@/app/components/ui/input"
import { <PERSON>rollA<PERSON> } from "@/app/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar"
import { cn } from "@/lib/utils"
import Map, { Marker, NavigationControl, FullscreenControl, ScaleControl } from "react-map-gl"
import { ToggleGroup, ToggleGroupItem } from "@/app/components/ui/toggle-group"
import type { ViewState } from 'react-map-gl'
import { useAffiliateAccess } from '@/app/lib/hooks/useAffiliateAccess'
import { AffiliateOnboardingBanner } from '@/app/components/ui/AffiliateOnboardingBanner'
import { useToast } from "@/app/components/ui/use-toast"
import { Loader2 } from "lucide-react"

// Mock data for active trips
const mockActiveTrips = [
  {
    id: "TR001",
    status: "scheduled",
    pickupDate: new Date("2024-03-19T15:00:00"),
    pickupLocation: "Four Seasons Austin",
    pickupDetails: "Main entrance, valet area",
    dropoffLocation: "Austin-Bergstrom International Airport",
    dropoffDetails: "Terminal 1, Departure level",
    passengers: 2,
    vehicleType: "LUXURY SEDAN",
    vehicle: "2023 Mercedes S-Class (V001)",
    driver: {
      name: "John Smith",
      phone: "(*************"
    },
    eventName: "SXSW 2024",
    coordinator: {
      name: "Sarah Johnson",
      phone: "(*************",
      email: "<EMAIL>"
    },
    specialInstructions: "VIP client - early arrival requested",
    timeline: [],
    unreadMessages: 1,
    messages: [
      {
        id: 1,
        sender: "System Manager",
        content: "Please ensure early arrival for VIP pickup",
        timestamp: "2024-03-19T14:30:00",
        read: false
      }
    ]
  },
  {
    id: "TR002",
    status: "otw",
    pickupDate: new Date("2024-03-19T14:30:00"),
    pickupLocation: "JW Marriott Austin",
    pickupDetails: "Congress Avenue entrance",
    dropoffLocation: "Circuit of The Americas",
    dropoffDetails: "VIP entrance, Gate A",
    passengers: 4,
    vehicleType: "SUV",
    vehicle: "2023 Cadillac Escalade (V002)",
    driver: {
      name: "Mike Johnson",
      phone: "(*************"
    },
    eventName: "F1 Testing",
    coordinator: {
      name: "David Wilson",
      phone: "(*************",
      email: "<EMAIL>"
    },
    specialInstructions: "VIP passes required for entry",
    timeline: [
      { status: "scheduled", timestamp: "2024-03-19T14:00:00" },
      { status: "otw", timestamp: "2024-03-19T14:15:00" }
    ],
    location: {
      lat: 30.2672,
      lng: -97.7431,
      lastUpdate: "2024-03-19T14:15:00",
      eta: "10 mins"
    },
    unreadMessages: 0,
    messages: []
  }
]

// Update the interface definition
interface MapViewport {
  latitude: number
  longitude: number
  zoom: number
  bearing: number
  pitch: number
  padding?: { top: number; bottom: number; left: number; right: number }
}

// Interface for real trip data
interface LiveTrip {
  id: string
  status: string
  referenceNumber: string
  serviceType: string
  vehicleType: string
  pickupLocation: string
  dropoffLocation: string
  date: string
  time: string
  duration: string
  distance: string
  passengerCount: number
  luggageCount: number
  totalAmount: number
  contactName: string
  contactEmail: string
  contactPhone: string
  city: string
  flightNumber?: string
  specialRequests?: string
  company: {
    id: string
    name: string
    city: string
    state: string
  }
  driver?: any
  vehicle?: any
  createdAt: string
  updatedAt: string
}

export default function LiveTripsPage() {
  const { showOnboardingBanner } = useAffiliateAccess()
  const { toast } = useToast()
  const [selectedTrip, setSelectedTrip] = useState<string | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [showMessages, setShowMessages] = useState(false)
  const [selectedTripForMessages, setSelectedTripForMessages] = useState<any>(null)
  const [newMessage, setNewMessage] = useState("")
  const [view, setView] = useState<"list" | "map">("list")
  const [searchTerm, setSearchTerm] = useState("")
  const [viewport, setViewport] = useState<MapViewport>({
    latitude: 30.2672,
    longitude: -97.7431,
    zoom: 12,
    bearing: 0,
    pitch: 0,
    padding: { top: 0, bottom: 0, left: 0, right: 0 }
  })
  const [mapLoaded, setMapLoaded] = useState(false)

  // Real data state
  const [liveTrips, setLiveTrips] = useState<LiveTrip[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch live trips data
  const fetchLiveTrips = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/affiliate/live-trips')
      if (!response.ok) {
        throw new Error(`Failed to fetch trips: ${response.status}`)
      }

      const data = await response.json()
      setLiveTrips(data.trips || [])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch live trips'
      setError(errorMessage)
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // Fetch data on component mount and set up polling
  useEffect(() => {
    fetchLiveTrips()

    // Poll for updates every 30 seconds
    const interval = setInterval(fetchLiveTrips, 30000)

    return () => clearInterval(interval)
  }, [])

  if (showOnboardingBanner) {
    return (
      <div className="p-8">
        <AffiliateOnboardingBanner>
          <div className="mt-2 text-yellow-800">You must complete onboarding to access live trips.</div>
        </AffiliateOnboardingBanner>
      </div>
    )
  }

  // Enhanced status badges with more detailed states
  const getStatusBadge = (status: string) => {
    const variants: Record<string, string> = {
      assigned: "bg-blue-100 text-blue-800 border-blue-200",
      confirmed: "bg-green-100 text-green-800 border-green-200",
      en_route: "bg-yellow-100 text-yellow-800 border-yellow-200",
      arrived: "bg-orange-100 text-orange-800 border-orange-200",
      in_progress: "bg-purple-100 text-purple-800 border-purple-200",
      completed: "bg-gray-100 text-gray-800 border-gray-200",
      cancelled: "bg-red-100 text-red-800 border-red-200"
    }

    const labels: Record<string, string> = {
      assigned: "Assigned",
      confirmed: "Confirmed",
      en_route: "En Route",
      arrived: "Arrived",
      in_progress: "In Progress",
      completed: "Completed",
      cancelled: "Cancelled"
    }

    return (
      <Badge variant="outline" className={variants[status] || variants.assigned}>
        {labels[status] || status}
      </Badge>
    )
  }

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedTripForMessages) return

    // Here you would typically make an API call to send the message
    console.log("Sending message:", newMessage, "for trip:", selectedTripForMessages.id)
    setNewMessage("")
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-semibold">Live Trips</h3>
          <p className="text-sm text-muted-foreground">
            Real-time monitoring and management of active trips
          </p>
        </div>
        <div className="flex items-center gap-4">
          <ToggleGroup type="single" value={view} onValueChange={(v: string) => v && setView(v as "list" | "map")}>
            <ToggleGroupItem value="list" aria-label="List view">
              <List className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="map" aria-label="Map view">
              <MapIcon className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Trips</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm text-muted-foreground">Loading...</span>
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold">{liveTrips.length}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Live trips in progress
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Assigned</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {liveTrips.filter(trip => trip.status === 'assigned').length}
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
              <Clock className="h-3 w-3" />
              Awaiting pickup
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">En Route</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {liveTrips.filter(trip => ['en_route', 'arrived', 'in_progress'].includes(trip.status)).length}
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
              <Users className="h-3 w-3" />
              Active journeys
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${liveTrips.reduce((sum, trip) => sum + trip.totalAmount, 0).toFixed(0)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              From active trips
            </p>
          </CardContent>
        </Card>
      </div>

      {view === "list" ? (
        <div className="space-y-4">
          {/* Search and Filters */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search trips by location, driver, or vehicle..."
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e?.target?.value)}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="otw">On the Way</SelectItem>
                <SelectItem value="olc">On Location</SelectItem>
                <SelectItem value="pob">Passenger on Board</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Trip Cards */}
          <div className="grid gap-4">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2 text-muted-foreground">Loading live trips...</span>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <div className="text-red-600 mb-2">Error loading trips</div>
                <p className="text-sm text-muted-foreground mb-4">{error}</p>
                <Button onClick={fetchLiveTrips} variant="outline">
                  Try Again
                </Button>
              </div>
            ) : liveTrips.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Car className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">No Active Trips</h3>
                <p className="text-sm">You don't have any live trips at the moment.</p>
              </div>
            ) : (
              liveTrips.map(trip => (
              <Card key={trip.id} className="relative overflow-hidden">
                <CardContent className="p-6">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className="h-10 w-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <Car className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">{trip.vehicleType}</div>
                        <div className="text-sm text-muted-foreground">Ref: {trip.referenceNumber}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      {getStatusBadge(trip.status)}
                      <Badge variant="outline" className="text-xs">
                        ${trip.totalAmount}
                      </Badge>
                    </div>
                  </div>

                  {/* Content Grid */}
                  <div className="grid grid-cols-2 gap-8">
                    {/* Left Column */}
                    <div className="space-y-4">
                      {/* Time and Service Info */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">
                            {trip.time} on {format(new Date(trip.date), "MMM d")}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {trip.serviceType}
                          </Badge>
                          {trip.flightNumber && (
                            <Badge variant="outline" className="text-xs">
                              Flight: {trip.flightNumber}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Locations */}
                      <div className="space-y-3">
                        <div className="flex items-start gap-2">
                          <div className="h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mt-0.5">
                            <MapPin className="h-3 w-3 text-green-700" />
                          </div>
                          <div>
                            <div className="text-sm font-medium">{trip.pickupLocation}</div>
                            <div className="text-xs text-muted-foreground">
                              {trip.passengerCount} passenger{trip.passengerCount !== 1 ? 's' : ''} • {trip.luggageCount} bag{trip.luggageCount !== 1 ? 's' : ''}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-start gap-2">
                          <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                            <MapPin className="h-3 w-3 text-blue-700" />
                          </div>
                          <div>
                            <div className="text-sm font-medium">{trip.dropoffLocation}</div>
                            <div className="text-xs text-muted-foreground">
                              {trip.duration} • {trip.distance}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Right Column */}
                    <div className="space-y-4">
                      {/* Customer Info */}
                      <div className="flex items-center gap-3 bg-muted/50 p-2 rounded-lg">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>{trip.contactName.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="text-sm font-medium">{trip.contactName}</div>
                          <div className="text-xs text-muted-foreground">{trip.contactPhone}</div>
                        </div>
                        <Button variant="ghost" size="icon">
                          <Phone className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Company Info */}
                      <div className="flex items-center gap-3 bg-muted/50 p-2 rounded-lg">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>{trip.company.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="text-sm font-medium">{trip.company.name}</div>
                          <div className="text-xs text-muted-foreground">{trip.company.city}, {trip.company.state}</div>
                        </div>
                      </div>

                      {/* Driver Assignment */}
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-2">
                        <div className="flex items-center gap-2 text-blue-800">
                          <Car className="h-4 w-4" />
                          <span className="text-sm font-medium">Driver Assignment</span>
                        </div>
                        <p className="text-xs text-blue-800 mt-1">
                          {trip.driver ? `Assigned to ${trip.driver.name}` : 'Pending driver assignment'}
                        </p>
                      </div>

                      {/* Special Requests */}
                      {trip.specialRequests && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2">
                          <div className="flex items-center gap-2 text-yellow-800">
                            <AlertCircle className="h-4 w-4" />
                            <span className="text-sm font-medium">Special Requests</span>
                          </div>
                          <p className="text-xs text-yellow-800 mt-1">{trip.specialRequests}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between mt-4 pt-4 border-t">
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="gap-2">
                        <Navigation className="h-4 w-4" />
                        Track
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedTripForMessages(trip)
                          setShowMessages(true)
                        }}
                      >
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Messages
                      </Button>
                    </div>
                    <Select
                      value={trip.status}
                      onValueChange={(value) => console.log(value)}
                    >
                      <SelectTrigger className="w-[140px]">
                        <SelectValue placeholder="Update Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="assigned">Assigned</SelectItem>
                        <SelectItem value="confirmed">Confirmed</SelectItem>
                        <SelectItem value="en_route">En Route</SelectItem>
                        <SelectItem value="arrived">Arrived</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
              ))
            )}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-[350px,1fr] gap-4">
          {/* Trip List */}
          <Card>
            <CardHeader className="pb-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search trips..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e?.target?.value)}
                />
              </div>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px] pr-4">
                <div className="space-y-2">
                  {mockActiveTrips.map(trip => (
                    <div
                      key={trip.id}
                      className={cn(
                        "p-3 rounded-lg cursor-pointer hover:bg-muted/50 transition-colors",
                        selectedTrip === trip.id && "bg-muted/50 ring-1 ring-primary"
                      )}
                      onClick={() => setSelectedTrip(trip.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Car className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">{trip.vehicleType}</span>
                        </div>
                        {getStatusBadge(trip.status)}
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span>{format(trip.pickupDate, "h:mm a")}</span>
                        </div>
                        <div className="flex items-start gap-2">
                          <MapPin className="h-3 w-3 text-muted-foreground mt-1" />
                          <div className="flex-1">
                            <div className="text-sm truncate">{trip.pickupLocation}</div>
                            <div className="text-xs text-muted-foreground truncate">{trip.dropoffLocation}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Map */}
          <Card>
            <CardContent className="p-0">
              <div className="h-[600px] rounded-lg overflow-hidden">
                {typeof window !== 'undefined' && (
                  <Map
                    reuseMaps
                    initialViewState={{
                      latitude: viewport.latitude,
                      longitude: viewport.longitude,
                      zoom: viewport.zoom,
                      bearing: viewport.bearing,
                      pitch: viewport.pitch
                    }}
                    style={{ width: '100%', height: '100%' }}
                    mapStyle="mapbox://styles/mapbox/streets-v12"
                    mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_TOKEN}
                    onLoad={(evt) => {
                      if (evt && evt.target) {
                        evt.target.on('mousemove', (e) => e.preventDefault());
                        evt.target.on('click', (e) => e.preventDefault());
                        setMapLoaded(true);
                      }
                    }}
                    interactiveLayerIds={[]}
                    renderWorldCopies={false}
                    onMove={evt => {
                      if (evt.viewState) {
                        setViewport({
                          ...viewport,
                          latitude: evt.viewState.latitude,
                          longitude: evt.viewState.longitude,
                          zoom: evt.viewState.zoom,
                          bearing: evt.viewState.bearing,
                          pitch: evt.viewState.pitch
                        })
                      }
                    }}
                  >
                    {mapLoaded && (
                      <>
                        <NavigationControl position="top-right" />
                        <FullscreenControl position="top-right" />
                        <ScaleControl position="bottom-right" />

                        {mockActiveTrips.map(trip => {
                          // In a real app, these would come from the trip data
                          const pickup = { lat: 30.2672, lng: -97.7431 }
                          const dropoff = { lat: 30.2842, lng: -97.7298 }

                          return (
                            <Fragment key={trip.id}>
                              {/* Vehicle Marker */}
                              <Marker
                                longitude={pickup.lng}
                                latitude={pickup.lat}
                                anchor="center"
                                style={{ zIndex: 1 }}
                                onClick={(e) => {
                                  if (e && e.originalEvent) {
                                    e.originalEvent.preventDefault();
                                    e.originalEvent.stopPropagation();
                                  }
                                  setSelectedTrip(trip.id);
                                }}
                              >
                                <div className="bg-primary text-white p-2 rounded-full shadow-lg cursor-pointer">
                                  <Car className="h-4 w-4" />
                                </div>
                              </Marker>

                              {/* Pickup Marker */}
                              <Marker
                                longitude={pickup.lng}
                                latitude={pickup.lat}
                                anchor="bottom"
                                offset={[0, -30]}
                                style={{ zIndex: 2 }}
                              >
                                <div className="bg-green-500 text-white px-2 py-1 rounded shadow-lg text-sm">
                                  Pickup
                                </div>
                              </Marker>

                              {/* Dropoff Marker */}
                              <Marker
                                longitude={dropoff.lng}
                                latitude={dropoff.lat}
                                anchor="bottom"
                                style={{ zIndex: 1 }}
                              >
                                <div className="bg-blue-500 text-white px-2 py-1 rounded shadow-lg text-sm">
                                  Dropoff
                                </div>
                              </Marker>
                            </Fragment>
                          )
                        })}
                      </>
                    )}
                  </Map>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Messages Sheet */}
      <Sheet open={showMessages} onOpenChange={setShowMessages}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Messages</SheetTitle>
            <SheetDescription>
              Communication with System Manager
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6 flex flex-col h-[calc(100vh-200px)]">
            <ScrollArea className="flex-1 pr-4">
              {selectedTripForMessages?.messages.map((message: any) => (
                <div
                  key={message.id}
                  className="mb-4 last:mb-0"
                >
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-medium">{message.sender}</span>
                    <span className="text-xs text-muted-foreground">
                      {format(new Date(message.timestamp), "h:mm a")}
                    </span>
                  </div>
                  <p className="text-sm bg-muted/50 p-2 rounded-lg">
                    {message.content}
                  </p>
                </div>
              ))}
            </ScrollArea>
            <div className="flex gap-2 pt-4">
              <Input
                placeholder="Type your message..."
                value={newMessage}
                onChange={(e) => setNewMessage(e?.target?.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault()
                    handleSendMessage()
                  }
                }}
              />
              <Button onClick={handleSendMessage}>Send</Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}