"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card } from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
// Simple debounce function to prevent rapid updates
const debounce = (func: (...args: any[]) => void, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import { Badge } from "@/app/components/ui/badge";
import {
  Clock,
  MapPin,
  Users,
  Car,
  AlertCircle,
  DollarSign,
  FileText,
  AlertTriangle,
  CheckCircle2,
  XCircle,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/app/components/ui/alert-dialog";
import { useToast } from "@/app/components/ui/use-toast";
import { Separator } from "@/app/components/ui/separator";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/app/components/ui/hover-card";
import { format, parseISO } from "date-fns";
import { QuoteRow } from "@/app/components/shared/rows/QuoteRow";
import type { QuoteRowData } from "@/app/components/shared/rows/QuoteRow";
import { AffiliateQuoteActionPanel } from "@/app/components/shared/panels/AffiliateQuoteActionPanel";
import { useWebSocket } from "@/hooks/useWebSocket";
import {
  getAffiliateOffers,
  acceptOffer,
  rejectOffer,
  submitRateProposal,
} from "@/lib/api/affiliate-offers";
import { createClient } from "@supabase/supabase-js";
import { CommonTabs, TabItem } from "@/app/components/shared/tabs/CommonTabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/app/components/ui/tooltip";
import { useAffiliateAccess } from "@/app/lib/hooks/useAffiliateAccess";
import { useAffiliateCompany } from "@/app/contexts/AffiliateCompanyContext";
import { AffiliateOnboardingBanner } from "@/app/components/ui/AffiliateOnboardingBanner";

// Error Boundary Component for Offers Page
class OffersErrorBoundary extends React.Component<{children: React.ReactNode}, {hasError: boolean, error: any}> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: any) {
    return { hasError: true, error };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error('OffersPage Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Something went wrong</h2>
            <p className="text-gray-600 mb-4">We're having trouble loading the offers page.</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Extended interface to match what the API actually returns
interface ApiAffiliateOffer {
  id: string;
  quote_id: string;
  company_id: string;
  status: "sent" | "pending" | "accepted" | "rejected" | "expired";
  rate_amount: number;
  rate_currency: string;
  timeout_at: string;
  created_at: string;
  updated_at: string;
  quotes: any[] | null;
}

// Extended quote type to include all required QuoteRowData fields
interface ExtendedQuote {
  id: string;
  reference_number: string;
  service_type: "airport" | "hourly" | "point";
  vehicle_type: string;
  pickup_location: string;
  dropoff_location: string;
  date: string;
  time: string;
  passenger_count: number;
  luggage_count: number;
  special_requests?: string;
  distance: string;
  duration: string;
  customer_id: string;
  contact_email?: string;
  contact_name?: string;
  contact_phone?: string;
  priority: "high" | "medium" | "low";
  created_at: string;
  updated_at: string;
  total_amount: number;
  is_multi_day: boolean;
  flight_number: string;
  is_return_trip: boolean;
  return_date: string;
  return_time: string;
  return_flight_number: string;
  car_seats_needed: boolean;
  infant_seats?: number | null;
  toddler_seats?: number | null;
  booster_seats?: number | null;
  intermediate_stops: any[];
  duration_hours?: number | null;
  trips?: Array<{
    id: string;
    status: string;
    quote_id?: string;
    created_at: string;
    updated_at: string;
    driver_id?: string;
    company_id?: string;
    estimated_distance?: string;
    estimated_duration?: string;
    scheduled_pickup_date?: string;
    scheduled_pickup_time?: string;
    estimated_amount?: number;
    final_amount?: number;
    trip_notes?: string;
    reference_number?: string;
  }>;
}

// Use a custom interface for our offers instead of extending AffiliateOffer
interface ExtendedAffiliateOffer {
  id: string;
  quote_id: string;
  company_id: string;
  status: "sent" | "pending" | "accepted" | "rejected" | "expired";
  rate_amount: number;
  rate_currency: string;
  timeout_at: string;
  created_at: string;
  updated_at: string;
  quote?: {
    id: string;
    reference_number: string;
    customer_id: string;
    service_type: "airport" | "hourly" | "point";
    vehicle_type: string;
    pickup_location: string;
    dropoff_location: string;
    date: string;
    time: string;
    passenger_count: number;
    luggage_count: number;
    special_requests?: string | null;
    distance: string;
    duration: string;
    priority: "high" | "medium" | "low";
    created_at: string;
    updated_at: string;
    total_amount: number | null;
    is_multi_day: boolean;
    flight_number: string | null;
    is_return_trip: boolean;
    return_date: string | null;
    return_time: string | null;
    return_flight_number: string | null;
    car_seats_needed: boolean;
    infant_seats?: number | null;
    toddler_seats?: number | null;
    booster_seats?: number | null;
    intermediate_stops: any[] | null;
    duration_hours?: number | null;
    trips?: any[];
    customer?: {
      id: string;
      name?: string;
      full_name?: string;
      email?: string;
      phone?: string;
      company_name?: string;
    };
    contact_name?: string | null;
    contact_email?: string | null;
    contact_phone?: string | null;
    city?: string;
  };
}

// Production-safe debug helper that never outputs during server rendering
const debug = (message: string, data?: any) => {
  // Only log in the browser environment and only in development
  if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
    if (data) {
      console.log(`[Affiliate Offers] ${message}:`, data);
    } else {
      console.log(`[Affiliate Offers] ${message}`);
    }
  }
};

// Helper function to safely get a value from a quote object
const getQuoteValue = (
  offer: any,
  path: string,
  fallback: any = "Not available"
) => {
  if (!offer || !offer.quote) return fallback;

  // For simple top-level properties
  if (path.indexOf(".") === -1) {
    return offer.quote[path] !== null && offer.quote[path] !== undefined
      ? offer.quote[path]
      : fallback;
  }

  // For nested properties using dot notation (like customer.name)
  const parts = path.split(".");
  let current: any = offer.quote;

  for (const part of parts) {
    if (
      current === null ||
      current === undefined ||
      typeof current !== "object"
    ) {
      return fallback;
    }
    current = current[part];
  }

  return current !== null && current !== undefined ? current : fallback;
};

// Custom controlled tabs component
const ControlledTabs = ({
  tabs,
  activeTab,
  setActiveTab,
}: {
  tabs: {
    id: string;
    label: string;
    count?: number;
    content: React.ReactNode;
  }[];
  activeTab: string;
  setActiveTab: (value: string) => void;
}) => {
  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList
        className="mb-4 w-full h-auto"
        style={{
          display: "grid",
          gridTemplateColumns: `repeat(${tabs.length}, minmax(0, 1fr))`,
        }}
      >
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.id}
            value={tab.id}
            className="py-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            {tab.label}
            {typeof tab.count !== "undefined" && (
              <Badge variant="secondary" className="ml-2">
                {tab.count}
              </Badge>
            )}
          </TabsTrigger>
        ))}
      </TabsList>

      {tabs.map((tab) => (
        <TabsContent key={tab.id} value={tab.id}>
          <div className="mt-6">{tab.content}</div>
        </TabsContent>
      ))}
    </Tabs>
  );
};

function OffersPageContent() {
  const router = useRouter();
  const { toast } = useToast();
  const [selectedOffer, setSelectedOffer] =
    useState<ExtendedAffiliateOffer | null>(null);
  const [offers, setOffers] = useState<ExtendedAffiliateOffer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showActionPanel, setShowActionPanel] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [actionInProgress, setActionInProgress] = useState(false);
  const [statCounts, setStatCounts] = useState({
    total: 0,
    pending: 0,
    accepted: 0,
    rejected: 0,
    expired: 0,
    counter_offers: 0,
  });
  const [activeTab, setActiveTab] = useState<string>("pending");
  const {
    showOnboardingBanner,
    canRespondToOffer,
    isPending: isAffiliateAccessPending,
    isActive: isAffiliateAccessActive,
    selectedCompany: affiliateAccessSelectedCompany,
  } = useAffiliateAccess();
  // Safe hook usage with error boundary
  const [contextError, setContextError] = useState<string | null>(null);

  // Use the hook normally - React will handle errors via error boundary
  const affiliateContext = useAffiliateCompany();
  const affiliateSelectedCompany = affiliateContext?.selectedCompany || null;
  const isLoadingCompanies = affiliateContext?.isLoadingCompanies ?? true;
  const affiliateCompanyError = contextError || affiliateContext?.companyError || null;
  const isFetchingRef = useRef(false);

  // WebSocket integration for real-time updates
  const { state: wsState, connect: wsConnect, onQuoteUpdate, onAffiliateResponse } = useWebSocket();

  // Helper function to handle auth issues
  const handleAuthRedirect = (reason: string) => {
    debug(`OffersPage Auth redirect triggered: ${reason}`);
    toast({
      title: "Information Missing",
      description:
        reason || "Cannot load offers without an active affiliate company.",
      variant: "destructive",
    });
    // Potentially redirect to a more appropriate page if not login, e.g., company selection or dashboard
    // For now, let's assume the main page guard (layout or middleware) handles general auth.
    // router.push("/affiliate/dashboard") // Or some other safe page
  };

  // Function to manually retry fetching data
  const handleRetry = useCallback(() => {
    setLoading(true);
    setError(null);
    setRetryCount((prev) => prev + 1);
  }, []);

  // Reset loading on component mount and setup a safety timeout
  useEffect(() => {
    // Only reset loading state on mount or when retry is pressed
    const safetyTimeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
        setError("Request timed out. Please try refreshing the page.");
        console.error("Request timed out after 15 seconds");

        // Show a toast notification for better UX
        toast({
          title: "Request Timeout",
          description: "The server took too long to respond. Please try again.",
          variant: "destructive",
        });
      }
    }, 15000);

    return () => clearTimeout(safetyTimeout);
  }, [retryCount, loading, toast]);

  const fetchDataRef = useRef<(() => Promise<void>) | null>(null);

  const fetchData = useCallback(async () => {
    debug("OffersPage: fetchData called");
    if (isFetchingRef.current) {
      debug("OffersPage: fetchData: Already fetching, returning.");
      return;
    }

    // Crucial check: Wait for company context to load and a company to be selected
    if (isLoadingCompanies) {
      debug("OffersPage: fetchData: Company context is loading, waiting...");
      setLoading(true); // Show general loading for offers page
      return;
    }

    if (!affiliateSelectedCompany || !affiliateSelectedCompany.id) {
      debug(
        `OffersPage: fetchData: No selected company or id. Selected Company: ${JSON.stringify(affiliateSelectedCompany)}`
      );
      setError("No active affiliate company selected. Cannot fetch offers.");
      setLoading(false);
      isFetchingRef.current = false;
      // Optional: redirect or show a more prominent message if this state is reached
      // handleAuthRedirect("No affiliate company selected to fetch offers for.")
      return;
    }

    // At this point, affiliateSelectedCompany and affiliateSelectedCompany.id are available.
    const currentAffiliateCompanyId = affiliateSelectedCompany.id;
    debug(
      `OffersPage: fetchData: Company context loaded. Affiliate ID: ${currentAffiliateCompanyId}`
    );

    isFetchingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      debug(
        `OffersPage: fetchData: Fetching offers for company ID: ${currentAffiliateCompanyId}`
      );
      const fetchedOffers = await getAffiliateOffers(currentAffiliateCompanyId);
      debug(
        `OffersPage: fetchData: Raw fetched offers: ${JSON.stringify(fetchedOffers)}`
      );
      console.log(
        `[OFFERS DEBUG] Fetched ${fetchedOffers.length} offers:`,
        fetchedOffers
      );
      handleOffersData(fetchedOffers as ExtendedAffiliateOffer[]);
    } catch (err: any) {
      debug(
        `OffersPage: fetchData: Error fetching offers: ${err.message}`,
        err
      );
      setError(err.message || "Failed to load offers.");
      toast({
        title: "Error Loading Offers",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      isFetchingRef.current = false;
      debug("OffersPage: fetchData: Fetch completed, loading set to false.");
    }
  }, [affiliateSelectedCompany, isLoadingCompanies, toast]);

  // Store fetchData in ref to avoid circular dependencies
  fetchDataRef.current = fetchData;

  // Create debounced update function to prevent flickering (like super-admin quotes page)
  const debouncedOffersUpdate = useCallback(
    debounce((update: any) => {
      console.log('[Affiliate Offers] Debounced update triggered:', update);

      // Only fetch if not currently loading to prevent conflicts
      if (!isFetchingRef.current && fetchDataRef.current) {
        fetchDataRef.current();
      }
    }, 2000), // 2 second debounce to prevent rapid updates
    []
  );

  // Handle real-time quote updates (moved after fetchData definition)
  useEffect(() => {
    const unsubscribeQuoteUpdate = onQuoteUpdate((update) => {
      console.log('[Affiliate Offers] Real-time quote update received:', update);

      // Show toast notification
      toast({
        title: "New Quote Available",
        description: `Quote ${update.data?.quote?.reference_number || update.quoteId} is now available`,
        duration: 4000
      });

      // Use debounced update to prevent flickering
      debouncedOffersUpdate(update);
    });

    return unsubscribeQuoteUpdate;
  }, [onQuoteUpdate, toast, debouncedOffersUpdate]);

  // Add direct Supabase subscription for quote_offers table
  useEffect(() => {
    if (!affiliateSelectedCompany?.id) return;

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    console.log('[Affiliate Offers] Setting up direct Supabase subscription...');

    const subscription = supabase
      .channel('affiliate-offers-realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'quote_offers',
          filter: `company_id=eq.${affiliateSelectedCompany.id}`
        },
        (payload: any) => {
          console.log('[Affiliate Offers] Direct Supabase update received:', payload);

          // Show toast for new offers
          if (payload.eventType === 'INSERT') {
            toast({
              title: "New Quote Offer",
              description: "A new quote is available for your review",
              duration: 4000
            });
          }

          // Use debounced update to prevent flickering (like super-admin quotes page)
          debouncedOffersUpdate(payload);
        }
      )
      .subscribe();

    return () => {
      console.log('[Affiliate Offers] Cleaning up Supabase subscription...');
      subscription.unsubscribe();
    };
  }, [affiliateSelectedCompany?.id, toast, debouncedOffersUpdate]);

  useEffect(() => {
    debug(
      `OffersPage: useEffect for fetch triggered. isLoadingCompanies: ${isLoadingCompanies}, affiliateSelectedCompany: ${affiliateSelectedCompany ? affiliateSelectedCompany.id : "null"}`
    );
    // Only fetch if company context is resolved and a company is selected (or potentially if selection changes)
    if (!isLoadingCompanies && affiliateSelectedCompany && fetchDataRef.current) {
      fetchDataRef.current();
    } else if (!isLoadingCompanies && !affiliateSelectedCompany) {
      // Handle case where context is loaded but no company is selected (e.g., user has no companies)
      debug(
        "OffersPage: Company context loaded, but no company selected. Clearing offers."
      );
      setOffers([]);
      setStatCounts({
        pending: 0,
        accepted: 0,
        rejected: 0,
        total: 0, // ADDED
        expired: 0, // ADDED
        counter_offers: 0,
      });
      setError("No affiliate company is associated or selected."); // Inform user
      setLoading(false);
    }
  }, [affiliateSelectedCompany, isLoadingCompanies]);

  // Optimized polling for real-time updates (reduced frequency to prevent flashing)
  useEffect(() => {
    if (!affiliateSelectedCompany || loading) return;

    // Removed polling since we have real-time Supabase subscriptions
    // This eliminates the main source of flickering
    console.log('[Affiliate Offers] Real-time subscriptions active, polling disabled to prevent flickering');

    return () => {
      // Cleanup handled by individual subscriptions
    };
  }, [affiliateSelectedCompany, loading, fetchData]);

  // Function to process the offers data with optimized change detection
  const handleOffersData = (offersData: ExtendedAffiliateOffer[]) => {
    // Calculate stats based on all offers
    const totalOffers = offersData.length;
    console.log(`[OFFERS DEBUG] Processing ${totalOffers} offers:`, offersData);

    // Use more efficient change detection to prevent flickering
    const prevOffersLength = offers.length;
    const hasLengthChange = totalOffers !== prevOffersLength;

    // Only do deep comparison if length changed or if it's been a while since last update
    let hasContentChange = false;
    if (hasLengthChange) {
      hasContentChange = true;
    } else if (offersData.length > 0 && offers.length > 0) {
      // Quick check: compare IDs and statuses only
      const newIds = offersData.map(o => `${o.id}-${o.status}`).sort().join(',');
      const currentIds = offers.map(o => `${o.id}-${o.status}`).sort().join(',');
      hasContentChange = newIds !== currentIds;
    }

    // Only update if there are actual meaningful changes
    if (hasContentChange) {
      console.log('[OFFERS DEBUG] Meaningful changes detected, updating offers');
      setOffers(offersData);
    } else {
      console.log('[OFFERS DEBUG] No meaningful changes, skipping update to prevent flickering');
    }
    const statCounts = {
      total: totalOffers,
      pending: offersData.filter(
        (o: ExtendedAffiliateOffer) =>
          (o.status?.toLowerCase() === "pending" || o.status?.toLowerCase() === "sent") && !o.is_counter_offer
      ).length,
      accepted: offersData.filter(
        (o: ExtendedAffiliateOffer) => o.status?.toLowerCase() === "accepted"
      ).length,
      rejected: offersData.filter(
        (o: ExtendedAffiliateOffer) => o.status?.toLowerCase() === "rejected"
      ).length,
      expired: offersData.filter(
        (o: ExtendedAffiliateOffer) => o.status?.toLowerCase() === "expired"
      ).length,
      counter_offers: offersData.filter(
        (o: ExtendedAffiliateOffer) => o.is_counter_offer === true
      ).length,
    };
    setStatCounts(statCounts);
    console.log(`[OFFERS DEBUG] Stat counts:`, statCounts);

    debug("Data loaded successfully:", { total: totalOffers });
    setLoading(false);

    if (totalOffers > 0 && retryCount > 0) {
      // Show success toast only on successful retry
      toast({
        title: "Success",
        description: `${totalOffers} offers loaded successfully`,
        variant: "default",
      });
    }
  };

  const handleAction = async (action: string, data: any) => {
    setActionInProgress(true);

    // Check if affiliate is approved for responding to offers
    if (!canRespondToOffer(data)) {
      toast({
        title: "Action Not Allowed",
        description:
          "Your affiliate company must be approved before you can respond to offers.",
        variant: "destructive",
      });
      setActionInProgress(false);
      return;
    }

    // Extract offerId correctly whether string or object is passed
    let offerId: string;
    let quoteId: string | null = null;

    if (typeof data === "string") {
      offerId = data;
      debug(`Handling ${action} with string ID: ${offerId}`);
    } else if (data && typeof data === "object") {
      // Try to get the offer_id first, then id, then quote_id
      offerId = data.offer_id || data.id || data.offerId || data.quote_id;
      quoteId = data.quote_id || null;

      debug(`Handling ${action} with object data:`, { offerId, quoteId, data });
    } else {
      toast({
        title: "Error Processing Action",
        description: "Invalid ID format provided.",
        variant: "destructive",
      });
      setActionInProgress(false);
      return;
    }

    try {
      if (action === "accept_offer") {
        debug(
          `Processing accept action for offer ID: ${offerId} (quote ID: ${quoteId})`
        );
        await handleAcceptOffer(offerId);
      } else if (action === "reject_offer") {
        debug(
          `Processing reject action for offer ID: ${offerId} (quote ID: ${quoteId})`
        );
        await handleDeclineRequest(offerId);
      } else if (action === "submit_rate") {
        // Rate submission remains the same object handling
        const rateData = typeof data === "string" ? {} : data;
        const rateAmountStr =
          rateData.rate?.toString() ||
          window.prompt("Enter your rate amount in USD:", "");

        if (rateAmountStr) {
          // Use the quote_id from the data or fallback to offerId
          const targetQuoteId = quoteId || offerId;
          debug(
            `Processing submit_rate for quote ID: ${targetQuoteId}, rate: ${rateAmountStr}`
          );
          await handleSubmitRate(targetQuoteId, rateAmountStr);
        }
      } else if (action === "refresh_data") {
        // Refresh the offers data after counter-offer submission
        debug("Refreshing offers data after counter-offer submission");
        await fetchData();
      }
    } catch (error) {
      console.error(`Error processing action ${action}:`, error);
      toast({
        title: `Error processing ${action.replace("_", " ")}`,
        description: `There was a problem completing this action. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setActionInProgress(false);
      setShowActionPanel(false);
    }
  };

  const handleAcceptOffer = async (offerId: string) => {
    try {
      // Show loading state
      setActionInProgress(true);

      debug(`Accepting offer with ID: ${offerId}`);

      // 1. First make sure the offer exists and is in a valid state
      let offerToAccept = offers.find((offer) => offer.id === offerId);

      // If not found by id, try to find it by quote_id
      if (!offerToAccept) {
        debug(
          `No offer found with ID ${offerId}, checking if it's a quote ID...`
        );
        offerToAccept = offers.find((offer) => offer.quote_id === offerId);

        if (offerToAccept) {
          debug(
            `Found offer with quote_id ${offerId}, using offer ID ${offerToAccept.id} instead`
          );
          offerId = offerToAccept.id; // Use the actual offer ID for the API call
        }
      }

      if (!offerToAccept) {
        throw new Error(`Offer with ID ${offerId} not found`);
      }

      // 2. Call the API to accept the offer
      const result = await acceptOffer(offerId);

      if (result.success) {
        // 3. Update the local UI state optimistically
        const updatedOffers = offers.map((offer) =>
          offer.id === offerId
            ? { ...offer, status: "accepted" as const }
            : offer
        );

        setOffers(updatedOffers);

        // Force a re-render of the appropriate tabs and switch to accepted tab
        setActiveTab("accepted");

        // Update the state counts immediately for a responsive UI
        setStatCounts((prev) => ({
          ...prev,
          accepted: prev.accepted + 1,
          pending: Math.max(0, prev.pending - 1),
        }));

        // 4. Show success notification
        toast({
          title: "Offer Accepted",
          description: "The offer has been successfully accepted.",
          variant: "default",
        });

        // 5. Fetch fresh data from the server to ensure our state is correct
        await fetchData();

        // 6. Close the action panel
        setShowActionPanel(false);
      } else {
        throw new Error("Failed to accept offer");
      }
    } catch (error) {
      console.error("Error accepting offer:", error);
      toast({
        title: "Error Accepting Offer",
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });

      // Refresh the offers data to ensure UI is in sync with server
      await fetchData();
    } finally {
      setActionInProgress(false);
    }
  };

  const handleDeclineRequest = async (offerId: string) => {
    try {
      // Show loading state
      setActionInProgress(true);

      debug(`Rejecting offer with ID: ${offerId}`);

      // 1. First make sure the offer exists and is in a valid state
      let offerToReject = offers.find((offer) => offer.id === offerId);

      // If not found by id, try to find it by quote_id
      if (!offerToReject) {
        debug(
          `No offer found with ID ${offerId}, checking if it's a quote ID...`
        );
        offerToReject = offers.find((offer) => offer.quote_id === offerId);

        if (offerToReject) {
          debug(
            `Found offer with quote_id ${offerId}, using offer ID ${offerToReject.id} instead`
          );
          offerId = offerToReject.id; // Use the actual offer ID for the API call
        }
      }

      if (!offerToReject) {
        throw new Error(`Offer with ID ${offerId} not found`);
      }

      // 2. Call the API to reject the offer
      const result = await rejectOffer(offerId);

      if (result.success) {
        // 3. Update the local UI state optimistically
        const updatedOffers = offers.map((offer) =>
          offer.id === offerId
            ? { ...offer, status: "rejected" as const }
            : offer
        );

        setOffers(updatedOffers);

        // Force a re-render of the appropriate tabs and switch to rejected tab
        setActiveTab("rejected");

        // Update the state counts immediately for a responsive UI
        setStatCounts((prev) => ({
          ...prev,
          rejected: prev.rejected + 1,
          pending: Math.max(0, prev.pending - 1),
        }));

        // 4. Show success notification
        toast({
          title: "Offer Rejected",
          description: "The offer has been rejected.",
          variant: "default",
        });

        // 5. Fetch fresh data from the server to ensure our state is correct
        await fetchData();

        // 6. Close the action panel
        setShowActionPanel(false);
      } else {
        throw new Error("Failed to reject offer");
      }
    } catch (error) {
      console.error("Error rejecting offer:", error);
      toast({
        title: "Error Rejecting Offer",
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });

      // Refresh the offers data to ensure UI is in sync with server
      await fetchData();
    } finally {
      setActionInProgress(false);
    }
  };

  const handleSubmitRate = async (quoteId: string, rateAmount: string) => {
    try {
      setActionInProgress(true);

      const response = await fetch(`/api/affiliate/quotes/${quoteId}/rate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ rateAmount }),
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      toast({
        title: "Success",
        description: "Rate submitted successfully",
      });
    } catch (error) {
      console.error("Error submitting rate:", error);
      toast({
        title: "Error",
        description: "Failed to submit rate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setActionInProgress(false);
    }
  };

  // Convert offer to QuoteRowData for display
  const convertOfferToQuoteData = (
    offer: ExtendedAffiliateOffer
  ): QuoteRowData => {
    try {
      // Log the conversion for debugging
      debug(`Converting offer ${offer.id} to quote data`);

      // Check if a trip has been generated for this offer
      const hasTripGenerated =
        offer.quote?.trips &&
        Array.isArray(offer.quote.trips) &&
        offer.quote?.trips?.length > 0;

      // Create a quote object with safe fallbacks
      return {
        id: offer.quote_id,
        offer_id: offer.id,
        reference_number: getQuoteValue(
          offer,
          "reference_number",
          `REF-${offer.id.substring(0, 6)}`
        ),
        customer_id: getQuoteValue(offer, "customer_id", offer.quote_id),
        service_type: getQuoteValue(offer, "service_type", "airport") as
          | "airport"
          | "hourly"
          | "point",
        vehicle_type: getQuoteValue(offer, "vehicle_type", "Standard"),
        pickup_location: getQuoteValue(
          offer,
          "pickup_location",
          "Not specified"
        ),
        dropoff_location: getQuoteValue(
          offer,
          "dropoff_location",
          "Not specified"
        ),
        date: getQuoteValue(
          offer,
          "date",
          new Date().toISOString().split("T")[0]
        ),
        time: getQuoteValue(offer, "time", "00:00"),
        duration: getQuoteValue(offer, "duration", ""),
        distance: getQuoteValue(offer, "distance", ""),
        status: offer.status,
        passenger_count: getQuoteValue(offer, "passenger_count", 1),
        luggage_count: getQuoteValue(offer, "luggage_count", 0),
        special_requests: getQuoteValue(offer, "special_requests", ""),
        priority: getQuoteValue(offer, "priority", "medium") as
          | "high"
          | "medium"
          | "low",
        total_amount: offer.rate_amount || 0,
        created_at: offer.created_at,
        updated_at: offer.updated_at,
        customer: {
          id: getQuoteValue(offer, "customer_id", offer.quote_id),
          full_name: `${getQuoteValue(offer, "passenger_count", 1)} Passengers`,
          email: "",
          phone: "",
          company_name: "",
        },
        // Add notes for missing data
        notes: !offer.quote ? "Missing quote data" : undefined,
      };
    } catch (error) {
      console.error(`Error converting offer ${offer.id} to quote data:`, error);

      // Return minimal valid object
      return {
        id: offer.quote_id || offer.id,
        offer_id: offer.id,
        reference_number: `REF-${offer.id.substring(0, 6)}`,
        customer_id: offer.quote_id || "unknown",
        service_type: "airport" as "airport" | "hourly" | "point",
        vehicle_type: "Standard",
        pickup_location: "Not specified",
        dropoff_location: "Not specified",
        date: new Date().toISOString().split("T")[0],
        time: "00:00",
        duration: "",
        distance: "",
        status: offer.status,
        passenger_count: 1,
        luggage_count: 0,
        special_requests: "",
        priority: "medium" as "high" | "medium" | "low",
        total_amount: offer.rate_amount || 0,
        created_at: offer.created_at,
        updated_at: offer.updated_at,
        customer: {
          id: offer.quote_id || "unknown",
          full_name: "1 Passenger",
          email: "",
          phone: "",
          company_name: "",
        },
        notes: "Error processing quote data",
      };
    }
  };

  const TabContent = ({ children }: { children: React.ReactNode }) => {
    return <div className="mt-6">{children}</div>;
  };

  // Add a helper function to extract filtering logic
  const filteredOffers = (status: string) => {
    if (status === "pending") {
      return offers.filter(
        (o) => (o.status?.toLowerCase() === "pending" || o.status?.toLowerCase() === "sent") && !o.is_counter_offer
      );
    }
    if (status === "counter_offers") {
      return offers.filter((o) => o.is_counter_offer === true);
    }
    return offers.filter((o) => o.status?.toLowerCase() === status.toLowerCase());
  };

  // Create a reusable tab content renderer
  const renderTabContent = (status: string) => {
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center p-12">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin h-8 w-8 border-t-2 border-b-2 border-primary rounded-full"></div>
            <p className="text-muted-foreground">Loading offers...</p>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center p-12">
          <div className="flex flex-col items-center space-y-4">
            <div className="flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>
            <p className="text-red-600 font-medium">{error}</p>
            <div className="flex gap-3 mt-4">
              <Button onClick={handleRetry} size="sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Retry
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </Button>
            </div>
          </div>
        </div>
      );
    }

    const statusOffers = filteredOffers(status);

    if (statusOffers.length === 0) {
      return (
        <div className="text-center py-10">
          <p className="text-gray-500">No {status} offers found.</p>
        </div>
      );
    }

    // Track how many offers have missing quote data
    let missingQuoteDataCount = 0;

    return (
      <div className="flex flex-col space-y-4">
        {statusOffers.map((offer) => {
          const quoteData = convertOfferToQuoteData(offer);

          // Check if this row has missing data via the notes field
          if (
            quoteData.notes &&
            quoteData?.notes?.includes("Missing quote data")
          ) {
            missingQuoteDataCount++;
          }

          // Check if this is an accepted offer waiting for trip generation
          const hasTripGenerated =
            offer.quote?.trips &&
            Array.isArray(offer.quote.trips) &&
            offer.quote?.trips?.length > 0;
          const isAcceptedAwaitingTrip =
            offer.status?.toLowerCase() === "accepted" && !hasTripGenerated;

          // Use a simpler card view for mobile
          if (typeof window !== "undefined" && window.innerWidth < 768) {
            return (
              <Card key={offer.id} className="p-4">
                <div className="flex flex-col space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium text-lg">
                        {quoteData.reference_number ||
                          `REF-${offer.id.substring(0, 8)}`}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {quoteData.customer?.full_name || "Unknown Customer"}
                      </p>
                    </div>
                    <div className="flex flex-col gap-1">
                      <Badge
                        className={`${offer.status?.toLowerCase() === "pending" ? "bg-blue-100 text-blue-800" : offer.status?.toLowerCase() === "accepted" ? "bg-green-100 text-green-800" : offer.status?.toLowerCase() === "expired" ? "bg-gray-100 text-gray-800" : "bg-red-100 text-red-800"}`}
                      >
                        {offer.status.charAt(0).toUpperCase() +
                          offer.status.slice(1)}
                      </Badge>
                      {isAcceptedAwaitingTrip && (
                        <Badge
                          variant="outline"
                          className="bg-amber-50 text-amber-800 border-amber-200"
                        >
                          Awaiting Trip
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center text-sm">
                      <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="truncate">
                        {quoteData.pickup_location}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="truncate">
                        {quoteData.dropoff_location}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Clock className="h-4 w-4 mr-2 text-gray-500" />
                      <span>
                        {quoteData.date} at {quoteData.time}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Users className="h-4 w-4 mr-2 text-gray-500" />
                      <span>{quoteData.passenger_count || 0} passengers</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Car className="h-4 w-4 mr-2 text-gray-500" />
                      <span>{quoteData.vehicle_type}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                      <span>
                        ${quoteData.total_amount || offer.rate_amount || 0}
                      </span>
                    </div>
                  </div>

                  <div className="pt-2">
                    <Button
                      className="w-full"
                      onClick={() => {
                        setSelectedOffer(offer);
                        setShowActionPanel(true);
                      }}
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              </Card>
            );
          }

          return (
            <div key={offer.id} className="relative">
              {isAcceptedAwaitingTrip && (
                <div className="absolute right-0 top-0 -mt-2 z-10">
                  <Badge
                    variant="outline"
                    className="bg-amber-50 text-amber-800 border-amber-200"
                  >
                    Awaiting Trip Generation
                  </Badge>
                </div>
              )}
              <QuoteRow
                quote={quoteData}
                userType="affiliate"
                onClick={() => {
                  setSelectedOffer(offer);
                  setShowActionPanel(true);
                }}
                onAccept={
                  status === "pending" && canRespondToOffer(offer)
                    ? () => handleAction("accept_offer", offer.id)
                    : undefined
                }
                onReject={
                  status === "pending" && canRespondToOffer(offer)
                    ? () => handleAction("reject_offer", offer.id)
                    : undefined
                }
                onSubmitRate={
                  status === "pending" && canRespondToOffer(offer)
                    ? () =>
                        handleAction("submit_rate", {
                          quote_id: offer.quote_id,
                          rate: offer.rate_amount,
                        })
                    : undefined
                }
              />
            </div>
          );
        })}

        {/* Display a warning if there were missing quote data */}
        {missingQuoteDataCount > 0 && (
          <div className="rounded-md bg-yellow-50 p-3 mt-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  {missingQuoteDataCount} offer
                  {missingQuoteDataCount > 1 ? "s" : ""} with missing data
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>
                    Some offers couldn't be displayed properly due to missing
                    quote data. This may be caused by permission issues or the
                    quotes may no longer exist.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Update fetchData to be cleaner and more reliable
  const fetchOffers = useCallback(async () => {
    if (loading && !actionInProgress) return;

    try {
      setError(null);

      // Don't show loading state if this is a background refresh during an action
      if (!actionInProgress) {
        setLoading(true);
      }

      // Get the affiliate ID
      const affiliateIdResult =
        typeof affiliateSelectedCompany === "string"
          ? affiliateSelectedCompany
          : affiliateSelectedCompany?.id;

      if (!affiliateIdResult) {
        throw new Error("Could not determine affiliate ID");
      }

      debug(`Fetching offers for affiliate: ${affiliateIdResult}`);

      // Fetch the affiliate offers data
      const data = await getAffiliateOffers(affiliateIdResult);

      if (data && data.length > 0) {
        // Cast the data to the correct type and process it
        handleOffersData(data as unknown as ExtendedAffiliateOffer[]);

        // Check if we need to focus on a specific tab after an action (for UX)
        debug(`Fetched ${data.length} offers. Current tab: ${activeTab}`);
      } else {
        debug("No offers found");
        setOffers([]);
        setStatCounts({
          total: 0,
          pending: 0,
          accepted: 0,
          rejected: 0,
          expired: 0,
          counter_offers: 0,
        });
        setLoading(false);
      }
    } catch (err) {
      console.error("Error fetching offers:", err);
      setError("Failed to load offers. Please refresh and try again.");
      setLoading(false);
    }
  }, [activeTab, loading, actionInProgress, affiliateSelectedCompany]);

  // Add another useEffect to handle tab changes
  useEffect(() => {
    if (activeTab && !loading) {
      // If the tab is changed by the user, ensure we select relevant content
      debug(`Active tab changed to: ${activeTab}`);

      // If there are no items in the active tab but there are items in other tabs,
      // we might want to add a message here
    }
  }, [activeTab, loading, offers]);

  // If company context itself has an error
  if (affiliateCompanyError) {
    return (
      <div className="p-8 text-red-600">
        Error loading affiliate context: {affiliateCompanyError}
      </div>
    );
  }

  // If still loading company context, show a page-level loader
  // The `showOnboardingBanner` logic will be handled within the main return if company is loaded but pending.
  if (isLoadingCompanies && !affiliateSelectedCompany) {
    // Show loader if context is loading AND no company yet selected
    return (
      <div className="flex justify-center items-center h-screen p-8">
        <div>Loading affiliate data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {showOnboardingBanner && <AffiliateOnboardingBanner />}
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-6">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tight">Offers</h2>
            <p className="text-muted-foreground">
              Manage offers sent to your affiliate company.
            </p>
          </div>

          <div className="bg-white shadow rounded-lg overflow-hidden">
            <ControlledTabs
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              tabs={[
                {
                  id: "pending",
                  label: "Pending",
                  count: statCounts.pending,
                  content: renderTabContent("pending"),
                },
                {
                  id: "counter_offers",
                  label: "Counter Offers",
                  count: statCounts.counter_offers,
                  content: renderTabContent("counter_offers"),
                },
                {
                  id: "accepted",
                  label: "Accepted",
                  count: statCounts.accepted,
                  content: renderTabContent("accepted"),
                },
                {
                  id: "rejected",
                  label: "Rejected",
                  count: statCounts.rejected,
                  content: renderTabContent("rejected"),
                },
              ]}
            />

            {/* Tab content is handled by ControlledTabs component above */}
            {error && !loading && (
              <div className="p-6">
                <div className="rounded-md bg-red-50 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <AlertCircle className="h-5 w-5 text-red-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Error loading offers
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{error}</p>
                      </div>
                      <div className="mt-4">
                        <Button
                          size="sm"
                          onClick={handleRetry}
                          className="bg-red-50 text-red-800 hover:bg-red-100 border border-red-300"
                        >
                          Retry
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Action Panel */}
      {showActionPanel && selectedOffer && (
        <AffiliateQuoteActionPanel
          offer={selectedOffer}
          isOpen={showActionPanel}
          onClose={() => setShowActionPanel(false)}
          onAction={handleAction}
          canRespond={canRespondToOffer(selectedOffer)}
          isApproved={isAffiliateAccessActive}
        />
      )}
    </div>
  );
}

// Export wrapped with error boundary
export default function OffersPage() {
  return (
    <OffersErrorBoundary>
      <OffersPageContent />
    </OffersErrorBoundary>
  );
}
