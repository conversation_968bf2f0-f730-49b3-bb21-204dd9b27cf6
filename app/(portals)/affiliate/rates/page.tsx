"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/app/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form"
import { Input } from "@/app/components/ui/input"
import { Textarea } from "@/app/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Separator } from "@/app/components/ui/separator"
import { Checkbox } from "@/app/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { Calendar } from "@/app/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/app/components/ui/popover"
import { toast } from "sonner"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Plus, X } from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { PricingStrategySelect } from "@/app/components/ui/pricing-strategy-select"
import { ToggleGroup, ToggleGroupItem } from "@/app/components/ui/toggle-group"

// Vehicle types from your form
const vehicleTypes = [
  { id: "sedan", label: "SEDAN" },
  { id: "suv", label: "SUV" },
  { id: "luxury_sedan", label: "LUXURY SEDAN" },
  { id: "luxury_suv", label: "LUXURY SUV" },
  { id: "sprinter", label: "MERCEDES SPRINTER" },
  { id: "executive_sprinter", label: "EXECUTIVE SPRINTER" },
  { id: "stretch", label: "STRETCH LIMOUSINE" },
  { id: "hummer", label: "HUMMER LIMOUSINE" },
  { id: "sprinter_limo", label: "SPRINTER LIMO" },
  { id: "minibus", label: "MINI BUS" },
  { id: "motorcoach", label: "MOTOR COACH" },
  { id: "tesla", label: "TESLA" },
  { id: "party_bus", label: "PARTY BUS" },
  { id: "wheelchair", label: "WHEELCHAIR ACCESSIBLE VEHICLE" },
]

const pricingStrategies = [
  { id: "transfer", label: "Transfer/One-Way" },
  { id: "hourly", label: "As Directed/Charter" },
  { id: "distance_time", label: "Distance + Time" },
]

const formSchema = z.object({
  vehicles: z.array(z.object({
    type: z.string(),
    enabled: z.boolean(),
    // Pricing Type
    useDistanceTime: z.boolean().default(false),
    // Point to Point Rate
    pointToPointRate: z.string().regex(/^\d*\.?\d*$/, "Please enter a valid rate").optional(),
    // Distance + Time fields
    baseDistanceRate: z.string().regex(/^\d*\.?\d*$/, "Please enter a valid rate").optional(),
    perMileRate: z.string().regex(/^\d*\.?\d*$/, "Please enter a valid rate").optional(),
    perHourRate: z.string().regex(/^\d*\.?\d*$/, "Please enter a valid rate").optional(),
    minimumMiles: z.string().regex(/^\d*\.?\d*$/, "Please enter a valid number").optional(),
    // Common fields for both pricing types
    extraHourRate: z.string().regex(/^\d*\.?\d*$/, "Please enter a valid rate").optional(),
    airportRate: z.string().regex(/^\d*\.?\d*$/, "Please enter a valid rate").optional(),
    hourlyRate: z.string().regex(/^\d*\.?\d*$/, "Please enter a valid rate").optional(),
    minimumHours: z.string().regex(/^\d*\.?\d*$/, "Please enter a valid number").optional(),
    cancellationPolicy: z.string(),
  })),
  seasonalRanges: z.array(z.object({
    id: z.string(),
    name: z.string().min(1, "Please enter a name for this seasonal period"),
    startDate: z.date(),
    endDate: z.date(),
    description: z.string().optional(),
  })),
})

type SeasonalRange = {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  description?: string;
}

export default function RatesPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [seasonalRanges, setSeasonalRanges] = useState<SeasonalRange[]>([])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      vehicles: vehicleTypes.map(type => ({
        type: type.id,
        enabled: false,
        useDistanceTime: false,
        pointToPointRate: "",
        baseDistanceRate: "",
        perMileRate: "",
        perHourRate: "",
        minimumMiles: "",
        extraHourRate: "",
        airportRate: "",
        hourlyRate: "",
        minimumHours: "",
        cancellationPolicy: "",
      })),
      seasonalRanges: [],
    },
  })

  // Function to handle keyboard navigation in the table
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>, currentIndex: number, currentField: string) => {
    if (event.key === 'Enter' || event.key === 'ArrowDown') {
      event.preventDefault()
      const nextInput = document.querySelector(`input[name="vehicles.${currentIndex + 1}.${currentField}"]`) as HTMLElement
      if (nextInput) nextInput.focus()
    } else if (event.key === 'ArrowUp') {
      event.preventDefault()
      const prevInput = document.querySelector(`input[name="vehicles.${currentIndex - 1}.${currentField}"]`) as HTMLElement
      if (prevInput) prevInput.focus()
    }
  }

  const addSeasonalRange = () => {
    const newRange: SeasonalRange = {
      id: Math.random().toString(36).substring(7),
      name: "",
      startDate: new Date(),
      endDate: new Date(),
      description: "",
    }
    setSeasonalRanges([...seasonalRanges, newRange])
  }

  const removeSeasonalRange = (id: string) => {
    setSeasonalRanges(seasonalRanges.filter(range => range.id !== id))
  }

  const updateSeasonalRange = (id: string, field: keyof SeasonalRange, value: any) => {
    setSeasonalRanges(ranges =>
      ranges.map(range =>
        range.id === id ? { ...range, [field]: value } : range
      )
    )
  }

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)
    try {
      // Include seasonal ranges in the submission
      const dataToSubmit = {
        ...values,
        seasonalRanges,
      }
      console.log(dataToSubmit)
      toast.success("Rate information updated successfully")
    } catch (error) {
      toast.error("Failed to update rate information")
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderRateFields = (index: number) => {
    const isEnabled = form.watch(`vehicles.${index}.enabled`)
    const useDistanceTime = form.watch(`vehicles.${index}.useDistanceTime`)

    return (
      <>
        {/* Pricing Model Toggle */}
        <TableCell className="border-r">
          <FormField
            control={form.control}
            name={`vehicles.${index}.useDistanceTime`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <ToggleGroup
                    type="single"
                    value={field.value ? "distance" : "fixed"}
                    onValueChange={(value: string) => {
                      field.onChange(value === "distance")
                    }}
                    className="flex gap-1 bg-muted/30 p-0.5 rounded-md"
                    disabled={!isEnabled}
                  >
                    <ToggleGroupItem
                      value="fixed"
                      size="sm"
                      className="text-xs px-3 py-1.5 data-[state=on]:bg-white data-[state=on]:text-primary data-[state=on]:shadow-sm rounded-sm"
                      disabled={!isEnabled}
                    >
                      Point to Point
                    </ToggleGroupItem>
                    <ToggleGroupItem
                      value="distance"
                      size="sm"
                      className="text-xs px-3 py-1.5 data-[state=on]:bg-white data-[state=on]:text-primary data-[state=on]:shadow-sm rounded-sm"
                      disabled={!isEnabled}
                    >
                      Distance + Time
                    </ToggleGroupItem>
                  </ToggleGroup>
                </FormControl>
              </FormItem>
            )}
          />
        </TableCell>

        {/* Dynamic Rate Fields */}
        {!useDistanceTime ? (
          <>
            {/* Point to Point Rates */}
            <TableCell className="bg-muted/50">
              <FormField
                control={form.control}
                name={`vehicles.${index}.pointToPointRate`}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        className="w-24"
                        {...field}
                        disabled={!isEnabled}
                        onKeyDown={(e) => handleKeyDown(e, index, 'pointToPointRate')}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </TableCell>
            <TableCell className="border-r bg-muted/50">
              <FormField
                control={form.control}
                name={`vehicles.${index}.extraHourRate`}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        className="w-24"
                        {...field}
                        disabled={!isEnabled}
                        onKeyDown={(e) => handleKeyDown(e, index, 'extraHourRate')}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </TableCell>
          </>
        ) : (
          <>
            {/* Distance + Time Fields */}
            <TableCell className="bg-muted/50">
              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name={`vehicles.${index}.baseDistanceRate`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          className="w-24"
                          {...field}
                          disabled={!isEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`vehicles.${index}.perHourRate`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="$/hour"
                          className="w-24"
                          {...field}
                          disabled={!isEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </TableCell>
            <TableCell className="border-r bg-muted/50">
              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name={`vehicles.${index}.perMileRate`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="$/mile"
                          className="w-24"
                          {...field}
                          disabled={!isEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`vehicles.${index}.minimumMiles`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="number"
                          step="1"
                          min="0"
                          placeholder="Min miles"
                          className="w-24"
                          {...field}
                          disabled={!isEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </TableCell>
          </>
        )}

        {/* Airport Rate */}
        <TableCell className="border-r bg-muted/30">
          <FormField
            control={form.control}
            name={`vehicles.${index}.airportRate`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    className="w-24"
                    {...field}
                    disabled={!isEnabled}
                    onKeyDown={(e) => handleKeyDown(e, index, 'airportRate')}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </TableCell>

        {/* Hourly Service */}
        <TableCell className="bg-muted/30">
          <FormField
            control={form.control}
            name={`vehicles.${index}.hourlyRate`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    className="w-24"
                    {...field}
                    disabled={!isEnabled}
                    onKeyDown={(e) => handleKeyDown(e, index, 'hourlyRate')}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </TableCell>
        <TableCell className="bg-muted/30">
          <FormField
            control={form.control}
            name={`vehicles.${index}.minimumHours`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="number"
                    step="0.5"
                    min="0"
                    placeholder="0"
                    className="w-20"
                    {...field}
                    disabled={!isEnabled}
                    onKeyDown={(e) => handleKeyDown(e, index, 'minimumHours')}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </TableCell>
      </>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Rate Management</h3>
        <p className="text-sm text-muted-foreground">
          Set your rates for each vehicle type and specify seasonal changes
        </p>
      </div>
      <Separator />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Vehicle Rates</CardTitle>
              <CardDescription>
                Set point-to-point or distance-based pricing, plus hourly and airport rates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px] border-r">Enable</TableHead>
                      <TableHead className="min-w-[200px] border-r">Vehicle Type</TableHead>
                      <TableHead className="w-[200px] border-r">Pricing Model</TableHead>
                      {/* Dynamic Rate Block based on pricing model */}
                      {!form.watch(`vehicles.0.useDistanceTime`) ? (
                        <>
                          {/* Point to Point Rate Block */}
                          <TableHead colSpan={2} className="text-center border-r bg-muted/50">
                            Point to Point Rates
                          </TableHead>
                        </>
                      ) : (
                        <>
                          {/* Distance + Time Block */}
                          <TableHead colSpan={2} className="text-center border-r bg-muted/50">
                            Distance + Time
                          </TableHead>
                        </>
                      )}
                      {/* Airport Rate Block */}
                      <TableHead className="text-center border-r bg-muted/30">
                        Airport Rate
                      </TableHead>
                      {/* Hourly Service Block */}
                      <TableHead colSpan={2} className="text-center bg-muted/30">
                        Hourly Service
                      </TableHead>
                    </TableRow>
                    <TableRow>
                      <TableHead className="border-r"></TableHead>
                      <TableHead className="border-r"></TableHead>
                      <TableHead className="border-r"></TableHead>
                      {/* Dynamic Rate Fields based on pricing model */}
                      {!form.watch(`vehicles.0.useDistanceTime`) ? (
                        <>
                          <TableHead className="bg-muted/50">Point to Point ($)</TableHead>
                          <TableHead className="border-r bg-muted/50">Extra Hour ($)</TableHead>
                        </>
                      ) : (
                        <>
                          <TableHead className="bg-muted/50">Base Rate ($)</TableHead>
                          <TableHead className="border-r bg-muted/50">Per Mile ($)</TableHead>
                        </>
                      )}
                      {/* Airport Rate Field */}
                      <TableHead className="border-r bg-muted/30">Airport Rate ($)</TableHead>
                      {/* Hourly Service Fields */}
                      <TableHead className="bg-muted/30">Hourly Rate ($)</TableHead>
                      <TableHead className="bg-muted/30">Min. Hours</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {vehicleTypes.map((vehicleType, index) => (
                      <TableRow key={vehicleType.id} className="relative">
                        <TableCell className="border-r">
                          <FormField
                            control={form.control}
                            name={`vehicles.${index}.enabled`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </TableCell>
                        <TableCell className="font-medium border-r">{vehicleType.label}</TableCell>
                        {renderRateFields(index)}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Seasonal & Event Pricing</CardTitle>
                  <CardDescription>
                    Add date ranges for special events or peak seasons
                  </CardDescription>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addSeasonalRange}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Event
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {seasonalRanges.map((range) => (
                  <div
                    key={range.id}
                    className="flex items-start space-x-4 rounded-lg border p-4"
                  >
                    <div className="flex-1 space-y-4">
                      <div className="grid gap-4 md:grid-cols-3">
                        <div>
                          <FormLabel>Event Name</FormLabel>
                          <Input
                            placeholder="e.g., SXSW 2024"
                            value={range.name}
                            onChange={(e) => updateSeasonalRange(range.id, 'name', e?.target?.value)}
                          />
                        </div>
                        <div>
                          <FormLabel>Start Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className="w-full justify-start text-left font-normal"
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {range.startDate ? format(range.startDate, "PPP") : "Pick a date"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={range.startDate}
                                onSelect={(date) => date && updateSeasonalRange(range.id, 'startDate', date)}
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                        <div>
                          <FormLabel>End Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className="w-full justify-start text-left font-normal"
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {range.endDate ? format(range.endDate, "PPP") : "Pick a date"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={range.endDate}
                                onSelect={(date) => date && updateSeasonalRange(range.id, 'endDate', date)}
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                      </div>
                      <div>
                        <FormLabel>Description</FormLabel>
                        <Textarea
                          placeholder="Add any notes about this event"
                          value={range.description || ""}
                          onChange={(e) => updateSeasonalRange(range.id, 'description', e?.target?.value)}
                        />
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeSeasonalRange(range.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Rates"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
} 