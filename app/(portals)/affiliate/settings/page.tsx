"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { getSupabaseClient } from "@/lib/supabase"
import { Button } from "@/app/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Switch } from "@/app/components/ui/switch"
import { Label } from "@/app/components/ui/label"
import { Input } from "@/app/components/ui/input"
import { Separator } from "@/app/components/ui/separator"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { Checkbox } from "@/app/components/ui/checkbox"
import { Badge } from "@/app/components/ui/badge"
import { useToast } from "@/app/components/ui/use-toast"
import { CreateCompanyForm } from "@/components/affiliate/forms/CreateCompanyForm"
import {
  Bell,
  Clock,
  Mail,
  Shield,
  Smartphone,
  User,
  Wallet,
  Building2,
  Car,
  MapPin,
} from "lucide-react"
import { useAffiliateAccess } from '@/app/lib/hooks/useAffiliateAccess';
import { AffiliateOnboardingBanner } from '@/app/components/ui/AffiliateOnboardingBanner';
import { useAffiliateCompany } from '@/app/contexts/AffiliateCompanyContext';

interface CompanyData {
  id: string
  name: string
  city: string
  state: string
  status: string
}

interface DispatcherData {
  id: string
  email: string
  first_name: string
  last_name: string
  status: string
  created_at: string
  companies: string[]
}

export default function SettingsPage() {
  const { showOnboardingBanner } = useAffiliateAccess();
  const { selectedCompany, userCompanies: contextUserCompanies, isLoadingCompanies, fetchUserCompanies: refreshCompanies } = useAffiliateCompany();
  const supabase = getSupabaseClient()
  const { toast } = useToast()

  const [emailNotifications, setEmailNotifications] = useState(true)
  const [smsNotifications, setSmsNotifications] = useState(true)
  const [autoAccept, setAutoAccept] = useState(false)
  const [showCreateCompanyDialog, setShowCreateCompanyDialog] = useState(false)
  const [userRole, setUserRole] = useState<string | null>(null)

  // Dispatcher management state
  const [dispatchers, setDispatchers] = useState<DispatcherData[]>([])
  const [userCompanies, setUserCompanies] = useState<CompanyData[]>([])
  const [showAddDispatcherDialog, setShowAddDispatcherDialog] = useState(false)
  const [dispatcherForm, setDispatcherForm] = useState({
    email: '',
    firstName: '',
    lastName: '',
    selectedCompanies: [] as string[]
  })
  const [isCreatingDispatcher, setIsCreatingDispatcher] = useState(false)

  useEffect(() => {
    fetchUserRole()
    fetchUserCompanies()
    fetchDispatchers()
  }, [])

  // Refresh companies when the create company dialog closes
  useEffect(() => {
    if (!showCreateCompanyDialog) {
      // Dialog was closed, refresh companies to show any newly created ones
      refreshCompanies()
    }
  }, [showCreateCompanyDialog, refreshCompanies])

  const fetchUserRole = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (error) {
        console.error('Error fetching user role:', error)
        return
      }

      setUserRole(profile?.role || null)
    } catch (error) {
      console.error('Error:', error)
    }
  }



  const fetchUserCompanies = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { data: companies, error } = await supabase
        .from('affiliate_companies')
        .select('id, name, city, state, status')
        .eq('owner_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching companies:', error)
        return
      }

      setUserCompanies(companies || [])
    } catch (error) {
      console.error('Error:', error)
    }
  }

  const fetchDispatchers = async () => {
    try {
      const response = await fetch('/api/affiliate/dispatchers')
      if (response.ok) {
        const data = await response.json()
        setDispatchers(data.dispatchers || [])
      }
    } catch (error) {
      console.error('Error fetching dispatchers:', error)
    }
  }

  const handleCreateDispatcher = async () => {
    if (!dispatcherForm.email || !dispatcherForm.firstName || !dispatcherForm.lastName) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    setIsCreatingDispatcher(true)
    try {
      const response = await fetch('/api/affiliate/dispatchers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dispatcherForm),
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Dispatcher Added",
          description: `${dispatcherForm.firstName} ${dispatcherForm.lastName} has been added as a dispatcher.`,
        })

        // Reset form and close dialog
        setDispatcherForm({
          email: '',
          firstName: '',
          lastName: '',
          selectedCompanies: []
        })
        setShowAddDispatcherDialog(false)

        // Refresh dispatchers list
        fetchDispatchers()
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create dispatcher')
      }
    } catch (error) {
      console.error('Error creating dispatcher:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create dispatcher account.",
        variant: "destructive",
      })
    } finally {
      setIsCreatingDispatcher(false)
    }
  }

  if (showOnboardingBanner) {
    return (
      <div className="p-8">
        <AffiliateOnboardingBanner>
          <div className="mt-2 text-yellow-800">You must complete onboarding to access settings.</div>
        </AffiliateOnboardingBanner>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Settings</h3>
        <p className="text-sm text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      <Tabs defaultValue={userRole === 'dispatcher' ? "notifications" : "general"} className="space-y-4">
        <TabsList>
          {userRole !== 'dispatcher' && <TabsTrigger value="general">General</TabsTrigger>}
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          {userRole !== 'dispatcher' && <TabsTrigger value="companies">Companies</TabsTrigger>}
          {userRole !== 'dispatcher' && <TabsTrigger value="dispatchers">Dispatchers</TabsTrigger>}
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Account Preferences</CardTitle>
              <CardDescription>
                Configure your general account settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Business Profile Link */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <Building2 className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-blue-800">Business Information</h5>
                    <p className="text-xs text-blue-700">
                      Manage your company details, fleet, rates, and service areas in the Company Profile section.
                    </p>
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/affiliate/company/enhanced">
                        Go to Company Profile
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Auto-Accept Standard Rate Trips</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically accept trips that match your standard rates
                    </p>
                  </div>
                  <Switch
                    checked={autoAccept}
                    onCheckedChange={setAutoAccept}
                  />
                </div>
                <Separator />
                <div className="space-y-2">
                  <Label>Response Time Window</Label>
                  <p className="text-sm text-muted-foreground">
                    Set your preferred response time for trip offers
                  </p>
                  <div className="flex items-center gap-2">
                    <Input type="number" placeholder="2" className="w-20" />
                    <span className="text-sm">hours</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Choose how you want to receive updates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    <Label>Email Notifications</Label>
                  </div>
                  <Switch
                    checked={emailNotifications}
                    onCheckedChange={setEmailNotifications}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Smartphone className="h-4 w-4" />
                    <Label>SMS Notifications</Label>
                  </div>
                  <Switch
                    checked={smsNotifications}
                    onCheckedChange={setSmsNotifications}
                  />
                </div>
                <Separator />
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Notify me about:</h4>
                  <div className="grid gap-2">
                    <div className="flex items-center gap-2">
                      <Switch defaultChecked id="new-trips" />
                      <Label htmlFor="new-trips">New trip offers</Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch defaultChecked id="rate-queries" />
                      <Label htmlFor="rate-queries">Rate queries</Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch defaultChecked id="trip-updates" />
                      <Label htmlFor="trip-updates">Trip updates & changes</Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch defaultChecked id="billing" />
                      <Label htmlFor="billing">Billing & payments</Label>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>



        <TabsContent value="companies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Multi-Company Management
              </CardTitle>
              <CardDescription>
                Expand your business by creating additional transportation companies
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Current Company Status */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Current Company</h4>
                  <span className="text-xs text-muted-foreground">Selected via top-right dropdown</span>
                </div>

                {isLoadingCompanies ? (
                  <div className="p-4 border rounded-lg bg-gray-50/50">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
                      <div className="space-y-2">
                        <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
                        <div className="h-3 w-24 bg-gray-200 rounded animate-pulse" />
                      </div>
                    </div>
                  </div>
                ) : selectedCompany ? (
                  <div className="p-4 border rounded-lg bg-blue-50/50">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <Building2 className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{selectedCompany.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {selectedCompany.city}, {selectedCompany.state} • Currently Active
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-4 border-2 border-dashed border-gray-200 rounded-lg text-center">
                    <Building2 className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm font-medium text-gray-600">No Company Found</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Complete your company profile to get started
                    </p>
                  </div>
                )}
              </div>

              <Separator />

              {/* Add New Company */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Create Additional Company</h4>
                  <Dialog open={showCreateCompanyDialog} onOpenChange={setShowCreateCompanyDialog}>
                    <DialogTrigger asChild>
                      <Button>
                        <Building2 className="h-4 w-4 mr-2" />
                        Add New Company
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Create New Company</DialogTitle>
                        <DialogDescription>
                          Add a new transportation company for a different city where you operate
                        </DialogDescription>
                      </DialogHeader>
                      <CreateCompanyForm
                        redirectAfterCreate={false}
                        onSuccess={() => setShowCreateCompanyDialog(false)}
                      />
                    </DialogContent>
                  </Dialog>
                </div>

                <div className="p-4 border-2 border-dashed border-gray-200 rounded-lg text-center">
                  <Building2 className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-600">Add Your Other City Operations</p>
                  <p className="text-xs text-gray-500 mt-1">
                    Create separate affiliate accounts for each city where you operate
                  </p>
                </div>
              </div>

              <Separator />

              {/* Multi-Company Benefits */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium">Why Create Multiple Affiliate Accounts?</h4>
                <div className="grid gap-3 text-sm text-muted-foreground">
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2" />
                    <p><strong>Multi-City Operations:</strong> Separate affiliate accounts for each city you serve</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2" />
                    <p><strong>Local Market Focus:</strong> Receive quotes specific to each city's market</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2" />
                    <p><strong>Separate Fleet Management:</strong> Different vehicles and rates per city</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2" />
                    <p><strong>Unified Dashboard:</strong> Manage all city operations from one account</p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Important Notes */}
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 text-amber-600 mt-0.5">⚠️</div>
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-amber-800">Important Guidelines</h5>
                    <ul className="text-xs text-amber-700 space-y-1">
                      <li>• Only create additional companies for cities where you physically operate</li>
                      <li>• Each company represents your existing business presence in that specific city</li>
                      <li>• Multiple companies in the same city will compete for the same quotes</li>
                      <li>• Use the top-right dropdown to switch between your city operations</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dispatchers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Dispatcher Management
              </CardTitle>
              <CardDescription>
                Add and manage dispatchers who can help operate your transportation business
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Add Dispatcher Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Team Members</h4>
                  <Dialog open={showAddDispatcherDialog} onOpenChange={setShowAddDispatcherDialog}>
                    <DialogTrigger asChild>
                      <Button>
                        <User className="h-4 w-4 mr-2" />
                        Add Dispatcher
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle>Add New Dispatcher</DialogTitle>
                        <DialogDescription>
                          Create a dispatcher account to help manage your operations
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="firstName">First Name</Label>
                            <Input
                              id="firstName"
                              value={dispatcherForm.firstName}
                              onChange={(e) => setDispatcherForm(prev => ({ ...prev, firstName: e?.target?.value }))}
                              placeholder="John"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="lastName">Last Name</Label>
                            <Input
                              id="lastName"
                              value={dispatcherForm.lastName}
                              onChange={(e) => setDispatcherForm(prev => ({ ...prev, lastName: e?.target?.value }))}
                              placeholder="Doe"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="email">Email Address</Label>
                          <Input
                            id="email"
                            type="email"
                            value={dispatcherForm.email}
                            onChange={(e) => setDispatcherForm(prev => ({ ...prev, email: e?.target?.value }))}
                            placeholder="<EMAIL>"
                          />
                        </div>

                        {userCompanies.length > 1 && (
                          <div className="space-y-2">
                            <Label>Company Access</Label>
                            <p className="text-xs text-muted-foreground">
                              Select which companies this dispatcher can access
                            </p>
                            <div className="space-y-2 max-h-32 overflow-y-auto">
                              {userCompanies.map((company) => (
                                <div key={company.id} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`company-${company.id}`}
                                    checked={dispatcherForm?.selectedCompanies?.includes(company.id)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setDispatcherForm(prev => ({
                                          ...prev,
                                          selectedCompanies: [...prev.selectedCompanies, company.id]
                                        }))
                                      } else {
                                        setDispatcherForm(prev => ({
                                          ...prev,
                                          selectedCompanies: prev?.selectedCompanies?.filter(id => id !== company.id)
                                        }))
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`company-${company.id}`} className="text-sm">
                                    {company.name}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="flex justify-end gap-2">
                          <Button variant="outline" onClick={() => setShowAddDispatcherDialog(false)}>
                            Cancel
                          </Button>
                          <Button
                            onClick={handleCreateDispatcher}
                            disabled={isCreatingDispatcher}
                          >
                            {isCreatingDispatcher ? 'Creating...' : 'Create Account'}
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>

                {/* Dispatchers Table */}
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Companies</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {dispatchers.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8">
                            <User className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                            <p className="text-sm font-medium text-muted-foreground">No dispatchers added yet</p>
                            <p className="text-xs text-muted-foreground">
                              Add dispatchers to help manage your operations
                            </p>
                          </TableCell>
                        </TableRow>
                      ) : (
                        dispatchers.map((dispatcher) => (
                          <TableRow key={dispatcher.id}>
                            <TableCell>
                              {dispatcher.first_name} {dispatcher.last_name}
                            </TableCell>
                            <TableCell>{dispatcher.email}</TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {dispatcher?.companies?.map((companyId) => {
                                  const company = userCompanies.find(c => c.id === companyId)
                                  return (
                                    <Badge key={companyId} variant="secondary" className="text-xs">
                                      {company?.name || companyId}
                                    </Badge>
                                  )
                                })}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant={dispatcher.status === 'active' ? 'default' : 'secondary'}>
                                {dispatcher.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Button variant="ghost" size="sm">
                                Edit
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>

              <Separator />

              {/* Dispatcher Permissions Info */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium">Dispatcher Permissions</h4>
                <div className="grid gap-3 text-sm text-muted-foreground">
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2" />
                    <p><strong>Full Operations Access:</strong> Dashboard, Fleet & Rates, Offers, Live Trips, Drivers</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2" />
                    <p><strong>Company-Specific:</strong> Access only to assigned companies</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2" />
                    <p><strong>Restricted Access:</strong> Cannot access Settings (General/Companies) or Billing</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2" />
                    <p><strong>Temporary Passwords:</strong> Dispatchers will receive login credentials via email</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Manage your account security preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label>Change Password</Label>
                  <Input type="password" placeholder="Current password" />
                  <Input type="password" placeholder="New password" />
                  <Input type="password" placeholder="Confirm new password" />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Two-Factor Authentication</Label>
                    <p className="text-sm text-muted-foreground">
                      Add an extra layer of security to your account
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <Button>Update Security Settings</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}