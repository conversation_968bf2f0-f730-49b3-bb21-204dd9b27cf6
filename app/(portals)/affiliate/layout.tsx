"use client"

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/lib/auth/context';
import { UserRole } from '@/app/lib/auth/roles';
import {
  LayoutDashboard,
  Car,
  DollarSign,
  Users,
  MapPin,
  Shield,
  Globe,
} from "lucide-react";
import Link from "next/link";
import { MainNav } from "@/app/components/features/navigation/main-nav";
import { UserNav } from "@/app/components/features/navigation/user-nav";
import { MobileNav } from "@/app/components/features/navigation/mobile-nav";
import { AffiliateCompanyProvider } from '@/app/contexts/AffiliateCompanyContext';
import { CompanySelector } from '@/app/components/features/affiliate/CompanySelector';

const navigation = [
  {
    title: "Dashboard",
    href: "/affiliate/dashboard",
    icon: LayoutDashboard
  },
  {
    title: "Fleet & Rates",
    href: "/affiliate/fleet-rates",
    icon: DollarSign,
  },
  {
    title: "Drivers",
    href: "/affiliate/drivers",
    icon: Users
  },
  {
    title: "Offers",
    href: "/affiliate/offers",
    icon: DollarSign,
  },
  {
    title: "Live Trips",
    href: "/affiliate/liveTrips",
    icon: MapPin,
  }
];

// Custom Affiliate Header component
function AffiliateHeader() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 hidden md:flex">
          <Link href="/affiliate/dashboard" className="mr-6 flex items-center space-x-2">
            <Globe className="h-6 w-6 text-primary" />
            <span className="hidden font-bold sm:inline-block">
              transflow
            </span>
          </Link>
          <MainNav items={navigation.map(item => ({
            title: item.title,
            href: item.href,
            icon: <item.icon className="h-4 w-4" />
          }))} />
        </div>
        <MobileNav
          superAdminItems={[]}
          tenantDataItems={navigation.map(item => ({
            title: item.title,
            href: item.href,
            // icon prop is not expected by this MobileNav's NavItem type
            // If icons are needed, MobileNav or its NavItem type needs adjustment
          }))}
        />
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            {/* Add search or other controls here if needed */}
          </div>
          <div className="flex items-center gap-4">
            <CompanySelector />
            <Link
              href="/affiliate/duty-of-care"
              className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-accent hover:bg-opacity-50 rounded-md transition-colors"
            >
              <Shield className="h-4 w-4" />
              <span className="hidden sm:inline">Duty of Care</span>
            </Link>
            <UserNav />
          </div>
        </div>
      </div>
    </header>
  );
}

export default function AffiliateLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  console.log("Affiliate layout - rendered");

  return (
    <AffiliateCompanyProvider>
      <div className="flex min-h-screen flex-col">
        <AffiliateHeader />
        <div className="container flex-1 py-6">
          <main className="flex w-full flex-1 flex-col overflow-hidden">
            {children}
          </main>
        </div>
      </div>
    </AffiliateCompanyProvider>
  );
}