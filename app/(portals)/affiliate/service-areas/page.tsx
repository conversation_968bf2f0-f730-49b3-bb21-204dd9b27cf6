"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { Badge } from '@/app/components/ui/badge';
import { Switch } from '@/app/components/ui/switch';
import { Textarea } from '@/app/components/ui/textarea';
import { useToast } from '@/app/components/ui/use-toast';
import { 
  MapPin, 
  Plus, 
  Trash2, 
  Save, 
  AlertCircle, 
  CheckCircle2,
  Globe,
  Navigation,
  Clock,
  Car
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/app/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import { useAffiliateCompany } from '@/app/contexts/AffiliateCompanyContext';
import { authenticateApiRequestWithRoles } from '@/lib/access-control-unified';

/**
 * GUG-26: Advanced Affiliate Features - Service Areas Management
 * 
 * This component allows affiliates to:
 * - Define geographic service areas with radius-based coverage
 * - Set different pricing zones and multipliers
 * - Configure availability schedules per area
 * - Manage special service conditions and restrictions
 */

interface ServiceArea {
  id: string;
  name: string;
  center_lat: number;
  center_lng: number;
  radius_miles: number;
  pricing_multiplier: number;
  is_active: boolean;
  service_types: string[];
  operating_hours: {
    [key: string]: {
      start: string;
      end: string;
      is_24_7: boolean;
    };
  };
  special_conditions?: string;
  created_at: string;
  updated_at: string;
}

interface ServiceAreaFormData {
  name: string;
  address: string;
  radius_miles: number;
  pricing_multiplier: number;
  service_types: string[];
  operating_hours: {
    [key: string]: {
      start: string;
      end: string;
      is_24_7: boolean;
    };
  };
  special_conditions: string;
}

const SERVICE_TYPES = [
  'airport_transfer',
  'point_to_point',
  'hourly_charter',
  'event_transport',
  'corporate_travel'
];

const DAYS_OF_WEEK = [
  'monday', 'tuesday', 'wednesday', 'thursday', 
  'friday', 'saturday', 'sunday'
];

export default function ServiceAreasPage() {
  const [serviceAreas, setServiceAreas] = useState<ServiceArea[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingArea, setEditingArea] = useState<ServiceArea | null>(null);
  const { toast } = useToast();
  const { selectedCompany } = useAffiliateCompany();

  const [formData, setFormData] = useState<ServiceAreaFormData>({
    name: '',
    address: '',
    radius_miles: 25,
    pricing_multiplier: 1.0,
    service_types: ['point_to_point'],
    operating_hours: DAYS_OF_WEEK.reduce((acc, day) => ({
      ...acc,
      [day]: {
        start: '06:00',
        end: '22:00',
        is_24_7: false
      }
    }), {}),
    special_conditions: ''
  });

  // Fetch service areas for the current company
  const fetchServiceAreas = async () => {
    if (!selectedCompany?.id) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/affiliate/companies/${selectedCompany.id}/service-areas`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch service areas');
      }

      const data = await response.json();
      setServiceAreas(data.serviceAreas || []);
    } catch (error) {
      console.error('Error fetching service areas:', error);
      toast({
        title: "Error",
        description: "Failed to load service areas",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Geocode address to get coordinates
  const geocodeAddress = async (address: string): Promise<{lat: number, lng: number} | null> => {
    try {
      // Using a simple geocoding approach - in production, use Google Maps API or similar
      const response = await fetch(`/api/geocoding?address=${encodeURIComponent(address)}`);
      
      if (!response.ok) {
        throw new Error('Geocoding failed');
      }

      const data = await response.json();
      return data.coordinates;
    } catch (error) {
      console.error('Geocoding error:', error);
      toast({
        title: "Geocoding Error",
        description: "Could not find coordinates for the address. Please try a different address.",
        variant: "destructive",
      });
      return null;
    }
  };

  // Save or update service area
  const handleSaveServiceArea = async () => {
    if (!selectedCompany?.id || !formData.name || !formData.address) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setSaving(true);

      // Geocode the address
      const coordinates = await geocodeAddress(formData.address);
      if (!coordinates) {
        return; // Error already shown by geocodeAddress
      }

      const serviceAreaData = {
        name: formData.name,
        center_lat: coordinates.lat,
        center_lng: coordinates.lng,
        radius_miles: formData.radius_miles,
        pricing_multiplier: formData.pricing_multiplier,
        service_types: formData.service_types,
        operating_hours: formData.operating_hours,
        special_conditions: formData.special_conditions,
        is_active: true
      };

      const url = editingArea 
        ? `/api/affiliate/companies/${selectedCompany.id}/service-areas/${editingArea.id}`
        : `/api/affiliate/companies/${selectedCompany.id}/service-areas`;
      
      const method = editingArea ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(serviceAreaData),
      });

      if (!response.ok) {
        throw new Error('Failed to save service area');
      }

      toast({
        title: "Success",
        description: `Service area ${editingArea ? 'updated' : 'created'} successfully`,
      });

      // Reset form and close dialog
      resetForm();
      setIsDialogOpen(false);
      
      // Refresh the list
      await fetchServiceAreas();

    } catch (error) {
      console.error('Error saving service area:', error);
      toast({
        title: "Error",
        description: "Failed to save service area",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Delete service area
  const handleDeleteServiceArea = async (areaId: string) => {
    if (!selectedCompany?.id) return;

    try {
      const response = await fetch(`/api/affiliate/companies/${selectedCompany.id}/service-areas/${areaId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete service area');
      }

      toast({
        title: "Success",
        description: "Service area deleted successfully",
      });

      await fetchServiceAreas();
    } catch (error) {
      console.error('Error deleting service area:', error);
      toast({
        title: "Error",
        description: "Failed to delete service area",
        variant: "destructive",
      });
    }
  };

  // Toggle service area active status
  const handleToggleActive = async (area: ServiceArea) => {
    if (!selectedCompany?.id) return;

    try {
      const response = await fetch(`/api/affiliate/companies/${selectedCompany.id}/service-areas/${area.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_active: !area.is_active
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update service area');
      }

      toast({
        title: "Success",
        description: `Service area ${!area.is_active ? 'activated' : 'deactivated'}`,
      });

      await fetchServiceAreas();
    } catch (error) {
      console.error('Error updating service area:', error);
      toast({
        title: "Error",
        description: "Failed to update service area",
        variant: "destructive",
      });
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      address: '',
      radius_miles: 25,
      pricing_multiplier: 1.0,
      service_types: ['point_to_point'],
      operating_hours: DAYS_OF_WEEK.reduce((acc, day) => ({
        ...acc,
        [day]: {
          start: '06:00',
          end: '22:00',
          is_24_7: false
        }
      }), {}),
      special_conditions: ''
    });
    setEditingArea(null);
  };

  // Edit service area
  const handleEditServiceArea = (area: ServiceArea) => {
    setFormData({
      name: area.name,
      address: '', // We don't store the original address, so leave empty
      radius_miles: area.radius_miles,
      pricing_multiplier: area.pricing_multiplier,
      service_types: area.service_types,
      operating_hours: area.operating_hours,
      special_conditions: area.special_conditions || ''
    });
    setEditingArea(area);
    setIsDialogOpen(true);
  };

  useEffect(() => {
    fetchServiceAreas();
  }, [selectedCompany?.id]);

  if (!selectedCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium">No Company Selected</h3>
          <p className="text-muted-foreground">Please select a company to manage service areas.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Service Areas</h1>
          <p className="text-muted-foreground">
            Manage your geographic service coverage and pricing zones
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add Service Area
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingArea ? 'Edit Service Area' : 'Add New Service Area'}
              </DialogTitle>
              <DialogDescription>
                Define a geographic area where you provide transportation services
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Service Area Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e?.target?.value })}
                    placeholder="e.g., Manhattan Financial District"
                  />
                </div>

                <div>
                  <Label htmlFor="address">Center Address *</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => setFormData({ ...formData, address: e?.target?.value })}
                    placeholder="e.g., 123 Wall Street, New York, NY 10005"
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    This will be the center point of your service area
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="radius">Service Radius (miles)</Label>
                    <Input
                      id="radius"
                      type="number"
                      min="1"
                      max="100"
                      value={formData.radius_miles}
                      onChange={(e) => setFormData({ ...formData, radius_miles: parseInt(e?.target?.value) || 25 })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="multiplier">Pricing Multiplier</Label>
                    <Input
                      id="multiplier"
                      type="number"
                      min="0.5"
                      max="3.0"
                      step="0.1"
                      value={formData.pricing_multiplier}
                      onChange={(e) => setFormData({ ...formData, pricing_multiplier: parseFloat(e?.target?.value) || 1.0 })}
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      1.0 = standard pricing, 1.5 = 50% premium
                    </p>
                  </div>
                </div>
              </div>

              {/* Service Types */}
              <div>
                <Label>Service Types Offered</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {SERVICE_TYPES.map((type) => (
                    <label key={type} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData?.service_types?.includes(type)}
                        onChange={(e) => {
                          if (e?.target?.checked) {
                            setFormData({
                              ...formData,
                              service_types: [...formData.service_types, type]
                            });
                          } else {
                            setFormData({
                              ...formData,
                              service_types: formData?.service_types?.filter(t => t !== type)
                            });
                          }
                        }}
                        className="rounded"
                      />
                      <span className="text-sm capitalize">
                        {type.replace('_', ' ')}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Operating Hours */}
              <div>
                <Label>Operating Hours</Label>
                <div className="space-y-3 mt-2">
                  {DAYS_OF_WEEK.map((day) => (
                    <div key={day} className="flex items-center space-x-4">
                      <div className="w-20 text-sm capitalize font-medium">
                        {day}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={formData.operating_hours[day]?.is_24_7 || false}
                          onCheckedChange={(checked) => {
                            setFormData({
                              ...formData,
                              operating_hours: {
                                ...formData.operating_hours,
                                [day]: {
                                  ...formData.operating_hours[day],
                                  is_24_7: checked
                                }
                              }
                            });
                          }}
                        />
                        <span className="text-sm">24/7</span>
                      </div>
                      {!formData.operating_hours[day]?.is_24_7 && (
                        <div className="flex items-center space-x-2">
                          <Input
                            type="time"
                            value={formData.operating_hours[day]?.start || '06:00'}
                            onChange={(e) => {
                              setFormData({
                                ...formData,
                                operating_hours: {
                                  ...formData.operating_hours,
                                  [day]: {
                                    ...formData.operating_hours[day],
                                    start: e?.target?.value
                                  }
                                }
                              });
                            }}
                            className="w-24"
                          />
                          <span>to</span>
                          <Input
                            type="time"
                            value={formData.operating_hours[day]?.end || '22:00'}
                            onChange={(e) => {
                              setFormData({
                                ...formData,
                                operating_hours: {
                                  ...formData.operating_hours,
                                  [day]: {
                                    ...formData.operating_hours[day],
                                    end: e?.target?.value
                                  }
                                }
                              });
                            }}
                            className="w-24"
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Special Conditions */}
              <div>
                <Label htmlFor="conditions">Special Conditions</Label>
                <Textarea
                  id="conditions"
                  value={formData.special_conditions}
                  onChange={(e) => setFormData({ ...formData, special_conditions: e?.target?.value })}
                  placeholder="e.g., Additional fees for events, holiday surcharges, minimum booking requirements..."
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveServiceArea} disabled={saving}>
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {editingArea ? 'Update' : 'Create'} Service Area
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Service Areas List */}
      <div className="grid gap-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : serviceAreas.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center h-64">
              <MapPin className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Service Areas Defined</h3>
              <p className="text-muted-foreground text-center mb-4">
                Start by adding your first service area to define where you provide transportation services.
              </p>
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Service Area
              </Button>
            </CardContent>
          </Card>
        ) : (
          serviceAreas.map((area) => (
            <Card key={area.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-5 w-5 text-primary" />
                      <CardTitle className="text-lg">{area.name}</CardTitle>
                    </div>
                    <Badge variant={area.is_active ? "default" : "secondary"}>
                      {area.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={area.is_active}
                      onCheckedChange={() => handleToggleActive(area)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditServiceArea(area)}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteServiceArea(area.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Navigation className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Coverage</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {area.radius_miles} mile radius
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Pricing: {area.pricing_multiplier}x standard rate
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Car className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Services</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {area?.service_types?.map((type) => (
                        <Badge key={type} variant="outline" className="text-xs">
                          {type.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Hours</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {Object.values(area.operating_hours).some(h => h.is_24_7) ? (
                        <span>24/7 Available</span>
                      ) : (
                        <span>
                          {area.operating_hours.monday?.start} - {area.operating_hours.monday?.end}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {area.special_conditions && (
                  <div className="mt-4 p-3 bg-muted rounded-lg">
                    <p className="text-sm">
                      <strong>Special Conditions:</strong> {area.special_conditions}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}