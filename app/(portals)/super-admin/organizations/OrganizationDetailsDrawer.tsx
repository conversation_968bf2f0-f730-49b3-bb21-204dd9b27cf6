"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Textarea } from "@/app/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/app/components/ui/sheet";
import { Badge } from "@/app/components/ui/badge";
import { Separator } from "@/app/components/ui/separator";
import { useToast } from "@/app/components/ui/use-toast";
import { Loader2, Save, X } from "lucide-react";

interface Organization {
  id: string;
  name: string;
  slug: string;
  description?: string;
  website?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  logo_url?: string;
  status: string;
  subscription_plan_id?: string;
  settings: Record<string, any>;
  branding: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface OrganizationDetailsDrawerProps {
  organization: Organization | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (organization: Organization) => void;
  mode: "view" | "edit" | "create";
}

export function OrganizationDetailsDrawer({
  organization,
  isOpen,
  onClose,
  onSave,
  mode,
}: OrganizationDetailsDrawerProps) {
  const [formData, setFormData] = useState<Partial<Organization>>({
    name: '',
    slug: '',
    description: '',
    website: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    state: '',
    zip_code: '',
    country: 'US',
    logo_url: '',
    status: 'active',
    settings: {},
    branding: {},
  });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Initialize form data when organization changes
  useEffect(() => {
    if (organization) {
      setFormData({
        ...organization,
      });
    } else {
      // Reset form for create mode
      setFormData({
        name: '',
        slug: '',
        description: '',
        website: '',
        phone: '',
        email: '',
        address: '',
        city: '',
        state: '',
        zip_code: '',
        country: 'US',
        logo_url: '',
        status: 'active',
        settings: {},
        branding: {},
      });
    }
  }, [organization]);

  const handleInputChange = (field: keyof Organization, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    if (!formData.name?.trim()) {
      toast({
        title: "Validation Error",
        description: "Organization name is required",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const organizationData = {
        ...formData,
        updated_at: new Date().toISOString(),
      } as Organization;

      await onSave(organizationData);
      
      toast({
        title: "Success",
        description: `Organization ${mode === 'create' ? 'created' : 'updated'} successfully`,
      });
      
      onClose();
    } catch (error) {
      console.error('Error saving organization:', error);
      toast({
        title: "Error",
        description: `Failed to ${mode === 'create' ? 'create' : 'update'} organization`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const isReadOnly = mode === "view";
  const title = mode === "create" ? "Create Organization" : 
                mode === "edit" ? "Edit Organization" : "Organization Details";

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[600px] sm:max-w-[600px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="flex items-center justify-between">
            {title}
            {organization && (
              <Badge variant={organization.status === 'active' ? 'default' : 'secondary'}>
                {organization.status}
              </Badge>
            )}
          </SheetTitle>
          <SheetDescription>
            {mode === "create" 
              ? "Create a new organization in the system"
              : mode === "edit"
              ? "Update organization information and settings"
              : "View organization details and configuration"
            }
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6 py-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Organization Name *</Label>
                <Input
                  id="name"
                  value={formData.name || ''}
                  onChange={(e) => handleInputChange('name', e?.target?.value)}
                  disabled={isReadOnly}
                  placeholder="Enter organization name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug || ''}
                  onChange={(e) => handleInputChange('slug', e?.target?.value)}
                  disabled={isReadOnly}
                  placeholder="organization-slug"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e?.target?.value)}
                disabled={isReadOnly}
                placeholder="Organization description"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email || ''}
                  onChange={(e) => handleInputChange('email', e?.target?.value)}
                  disabled={isReadOnly}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.phone || ''}
                  onChange={(e) => handleInputChange('phone', e?.target?.value)}
                  disabled={isReadOnly}
                  placeholder="+****************"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                value={formData.website || ''}
                onChange={(e) => handleInputChange('website', e?.target?.value)}
                disabled={isReadOnly}
                placeholder="https://organization.com"
              />
            </div>
          </div>

          <Separator />

          {/* Address Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Address</h3>
            
            <div className="space-y-2">
              <Label htmlFor="address">Street Address</Label>
              <Input
                id="address"
                value={formData.address || ''}
                onChange={(e) => handleInputChange('address', e?.target?.value)}
                disabled={isReadOnly}
                placeholder="123 Main Street"
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city || ''}
                  onChange={(e) => handleInputChange('city', e?.target?.value)}
                  disabled={isReadOnly}
                  placeholder="City"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  value={formData.state || ''}
                  onChange={(e) => handleInputChange('state', e?.target?.value)}
                  disabled={isReadOnly}
                  placeholder="State"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="zip_code">ZIP Code</Label>
                <Input
                  id="zip_code"
                  value={formData.zip_code || ''}
                  onChange={(e) => handleInputChange('zip_code', e?.target?.value)}
                  disabled={isReadOnly}
                  placeholder="12345"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Select
                value={formData.country || 'US'}
                onValueChange={(value) => handleInputChange('country', value)}
                disabled={isReadOnly}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select country" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="US">United States</SelectItem>
                  <SelectItem value="CA">Canada</SelectItem>
                  <SelectItem value="UK">United Kingdom</SelectItem>
                  <SelectItem value="AU">Australia</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          {/* Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Settings</h3>
            
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status || 'active'}
                onValueChange={(value) => handleInputChange('status', value)}
                disabled={isReadOnly}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="logo_url">Logo URL</Label>
              <Input
                id="logo_url"
                value={formData.logo_url || ''}
                onChange={(e) => handleInputChange('logo_url', e?.target?.value)}
                disabled={isReadOnly}
                placeholder="https://example.com/logo.png"
              />
            </div>
          </div>

          {/* Action Buttons */}
          {!isReadOnly && (
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={onClose} disabled={loading}>
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={loading}>
                {loading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {mode === 'create' ? 'Create' : 'Save'} Organization
              </Button>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}