/**
 * GUG-32: White-Label Engine - Branding Management Interface
 * 
 * Admin interface for managing organization branding configurations
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { But<PERSON> } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { Textarea } from '@/app/components/ui/textarea';
import { Badge } from '@/app/components/ui/badge';
import { useToast } from '@/app/components/ui/use-toast';
import { 
  Palette, 
  Globe, 
  Eye, 
  Save, 
  Plus,
  Edit,
  Trash2,
  Settings
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/app/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';

interface BrandingConfig {
  id: string;
  organization_id: string;
  subdomain: string;
  custom_domain?: string;
  brand_name: string;
  logo_url?: string;
  favicon_url?: string;
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  background_color: string;
  text_color: string;
  font_family: string;
  font_url?: string;
  header_style: Record<string, any>;
  footer_style: Record<string, any>;
  sidebar_style: Record<string, any>;
  email_header_color: string;
  email_footer_text?: string;
  email_signature?: string;
  is_active: boolean;
}

interface Organization {
  id: string;
  name: string;
}

export default function BrandingManagementPage() {
  const [brandings, setBrandings] = useState<BrandingConfig[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingBranding, setEditingBranding] = useState<BrandingConfig | null>(null);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    organization_id: '',
    subdomain: '',
    custom_domain: '',
    brand_name: '',
    logo_url: '',
    favicon_url: '',
    primary_color: '#3B82F6',
    secondary_color: '#1E40AF',
    accent_color: '#F59E0B',
    background_color: '#FFFFFF',
    text_color: '#1F2937',
    font_family: 'Inter',
    font_url: '',
    email_header_color: '#3B82F6',
    email_footer_text: '',
    email_signature: '',
    is_active: true
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Load organizations
      const orgsResponse = await fetch('/api/super-admin/organizations');
      if (orgsResponse.ok) {
        const orgsData = await orgsResponse.json();
        setOrganizations(orgsData.organizations || []);
      }

      // Load existing branding configurations
      const brandingResponse = await fetch('/api/super-admin/organizations/branding');
      if (brandingResponse.ok) {
        const brandingData = await brandingResponse.json();
        setBrandings(brandingData.brandings || []);
      }

    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        title: "Error",
        description: "Failed to load branding data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const url = editingBranding 
        ? `/api/super-admin/organizations/${editingBranding.organization_id}/branding`
        : '/api/super-admin/organizations/branding';
      
      const method = editingBranding ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to save branding configuration');
      }

      toast({
        title: "Success",
        description: `Branding configuration ${editingBranding ? 'updated' : 'created'} successfully`,
      });

      setIsDialogOpen(false);
      resetForm();
      loadData();

    } catch (error) {
      console.error('Error saving branding:', error);
      toast({
        title: "Error",
        description: "Failed to save branding configuration",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (branding: BrandingConfig) => {
    setEditingBranding(branding);
    setFormData({
      organization_id: branding.organization_id,
      subdomain: branding.subdomain,
      custom_domain: branding.custom_domain || '',
      brand_name: branding.brand_name,
      logo_url: branding.logo_url || '',
      favicon_url: branding.favicon_url || '',
      primary_color: branding.primary_color,
      secondary_color: branding.secondary_color,
      accent_color: branding.accent_color,
      background_color: branding.background_color,
      text_color: branding.text_color,
      font_family: branding.font_family,
      font_url: branding.font_url || '',
      email_header_color: branding.email_header_color,
      email_footer_text: branding.email_footer_text || '',
      email_signature: branding.email_signature || '',
      is_active: branding.is_active
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (brandingId: string) => {
    if (!confirm('Are you sure you want to delete this branding configuration?')) {
      return;
    }

    try {
      const response = await fetch(`/api/super-admin/organizations/branding/${brandingId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete branding configuration');
      }

      toast({
        title: "Success",
        description: "Branding configuration deleted successfully",
      });

      loadData();

    } catch (error) {
      console.error('Error deleting branding:', error);
      toast({
        title: "Error",
        description: "Failed to delete branding configuration",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setEditingBranding(null);
    setFormData({
      organization_id: '',
      subdomain: '',
      custom_domain: '',
      brand_name: '',
      logo_url: '',
      favicon_url: '',
      primary_color: '#3B82F6',
      secondary_color: '#1E40AF',
      accent_color: '#F59E0B',
      background_color: '#FFFFFF',
      text_color: '#1F2937',
      font_family: 'Inter',
      font_url: '',
      email_header_color: '#3B82F6',
      email_footer_text: '',
      email_signature: '',
      is_active: true
    });
  };

  const previewBranding = (branding: BrandingConfig) => {
    const previewUrl = `${window.location.protocol}//${branding.subdomain}.${window.location.host}`;
    window.open(previewUrl, '_blank');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading branding configurations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">White-Label Branding</h1>
          <p className="text-gray-600">Manage organization branding and white-label configurations</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="w-4 h-4 mr-2" />
              Create Branding
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingBranding ? 'Edit' : 'Create'} Branding Configuration
              </DialogTitle>
              <DialogDescription>
                Configure white-label branding for an organization
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="organization_id">Organization</Label>
                  <Select 
                    value={formData.organization_id} 
                    onValueChange={(value) => setFormData({...formData, organization_id: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select organization" />
                    </SelectTrigger>
                    <SelectContent>
                      {organizations.map((org) => (
                        <SelectItem key={org.id} value={org.id}>
                          {org.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="brand_name">Brand Name</Label>
                  <Input
                    id="brand_name"
                    value={formData.brand_name}
                    onChange={(e) => setFormData({...formData, brand_name: e?.target?.value})}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="subdomain">Subdomain</Label>
                  <Input
                    id="subdomain"
                    value={formData.subdomain}
                    onChange={(e) => setFormData({...formData, subdomain: e?.target?.value})}
                    placeholder="client-name"
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Will be accessible at: {formData.subdomain}.yourdomain.com
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="custom_domain">Custom Domain (Optional)</Label>
                  <Input
                    id="custom_domain"
                    value={formData.custom_domain}
                    onChange={(e) => setFormData({...formData, custom_domain: e?.target?.value})}
                    placeholder="client.com"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="logo_url">Logo URL</Label>
                  <Input
                    id="logo_url"
                    value={formData.logo_url}
                    onChange={(e) => setFormData({...formData, logo_url: e?.target?.value})}
                    placeholder="https://example.com/logo.png"
                  />
                </div>
                
                <div>
                  <Label htmlFor="favicon_url">Favicon URL</Label>
                  <Input
                    id="favicon_url"
                    value={formData.favicon_url}
                    onChange={(e) => setFormData({...formData, favicon_url: e?.target?.value})}
                    placeholder="https://example.com/favicon.ico"
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="primary_color">Primary Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="primary_color"
                      type="color"
                      value={formData.primary_color}
                      onChange={(e) => setFormData({...formData, primary_color: e?.target?.value})}
                      className="w-16 h-10"
                    />
                    <Input
                      value={formData.primary_color}
                      onChange={(e) => setFormData({...formData, primary_color: e?.target?.value})}
                      placeholder="#3B82F6"
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="secondary_color">Secondary Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="secondary_color"
                      type="color"
                      value={formData.secondary_color}
                      onChange={(e) => setFormData({...formData, secondary_color: e?.target?.value})}
                      className="w-16 h-10"
                    />
                    <Input
                      value={formData.secondary_color}
                      onChange={(e) => setFormData({...formData, secondary_color: e?.target?.value})}
                      placeholder="#1E40AF"
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="accent_color">Accent Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="accent_color"
                      type="color"
                      value={formData.accent_color}
                      onChange={(e) => setFormData({...formData, accent_color: e?.target?.value})}
                      className="w-16 h-10"
                    />
                    <Input
                      value={formData.accent_color}
                      onChange={(e) => setFormData({...formData, accent_color: e?.target?.value})}
                      placeholder="#F59E0B"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="font_family">Font Family</Label>
                  <Select 
                    value={formData.font_family} 
                    onValueChange={(value) => setFormData({...formData, font_family: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Inter">Inter</SelectItem>
                      <SelectItem value="Roboto">Roboto</SelectItem>
                      <SelectItem value="Open Sans">Open Sans</SelectItem>
                      <SelectItem value="Lato">Lato</SelectItem>
                      <SelectItem value="Montserrat">Montserrat</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="font_url">Custom Font URL (Optional)</Label>
                  <Input
                    id="font_url"
                    value={formData.font_url}
                    onChange={(e) => setFormData({...formData, font_url: e?.target?.value})}
                    placeholder="https://fonts.googleapis.com/css2?family=..."
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="email_footer_text">Email Footer Text</Label>
                <Textarea
                  id="email_footer_text"
                  value={formData.email_footer_text}
                  onChange={(e) => setFormData({...formData, email_footer_text: e?.target?.value})}
                  placeholder="© 2024 Your Company. All rights reserved."
                />
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  <Save className="w-4 h-4 mr-2" />
                  {editingBranding ? 'Update' : 'Create'} Branding
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6">
        {brandings.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Palette className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Branding Configurations</h3>
              <p className="text-gray-600 mb-4">Create your first white-label branding configuration</p>
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Branding
              </Button>
            </CardContent>
          </Card>
        ) : (
          brandings.map((branding) => (
            <Card key={branding.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {branding.brand_name}
                      <Badge variant={branding.is_active ? "default" : "secondary"}>
                        {branding.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </CardTitle>
                    <p className="text-gray-600">
                      {branding.subdomain}.yourdomain.com
                      {branding.custom_domain && ` • ${branding.custom_domain}`}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => previewBranding(branding)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Preview
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(branding)}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(branding.id)}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-700">Primary Color</p>
                    <div className="flex items-center gap-2 mt-1">
                      <div 
                        className="w-6 h-6 rounded border"
                        style={{ backgroundColor: branding.primary_color }}
                      ></div>
                      <span className="text-sm text-gray-600">{branding.primary_color}</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Font Family</p>
                    <p className="text-sm text-gray-600 mt-1">{branding.font_family}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Logo</p>
                    <p className="text-sm text-gray-600 mt-1">
                      {branding.logo_url ? "Configured" : "Not set"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Custom Domain</p>
                    <p className="text-sm text-gray-600 mt-1">
                      {branding.custom_domain || "Not set"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}