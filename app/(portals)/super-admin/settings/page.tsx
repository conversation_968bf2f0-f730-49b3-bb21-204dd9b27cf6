"use client"

import { useState, useEffect, useTransition } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardFooter } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Button } from "@/app/components/ui/button"
import { Switch } from "@/app/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Bell, CreditCard, Settings as SettingsIcon, Link2, Loader2 } from "lucide-react"
import { getPlatformSettings, updatePlatformSettings, PlatformConfig } from '@/app/actions/super-admin/settings'
import { useToast } from "@/app/components/ui/use-toast"

// Helper component for settings section cards to manage loading/saving state
interface SettingsCardProps {
  title: string;
  icon: React.ElementType;
  children: React.ReactNode;
  footerButtonLabel: string;
  isSaving: boolean;
  onSave: () => void;
}

function SettingsCard({ title, icon: Icon, children, footer<PERSON>uttonLabel, isSaving, onSave }: SettingsCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center gap-3 pb-2">
        <Icon className="h-5 w-5 text-primary" />
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {children}
      </CardContent>
      <CardFooter>
        <Button variant="default" onClick={onSave} disabled={isSaving}>
           {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          {isSaving ? 'Saving...' : footerButtonLabel}
        </Button>
      </CardFooter>
    </Card>
  )
}

export default function SuperAdminSettingsPage() {
  const [settings, setSettings] = useState<PlatformConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isSaving, startTransition] = useTransition()
  const { toast } = useToast()

  // Fetch initial settings
  useEffect(() => {
    async function fetchSettings() {
      setLoading(true)
      setError(null)
      const result = await getPlatformSettings()
      if (result.error) {
        setError(result.error)
        toast({
          title: "Error",
          description: `Failed to load settings: ${result.error}`,
          variant: "destructive",
        })
      } else if (result.data) {
        setSettings(result.data)
      }
      setLoading(false)
    }
    fetchSettings()
  }, [toast]) // Add toast to dependency array

  // Generic handler to update specific setting field
  const handleSettingChange = <K extends keyof PlatformConfig>(key: K, value: PlatformConfig[K]) => {
    setSettings(prev => (prev ? { ...prev, [key]: value } : null))
  }

  // Handle saving all settings via server action
  const handleSave = () => {
    if (!settings) return;

    startTransition(async () => {
      const result = await updatePlatformSettings(settings)
      if (result.error) {
        toast({
          title: "Save Failed",
          description: `Could not update settings: ${result.error}`,
          variant: "destructive",
        })
      } else {
        toast({
          title: "Settings Saved",
          description: "Platform settings updated successfully.",
        })
        // Optionally refetch or trust revalidatePath
        // const refreshResult = await getPlatformSettings();
        // if (refreshResult.data) setSettings(refreshResult.data);
      }
    })
  }

  // Render loading state
  if (loading) {
    return (
        <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="text-center text-red-500 p-6">
        Error loading settings: {error}
      </div>
    )
  }

  // Render settings form once data is loaded
  if (!settings) {
    return (
      <div className="text-center text-muted-foreground p-6">
        Could not load settings data.
      </div>
    )
  }

  return (
    <div className="space-y-8 max-w-3xl mx-auto p-6">
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Manage global platform, billing, notification, and integration settings for your organization.
        </p>
      </div>

      {/* Use SettingsCard for each section */}
      <SettingsCard title="Platform" icon={SettingsIcon} footerButtonLabel="Save Platform Settings" isSaving={isSaving} onSave={handleSave}>
        <div>
          <label className="block text-sm font-medium mb-1">Platform Name</label>
          <Input 
            value={settings.platform_name}
            onChange={e => handleSettingChange('platform_name', e?.target?.value)}
            disabled={isSaving}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Default Timezone</label>
          <Select 
            value={settings.default_timezone} 
            onValueChange={value => handleSettingChange('default_timezone', value)}
            disabled={isSaving}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select timezone" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="UTC">UTC</SelectItem>
              <SelectItem value="America/New_York">America/New_York</SelectItem>
              <SelectItem value="Europe/London">Europe/London</SelectItem>
              <SelectItem value="Asia/Dubai">Asia/Dubai</SelectItem>
              <SelectItem value="Asia/Singapore">Asia/Singapore</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </SettingsCard>

      <SettingsCard title="Billing" icon={CreditCard} footerButtonLabel="Update Billing Info" isSaving={isSaving} onSave={handleSave}>
        <div>
          <label className="block text-sm font-medium mb-1">Billing Email</label>
          <Input 
            type="email" 
            value={settings.billing_email || ''} 
            onChange={e => handleSettingChange('billing_email', e?.target?.value)}
            disabled={isSaving} 
          />
        </div>
        {/* Payment method section remains disabled as per original mock */}
        <div>
          <label className="block text-sm font-medium mb-1">Payment Method</label>
          <Select value="credit_card" onValueChange={() => {}} disabled>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Credit Card" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="credit_card">Credit Card</SelectItem>
              <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
              <SelectItem value="invoice">Invoice</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground mt-1">(Payment method management coming soon)</p>
        </div>
      </SettingsCard>

      <SettingsCard title="Notifications" icon={Bell} footerButtonLabel="Save Notification Settings" isSaving={isSaving} onSave={handleSave}>
        <div className="flex items-center justify-between">
          <span className="text-sm">Enable Email Notifications</span>
          <Switch 
            checked={settings.notifications_enabled} 
            onCheckedChange={checked => handleSettingChange('notifications_enabled', checked)}
            disabled={isSaving} 
          />
        </div>
      </SettingsCard>

      <SettingsCard title="Integrations" icon={Link2} footerButtonLabel="Save Integrations" isSaving={isSaving} onSave={handleSave}>
        <div className="flex items-center justify-between">
          <span className="text-sm">Enable Slack Integration</span>
          <Switch 
            checked={settings.slack_integration_enabled} 
            onCheckedChange={checked => handleSettingChange('slack_integration_enabled', checked)}
            disabled={isSaving} 
          />
        </div>
    <div>
          <label className="block text-sm font-medium mb-1">Webhook URL</label>
          <Input 
            value={settings.webhook_url || ''} 
            onChange={e => handleSettingChange('webhook_url', e?.target?.value)} 
            placeholder="https://hooks.example.com/..." 
            disabled={isSaving} 
          />
        </div>
      </SettingsCard>

      {/* Note: Security/Operations settings (MFA, Session, Maintenance) are managed on their respective pages */}
      {/* TODO: Add more specific error handling based on validation details if needed */}
    </div>
  )
} 