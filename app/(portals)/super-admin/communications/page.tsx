/**
 * BACKEND UI AUDIT FIX: Communication Center - Super Admin Communication Management
 * 
 * Critical UI implementation for managing platform communications and notifications
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { Badge } from '@/app/components/ui/badge';
import { Textarea } from '@/app/components/ui/textarea';
import { useToast } from '@/app/components/ui/use-toast';
import { 
  MessageSquare, 
  Mail, 
  Phone, 
  Bell,
  Send,
  Users,
  Filter,
  Search,
  Eye,
  Plus,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTit<PERSON>,
  DialogTrigger,
  DialogFooter,
} from '@/app/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/app/components/ui/tabs';

interface Communication {
  id: string;
  type: 'email' | 'sms' | 'notification' | 'system_alert';
  recipient_type: 'client' | 'affiliate' | 'driver' | 'all_users';
  recipient_id?: string;
  recipient_name: string;
  subject: string;
  message: string;
  status: 'draft' | 'sent' | 'delivered' | 'failed';
  scheduled_at?: string;
  sent_at?: string;
  created_by: string;
  created_at: string;
}

interface NotificationTemplate {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'notification';
  subject: string;
  content: string;
  variables: string[];
  is_active: boolean;
}

interface CommunicationStats {
  total_sent: number;
  emails_sent: number;
  sms_sent: number;
  notifications_sent: number;
  delivery_rate: number;
}

export default function CommunicationCenterPage() {
  const [communications, setCommunications] = useState<Communication[]>([]);
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [stats, setStats] = useState<CommunicationStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isComposeOpen, setIsComposeOpen] = useState(false);
  const [isTemplateOpen, setIsTemplateOpen] = useState(false);
  const { toast } = useToast();

  const [filters, setFilters] = useState({
    type: 'all',
    status: 'all',
    recipient: 'all',
    dateRange: '7'
  });

  const [composeForm, setComposeForm] = useState({
    type: 'email',
    recipient_type: 'client',
    recipient_id: '',
    subject: '',
    message: '',
    scheduled_at: ''
  });

  const [templateForm, setTemplateForm] = useState({
    name: '',
    type: 'email',
    subject: '',
    content: '',
    variables: [] as string[]
  });

  useEffect(() => {
    loadCommunicationData();
  }, [filters]);

  const loadCommunicationData = async () => {
    try {
      setIsLoading(true);
      
      // Load communication statistics
      const statsResponse = await fetch('/api/super-admin/communications/stats');
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.stats);
      }

      // Load communications
      const params = new URLSearchParams();
      if (filters.type !== 'all') params.set('type', filters.type);
      if (filters.status !== 'all') params.set('status', filters.status);
      if (filters.recipient !== 'all') params.set('recipient', filters.recipient);
      if (filters.dateRange) params.set('days', filters.dateRange);

      const commsResponse = await fetch(`/api/super-admin/communications?${params}`);
      if (commsResponse.ok) {
        const commsData = await commsResponse.json();
        setCommunications(commsData.communications || []);
      }

      // Load templates
      const templatesResponse = await fetch('/api/super-admin/communications/templates');
      if (templatesResponse.ok) {
        const templatesData = await templatesResponse.json();
        setTemplates(templatesData.templates || []);
      }

    } catch (error) {
      console.error('Error loading communication data:', error);
      toast({
        title: "Error",
        description: "Failed to load communication data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendCommunication = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/super-admin/communications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(composeForm),
      });

      if (!response.ok) {
        throw new Error('Failed to send communication');
      }

      toast({
        title: "Success",
        description: "Communication sent successfully",
      });

      setIsComposeOpen(false);
      setComposeForm({
        type: 'email',
        recipient_type: 'client',
        recipient_id: '',
        subject: '',
        message: '',
        scheduled_at: ''
      });
      loadCommunicationData();

    } catch (error) {
      console.error('Error sending communication:', error);
      toast({
        title: "Error",
        description: "Failed to send communication",
        variant: "destructive",
      });
    }
  };

  const handleCreateTemplate = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/super-admin/communications/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateForm),
      });

      if (!response.ok) {
        throw new Error('Failed to create template');
      }

      toast({
        title: "Success",
        description: "Template created successfully",
      });

      setIsTemplateOpen(false);
      setTemplateForm({
        name: '',
        type: 'email',
        subject: '',
        content: '',
        variables: []
      });
      loadCommunicationData();

    } catch (error) {
      console.error('Error creating template:', error);
      toast({
        title: "Error",
        description: "Failed to create template",
        variant: "destructive",
      });
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <Mail className="w-4 h-4" />;
      case 'sms':
        return <Phone className="w-4 h-4" />;
      case 'notification':
        return <Bell className="w-4 h-4" />;
      case 'system_alert':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      case 'draft':
        return <Clock className="w-4 h-4 text-gray-600" />;
      default:
        return <Clock className="w-4 h-4 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading communication data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Communication Center</h1>
          <p className="text-gray-600">Manage platform communications and notifications</p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isTemplateOpen} onOpenChange={setIsTemplateOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Settings className="w-4 h-4 mr-2" />
                Templates
              </Button>
            </DialogTrigger>
          </Dialog>
          <Dialog open={isComposeOpen} onOpenChange={setIsComposeOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Compose
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>
      </div>

      {/* Communication Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Sent</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total_sent}</p>
                </div>
                <MessageSquare className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Emails</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.emails_sent}</p>
                </div>
                <Mail className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">SMS</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.sms_sent}</p>
                </div>
                <Phone className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Notifications</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.notifications_sent}</p>
                </div>
                <Bell className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Delivery Rate</p>
                  <p className="text-2xl font-bold text-green-600">{stats?.delivery_rate?.toFixed(1)}%</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="communications" className="space-y-4">
        <TabsList>
          <TabsTrigger value="communications">Communications</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="communications" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="type">Type</Label>
                  <Select 
                    value={filters.type} 
                    onValueChange={(value) => setFilters({...filters, type: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="sms">SMS</SelectItem>
                      <SelectItem value="notification">Notification</SelectItem>
                      <SelectItem value="system_alert">System Alert</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select 
                    value={filters.status} 
                    onValueChange={(value) => setFilters({...filters, status: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="sent">Sent</SelectItem>
                      <SelectItem value="delivered">Delivered</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="recipient">Recipient</Label>
                  <Select 
                    value={filters.recipient} 
                    onValueChange={(value) => setFilters({...filters, recipient: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Recipients</SelectItem>
                      <SelectItem value="client">Clients</SelectItem>
                      <SelectItem value="affiliate">Affiliates</SelectItem>
                      <SelectItem value="driver">Drivers</SelectItem>
                      <SelectItem value="all_users">All Users</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="dateRange">Date Range</Label>
                  <Select 
                    value={filters.dateRange} 
                    onValueChange={(value) => setFilters({...filters, dateRange: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Today</SelectItem>
                      <SelectItem value="7">Last 7 days</SelectItem>
                      <SelectItem value="30">Last 30 days</SelectItem>
                      <SelectItem value="90">Last 90 days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Communications List */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Communications</CardTitle>
            </CardHeader>
            <CardContent>
              {communications.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Communications</h3>
                  <p className="text-gray-600">No communications found for the selected criteria.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {communications.map((comm) => (
                    <div key={comm.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            {getTypeIcon(comm.type)}
                            <span className="font-medium">{comm.subject}</span>
                            <Badge className={getStatusColor(comm.status)}>
                              <div className="flex items-center gap-1">
                                {getStatusIcon(comm.status)}
                                {comm.status}
                              </div>
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{comm.message.substring(0, 150)}...</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>To: {comm.recipient_name}</span>
                            <span>Type: {comm.recipient_type}</span>
                            <span>Sent: {comm.sent_at ? new Date(comm.sent_at).toLocaleString() : 'Not sent'}</span>
                          </div>
                        </div>
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Communication Templates</CardTitle>
                <Button onClick={() => setIsTemplateOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  New Template
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {templates.map((template) => (
                  <Card key={template.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{template.name}</h3>
                        <Badge variant={template.is_active ? "default" : "secondary"}>
                          {template.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        {getTypeIcon(template.type)}
                        <span className="text-sm text-gray-600">{template.type}</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{template.subject}</p>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">Edit</Button>
                        <Button variant="outline" size="sm">Use</Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Communication Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label>Email Provider Configuration</Label>
                  <p className="text-sm text-gray-600">Configure SMTP settings and email service providers</p>
                </div>
                <div>
                  <Label>SMS Provider Configuration</Label>
                  <p className="text-sm text-gray-600">Configure SMS gateway and messaging services</p>
                </div>
                <div>
                  <Label>Notification Settings</Label>
                  <p className="text-sm text-gray-600">Configure push notification and in-app messaging</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Compose Communication Dialog */}
      <Dialog open={isComposeOpen} onOpenChange={setIsComposeOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Compose Communication</DialogTitle>
            <DialogDescription>
              Send a message to users on the platform
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSendCommunication} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="compose_type">Type</Label>
                <Select 
                  value={composeForm.type} 
                  onValueChange={(value) => setComposeForm({...composeForm, type: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="sms">SMS</SelectItem>
                    <SelectItem value="notification">Notification</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="recipient_type">Recipient Type</Label>
                <Select 
                  value={composeForm.recipient_type} 
                  onValueChange={(value) => setComposeForm({...composeForm, recipient_type: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="client">Clients</SelectItem>
                    <SelectItem value="affiliate">Affiliates</SelectItem>
                    <SelectItem value="driver">Drivers</SelectItem>
                    <SelectItem value="all_users">All Users</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                value={composeForm.subject}
                onChange={(e) => setComposeForm({...composeForm, subject: e?.target?.value})}
                required
              />
            </div>

            <div>
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                value={composeForm.message}
                onChange={(e) => setComposeForm({...composeForm, message: e?.target?.value})}
                rows={6}
                required
              />
            </div>

            <div>
              <Label htmlFor="scheduled_at">Schedule (Optional)</Label>
              <Input
                id="scheduled_at"
                type="datetime-local"
                value={composeForm.scheduled_at}
                onChange={(e) => setComposeForm({...composeForm, scheduled_at: e?.target?.value})}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsComposeOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <Send className="w-4 h-4 mr-2" />
                Send Communication
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Create Template Dialog */}
      <Dialog open={isTemplateOpen} onOpenChange={setIsTemplateOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Template</DialogTitle>
            <DialogDescription>
              Create a reusable communication template
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleCreateTemplate} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="template_name">Template Name</Label>
                <Input
                  id="template_name"
                  value={templateForm.name}
                  onChange={(e) => setTemplateForm({...templateForm, name: e?.target?.value})}
                  required
                />
              </div>

              <div>
                <Label htmlFor="template_type">Type</Label>
                <Select 
                  value={templateForm.type} 
                  onValueChange={(value) => setTemplateForm({...templateForm, type: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="sms">SMS</SelectItem>
                    <SelectItem value="notification">Notification</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="template_subject">Subject</Label>
              <Input
                id="template_subject"
                value={templateForm.subject}
                onChange={(e) => setTemplateForm({...templateForm, subject: e?.target?.value})}
                required
              />
            </div>

            <div>
              <Label htmlFor="template_content">Content</Label>
              <Textarea
                id="template_content"
                value={templateForm.content}
                onChange={(e) => setTemplateForm({...templateForm, content: e?.target?.value})}
                rows={8}
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Use variables like {'{user_name}'}, {'{trip_id}'}, {'{amount}'} for dynamic content
              </p>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsTemplateOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Create Template
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}