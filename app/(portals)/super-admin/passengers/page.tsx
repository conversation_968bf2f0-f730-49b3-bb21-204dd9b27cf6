"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Avatar } from "@/app/components/ui/avatar"
import { Checkbox } from "@/app/components/ui/checkbox"
import { Input } from "@/app/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger, TabsContent } from "@/app/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog"
import { Plus, Search, Star, MoreVertical, Users, Mail, Car, Filter, UserPlus, AlertTriangle } from "lucide-react"
import Link from "next/link"
import { useGlobalFilters } from "@/app/contexts/GlobalFilterContext"

// Types
interface Passenger {
  id: string
  quote_id: string
  quote_reference: string
  passenger_number: number
  total_passengers: number
  pickup_location: string
  dropoff_location: string
  date: string
  time: string
  status: string
  special_requests?: string[]
  created_at: string
  customer?: {
    id: string
    email: string
    full_name: string
    phone?: string
  }
  organization?: {
    id: string
    name: string
    slug: string
  }
}

// Mock data
const mockPassengers: Passenger[] = [
  {
    id: "p1",
    name: "Lamar Latrelle",
    email: "<EMAIL>",
    jobTitle: "designer",
    status: "active",
    permission: "accepted",
    initials: "LL",
    isVIP: true,
    group: "VIP Guests",
    travelArrangers: [
      { name: "Matt Sasso", status: "accepted" }
    ],
    ridePreferences: {
      allowedCars: ["Economy", "Luxury", "SUV", "Mercedes Benz", "Van"],
      preferredCarType: "Luxury"
    }
  },
  {
    id: "p2",
    name: "Nikola Lukic",
    email: "<EMAIL>",
    jobTitle: "tester",
    status: "active",
    permission: "accepted",
    initials: "NL",
    isVIP: true,
    group: "VIP Guests"
  },
  {
    id: "p3",
    name: "Dan Berg",
    email: "<EMAIL>",
    status: "active",
    permission: "request",
    initials: "DB",
    group: "Staff"
  }
]

export default function PassengersPage() {
  const [passengers, setPassengers] = useState<Passenger[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPassengers, setSelectedPassengers] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedPassenger, setSelectedPassenger] = useState<Passenger | null>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)
  const [filterGroup, setFilterGroup] = useState<string>("all")
  const [filterStatus, setFilterStatus] = useState<string>("all")

  // Get selected organization from global filter
  const { selectedOrg } = useGlobalFilters()

  // Fetch passengers from the API
  useEffect(() => {
    const fetchPassengers = async () => {
      try {
        setLoading(true)
        setError(null)

        // Build query parameters for organization filtering
        const params = new URLSearchParams()

        // Add organization filter
        if (selectedOrg && selectedOrg !== "all") {
          params.set("org", selectedOrg.id)
        }

        const url = `/api/super-admin/passengers-simple${params.toString() ? `?${params.toString()}` : ''}`
        console.log('Fetching passengers with URL:', url)

        const response = await fetch(url, {
          credentials: 'include', // Required for authentication
        })
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        console.log('Passengers API Response:', data)

        if (!data || !data.passengers) {
          throw new Error('No passengers data found in response')
        }

        setPassengers(data.passengers)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while fetching passengers')
        console.error('Error fetching passengers:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchPassengers()
  }, [selectedOrg]) // Re-fetch when organization changes

  const filteredPassengers = passengers.filter(passenger => {
    const matchesSearch =
      passenger.customer?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      passenger.customer?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      passenger.quote_reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      passenger.pickup_location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      passenger.dropoff_location?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = filterStatus === "all" || passenger.status === filterStatus

    return matchesSearch && matchesStatus
  })

  // For now, treat all passengers as regular (no VIP distinction in current data)
  const vipPassengers: Passenger[] = []
  const regularPassengers = filteredPassengers

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading passengers...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 text-destructive mx-auto" />
          <p className="mt-4 text-destructive">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Passengers</h2>
        <div className="flex items-center gap-x-2">
          <Button variant="outline" size="sm">
            <Mail className="h-4 w-4 mr-2" />
            Invite Passenger
          </Button>
          <Link href="/customer/passengers/new">
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Passenger
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Passengers</p>
                <h3 className="text-2xl font-bold mt-2">{passengers.length}</h3>
              </div>
              <Users className="h-8 w-8 text-primary/20" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">VIP Passengers</p>
                <h3 className="text-2xl font-bold mt-2">{vipPassengers.length}</h3>
              </div>
              <Star className="h-8 w-8 text-yellow-500/20" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Groups</p>
                <h3 className="text-2xl font-bold mt-2">2</h3>
              </div>
              <Users className="h-8 w-8 text-primary/20" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Invites</p>
                <h3 className="text-2xl font-bold mt-2">3</h3>
              </div>
              <UserPlus className="h-8 w-8 text-primary/20" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>All Passengers</CardTitle>
            <div className="flex items-center gap-x-2">
              <Link href="/customer/passengers/groups">
                <Button variant="outline" size="sm">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Groups
                </Button>
              </Link>
              {selectedPassengers.length > 0 && (
                <Button variant="outline" size="sm">
                  Bulk Actions ({selectedPassengers.length})
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search passengers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e?.target?.value)}
                className="pl-8"
              />
            </div>
            <Select value={filterGroup} onValueChange={setFilterGroup}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by Group" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Groups</SelectItem>
                <SelectItem value="VIP Guests">VIP Guests</SelectItem>
                <SelectItem value="Staff">Staff</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* VIP Section */}
          {vipPassengers.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                VIP Passengers
              </h3>
              <div className="space-y-4">
                {vipPassengers.map((passenger) => (
                  <PassengerCard
                    key={passenger.id}
                    passenger={passenger}
                    isSelected={selectedPassengers.includes(passenger.id)}
                    onSelect={(checked) => {
                      setSelectedPassengers(prev =>
                        checked
                          ? [...prev, passenger.id]
                          : prev.filter(id => id !== passenger.id)
                      )
                    }}
                    onViewDetails={() => {
                      setSelectedPassenger(passenger)
                      setDetailsOpen(true)
                    }}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Regular Passengers */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Regular Passengers</h3>
            <div className="space-y-4">
              {regularPassengers.map((passenger) => (
                <PassengerCard
                  key={passenger.id}
                  passenger={passenger}
                  isSelected={selectedPassengers.includes(passenger.id)}
                  onSelect={(checked) => {
                    setSelectedPassengers(prev =>
                      checked
                        ? [...prev, passenger.id]
                        : prev.filter(id => id !== passenger.id)
                    )
                  }}
                  onViewDetails={() => {
                    setSelectedPassenger(passenger)
                    setDetailsOpen(true)
                  }}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Passenger Details Dialog */}
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Passenger Details</DialogTitle>
            <DialogDescription>
              View and manage passenger information
            </DialogDescription>
          </DialogHeader>
          {selectedPassenger && (
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-2">Passenger Information</h4>
                <div className="space-y-1">
                  <p className="text-sm">
                    <span className="text-muted-foreground">Name:</span> {selectedPassenger.customer?.full_name || 'Unknown'}
                  </p>
                  <p className="text-sm">
                    <span className="text-muted-foreground">Email:</span> {selectedPassenger.customer?.email || 'N/A'}
                  </p>
                  {selectedPassenger.customer?.phone && (
                    <p className="text-sm">
                      <span className="text-muted-foreground">Phone:</span> {selectedPassenger.customer.phone}
                    </p>
                  )}
                  <p className="text-sm">
                    <span className="text-muted-foreground">Passenger Number:</span> {selectedPassenger.passenger_number} of {selectedPassenger.total_passengers}
                  </p>
                  {selectedPassenger.organization && (
                    <p className="text-sm">
                      <span className="text-muted-foreground">Organization:</span> {selectedPassenger.organization.name}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Trip Details</h4>
                <div className="space-y-1">
                  <p className="text-sm">
                    <span className="text-muted-foreground">Quote Reference:</span> {selectedPassenger.quote_reference}
                  </p>
                  <p className="text-sm">
                    <span className="text-muted-foreground">Date & Time:</span> {selectedPassenger.date} at {selectedPassenger.time}
                  </p>
                  <p className="text-sm">
                    <span className="text-muted-foreground">Pickup:</span> {selectedPassenger.pickup_location}
                  </p>
                  <p className="text-sm">
                    <span className="text-muted-foreground">Dropoff:</span> {selectedPassenger.dropoff_location}
                  </p>
                  <p className="text-sm">
                    <span className="text-muted-foreground">Status:</span>
                    <Badge variant="secondary" className="ml-2">{selectedPassenger.status}</Badge>
                  </p>
                </div>
              </div>

              {selectedPassenger.special_requests && selectedPassenger?.special_requests?.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Special Requests</h4>
                  <div className="space-y-1">
                    {selectedPassenger?.special_requests?.map((request, index) => (
                      <p key={index} className="text-sm">{request}</p>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-x-2">
                <Button variant="outline" onClick={() => setDetailsOpen(false)}>
                  Close
                </Button>
                <Link href={`/super-admin/quotes/${selectedPassenger.quote_id}`}>
                  <Button>
                    View Quote
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

function PassengerCard({
  passenger,
  isSelected,
  onSelect,
  onViewDetails
}: {
  passenger: Passenger
  isSelected: boolean
  onSelect: (checked: boolean) => void
  onViewDetails: () => void
}) {
  const initials = passenger.customer?.full_name
    ?.split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase() || 'P'

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex items-center gap-x-4">
        <Checkbox
          checked={isSelected}
          onCheckedChange={onSelect}
        />
        <Avatar className="h-10 w-10">
          <div className="bg-primary/10 h-full w-full flex items-center justify-center text-sm font-medium">
            {initials}
          </div>
        </Avatar>
        <div>
          <div className="flex items-center gap-x-2">
            <span className="font-medium">
              {passenger.customer?.full_name || 'Unknown Passenger'}
            </span>
            <Badge variant="secondary" className="ml-2">
              Passenger {passenger.passenger_number} of {passenger.total_passengers}
            </Badge>
            {passenger.organization && (
              <Badge variant="outline" className="ml-2">
                {passenger.organization.name}
              </Badge>
            )}
          </div>
          <div className="text-sm text-muted-foreground">
            {passenger.customer?.email} • {passenger.quote_reference}
          </div>
          <div className="text-xs text-muted-foreground">
            {passenger.pickup_location} → {passenger.dropoff_location}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-x-4">
        <div className="text-right text-sm">
          <div className="font-medium">{passenger.date}</div>
          <div className="text-muted-foreground">{passenger.time}</div>
        </div>
        <Badge variant={passenger.status === "accepted" ? "default" : "secondary"}>
          {passenger.status}
        </Badge>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={onViewDetails}>
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Link href={`/super-admin/quotes/${passenger.quote_id}`} className="flex w-full">
                View Quote
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Contact Customer</DropdownMenuItem>
            <DropdownMenuItem>Create New Quote</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}