"use client";

import React, { ReactNode } from "react";
import { useRouter, usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { UserNav } from "@/app/components/features/navigation/user-nav";
import { MobileNav } from "@/app/components/features/navigation/mobile-nav";
import { useAuth } from "@/lib/auth/context";
import { UserRole, hasRole, isSuperAdmin, isTNCAdmin, isTenantAdmin } from "@/app/lib/auth/roles";
import { GlobalFilterProvider } from "@/app/contexts/GlobalFilterContext";
import { OrgSelector } from "@/app/components/features/super-admin/filters/OrgSelector";
import { GlobalDateRangePicker } from "@/app/components/features/super-admin/filters/GlobalDateRangePicker";
import { EnhancedNetworkSwitcher } from "@/app/components/features/tenant/EnhancedNetworkSwitcher";
import { Loader2 } from "lucide-react";
import {
  LayoutDashboard,
  Users,
  Building2,
  <PERSON>tings,
  Shield,
  Globe,
  CreditCard,
  BarChart3,
  Network,
  FileText,
  Briefcase,
  ClipboardList,
  CalendarDays,
  Plane,
  Map as MapIcon,
  Bell,
  UsersRound,
  Car,
  Code,
  ChevronDown,
} from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/app/components/ui/dropdown-menu";
import Link from "next/link";

// Super Admin Specific Navigation (Top Menu - Platform Management)
const superAdminNavigation = [
  {
    title: "Dashboard",
    href: "/super-admin/dashboard",
    icon: LayoutDashboard,
    roles: ['SUPER_ADMIN'], // Only Super Admin
  },
  {
    title: "Analytics",
    href: "/super-admin/analytics",
    icon: BarChart3,
    roles: ['SUPER_ADMIN', 'TNC_ADMIN'], // Super Admin and TNC Admin
  },
  {
    title: "Tenant Management",
    href: "/super-admin/orgs",
    icon: Building2,
    highlight: true,
    roles: ['SUPER_ADMIN', 'TNC_ADMIN'], // Super Admin and TNC Admin
    children: [
      {
        title: "Organizations",
        href: "/super-admin/orgs",
        roles: ['SUPER_ADMIN', 'TNC_ADMIN'],
      },
      {
        title: "Tenants",
        href: "/super-admin/tenants",
        roles: ['SUPER_ADMIN', 'TNC_ADMIN'],
      },
      {
        title: "Users",
        href: "/super-admin/users",
        roles: ['SUPER_ADMIN', 'TNC_ADMIN'],
      },
      {
        title: "Analytics Dashboard",
        href: "/super-admin/subscriptions/analytics",
        roles: ['SUPER_ADMIN', 'TNC_ADMIN'],
      },
      {
        title: "Reports & Exports",
        href: "/super-admin/subscriptions/reports",
        roles: ['SUPER_ADMIN', 'TNC_ADMIN'],
      },
    ],
  },
  {
    title: "Affiliate & Coverage",
    href: "/super-admin/affiliate-config",
    icon: Network,
    roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN'], // Available to tenant admins too
    children: [
      {
        title: "Affiliate Config",
        href: "/super-admin/affiliate-config",
        roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN'],
      },
      {
        title: "Coverage",
        href: "/super-admin/coverage",
        roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN'],
      },
    ],
  },
  {
    title: "Settings & Security",
    href: "/super-admin/settings",
    icon: Settings,
    roles: ['SUPER_ADMIN'], // Only Super Admin
    children: [
      {
        title: "Settings",
        href: "/super-admin/settings",
        roles: ['SUPER_ADMIN'],
      },
      {
        title: "Security",
        href: "/super-admin/security",
        roles: ['SUPER_ADMIN'],
      },
    ],
  },
  {
    title: "Documentation",
    href: "/super-admin/docs",
    icon: FileText,
    roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN', 'TENANT_MANAGER'], // Available to all admin roles
  },
  {
    title: "Enterprise",
    href: "/super-admin/enterprise/applications",
    icon: Briefcase,
    roles: ['SUPER_ADMIN'], // Only Super Admin
  },
  {
    title: "Embeddable Forms",
    href: "/super-admin/embeddable-forms",
    icon: Code,
    description: "Analytics Dashboard",
    roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN'], // TNC and Tenant admins can manage forms
  },
];

// Operations Related Navigation (Ops Menu - Day-to-day Operations)
const operationsNavigation = [
  {
    title: "OPS Snapshot",
    href: "/super-admin/tenant-dashboards",
    icon: LayoutDashboard,
    roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN', 'TENANT_MANAGER'], // All admin roles
  },
  {
    title: "Events",
    href: "/super-admin/events",
    icon: CalendarDays,
    roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN', 'TENANT_MANAGER'], // All admin roles
  },
  {
    title: "Quotes",
    href: "/super-admin/quotes",
    icon: FileText,
    roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN', 'TENANT_MANAGER'], // All admin roles
  },
  {
    title: "Trips",
    href: "/super-admin/trips",
    icon: Plane,
    roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN', 'TENANT_MANAGER'], // All admin roles
  },
  {
    title: "Passengers",
    href: "/super-admin/passengers",
    icon: UsersRound,
    roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN', 'TENANT_MANAGER'], // All admin roles
  },
  {
    title: "Affiliate Operations",
    href: "/super-admin/affiliates",
    icon: Network,
    roles: ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN'], // Exclude TENANT_MANAGER from affiliate ops
  },
];

// Helper function to filter navigation items based on user roles
const filterNavigationByRole = (navigation: any[], userRoles: UserRole[]) => {
  return navigation.filter(item => {
    // If no roles specified, show to everyone (backward compatibility)
    if (!item.roles) return true;
    
    // Check if user has any of the required roles
    const hasRequiredRole = item.roles.some((role: string) => 
      userRoles.includes(role as UserRole)
    );
    
    if (!hasRequiredRole) return false;
    
    // Filter children if they exist
    if (item.children) {
      item.children = item?.children?.filter((child: any) => {
        if (!child.roles) return true;
        return child.roles.some((role: string) => 
          userRoles.includes(role as UserRole)
        );
      });
    }
    
    return true;
  });
};

export default function SuperAdminLayout({
  children,
}: {
  children: ReactNode;
}) {
  const pathname = usePathname();
  const { user, loading, initialized } = useAuth();
  const router = useRouter();

  // Define paths where each global filter is relevant
  const orgFilterRelevantPaths = [
    "/super-admin/quotes",
    "/super-admin/events",
    "/super-admin/trips",
    "/super-admin/passengers",
    "/super-admin/affiliates", // Affiliate Operations
    "/super-admin/tenant-dashboards", // Operational Tenant Dashboards
  ];

  const dateRangeFilterRelevantPaths = [
    "/super-admin/quotes",
    "/super-admin/events",
    "/super-admin/trips",
    "/super-admin/passengers",
    "/super-admin/tenant-dashboards", // Operational Dashboards
    "/super-admin/dashboard", // Platform SA Dashboard
    "/super-admin/analytics", // Platform Analytics
    "/super-admin/orgs", // Organizations & Subscriptions
  ];

  // Filter navigation based on user roles
  const filteredSuperAdminNav = React.useMemo(() => {
    if (!user?.roles) return [];
    return filterNavigationByRole(superAdminNavigation, user.roles);
  }, [user?.roles]);

  const filteredOperationsNav = React.useMemo(() => {
    if (!user?.roles) return [];
    return filterNavigationByRole(operationsNavigation, user.roles);
  }, [user?.roles]);

  // Determine if user should see the top menu (Super Admin only)
  const showTopMenu = React.useMemo(() => {
    if (!user?.roles) return false;
    return isSuperAdmin(user.roles);
  }, [user?.roles]);

  // Determine user's access level for UI customization
  const userAccessLevel = React.useMemo(() => {
    if (!user?.roles) return 'none';
    if (isSuperAdmin(user.roles)) return 'super_admin';
    if (isTNCAdmin(user.roles)) return 'tnc_admin';
    if (isTenantAdmin(user.roles)) return 'tenant_admin';
    return 'limited';
  }, [user?.roles]);

  React.useEffect(() => {
    console.log("SuperAdminLayout - Auth state check:", {
      loading,
      initialized,
      userPresent: !!user,
      userEmail: user?.email,
      userRoles: user?.roles,
      userAccessLevel,
      showTopMenu,
      filteredSuperAdminNavCount: filteredSuperAdminNav.length,
      filteredOperationsNavCount: filteredOperationsNav.length,
    });

    // Don't redirect while loading or not initialized
    if (loading || !initialized) {
      console.log(
        "SuperAdminLayout - Still loading or not initialized, skipping redirect check"
      );
      return;
    }

    // If we have no user after initialization, redirect
    if (!user) {
      console.log(
        "SuperAdminLayout - No user after initialization, redirecting to login"
      );
      router.push("/login");
      return;
    }

    // Check for admin roles (SUPER_ADMIN, TNC_ADMIN, TENANT_ADMIN, TENANT_MANAGER)
    const hasAdminRole = user.roles?.some(role => 
      ['SUPER_ADMIN', 'TNC_ADMIN', 'TENANT_ADMIN', 'TENANT_MANAGER'].includes(role)
    );
    console.log("SuperAdminLayout - User role check:", {
      roles: user.roles,
      hasAdminRole,
      userAccessLevel,
      userId: user.id,
      email: user.email,
    });

    // TEMPORARY FIX: Commenting out auth check to allow Network Switcher testing
    // TODO: CRITICAL - Implement proper authentication context sync
    // 
    // ISSUE: AuthProvider.tsx uses SessionManager.validateAndRefreshSession() which is
    // different from the login API session, causing authentication context mismatch.
    // 
    // PROPER FIX NEEDED:
    // 1. Update AuthProvider to sync with login API session
    // 2. Modify SessionManager to use same session mechanism as login API
    // 3. Ensure useAuth() hook returns correct user roles from login
    // 4. Test that super-admin layout gets proper SUPER_ADMIN roles
    // 
    // CURRENT STATE: Login API returns correct roles but useAuth() doesn't see them
    // IMPACT: Network Switcher and other components get wrong authentication state
    // 
    // UNCOMMENT BELOW AFTER FIXING AUTHENTICATION CONTEXT:
    /*
    if (!hasAdminRole) {
      console.error(
        "SuperAdminLayout - User does not have admin role, redirecting to login"
      );
      router.push("/login");
    }
    */
  }, [user, loading, initialized, router]);

  if (loading || !initialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p>Loading super admin dashboard...</p>
        </div>
      </div>
    );
  }

  const isSuperAdminUser = user?.roles?.includes("SUPER_ADMIN" as UserRole);

  return (
    <GlobalFilterProvider>
      <>
        {/* Desktop Header (Two Rows) */}
        <div className="hidden md:flex flex-col">
          {/* Row 1: Title, Super Admin Links, UserNav & Notifications Icon */}
          <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container flex h-16 items-center">
              <div className="mr-6 flex items-center space-x-2 shrink-0">
                <Link
                  href="/super-admin/dashboard"
                  className="flex flex-col items-center"
                >
                  <div className="flex items-center space-x-2">
                    <Globe className="h-7 w-7 text-primary" />
                    <span className="text-xl font-bold">transflow</span>
                  </div>
                  {isSuperAdminUser && (
                    <span className="text-xs font-semibold text-muted-foreground mt-1">
                      Super Admin
                    </span>
                  )}
                </Link>
              </div>

              {/* Super Admin Specific Links (center area) - Only show if user has access */}
              {showTopMenu && (
                <nav className="flex items-center space-x-1 lg:space-x-2 mx-auto overflow-x-auto whitespace-nowrap">
                  {filteredSuperAdminNav.map((item) => {
                  if (item.children && item?.children?.length > 0) {
                    // Render dropdown for items with children
                    return (
                      <DropdownMenu key={item.title}>
                        <DropdownMenuTrigger asChild>
                          <button
                            className={cn(
                              "group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                              item.children.some(child => pathname === child.href) ? "bg-accent" : "transparent",
                              item.highlight && "text-primary font-semibold"
                            )}
                          >
                            <item.icon
                              className={cn(
                                "mr-2 h-4 w-4 shrink-0",
                                item.highlight && "text-primary"
                              )}
                            />
                            <span>{item.title}</span>
                            <ChevronDown className="ml-1 h-3 w-3" />
                          </button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start">
                          {item?.children?.map((child) => (
                            <DropdownMenuItem key={child.href} asChild>
                              <Link href={child.href} className="w-full">
                                {child.title}
                              </Link>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )
                  } else {
                    // Render regular link for items without children
                    return (
                      <Link
                        key={item.title}
                        href={item.href}
                        className={cn(
                          "group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                          pathname === item.href ? "bg-accent" : "transparent",
                          item.highlight && "text-primary font-semibold"
                        )}
                      >
                        <item.icon
                          className={cn(
                            "mr-2 h-4 w-4 shrink-0",
                            item.highlight && "text-primary"
                          )}
                        />
                        <span>{item.title}</span>
                      </Link>
                    )
                  }
                })}
                </nav>
              )}

              {/* Right side: Unified Tenant Switcher, Notifications Icon and UserNav */}
              <div className="ml-auto flex items-center space-x-2 pl-4 shrink-0">
                <EnhancedNetworkSwitcher />
                <Link
                  href="/super-admin/notifications"
                  className="p-2 rounded-full hover:bg-accent"
                >
                  <Bell className="h-5 w-5" />
                  <span className="sr-only">Notifications</span>
                </Link>
                <UserNav />
              </div>
            </div>
          </header>

          {/* Row 2: Global Filters & Tenant Data Related Links */}
          <nav className="sticky top-16 z-40 w-full border-b bg-background/90 backdrop-blur supports-[backdrop-filter]:bg-background/50 shadow-sm">
            <div className="container flex h-14 items-center justify-between px-4">
              {/* Global Filters - Left Aligned */}
              <div className="flex items-center space-x-3">
                {orgFilterRelevantPaths.includes(pathname) && <OrgSelector />}
                {dateRangeFilterRelevantPaths.includes(pathname) && (
                  <GlobalDateRangePicker />
                )}
              </div>
              {/* Tenant Data Nav Links - Right Aligned */}
              <div className="flex items-center space-x-1 lg:space-x-2 overflow-x-auto whitespace-nowrap">
                {filteredOperationsNav.map((item) => (
                  <Link
                    key={item.title}
                    href={item.href}
                    className={cn(
                      "group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                      pathname === item.href ? "bg-accent" : "transparent"
                    )}
                  >
                    <item.icon className="mr-2 h-4 w-4 shrink-0" />
                    <span>{item.title}</span>
                  </Link>
                ))}
              </div>
            </div>
          </nav>
        </div>

        {/* Mobile Header */}
        <div className="md:hidden">
          <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container flex h-16 items-center justify-between px-4">
              <Link
                href="/super-admin/dashboard"
                className="flex flex-col items-center"
              >
                <div className="flex items-center space-x-2">
                  <Globe className="h-6 w-6 text-primary" />
                  <span className="text-lg font-bold">transflow</span>
                </div>
                {isSuperAdminUser && (
                  <span className="text-xs font-semibold text-muted-foreground">
                    Super Admin
                  </span>
                )}
              </Link>
              <div className="flex items-center space-x-1">
                <EnhancedNetworkSwitcher className="hidden sm:flex" />
                <Link
                  href="/super-admin/notifications"
                  className="p-2 rounded-full hover:bg-accent"
                >
                  <Bell className="h-5 w-5" />
                  <span className="sr-only">Notifications</span>
                </Link>
                <UserNav />
                <MobileNav
                  superAdminItems={filteredSuperAdminNav}
                  tenantDataItems={filteredOperationsNav}
                />
              </div>
            </div>
          </header>
        </div>

        <main className="flex-1 space-y-4 p-8 pt-6">{children}</main>
      </>
    </GlobalFilterProvider>
  );
}
