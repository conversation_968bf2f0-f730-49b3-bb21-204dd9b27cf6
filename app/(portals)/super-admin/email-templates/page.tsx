"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { Textarea } from "@/app/components/ui/textarea"
import { Badge } from "@/app/components/ui/badge"
import { useToast } from "@/app/components/ui/use-toast"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog"
import { 
  Mail, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Copy, 
  Save, 
  RefreshCw,
  Code,
  Palette,
  Settings,
  Send,
  FileText,
  Users,
  Calendar,
  DollarSign
} from "lucide-react"
import { format } from 'date-fns'

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  type: string;
  category: string;
  variables: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const templateCategories = [
  { value: 'quote', label: 'Quote Management', icon: FileText },
  { value: 'booking', label: 'Booking Confirmations', icon: Calendar },
  { value: 'payment', label: 'Payment & Billing', icon: DollarSign },
  { value: 'user', label: 'User Management', icon: Users },
  { value: 'system', label: 'System Notifications', icon: Settings },
];

const templateTypes = [
  'quote_created',
  'quote_accepted',
  'quote_rejected',
  'booking_confirmed',
  'payment_received',
  'payment_failed',
  'user_welcome',
  'password_reset',
  'affiliate_approved',
  'system_maintenance'
];

const availableVariables = {
  user: ['{{user.first_name}}', '{{user.last_name}}', '{{user.email}}'],
  quote: ['{{quote.reference_number}}', '{{quote.pickup_location}}', '{{quote.dropoff_location}}', '{{quote.pickup_date}}', '{{quote.total_amount}}'],
  company: ['{{company.name}}', '{{company.email}}', '{{company.phone}}'],
  system: ['{{system.app_name}}', '{{system.support_email}}', '{{system.current_date}}']
};

export default function EmailTemplatesPage() {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [previewData, setPreviewData] = useState<any>({});

  // Form state for editing templates
  const [editForm, setEditForm] = useState({
    name: "",
    subject: "",
    content: "",
    type: "",
    category: "",
    is_active: true
  });

  useEffect(() => {
    fetchTemplates();
  }, [categoryFilter]);

  const fetchTemplates = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams();
      if (categoryFilter !== 'all') params.append('category', categoryFilter);

      const response = await fetch(`/api/admin/email-templates?${params}`);
      const data = await response.json();

      if (data.success) {
        setTemplates(data.data.templates);
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch email templates. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveTemplate = async () => {
    try {
      const method = selectedTemplate ? 'PUT' : 'POST';
      const url = selectedTemplate 
        ? `/api/admin/email-templates/${selectedTemplate.id}`
        : '/api/admin/email-templates';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editForm)
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: `Template ${selectedTemplate ? 'updated' : 'created'} successfully.`,
        });
        setIsEditDialogOpen(false);
        fetchTemplates();
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save template. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const duplicateTemplate = async (template: EmailTemplate) => {
    setEditForm({
      name: `${template.name} (Copy)`,
      subject: template.subject,
      content: template.content,
      type: template.type,
      category: template.category,
      is_active: false
    });
    setSelectedTemplate(null);
    setIsEditDialogOpen(true);
  };

  const previewTemplate = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    // Generate sample data for preview
    setPreviewData({
      user: {
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>'
      },
      quote: {
        reference_number: 'Q-2024-001',
        pickup_location: 'Logan Airport',
        dropoff_location: 'Boston Convention Center',
        pickup_date: '2024-01-15',
        total_amount: '$150.00'
      },
      company: {
        name: 'Elite Transport Co',
        email: '<EMAIL>',
        phone: '******-0101'
      },
      system: {
        app_name: 'TransFlow',
        support_email: '<EMAIL>',
        current_date: format(new Date(), 'MMMM d, yyyy')
      }
    });
    setIsPreviewDialogOpen(true);
  };

  const renderPreviewContent = (content: string) => {
    let rendered = content;
    
    // Replace variables with sample data
    Object.entries(previewData).forEach(([category, data]) => {
      Object.entries(data as any).forEach(([key, value]) => {
        const variable = `{{${category}.${key}}}`;
        rendered = rendered.replace(new RegExp(variable, 'g'), value as string);
      });
    });
    
    return rendered;
  };

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById('template-content') as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const before = text.substring(0, start);
      const after = text.substring(end, text.length);
      
      setEditForm(prev => ({
        ...prev,
        content: before + variable + after
      }));
      
      // Set cursor position after inserted variable
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variable.length, start + variable.length);
      }, 0);
    }
  };

  const filteredTemplates = templates.filter(template =>
    template?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template?.subject?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template?.type?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Email Templates</h2>
          <p className="text-muted-foreground">Customize email templates for automated communications</p>
        </div>
        
        <Button onClick={() => {
          setEditForm({
            name: "",
            subject: "",
            content: "",
            type: "",
            category: "",
            is_active: true
          });
          setSelectedTemplate(null);
          setIsEditDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          New Template
        </Button>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e?.target?.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {templateCategories.map(category => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Button variant="outline" size="icon" onClick={fetchTemplates}>
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>

      {/* Template Categories */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {templateCategories.map((category) => {
          const Icon = category.icon;
          const count = templates.filter(t => t.category === category.value).length;
          
          return (
            <Card key={category.value} className="cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => setCategoryFilter(category.value)}>
              <CardContent className="p-4 text-center">
                <Icon className="h-8 w-8 mx-auto mb-2 text-primary" />
                <h3 className="font-medium">{category.label}</h3>
                <p className="text-sm text-muted-foreground">{count} templates</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Templates List */}
      <Card>
        <CardHeader>
          <CardTitle>Email Templates</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
            </div>
          ) : filteredTemplates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No email templates found.
            </div>
          ) : (
            <div className="space-y-4">
              {filteredTemplates.map((template) => (
                <div key={template.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium">{template.name}</h3>
                        <Badge variant={template.is_active ? 'default' : 'secondary'}>
                          {template.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        <Badge variant="outline">{template.category}</Badge>
                      </div>
                      
                      <div className="text-sm text-muted-foreground mb-2">
                        <strong>Subject:</strong> {template.subject}
                      </div>
                      
                      <div className="text-sm text-muted-foreground mb-2">
                        <strong>Type:</strong> {template.type.replace('_', ' ')}
                      </div>
                      
                      <div className="text-sm text-muted-foreground">
                        Last updated: {format(new Date(template.updated_at), 'MMM d, yyyy HH:mm')}
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" onClick={() => previewTemplate(template)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => {
                        setEditForm({
                          name: template.name,
                          subject: template.subject,
                          content: template.content,
                          type: template.type,
                          category: template.category,
                          is_active: template.is_active
                        });
                        setSelectedTemplate(template);
                        setIsEditDialogOpen(true);
                      }}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => duplicateTemplate(template)}>
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Template Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedTemplate ? 'Edit Template' : 'Create New Template'}
            </DialogTitle>
          </DialogHeader>
          
          <Tabs defaultValue="editor" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="editor">Editor</TabsTrigger>
              <TabsTrigger value="variables">Variables</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="editor" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Template Name</Label>
                  <Input
                    id="name"
                    value={editForm.name}
                    onChange={(e) => setEditForm(prev => ({ ...prev, name: e?.target?.value }))}
                    placeholder="Enter template name"
                  />
                </div>
                
                <div>
                  <Label htmlFor="subject">Email Subject</Label>
                  <Input
                    id="subject"
                    value={editForm.subject}
                    onChange={(e) => setEditForm(prev => ({ ...prev, subject: e?.target?.value }))}
                    placeholder="Enter email subject"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="template-content">Email Content</Label>
                <Textarea
                  id="template-content"
                  value={editForm.content}
                  onChange={(e) => setEditForm(prev => ({ ...prev, content: e?.target?.value }))}
                  placeholder="Enter email content with variables like {{user.first_name}}"
                  rows={15}
                  className="font-mono"
                />
              </div>
            </TabsContent>
            
            <TabsContent value="variables" className="space-y-4">
              <div className="text-sm text-muted-foreground mb-4">
                Click on any variable to insert it into your template content.
              </div>
              
              {Object.entries(availableVariables).map(([category, variables]) => (
                <div key={category}>
                  <h4 className="font-medium mb-2 capitalize">{category} Variables</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {variables.map((variable) => (
                      <Button
                        key={variable}
                        variant="outline"
                        size="sm"
                        onClick={() => insertVariable(variable)}
                        className="justify-start font-mono text-xs"
                      >
                        <Code className="h-3 w-3 mr-1" />
                        {variable}
                      </Button>
                    ))}
                  </div>
                </div>
              ))}
            </TabsContent>
            
            <TabsContent value="settings" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="type">Template Type</Label>
                  <Select value={editForm.type} onValueChange={(value) => setEditForm(prev => ({ ...prev, type: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {templateTypes.map(type => (
                        <SelectItem key={type} value={type}>
                          {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={editForm.category} onValueChange={(value) => setEditForm(prev => ({ ...prev, category: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {templateCategories.map(category => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={editForm.is_active}
                  onChange={(e) => setEditForm(prev => ({ ...prev, is_active: e?.target?.checked }))}
                />
                <Label htmlFor="is_active">Template is active</Label>
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveTemplate}>
              <Save className="h-4 w-4 mr-2" />
              {selectedTemplate ? 'Update' : 'Create'} Template
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Preview Template Dialog */}
      <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Template Preview</DialogTitle>
          </DialogHeader>
          
          {selectedTemplate && (
            <div className="space-y-4">
              <div className="border rounded-lg p-4 bg-muted/50">
                <div className="text-sm text-muted-foreground mb-2">Subject:</div>
                <div className="font-medium">{renderPreviewContent(selectedTemplate.subject)}</div>
              </div>
              
              <div className="border rounded-lg p-4">
                <div className="text-sm text-muted-foreground mb-2">Content:</div>
                <div className="whitespace-pre-wrap">{renderPreviewContent(selectedTemplate.content)}</div>
              </div>
              
              <div className="text-xs text-muted-foreground">
                * This preview uses sample data. Actual emails will use real data from your system.
              </div>
            </div>
          )}
          
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setIsPreviewDialogOpen(false)}>
              Close
            </Button>
            <Button>
              <Send className="h-4 w-4 mr-2" />
              Send Test Email
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
