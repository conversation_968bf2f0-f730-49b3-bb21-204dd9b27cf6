/**
 * Real-time Quote Testing Page
 * Demonstrates WebSocket functionality for quote updates
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import RealtimeQuoteStatus from '@/components/quotes/RealtimeQuoteStatus'
import { useSupabase } from '@/hooks/useSupabase'
import { toast } from 'sonner'
import { 
  Play, 
  Square, 
  RefreshCw, 
  Send, 
  DollarSign,
  Clock,
  Users
} from 'lucide-react'

export default function RealtimeTestPage() {
  const [selectedQuoteId, setSelectedQuoteId] = useState<string>('')
  const [quotes, setQuotes] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [testOfferAmount, setTestOfferAmount] = useState<string>('150.00')
  const [testOfferNotes, setTestOfferNotes] = useState<string>('Test offer from WebSocket demo')
  const { supabase } = useSupabase()

  // Load available quotes
  useEffect(() => {
    loadQuotes()
  }, [])

  const loadQuotes = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('quotes')
        .select(`
          id,
          reference_number,
          status,
          pickup_location,
          dropoff_location,
          service_type,
          created_at,
          profiles!quotes_customer_id_fkey (
            full_name,
            email
          )
        `)
        .order('created_at', { ascending: false })
        .limit(20)

      if (error) throw error

      setQuotes(data || [])
      if (data && data.length > 0 && !selectedQuoteId) {
        setSelectedQuoteId(data[0].id)
      }
    } catch (error) {
      console.error('Error loading quotes:', error)
      toast.error('Failed to load quotes')
    } finally {
      setLoading(false)
    }
  }

  const simulateStatusUpdate = async (newStatus: string) => {
    if (!selectedQuoteId) return

    try {
      const response = await fetch(`/api/quotes/${selectedQuoteId}/${getActionFromStatus(newStatus)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reason: 'Simulated status update from WebSocket test page'
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update quote status')
      }

      toast.success(`Quote status updated to ${newStatus}`)
    } catch (error) {
      console.error('Error updating quote status:', error)
      toast.error('Failed to update quote status')
    }
  }

  const simulateAffiliateOffer = async () => {
    if (!selectedQuoteId || !testOfferAmount) return

    try {
      // First, get a test affiliate company
      const { data: affiliateCompanies } = await supabase
        .from('affiliate_companies')
        .select('id')
        .limit(1)

      if (!affiliateCompanies || affiliateCompanies.length === 0) {
        toast.error('No affiliate companies found for testing')
        return
      }

      const response = await fetch(`/api/affiliate/quotes/${selectedQuoteId}/offers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          rateAmount: parseFloat(testOfferAmount),
          currency: 'USD',
          notes: testOfferNotes,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create affiliate offer')
      }

      toast.success('Affiliate offer created successfully')
    } catch (error) {
      console.error('Error creating affiliate offer:', error)
      toast.error('Failed to create affiliate offer')
    }
  }

  const getActionFromStatus = (status: string): string => {
    const statusActionMap: { [key: string]: string } = {
      'accepted': 'accept',
      'rejected': 'reject',
      'cancelled': 'cancel',
      'expired': 'expire'
    }
    return statusActionMap[status] || 'change'
  }

  const selectedQuote = quotes.find(q => q.id === selectedQuoteId)

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Real-time Quote Testing</h1>
          <p className="text-gray-600 mt-2">
            Test WebSocket functionality for live quote updates and affiliate responses
          </p>
        </div>
        <Button onClick={loadQuotes} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh Quotes
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quote Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Select Quote
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Available Quotes</Label>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {quotes.map((quote) => (
                  <div
                    key={quote.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedQuoteId === quote.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedQuoteId(quote.id)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-sm">
                        {quote.reference_number}
                      </span>
                      <Badge variant="outline">
                        {quote.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">
                      {quote.pickup_location} → {quote.dropoff_location}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {quote.profiles?.full_name || 'Unknown Customer'}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {selectedQuote && (
              <div className="pt-4 border-t">
                <h4 className="font-medium mb-2">Selected Quote</h4>
                <div className="text-sm space-y-1">
                  <p><strong>ID:</strong> {selectedQuote.id}</p>
                  <p><strong>Status:</strong> {selectedQuote.status}</p>
                  <p><strong>Service:</strong> {selectedQuote.service_type}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Test Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium mb-3 block">Status Updates</Label>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => simulateStatusUpdate('sent_to_affiliates')}
                  disabled={!selectedQuoteId}
                >
                  <Users className="h-4 w-4 mr-1" />
                  Send to Affiliates
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => simulateStatusUpdate('offers_received')}
                  disabled={!selectedQuoteId}
                >
                  <DollarSign className="h-4 w-4 mr-1" />
                  Offers Received
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => simulateStatusUpdate('accepted')}
                  disabled={!selectedQuoteId}
                >
                  Accept
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => simulateStatusUpdate('rejected')}
                  disabled={!selectedQuoteId}
                >
                  Reject
                </Button>
              </div>
            </div>

            <Separator />

            <div>
              <Label className="text-sm font-medium mb-3 block">Simulate Affiliate Offer</Label>
              <div className="space-y-3">
                <div>
                  <Label htmlFor="offer-amount" className="text-xs">Offer Amount ($)</Label>
                  <Input
                    id="offer-amount"
                    type="number"
                    step="0.01"
                    value={testOfferAmount}
                    onChange={(e) => setTestOfferAmount(e?.target?.value)}
                    placeholder="150.00"
                  />
                </div>
                <div>
                  <Label htmlFor="offer-notes" className="text-xs">Notes</Label>
                  <Textarea
                    id="offer-notes"
                    value={testOfferNotes}
                    onChange={(e) => setTestOfferNotes(e?.target?.value)}
                    placeholder="Test offer notes..."
                    rows={2}
                  />
                </div>
                <Button
                  size="sm"
                  onClick={simulateAffiliateOffer}
                  disabled={!selectedQuoteId || !testOfferAmount}
                  className="w-full"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Submit Test Offer
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Real-time Status Display */}
        <div className="lg:col-span-1">
          {selectedQuoteId ? (
            <RealtimeQuoteStatus
              quoteId={selectedQuoteId}
              initialStatus={selectedQuote?.status}
              showAffiliateResponses={true}
              showStatusHistory={true}
              allowStatusUpdates={true}
              userRole="SUPER_ADMIN"
            />
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Square className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">Select a quote to see real-time updates</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
