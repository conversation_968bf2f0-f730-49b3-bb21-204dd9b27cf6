"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Button } from "@/app/components/ui/button"
import { Progress } from "@/app/components/ui/progress"
import { 
  Clock, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle, 
  CheckCircle, 
  Users, 
  DollarSign,
  Target,
  Zap,
  Award
} from "lucide-react"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"

interface AffiliatePerformance {
  id: string
  name: string
  responseTime: {
    average: number
    trend: 'up' | 'down' | 'stable'
    percentChange: number
  }
  conversionRate: {
    current: number
    previous: number
    trend: 'up' | 'down' | 'stable'
  }
  totalQuotes: number
  acceptedQuotes: number
  revenue: number
  tier: 'Elite' | 'Premium' | 'Standard'
  slaCompliance: number
  customerRating: number
  lastActive: string
}

interface QuoteFlowMetrics {
  averageTimeToFirstResponse: number
  averageTimeToAcceptance: number
  bottlenecks: Array<{
    stage: string
    averageTime: number
    impact: 'high' | 'medium' | 'low'
  }>
  slaBreaches: number
  escalations: number
}

interface QuotePerformanceMetricsProps {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedOrg?: string
}

export function QuotePerformanceMetrics({ dateRange, selectedOrg }: QuotePerformanceMetricsProps) {
  const [affiliatePerformance, setAffiliatePerformance] = useState<AffiliatePerformance[]>([])
  const [quoteFlowMetrics, setQuoteFlowMetrics] = useState<QuoteFlowMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPerformanceMetrics = async () => {
      try {
        setLoading(true)
        
        // Build query parameters
        const params = new URLSearchParams()
        if (selectedOrg && selectedOrg !== 'all') {
          params.append('orgId', selectedOrg)
        }
        if (dateRange?.from) {
          params.append('from', dateRange.from.toISOString())
        }
        if (dateRange?.to) {
          params.append('to', dateRange.to.toISOString())
        }

        const [affiliateResponse, flowResponse] = await Promise.all([
          fetch(`/api/super-admin/analytics/affiliate-performance?${params}`),
          fetch(`/api/super-admin/analytics/quote-flow?${params}`)
        ])

        if (!affiliateResponse.ok || !flowResponse.ok) {
          throw new Error('Failed to fetch performance metrics')
        }

        const [affiliateData, flowData] = await Promise.all([
          affiliateResponse.json(),
          flowResponse.json()
        ])

        setAffiliatePerformance(affiliateData.affiliates || [])
        setQuoteFlowMetrics(flowData)
      } catch (err) {
        console.error('Error fetching performance metrics:', err)
        setError(err instanceof Error ? err.message : 'Failed to load metrics')
        
        // Set mock data for development
        setAffiliatePerformance([
          {
            id: '1',
            name: 'Elite Transport Co',
            responseTime: { average: 1.2, trend: 'down', percentChange: -15 },
            conversionRate: { current: 85, previous: 82, trend: 'up' },
            totalQuotes: 34,
            acceptedQuotes: 29,
            revenue: 24650,
            tier: 'Elite',
            slaCompliance: 98,
            customerRating: 4.9,
            lastActive: '2024-01-15T10:30:00Z'
          },
          {
            id: '2',
            name: 'Premium Rides LLC',
            responseTime: { average: 1.8, trend: 'stable', percentChange: 2 },
            conversionRate: { current: 78, previous: 80, trend: 'down' },
            totalQuotes: 28,
            acceptedQuotes: 22,
            revenue: 18900,
            tier: 'Premium',
            slaCompliance: 94,
            customerRating: 4.7,
            lastActive: '2024-01-15T09:45:00Z'
          },
          {
            id: '3',
            name: 'City Executive Cars',
            responseTime: { average: 2.1, trend: 'up', percentChange: 8 },
            conversionRate: { current: 72, previous: 75, trend: 'down' },
            totalQuotes: 25,
            acceptedQuotes: 18,
            revenue: 15400,
            tier: 'Standard',
            slaCompliance: 89,
            customerRating: 4.5,
            lastActive: '2024-01-15T08:20:00Z'
          }
        ])

        setQuoteFlowMetrics({
          averageTimeToFirstResponse: 2.4,
          averageTimeToAcceptance: 4.8,
          bottlenecks: [
            { stage: 'Affiliate Selection', averageTime: 0.8, impact: 'medium' },
            { stage: 'Rate Calculation', averageTime: 1.2, impact: 'high' },
            { stage: 'Customer Decision', averageTime: 2.4, impact: 'low' }
          ],
          slaBreaches: 3,
          escalations: 1
        })
      } finally {
        setLoading(false)
      }
    }

    fetchPerformanceMetrics()
  }, [dateRange, selectedOrg])

  const getTierBadgeVariant = (tier: string) => {
    switch (tier) {
      case 'Elite': return 'default'
      case 'Premium': return 'secondary'
      case 'Standard': return 'outline'
      default: return 'outline'
    }
  }

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-3 w-3 text-green-500" />
      case 'down': return <TrendingDown className="h-3 w-3 text-red-500" />
      case 'stable': return <div className="h-3 w-3 rounded-full bg-gray-400" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {[...Array(2)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[...Array(3)].map((_, j) => (
                    <div key={j} className="h-16 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>{error}</p>
            <Button variant="outline" className="mt-2" onClick={() => window.location.reload()}>
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="affiliates" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="affiliates">Affiliate Performance</TabsTrigger>
          <TabsTrigger value="workflow">Quote Flow Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="affiliates" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {affiliatePerformance.map((affiliate) => (
              <Card key={affiliate.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{affiliate.name}</CardTitle>
                    <Badge variant={getTierBadgeVariant(affiliate.tier)}>
                      {affiliate.tier}
                    </Badge>
                  </div>
                  <CardDescription>
                    Last active: {new Date(affiliate.lastActive).toLocaleDateString()}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Response Time */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Response Time</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="font-medium">{affiliate.responseTime.average}h</span>
                      {getTrendIcon(affiliate.responseTime.trend)}
                    </div>
                  </div>

                  {/* Conversion Rate */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">Conversion Rate</span>
                      </div>
                      <span className="font-medium">{affiliate.conversionRate.current}%</span>
                    </div>
                    <Progress value={affiliate.conversionRate.current} className="h-2" />
                  </div>

                  {/* Quote Stats */}
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold">{affiliate.totalQuotes}</div>
                      <p className="text-xs text-muted-foreground">Total Quotes</p>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">{affiliate.acceptedQuotes}</div>
                      <p className="text-xs text-muted-foreground">Accepted</p>
                    </div>
                  </div>

                  {/* Revenue */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Revenue</span>
                    </div>
                    <span className="font-medium">${affiliate?.revenue?.toLocaleString()}</span>
                  </div>

                  {/* Performance Indicators */}
                  <div className="grid grid-cols-2 gap-2 text-center">
                    <div>
                      <div className="text-sm font-medium">{affiliate.slaCompliance}%</div>
                      <p className="text-xs text-muted-foreground">SLA</p>
                    </div>
                    <div>
                      <div className="text-sm font-medium">{affiliate.customerRating}/5</div>
                      <p className="text-xs text-muted-foreground">Rating</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="workflow" className="space-y-4">
          {quoteFlowMetrics && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Flow Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle>Quote Flow Metrics</CardTitle>
                  <CardDescription>Average processing times across the workflow</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Time to First Response</span>
                    <Badge variant="outline">{quoteFlowMetrics.averageTimeToFirstResponse}h</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Time to Acceptance</span>
                    <Badge variant="outline">{quoteFlowMetrics.averageTimeToAcceptance}h</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">SLA Breaches</span>
                    <Badge variant={quoteFlowMetrics.slaBreaches > 5 ? "destructive" : "secondary"}>
                      {quoteFlowMetrics.slaBreaches}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Escalations</span>
                    <Badge variant={quoteFlowMetrics.escalations > 0 ? "destructive" : "default"}>
                      {quoteFlowMetrics.escalations}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Bottlenecks */}
              <Card>
                <CardHeader>
                  <CardTitle>Process Bottlenecks</CardTitle>
                  <CardDescription>Stages causing delays in quote processing</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {quoteFlowMetrics?.bottlenecks?.map((bottleneck, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{bottleneck.stage}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm">{bottleneck.averageTime}h</span>
                          <Badge 
                            variant={
                              bottleneck.impact === 'high' ? 'destructive' :
                              bottleneck.impact === 'medium' ? 'default' : 'secondary'
                            }
                            className="text-xs"
                          >
                            {bottleneck.impact}
                          </Badge>
                        </div>
                      </div>
                      <Progress 
                        value={(bottleneck.averageTime / 5) * 100} 
                        className="h-2"
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
