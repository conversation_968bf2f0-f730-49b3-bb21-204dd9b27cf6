"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { But<PERSON> } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { Checkbox } from "@/app/components/ui/checkbox"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/app/components/ui/select"
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/app/components/ui/dialog"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { Textarea } from "@/app/components/ui/textarea"
import { 
  Send, 
  Archive, 
  UserCheck, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Filter,
  Download,
  Upload
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface QuoteRowData {
  id: string
  reference_number: string
  customer?: {
    full_name: string
    email: string
  }
  status: string
  service_type: string
  vehicle_type: string
  pickup_location: string
  dropoff_location: string
  date: string
  total_amount?: number
  created_at: string
}

interface QuoteBulkOperationsProps {
  quotes: QuoteRowData[]
  selectedQuotes: string[]
  onSelectionChange: (quoteIds: string[]) => void
  onQuotesUpdate: () => void
}

export function QuoteBulkOperations({ 
  quotes, 
  selectedQuotes, 
  onSelectionChange, 
  onQuotesUpdate 
}: QuoteBulkOperationsProps) {
  const [loading, setLoading] = useState(false)
  const [showBulkAssignDialog, setShowBulkAssignDialog] = useState(false)
  const [showBulkSendDialog, setShowBulkSendDialog] = useState(false)
  const [selectedAffiliate, setSelectedAffiliate] = useState("")
  const [selectedAffiliates, setSelectedAffiliates] = useState<string[]>([])
  const [bulkNotes, setBulkNotes] = useState("")
  const [responseDeadline, setResponseDeadline] = useState("24h")

  const selectedQuoteObjects = quotes.filter(q => selectedQuotes.includes(q.id))
  const canBulkAssign = selectedQuotes.length > 0 && selectedQuoteObjects.every(q => 
    ['pending', 'rate_requested', 'sent_to_affiliates'].includes(q.status)
  )
  const canBulkSend = selectedQuotes.length > 0 && selectedQuoteObjects.every(q => 
    ['pending', 'pending_quote'].includes(q.status)
  )

  const handleSelectAll = () => {
    if (selectedQuotes.length === quotes.length) {
      onSelectionChange([])
    } else {
      onSelectionChange(quotes.map(q => q.id))
    }
  }

  const handleBulkAssign = async () => {
    if (!selectedAffiliate || selectedQuotes.length === 0) return

    setLoading(true)
    try {
      const response = await fetch('/api/super-admin/quotes/bulk-assign', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          quoteIds: selectedQuotes,
          affiliateId: selectedAffiliate,
          notes: bulkNotes
        })
      })

      if (!response.ok) {
        throw new Error('Failed to assign quotes')
      }

      const result = await response.json()
      
      toast({
        title: "Quotes Assigned",
        description: `Successfully assigned ${result.assignedCount} quotes to affiliate`,
      })

      setShowBulkAssignDialog(false)
      setSelectedAffiliate("")
      setBulkNotes("")
      onSelectionChange([])
      onQuotesUpdate()
    } catch (error) {
      console.error('Error assigning quotes:', error)
      toast({
        title: "Assignment Failed",
        description: "Failed to assign quotes. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBulkSend = async () => {
    if (selectedAffiliates.length === 0 || selectedQuotes.length === 0) return

    setLoading(true)
    try {
      const response = await fetch('/api/super-admin/quotes/bulk-send', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          quoteIds: selectedQuotes,
          affiliateIds: selectedAffiliates,
          deadline: responseDeadline,
          notes: bulkNotes
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send quotes')
      }

      const result = await response.json()
      
      toast({
        title: "Quotes Sent",
        description: `Successfully sent ${result.sentCount} quotes to ${selectedAffiliates.length} affiliates`,
      })

      setShowBulkSendDialog(false)
      setSelectedAffiliates([])
      setBulkNotes("")
      onSelectionChange([])
      onQuotesUpdate()
    } catch (error) {
      console.error('Error sending quotes:', error)
      toast({
        title: "Send Failed",
        description: "Failed to send quotes. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBulkArchive = async () => {
    if (selectedQuotes.length === 0) return

    setLoading(true)
    try {
      const response = await fetch('/api/super-admin/quotes/bulk-archive', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quoteIds: selectedQuotes })
      })

      if (!response.ok) {
        throw new Error('Failed to archive quotes')
      }

      const result = await response.json()
      
      toast({
        title: "Quotes Archived",
        description: `Successfully archived ${result.archivedCount} quotes`,
      })

      onSelectionChange([])
      onQuotesUpdate()
    } catch (error) {
      console.error('Error archiving quotes:', error)
      toast({
        title: "Archive Failed",
        description: "Failed to archive quotes. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleExportSelected = () => {
    if (selectedQuotes.length === 0) return

    const csvData = selectedQuoteObjects.map(quote => ({
      'Reference': quote.reference_number,
      'Customer': quote.customer?.full_name || 'N/A',
      'Email': quote.customer?.email || 'N/A',
      'Status': quote.status,
      'Service Type': quote.service_type,
      'Vehicle Type': quote.vehicle_type,
      'Pickup': quote.pickup_location,
      'Dropoff': quote.dropoff_location,
      'Date': quote.date,
      'Amount': quote.total_amount ? `$${quote.total_amount}` : 'N/A',
      'Created': new Date(quote.created_at).toLocaleDateString()
    }))

    const csv = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `quotes-export-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)

    toast({
      title: "Export Complete",
      description: `Exported ${selectedQuotes.length} quotes to CSV`,
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Bulk Operations</span>
          <Badge variant="outline">{selectedQuotes.length} selected</Badge>
        </CardTitle>
        <CardDescription>
          Perform actions on multiple quotes simultaneously
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Selection Controls */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="select-all"
              checked={selectedQuotes.length === quotes.length && quotes.length > 0}
              onCheckedChange={handleSelectAll}
            />
            <Label htmlFor="select-all" className="text-sm">
              Select All ({quotes.length})
            </Label>
          </div>
          
          {selectedQuotes.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSelectionChange([])}
            >
              Clear Selection
            </Button>
          )}
        </div>

        {/* Bulk Actions */}
        {selectedQuotes.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {/* Bulk Send to Affiliates */}
            <Dialog open={showBulkSendDialog} onOpenChange={setShowBulkSendDialog}>
              <DialogTrigger asChild>
                <Button 
                  variant="default" 
                  size="sm" 
                  disabled={!canBulkSend || loading}
                  className="flex items-center space-x-1"
                >
                  <Send className="h-4 w-4" />
                  <span>Send to Affiliates</span>
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Send Quotes to Affiliates</DialogTitle>
                  <DialogDescription>
                    Send {selectedQuotes.length} selected quotes to multiple affiliates
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label>Select Affiliates</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose affiliates..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Available Affiliates</SelectItem>
                        <SelectItem value="elite">Elite Tier Only</SelectItem>
                        <SelectItem value="premium">Premium+ Tiers</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Response Deadline</Label>
                    <Select value={responseDeadline} onValueChange={setResponseDeadline}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2h">2 hours</SelectItem>
                        <SelectItem value="6h">6 hours</SelectItem>
                        <SelectItem value="12h">12 hours</SelectItem>
                        <SelectItem value="24h">24 hours</SelectItem>
                        <SelectItem value="48h">48 hours</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Notes (Optional)</Label>
                    <Textarea
                      value={bulkNotes}
                      onChange={(e) => setBulkNotes(e?.target?.value)}
                      placeholder="Add any special instructions..."
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowBulkSendDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleBulkSend} disabled={loading}>
                    {loading ? "Sending..." : "Send Quotes"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Bulk Assign */}
            <Dialog open={showBulkAssignDialog} onOpenChange={setShowBulkAssignDialog}>
              <DialogTrigger asChild>
                <Button 
                  variant="secondary" 
                  size="sm" 
                  disabled={!canBulkAssign || loading}
                  className="flex items-center space-x-1"
                >
                  <UserCheck className="h-4 w-4" />
                  <span>Assign</span>
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Assign Quotes</DialogTitle>
                  <DialogDescription>
                    Assign {selectedQuotes.length} selected quotes to a specific affiliate
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label>Select Affiliate</Label>
                    <Select value={selectedAffiliate} onValueChange={setSelectedAffiliate}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose affiliate..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="affiliate-1">Elite Transport Co</SelectItem>
                        <SelectItem value="affiliate-2">Premium Rides LLC</SelectItem>
                        <SelectItem value="affiliate-3">City Executive Cars</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Assignment Notes (Optional)</Label>
                    <Textarea
                      value={bulkNotes}
                      onChange={(e) => setBulkNotes(e?.target?.value)}
                      placeholder="Add assignment notes..."
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowBulkAssignDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleBulkAssign} disabled={loading || !selectedAffiliate}>
                    {loading ? "Assigning..." : "Assign Quotes"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Export */}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleExportSelected}
              className="flex items-center space-x-1"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>

            {/* Archive */}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleBulkArchive}
              disabled={loading}
              className="flex items-center space-x-1 text-orange-600 hover:text-orange-700"
            >
              <Archive className="h-4 w-4" />
              <span>Archive</span>
            </Button>
          </div>
        )}

        {/* Quick Stats */}
        {selectedQuotes.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className="text-lg font-semibold">{selectedQuotes.length}</div>
              <p className="text-xs text-muted-foreground">Selected</p>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">
                ${selectedQuoteObjects.reduce((sum, q) => sum + (q.total_amount || 0), 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">Total Value</p>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">
                {new Set(selectedQuoteObjects.map(q => q.customer?.email)).size}
              </div>
              <p className="text-xs text-muted-foreground">Customers</p>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">
                {new Set(selectedQuoteObjects.map(q => q.status)).size}
              </div>
              <p className="text-xs text-muted-foreground">Statuses</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
