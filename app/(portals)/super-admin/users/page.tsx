// ---
// SYSTEM ENTITY SUMMARY
//
// Tenants: Isolated workspaces for each client/affiliate/network. All business data is scoped by organization_id.
// Users: Authenticated individuals. Linked to tenants via tenant_users (can belong to multiple tenants).
// Orgs/Companies: Business entities (e.g., affiliate companies, client companies). May be linked to tenants or used for grouping.
//
// Typical Flow:
// - Super Admin creates a tenant (<PERSON>lient, Affiliate, TNC, White Label, etc.).
// - Super Admin or Tenant Admin invites/creates users and assigns them to the tenant.
// - Users may be further associated with organizations/companies for business logic, reporting, or permissions.
// ---

"use client";

import React, { useState, useMemo, useEffect, useRef } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Badge } from "@/app/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Plus, Search, User as UserIconLucide, Mail, Shield, MoreVertical, Loader2, Users as UsersIcon, UserCheck, UserPlus, ShieldCheck, MailWarning } from "lucide-react";
import { DataTable } from "@/app/components/ui/data-table";
import { columns, User } from './components/columns';
import { useSmartRealTimeUpdates } from "@/hooks/useSmartRealTimeUpdates";
import { createClient } from "@supabase/supabase-js";
import { useToast } from "@/app/components/ui/use-toast";

// User-Organization relationship interface that matches our API
interface DatabaseUserOrganization {
  id: string;
  user_id: string;
  organization_id: string;
  role: string;
  status: string;
  created_at: string;
  updated_at: string;
  user: {
    id: string;
    email: string;
    full_name: string | null;
    phone: string | null;
    created_at: string;
  };
  organization: {
    id: string;
    name: string;
    slug: string;
    organization_type: string;
  };
}

// Helper function to transform user-organization relationship to UI user format
const transformUserOrganization = (userOrg: DatabaseUserOrganization): User => {
  return {
    id: userOrg.id, // Use relationship ID
    name: userOrg.user.full_name || userOrg.user.email.split('@')[0],
    email: userOrg.user.email,
    role: userOrg.role,
    status: userOrg.status === 'active' ? 'Active' : 'Inactive',
    lastLogin: userOrg.updated_at,
    org: userOrg.organization.name
  };
};

// Helper function to transform profile to UI user format
const transformUserFromProfile = (profile: any): User => {
  return {
    id: profile.id,
    name: profile.full_name || profile.email?.split('@')[0] || 'Unknown',
    email: profile.email || 'No email',
    role: Array.isArray(profile.roles) ? profile.roles[0] : 'USER',
    status: profile.status === 'active' ? 'Active' : 'Inactive',
    lastLogin: profile.updated_at || profile.created_at,
    org: 'N/A' // Profiles don't have direct org relationship
  };
};

export default function UsersPage() {
  const [search, setSearch] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const { toast } = useToast();

  // Filter logic (client-side for now)
  const filteredUsers = useMemo(() => {
    let result = users;

    if (search) {
      result = result.filter((user) =>
        user?.name?.toLowerCase().includes(search.toLowerCase()) ||
        user?.email?.toLowerCase().includes(search.toLowerCase())
      );
    }
    if (roleFilter !== "all") {
      result = result.filter((user) => user.role === roleFilter);
    }
    if (statusFilter !== "all") {
      result = result.filter((user) => user.status === statusFilter);
    }
    return result;
  }, [users, search, roleFilter, statusFilter]);

  // Fetch users from API with smart updates
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setFetching && setFetching(true); // Prevent concurrent fetches
      setError(null);

      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (roleFilter !== 'all') params.append('role', roleFilter);

      const response = await fetch(`/api/super-admin/users-simple?${params.toString()}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();

      if (data.success) {
        const transformedUsers = (data.users || []).map(transformUserFromProfile);

        // Only update if there are significant changes to prevent flickering
        if (hasSignificantChanges && hasSignificantChanges(transformedUsers, users, ['id', 'email', 'role', 'updated_at'])) {
          console.log('[Users] Meaningful changes detected, updating users');
          setUsers(transformedUsers);
          setTotalUsers(data.total);
        } else if (!hasSignificantChanges) {
          // First load or hook not ready yet
          setUsers(transformedUsers);
          setTotalUsers(data.total);
        } else {
          console.log('[Users] No significant changes, skipping update to prevent flickering');
        }
      } else {
        throw new Error(data.error || 'Failed to fetch users');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load users');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
      setFetching && setFetching(false);
    }
  };

  // Smart real-time updates hook (after fetchUsers is defined)
  const { debouncedUpdate, setFetching, hasSignificantChanges } = useSmartRealTimeUpdates(fetchUsers);

  useEffect(() => {
    fetchUsers();
  }, []);

  // Real-time updates subscription
  useEffect(() => {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    console.log('[Users] Setting up real-time subscription...');

    const subscription = supabase
      .channel('users-realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'profiles'
        },
        (payload: any) => {
          console.log('[Users] Real-time update received:', payload);

          // Show toast for new users
          if (payload.eventType === 'INSERT') {
            toast({
              title: "New User Created",
              description: `User ${payload.new.email} has been added`,
              duration: 4000
            });
          } else if (payload.eventType === 'UPDATE') {
            toast({
              title: "User Updated",
              description: `User ${payload.new.email} has been modified`,
              duration: 3000
            });
          }

          // Use debounced update to prevent flickering
          debouncedUpdate(payload);
        }
      )
      .subscribe();

    return () => {
      console.log('[Users] Cleaning up real-time subscription...');
      subscription.unsubscribe();
    };
  }, [debouncedUpdate, toast]);

  // Organization-User relationship stats
  const userStats = useMemo(() => ({
    totalRelationships: users.length,
    activeRelationships: users.filter(u => u.status === 'Active').length,
    inactiveRelationships: users.filter(u => u.status === 'Inactive').length,
    adminRoles: users.filter(u => u.role === 'admin').length
  }), [users]);

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Organization-User Management</h2>
        <div className="flex items-center space-x-2">
          <Button><UserPlus className="mr-2 h-4 w-4" /> Add User to Organization</Button>
        </div>
      </div>

      {/* --- Stats Section --- */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Relationships</CardTitle>
            <UsersIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.totalRelationships}</div>
            <p className="text-xs text-muted-foreground">User-Organization links</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.activeRelationships}</div>
            <p className="text-xs text-muted-foreground">Active memberships</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.inactiveRelationships}</div>
            <p className="text-xs text-muted-foreground">Suspended/Inactive</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Admin Roles</CardTitle>
            <ShieldCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.adminRoles}</div>
            <p className="text-xs text-muted-foreground">Organization admins</p>
          </CardContent>
        </Card>
      </div>
      {/* --- End Stats Section --- */}

      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row md:items-center gap-2 mb-4">
            <div className="relative w-full max-w-xs">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search users..."
                className="pl-8"
                value={search}
                onChange={(e) => setSearch(e?.target?.value)}
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="Super Admin">Super Admin</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
                <SelectItem value="User">User</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Suspended">Suspended</SelectItem>
                <SelectItem value="Deactivated">Deactivated</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {loading ? (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="animate-spin h-6 w-6 text-muted-foreground" />
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-10">{error}</div>
          ) : (
            <div className="overflow-x-auto">
              {/* DataTable renders only when loading is false and error is null */}
              <DataTable columns={columns} data={filteredUsers} />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
