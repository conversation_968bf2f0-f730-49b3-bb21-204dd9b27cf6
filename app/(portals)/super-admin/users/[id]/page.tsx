import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { notFound, redirect } from 'next/navigation';
import { EditUserForm } from '../_components/edit-user-form';
import { UserDetails } from './_components/user-details';

// Define the expected user data structure
type UserData = {
  id: string;
  email: string;
  created_at: string;
  role: string | null;
  roles: string[] | null;
  user_organizations: Array<{
    organization: {
      id: string;
      name: string;
      slug: string;
    };
  }>;
};

interface PageProps {
  params: { id: string };
  searchParams: { [key: string]: string | string[] | undefined };
}

export default async function UserPage({ params, searchParams }: PageProps) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          } catch {
            // The `setAll` method was called from a Server Component.
          }
        },
      },
    }
  );
  const { data: { session } } = await supabase.auth.getSession();
  const isEditMode = searchParams.mode === 'edit';

  // Check authentication
  if (!session) {
    redirect('/login');
  }

  // Check if user is a super admin
  const { data: currentUserData } = await supabase
    .from('profiles')
    .select('role, roles')
    .eq('id', session.user.id)
    .single();

  const userRole = currentUserData?.role || (currentUserData?.roles && currentUserData.roles[0]);
  if (userRole !== 'SUPER_ADMIN') {
    redirect('/dashboard');
  }

  try {
    // Fetch user data with related information
    const { data: user, error } = await supabase
      .from('profiles')
      .select(`
        id,
        email,
        created_at,
        role,
        roles
      `)
      .eq('id', params.id)
      .single();

    // Fetch user organizations separately since there's no direct FK relationship
    let userOrganizations = [];
    if (user) {
      const { data: orgData } = await supabase
        .from('user_organizations')
        .select(`
          organization_id,
          organizations(id, name, slug)
        `)
        .eq('user_id', user.id);

      userOrganizations = orgData || [];
    }

    if (error || !user) {
      console.error('Error fetching user:', error);
      notFound();
    }

    // Combine user data with organizations
    const userData = {
      ...user,
      user_organizations: userOrganizations.map(uo => ({
        organization: uo.organizations
      }))
    } as UserData;

    // Fetch all organizations for the organization selector
    const { data: organizations } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .order('name');

    if (isEditMode) {
      return (
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6">Edit User: {userData.email}</h1>
          <EditUserForm
            user={{
              id: userData.id,
              email: userData.email,
              role: userData.role || (userData.roles && userData.roles[0]) || '',
              organizationIds: userData.user_organizations?.map(uo => uo.organization.id) || []
            }}
            tenants={organizations || []}
          />
        </div>
      );
    }

    // Default to view mode
    return (
      <div className="container mx-auto px-4 py-8">
        <UserDetails user={userData} />
      </div>
    );
  } catch (error) {
    console.error('Error in user page:', error);
    notFound();
  }
}
