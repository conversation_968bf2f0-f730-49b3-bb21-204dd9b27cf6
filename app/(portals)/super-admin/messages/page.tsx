"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Badge } from "@/app/components/ui/badge"
import { useToast } from "@/app/components/ui/use-toast"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog"
import { Textarea } from "@/app/components/ui/textarea"
import { Label } from "@/app/components/ui/label"
import { 
  MessageSquare, 
  Plus, 
  Search, 
  Filter, 
  Clock, 
  User, 
  AlertTriangle,
  CheckCircle,
  Circle,
  MessageCircle,
  Send,
  Refresh<PERSON>w
} from "lucide-react"
import { format } from 'date-fns'

interface MessageThread {
  id: string;
  title: string;
  type: string;
  priority: string;
  status: string;
  created_by: string;
  assigned_to?: string;
  created_at: string;
  updated_at: string;
  creator?: { first_name: string; last_name: string; email: string };
  assignee?: { first_name: string; last_name: string; email: string };
  messages?: Array<{
    id: string;
    content: string;
    created_at: string;
    sender?: { first_name: string; last_name: string };
  }>;
}

interface UnreadCounts {
  total: number;
  byType: Record<string, number>;
  byPriority: Record<string, number>;
}

export default function AdminMessagesPage() {
  const { toast } = useToast();
  const [threads, setThreads] = useState<MessageThread[]>([]);
  const [unreadCounts, setUnreadCounts] = useState<UnreadCounts>({ total: 0, byType: {}, byPriority: {} });
  const [selectedThread, setSelectedThread] = useState<MessageThread | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // New thread form
  const [newThread, setNewThread] = useState({
    title: "",
    type: "general",
    priority: "normal",
    initialMessage: ""
  });

  useEffect(() => {
    fetchThreads();
  }, [statusFilter, typeFilter, priorityFilter]);

  const fetchThreads = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams();
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (typeFilter !== 'all') params.append('type', typeFilter);
      if (priorityFilter !== 'all') params.append('priority', priorityFilter);

      const response = await fetch(`/api/admin/messages?${params}`);
      const data = await response.json();

      if (data.success) {
        setThreads(data.data.threads);
        setUnreadCounts(data.data.unreadCounts);
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Error fetching threads:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch messages. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createThread = async () => {
    if (!newThread.title || !newThread.initialMessage) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await fetch('/api/admin/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newThread)
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: 'Message thread created successfully.',
        });
        setIsCreateDialogOpen(false);
        setNewThread({ title: "", type: "general", priority: "normal", initialMessage: "" });
        fetchThreads();
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create thread. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open': return <Circle className="h-4 w-4 text-blue-500" />;
      case 'in_progress': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'resolved': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'closed': return <CheckCircle className="h-4 w-4 text-gray-500" />;
      default: return <Circle className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'destructive';
      case 'high': return 'secondary';
      case 'normal': return 'outline';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const filteredThreads = threads.filter(thread =>
    thread?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    thread.creator?.first_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    thread.creator?.last_name?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Admin Messages</h2>
          <p className="text-muted-foreground">Manage communications and support threads</p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Thread
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Create New Message Thread</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={newThread.title}
                  onChange={(e) => setNewThread({ ...newThread, title: e?.target?.value })}
                  placeholder="Enter thread title"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="type">Type</Label>
                  <Select value={newThread.type} onValueChange={(value) => setNewThread({ ...newThread, type: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="system_alert">System Alert</SelectItem>
                      <SelectItem value="user_support">User Support</SelectItem>
                      <SelectItem value="affiliate_communication">Affiliate Communication</SelectItem>
                      <SelectItem value="quote_discussion">Quote Discussion</SelectItem>
                      <SelectItem value="general">General</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="priority">Priority</Label>
                  <Select value={newThread.priority} onValueChange={(value) => setNewThread({ ...newThread, priority: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="message">Initial Message *</Label>
                <Textarea
                  id="message"
                  value={newThread.initialMessage}
                  onChange={(e) => setNewThread({ ...newThread, initialMessage: e?.target?.value })}
                  placeholder="Enter your message"
                  rows={4}
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={createThread}>
                  <Send className="h-4 w-4 mr-2" />
                  Create Thread
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search messages..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e?.target?.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="open">Open</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="system_alert">System Alert</SelectItem>
              <SelectItem value="user_support">User Support</SelectItem>
              <SelectItem value="affiliate_communication">Affiliate Communication</SelectItem>
              <SelectItem value="quote_discussion">Quote Discussion</SelectItem>
              <SelectItem value="general">General</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priority</SelectItem>
              <SelectItem value="urgent">Urgent</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="normal">Normal</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="icon" onClick={fetchThreads}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Unread</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{unreadCounts.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Threads</CardTitle>
            <Circle className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {threads.filter(t => t.status === 'open').length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Urgent Priority</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {threads.filter(t => t.priority === 'urgent').length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Threads</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{threads.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Message Threads */}
      <Card>
        <CardHeader>
          <CardTitle>Message Threads</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
            </div>
          ) : filteredThreads.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No message threads found.
            </div>
          ) : (
            <div className="space-y-4">
              {filteredThreads.map((thread) => (
                <div
                  key={thread.id}
                  className="border rounded-lg p-4 hover:bg-muted/50 cursor-pointer transition-colors"
                  onClick={() => setSelectedThread(thread)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getStatusIcon(thread.status)}
                        <h3 className="font-medium">{thread.title}</h3>
                        <Badge variant={getPriorityColor(thread.priority)}>
                          {thread.priority}
                        </Badge>
                        <Badge variant="outline">{thread.type.replace('_', ' ')}</Badge>
                      </div>
                      
                      <div className="text-sm text-muted-foreground mb-2">
                        Created by {thread.creator?.first_name} {thread.creator?.last_name} • 
                        {format(new Date(thread.created_at), 'MMM d, yyyy')}
                      </div>
                      
                      {thread.messages && thread?.messages?.length > 0 && (
                        <div className="text-sm text-muted-foreground">
                          Latest: {thread.messages[thread?.messages?.length - 1].content.substring(0, 100)}...
                        </div>
                      )}
                    </div>
                    
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(thread.updated_at), 'MMM d, HH:mm')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
