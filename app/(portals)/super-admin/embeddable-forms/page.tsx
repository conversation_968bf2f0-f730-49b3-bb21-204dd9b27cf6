'use client';

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/app/components/ui/card';
import { Button } from '@/app/components/ui/button';
import { Code, Palette, Settings } from 'lucide-react';

export default function EmbeddableFormsPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Embeddable Forms Analytics</h1>
        <p className="text-muted-foreground">
          Monitor performance and analytics for all tenant embeddable forms across the platform
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Forms</CardTitle>
            <Palette className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1</div>
            <p className="text-xs text-muted-foreground">
              Forms ready for embedding
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Submissions</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              Bookings through embedded forms
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Integration Status</CardTitle>
            <Code className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Ready</div>
            <p className="text-xs text-muted-foreground">
              Forms configured and ready
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Individual Tenant Form Management</CardTitle>
          <p className="text-sm text-muted-foreground">
            Configure specific tenant forms through the organization management interface
          </p>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Code className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Per-Tenant Configuration</h3>
            <p className="text-muted-foreground mb-4">
              To configure embeddable forms for specific tenants, navigate to:<br />
              <strong>Tenant Management → Organizations → [Select Org] → Embeddable Forms</strong>
            </p>
            <div className="flex justify-center space-x-2">
              <Button variant="outline" asChild>
                <a href="/super-admin/orgs">Go to Organizations</a>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
