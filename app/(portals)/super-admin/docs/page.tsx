"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/app/components/ui/card"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/app/components/ui/tabs"
import {
  Book,
  HelpCircle,
  Shield,
  FileText,
  Link2,
  LifeBuoy,
  Settings,
  Users,
  Database,
  Globe,
  Zap,
  BarChart3,
  Key,
  Webhook,
  CreditCard,
  Building2,
  Code,
  Terminal,
  Layers,
  GitBranch,
  CheckCircle2,
  AlertTriangle,
  Info,
  Network
} from "lucide-react"
import { PlatformOverviewContent } from "@/app/components/docs/PlatformOverviewContent"
import { SuperAdminPortalContent } from "@/app/components/docs/SuperAdminPortalContent"
import { QuoteWorkflowContent } from "@/app/components/docs/QuoteWorkflowContent"
import { OrganizationManagementContent } from "@/app/components/docs/OrganizationManagementContent"
import { ApiDocumentationContent } from "@/app/components/docs/ApiDocumentationContent"
import { UserManagementContent } from "@/app/components/docs/UserManagementContent"
import { MultiTenantArchitectureContent } from "@/app/components/docs/MultiTenantArchitectureContent"
import { AffiliateOnboardingContent } from "@/app/components/docs/AffiliateOnboardingContent"
import { EventManagementContent } from "@/app/components/docs/EventManagementContent"
import { CustomerPortalContent } from "@/app/components/docs/CustomerPortalContent"
import { EmbeddableBookingFormsContent } from "@/app/components/docs/EmbeddableBookingFormsContent"
import { RealTimeFeaturesContent } from "@/app/components/docs/RealTimeFeaturesContent"
import { WhiteLabelSystemContent } from "@/app/components/docs/WhiteLabelSystemContent"
import { CustomerMigrationContent } from "@/app/components/docs/CustomerMigrationContent"
import { SubscriptionManagementContent } from "@/app/components/docs/SubscriptionManagementContent"
import { EnterpriseApiContent } from "@/app/components/docs/EnterpriseApiContent"
import { DatabaseArchitectureContent } from "@/app/components/docs/DatabaseArchitectureContent"
import { AuthenticationSystemContent } from "@/app/components/docs/AuthenticationSystemContent"
import { RealTimeArchitectureContent } from "@/app/components/docs/RealTimeArchitectureContent"
import { ApiDevelopmentContent } from "@/app/components/docs/ApiDevelopmentContent"
import { DeploymentDevOpsContent } from "@/app/components/docs/DeploymentDevOpsContent"
import { TroubleshootingContent } from "@/app/components/docs/TroubleshootingContent"
import { FAQContent } from "@/app/components/docs/FAQContent"
import { SupportResourcesContent } from "@/app/components/docs/SupportResourcesContent"
import { ClientPersonasContent } from "@/app/components/docs/ClientPersonasContent"
import { PermissionManagerSystemContent } from "@/app/components/docs/PermissionManagerSystemContent"
import { StandardizedErrorHandlingContent } from "@/app/components/docs/StandardizedErrorHandlingContent"
import { TncCustomerManagementContent } from "@/app/components/docs/TncCustomerManagementContent"

const documentationSections = {
  platform: [
    {
      title: "Platform Overview",
      description: "TransFlow's revolutionary four-tier SaaS architecture",
      icon: Layers,
      status: "complete",
      sections: [
        "Four-Tier Account Architecture",
        "TransFlow Super Admin (Platform Owner)",
        "TNC Accounts (Mini-SaaS Providers)",
        "TNC Customers (Managed Accounts)",
        "Direct Clients (Independent)",
        "Real-World Business Scenarios",
        "Network Inheritance & Portal Provisioning"
      ]
    },
    {
      title: "Client Personas & Use Cases",
      description: "Real-world success stories and comprehensive client personas",
      icon: Users,
      status: "complete",
      sections: [
        "Success Metrics Overview",
        "Client Journey Visualization",
        "StartUp Shuttle Co. (Shared Tier)",
        "Corporate Events Plus (Segregated Tier)",
        "Global Mobility Solutions (Isolated Tier)",
        "Industry Vertical Coverage",
        "Quantified Results & ROI"
      ]
    },
    {
      title: "Super Admin Portal",
      description: "Platform-wide management with ORG switching",
      icon: Settings,
      status: "complete",
      sections: [
        "Dashboard & Analytics",
        "ORG Selector & Context Switching",
        "Client Levels Management",
        "Organization Management",
        "User Administration",
        "System Configuration"
      ]
    },
    {
      title: "User Roles & Permissions",
      description: "Role-based access control across all portals",
      icon: Users,
      status: "complete",
      sections: [
        "SUPER_ADMIN Platform Control",
        "CLIENT & CLIENT_COORDINATOR Roles",
        "AFFILIATE & DISPATCHER Functions",
        "TNC_ADMIN Network Management",
        "PASSENGER User Access"
      ]
    },
    {
      title: "Multi-Tenant Architecture",
      description: "Complete tenant isolation and management",
      icon: Database,
      status: "complete",
      sections: [
        "Row-Level Security (RLS)",
        "Tenant Data Isolation",
        "Cross-Tenant Analytics",
        "Migration Workflows",
        "White-Label Branding"
      ]
    }
  ],
  workflows: [
    {
      title: "Quote Workflow System",
      description: "End-to-end quote management with real-time updates",
      icon: FileText,
      status: "complete",
      sections: [
        "Quote Lifecycle States (draft/pending/responded/accepted/rejected/expired/cancelled)",
        "Business Logic Validation",
        "CLIENT_COORDINATOR Enhanced Capabilities",
        "Organization-Scoped Quote Management",
        "Real-time Status Updates"
      ]
    },
    {
      title: "Affiliate Management",
      description: "Multi-company affiliate management with role hierarchy",
      icon: Building2,
      status: "complete",
      sections: [
        "AFFILIATE & DISPATCHER Role Hierarchy",
        "Multi-Company Management Architecture",
        "Company-Level Roles (MANAGER/DISPATCHER/DRIVER)",
        "Onboarding & SUPER_ADMIN Approval",
        "Network Integration & Performance Analytics"
      ]
    },
    {
      title: "Event Management",
      description: "Complex event planning with business logic validation",
      icon: Globe,
      status: "complete",
      sections: [
        "Event Business Rules & Validation",
        "Quote-Event Relationship Requirements",
        "Organization-Scoped Event Management",
        "Multi-Trip Coordination",
        "Live Tracking & Emergency Response"
      ]
    },
    {
      title: "TNC Customer Management",
      description: "TNCs as mini-SaaS providers with customer portal management",
      icon: Network,
      status: "complete",
      sections: [
        "TNCs as Mini-SaaS Providers",
        "Customer Account Creation & Management",
        "Automated Portal Provisioning",
        "Network Inheritance System",
        "Real-World Business Scenarios (Marriott)",
        "Technical Implementation & APIs"
      ]
    },
    {
      title: "CLIENT Portal (/event-manager/)",
      description: "CLIENT and CLIENT_COORDINATOR portal functionality",
      icon: Users,
      status: "complete",
      sections: [
        "CLIENT & CLIENT_COORDINATOR Role Access",
        "Organization-Scoped Quote Management",
        "PASSENGER User Creation & Management",
        "Event Planning & Coordination",
        "Multi-Tenant Data Isolation"
      ]
    }
  ],
  features: [
    {
      title: "Embeddable Booking Forms",
      description: "White-label booking forms for client websites",
      icon: Code,
      status: "complete",
      sections: [
        "Multi-Tenant Form Isolation",
        "Iframe & Widget Embedding",
        "Custom Branding Options",
        "Domain Whitelisting",
        "Analytics & Tracking"
      ]
    },
    {
      title: "Real-time Features",
      description: "WebSocket integration and live updates",
      icon: Zap,
      status: "complete",
      sections: [
        "Live Trips Map",
        "Real-time Quote Updates",
        "WebSocket Architecture",
        "Push Notifications",
        "Emergency Alerts"
      ]
    },
    {
      title: "White-Label System",
      description: "Custom branding and domain management",
      icon: Building2,
      status: "in-progress",
      sections: [
        "Dynamic Branding Engine",
        "Custom Domain Routing",
        "Theme Customization",
        "Client Configuration",
        "Subdomain Management"
      ]
    },
    {
      title: "Customer Migration",
      description: "Automated upsell and tenant migration workflows",
      icon: Users,
      status: "planned",
      sections: [
        "Upsell Detection",
        "Coverage Gap Analysis",
        "Migration Workflows",
        "Retention Analytics",
        "Success Tracking"
      ]
    },
    {
      title: "Subscription Management",
      description: "Tier-based pricing and billing system",
      icon: CreditCard,
      status: "planned",
      sections: [
        "3-Tier Pricing Model",
        "Usage Tracking",
        "Automated Billing",
        "Payment Processing",
        "Invoice Management"
      ]
    },
    {
      title: "Enterprise API",
      description: "RESTful API for enterprise integrations",
      icon: Terminal,
      status: "new",
      sections: [
        "API Endpoints",
        "Authentication",
        "Rate Limiting",
        "Webhook System",
        "Documentation"
      ]
    }
  ],
  technical: [
    {
      title: "Database Architecture",
      description: "Schema design and data relationships",
      icon: Database,
      status: "complete",
      sections: [
        "Multi-Tenant Schema",
        "Row-Level Security (RLS)",
        "Table Relationships",
        "Migration Management",
        "Performance Optimization"
      ]
    },
    {
      title: "Authentication System",
      description: "User authentication and authorization",
      icon: Shield,
      status: "complete",
      sections: [
        "Supabase Auth Integration",
        "Role-Based Access Control",
        "JWT Token Management",
        "Session Handling",
        "Security Best Practices"
      ]
    },
    {
      title: "Permission Manager System",
      description: "Centralized permission management with multi-layer validation",
      icon: Key,
      status: "complete",
      sections: [
        "Multi-Layer Permission Architecture",
        "Permission Templates & Granular Controls",
        "SUPER_ADMIN Override Capabilities",
        "Intelligent Caching Strategy",
        "Real-time Permission Updates",
        "Audit Trail & Compliance"
      ]
    },
    {
      title: "Standardized Error Handling",
      description: "Comprehensive error management and user feedback system",
      icon: AlertTriangle,
      status: "complete",
      sections: [
        "Error Classification System",
        "User-Friendly Error Messages",
        "API Error Response Standards",
        "Frontend Error Boundaries",
        "Logging & Monitoring Integration",
        "Recovery Mechanisms"
      ]
    },
    {
      title: "Real-time Architecture",
      description: "WebSocket and live update implementation",
      icon: Zap,
      status: "complete",
      sections: [
        "WebSocket Connection Management",
        "Real-time Subscriptions",
        "Event Broadcasting",
        "Connection Fallbacks",
        "Performance Optimization"
      ]
    },
    {
      title: "API Development",
      description: "RESTful API design and implementation",
      icon: Terminal,
      status: "in-progress",
      sections: [
        "API Route Structure",
        "Request/Response Patterns",
        "Error Handling",
        "Rate Limiting",
        "Documentation"
      ]
    },
    {
      title: "Deployment & DevOps",
      description: "Production deployment and monitoring",
      icon: GitBranch,
      status: "complete",
      sections: [
        "Environment Configuration",
        "Database Migrations",
        "Monitoring & Logging",
        "Backup Strategies",
        "Scaling Procedures"
      ]
    }
  ],
  support: [
    {
      title: "Troubleshooting",
      description: "Common issues and their solutions",
      icon: HelpCircle,
      status: "in-progress",
      sections: [
        "Authentication Issues",
        "API Errors",
        "Performance Problems",
        "WebSocket Issues",
        "Database Errors"
      ]
    },
    {
      title: "FAQ",
      description: "Frequently asked questions",
      icon: Info,
      status: "in-progress",
      sections: [
        "General Questions",
        "Technical Questions",
        "Billing Questions",
        "Security Questions",
        "Integration Questions"
      ]
    },
    {
      title: "Support Resources",
      description: "Getting help and contacting support",
      icon: LifeBuoy,
      status: "complete",
      sections: [
        "Support Channels",
        "Response Times",
        "Escalation Process",
        "Community Resources",
        "Training Materials"
      ]
    }
  ]
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'complete': return 'bg-green-100 text-green-800'
    case 'in-progress': return 'bg-blue-100 text-blue-800'
    case 'new': return 'bg-purple-100 text-purple-800'
    case 'planned': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'complete': return <CheckCircle2 className="h-4 w-4" />
    case 'in-progress': return <Settings className="h-4 w-4 animate-spin" />
    case 'new': return <Zap className="h-4 w-4" />
    case 'planned': return <AlertTriangle className="h-4 w-4" />
    default: return <Info className="h-4 w-4" />
  }
}

export default function SuperAdminDocsPage() {
  const [selectedSection, setSelectedSection] = useState<string | null>(null)

  const renderDocumentationGrid = (sections: any[]) => (
    <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {sections.map((section) => (
        <Card
          key={section.title}
          className="hover:shadow-lg transition-shadow cursor-pointer"
          onClick={() => setSelectedSection(section.title)}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <section.icon className="h-6 w-6 text-primary" />
                <CardTitle className="text-lg font-semibold">{section.title}</CardTitle>
              </div>
              <Badge className={getStatusColor(section.status)}>
                <div className="flex items-center gap-1">
                  {getStatusIcon(section.status)}
                  <span className="capitalize">{section.status.replace('-', ' ')}</span>
                </div>
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <CardDescription className="mb-4">{section.description}</CardDescription>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Sections:</p>
              <ul className="text-sm space-y-1">
                {section.sections.slice(0, 3).map((subsection: string, index: number) => (
                  <li key={index} className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                    {subsection}
                  </li>
                ))}
                {section?.sections?.length > 3 && (
                  <li className="text-muted-foreground">
                    +{section?.sections?.length - 3} more sections
                  </li>
                )}
              </ul>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  if (selectedSection) {
    // Show rich content for sections that have dedicated components
    if (selectedSection === 'Platform Overview') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <PlatformOverviewContent />
        </div>
      )
    }

    if (selectedSection === 'Super Admin Portal') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <SuperAdminPortalContent />
        </div>
      )
    }

    if (selectedSection === 'Client Personas & Use Cases') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <ClientPersonasContent />
        </div>
      )
    }

    if (selectedSection === 'Quote Workflow System') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <QuoteWorkflowContent />
        </div>
      )
    }

    if (selectedSection === 'Organization Management') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <OrganizationManagementContent />
        </div>
      )
    }

    if (selectedSection === 'Subscription Management') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <SubscriptionManagementContent />
        </div>
      )
    }

    if (selectedSection === 'Enterprise API') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <EnterpriseApiContent />
        </div>
      )
    }

    if (selectedSection === 'Database Architecture') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <DatabaseArchitectureContent />
        </div>
      )
    }

    if (selectedSection === 'Authentication System') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <AuthenticationSystemContent />
        </div>
      )
    }

    if (selectedSection === 'Real-time Architecture') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <RealTimeArchitectureContent />
        </div>
      )
    }

    if (selectedSection === 'API Development') {
      console.log('API Development section selected');
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <ApiDevelopmentContent />
        </div>
      )
    }

    if (selectedSection === 'Deployment & DevOps') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <DeploymentDevOpsContent />
        </div>
      )
    }

    if (selectedSection === 'Troubleshooting') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <TroubleshootingContent />
        </div>
      )
    }

    if (selectedSection === 'FAQ') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <FAQContent />
        </div>
      )
    }

    if (selectedSection === 'Support Resources') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <SupportResourcesContent />
        </div>
      )
    }

    if (selectedSection === 'API Documentation') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <ApiDocumentationContent />
        </div>
      )
    }

    if (selectedSection === 'User Roles & Permissions') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <UserManagementContent />
        </div>
      )
    }

    if (selectedSection === 'Multi-Tenant Architecture') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <MultiTenantArchitectureContent />
        </div>
      )
    }

    if (selectedSection === 'Affiliate Management') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <AffiliateOnboardingContent />
        </div>
      )
    }

    if (selectedSection === 'Event Management') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <EventManagementContent />
        </div>
      )
    }

    if (selectedSection === 'Customer Portal') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <CustomerPortalContent />
        </div>
      )
    }

    if (selectedSection === 'Embeddable Booking Forms') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <EmbeddableBookingFormsContent />
        </div>
      )
    }

    if (selectedSection === 'Real-time Features') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <RealTimeFeaturesContent />
        </div>
      )
    }

    if (selectedSection === 'White-Label System') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <WhiteLabelSystemContent />
        </div>
      )
    }

    if (selectedSection === 'Customer Migration') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <CustomerMigrationContent />
        </div>
      )
    }

    if (selectedSection === 'Permission Manager System') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <PermissionManagerSystemContent />
        </div>
      )
    }

    if (selectedSection === 'Standardized Error Handling') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <StandardizedErrorHandlingContent />
        </div>
      )
    }

    if (selectedSection === 'TNC Customer Management') {
      return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => setSelectedSection(null)}>
              Back to Documentation
            </Button>
          </div>
          <TncCustomerManagementContent />
        </div>
      )
    }

    // Fallback to table of contents for other sections
    const allSections = [
      ...documentationSections.platform,
      ...documentationSections.workflows,
      ...documentationSections.features,
      ...documentationSections.technical,
      ...documentationSections.support
    ]
    const section = allSections.find(s => s.title === selectedSection)

    return (
      <div className="space-y-6 max-w-4xl mx-auto p-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => setSelectedSection(null)}>
            Back to Documentation
          </Button>
          <div className="flex items-center gap-3">
            {section?.icon && <section.icon className="h-6 w-6 text-primary" />}
            <h1 className="text-3xl font-bold">{selectedSection}</h1>
            <Badge className={getStatusColor(section?.status || 'planned')}>
              <div className="flex items-center gap-1">
                {getStatusIcon(section?.status || 'planned')}
                <span className="capitalize">{section?.status?.replace('-', ' ') || 'Planned'}</span>
              </div>
            </Badge>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardDescription>{section?.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Table of Contents</h3>
              <div className="grid gap-3">
                {section?.sections.map((subsection: string, index: number) => (
                  <div key={index} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-primary">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{subsection}</p>
                      <p className="text-sm text-muted-foreground">
                        {section.status === 'complete' ? 'Documentation available' :
                          section.status === 'in-progress' ? 'In development' :
                            section.status === 'new' ? 'Recently added' : 'Coming soon'}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={section.status === 'planned'}
                      onClick={() => {
                        if (section.status !== 'planned') {
                          // For sections with rich content, show them directly
                          if (selectedSection === 'Platform Overview' || selectedSection === 'Super Admin Portal' || selectedSection === 'Client Personas & Use Cases') {
                            // Content will be shown by the existing selectedSection logic
                            return;
                          }
                          // For other sections, could add more content later
                          alert(`Content for "${subsection}" is coming soon!`);
                        }
                      }}
                    >
                      {section.status === 'planned' ? 'Coming Soon' : 'View'}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-8 max-w-7xl mx-auto p-6">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">TransFlow Documentation</h1>
        <p className="text-muted-foreground text-lg max-w-3xl mx-auto">
          Comprehensive documentation for platform administration, workflows, Enterprise API,
          and technical integration guides. Click on any section to explore detailed documentation.
        </p>

        {/* Status Legend */}
        <div className="flex items-center justify-center gap-4 pt-4">
          <div className="flex items-center gap-2">
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle2 className="h-3 w-3 mr-1" />
              Complete
            </Badge>
            <Badge className="bg-blue-100 text-blue-800">
              <Settings className="h-3 w-3 mr-1" />
              In Progress
            </Badge>
            <Badge className="bg-purple-100 text-purple-800">
              <Zap className="h-3 w-3 mr-1" />
              New
            </Badge>
            <Badge className="bg-gray-100 text-gray-800">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Planned
            </Badge>
          </div>
        </div>
      </div>

      <Tabs defaultValue="platform" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="platform">Platform</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="technical">Technical</TabsTrigger>
          <TabsTrigger value="support">Support</TabsTrigger>
        </TabsList>

        <TabsContent value="platform" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">Platform Architecture</h2>
            <p className="text-muted-foreground mb-6">
              3-tier multi-tenant SaaS architecture, user roles, and system administration.
            </p>
          </div>
          {renderDocumentationGrid(documentationSections.platform)}
        </TabsContent>

        <TabsContent value="workflows" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">Core Workflows</h2>
            <p className="text-muted-foreground mb-6">
              Quote management, affiliate operations, event coordination, and customer portals.
            </p>
          </div>
          {renderDocumentationGrid(documentationSections.workflows)}
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">Platform Features</h2>
            <p className="text-muted-foreground mb-6">
              Embeddable forms, real-time features, white-label system, and enterprise capabilities.
            </p>
          </div>
          {renderDocumentationGrid(documentationSections.features)}
        </TabsContent>

        <TabsContent value="technical" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">Technical Documentation</h2>
            <p className="text-muted-foreground mb-6">
              Database architecture, authentication, real-time systems, and deployment guides.
            </p>
          </div>
          {renderDocumentationGrid(documentationSections.technical)}
        </TabsContent>

        <TabsContent value="support" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">Support & Help</h2>
            <p className="text-muted-foreground mb-6">
              Troubleshooting guides, FAQs, and support resources.
            </p>
          </div>
          {renderDocumentationGrid(documentationSections.support)}
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LifeBuoy className="h-5 w-5" />
            Need Help?
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="justify-start">
              <HelpCircle className="h-4 w-4 mr-2" />
              Contact Support
            </Button>
            <Button variant="outline" className="justify-start">
              <Book className="h-4 w-4 mr-2" />
              API Reference
            </Button>
            <Button variant="outline" className="justify-start">
              <FileText className="h-4 w-4 mr-2" />
              Enterprise Guide
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}