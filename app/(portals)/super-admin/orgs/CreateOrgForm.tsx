"use client"

import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Building2, Globe, CreditCard, User, ArrowRight } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/app/components/ui/checkbox"
// Form component for creating organizations with integrated user creation

interface CreateOrgFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: (org: any) => void
}

export function CreateOrgForm({ open, onOpenChange, onSuccess }: CreateOrgFormProps) {
  const { toast } = useToast()
  const [step, setStep] = useState<'details' | 'plan' | 'admin' | 'confirmation'>('details')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    domain: '',
    industry: 'logistics',
    plan: 'essential',
    adminEmail: '',
    adminFirstName: '',
    adminLastName: '',
    adminPassword: '',
    adminRole: 'ADMIN',
    permissionTemplate: 'admin',
    sendInvitation: true,
  })

  const updateFormData = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }))
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)

    try {
      // Create organization with integrated tenant creation
      // The organizations API now handles both tenant and organization creation
      const orgResponse = await fetch('/api/super-admin/organizations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          name: formData.name,
          slug: formData.domain || formData?.name?.toLowerCase().replace(/[^a-z0-9]/g, '-'),
          industry: formData.industry,
          email: formData.adminEmail,
          plan: formData.plan,
          admin_user: {
            email: formData.adminEmail,
            first_name: formData.adminFirstName,
            last_name: formData.adminLastName,
            password: formData.adminPassword,
            role: formData.adminRole,
            permissions_template: formData.permissionTemplate,
            send_invitation: formData.sendInvitation
          }
        })
      });

      if (!orgResponse.ok) {
        const error = await orgResponse.json();
        throw new Error(error.error || 'Failed to create organization');
      }

      const orgResult = await orgResponse.json();
      console.log('Organization created successfully:', orgResult);

      // Create success object
      const newOrg = {
        id: orgResult.organization.id,
        name: formData.name,
        created_at: new Date().toISOString(),
        status: 'active',
        plan: formData.plan,
        users_count: orgResult.admin_user ? 1 : 0,
        industry: formData.industry,
        domain: formData.domain || null,
        last_active: new Date().toISOString(),
        organization_id: orgResult.organization?.id,
        admin_user: orgResult.admin_user || null
      }

      // Call the success callback with the new org
      onSuccess(newOrg)

      // Close the dialog
      onOpenChange(false)

      // Reset the form data
      setFormData({
        name: '',
        domain: '',
        industry: 'logistics',
        plan: 'essential',
        adminEmail: '',
        adminFirstName: '',
        adminLastName: '',
        adminPassword: '',
        adminRole: 'ADMIN',
        permissionTemplate: 'admin',
        sendInvitation: true,
      })
      setStep('details')

      // Show success toast
      toast({
        title: "Organization Created Successfully",
        description: `${formData.name} has been created${orgResult.admin_user ? ' and admin user invited' : ''}.`,
      })

    } catch (error) {
      console.error("Error creating organization:", error)
      toast({
        title: "Error Creating Organization",
        description: error instanceof Error ? error.message : "There was a problem creating the organization. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  const validateStep = () => {
    if (step === 'details') {
      return formData.name.trim() && formData.domain.trim()
    } else if (step === 'plan') {
      return formData.plan
    } else if (step === 'admin') {
      return formData.adminEmail.trim() && formData.adminFirstName.trim() && formData.adminLastName.trim() && formData.adminPassword.trim()
    }
    return true
  }
  
  const nextStep = () => {
    if (step === 'details') setStep('plan')
    else if (step === 'plan') setStep('admin')
    else if (step === 'admin') setStep('confirmation')
  }
  
  const prevStep = () => {
    if (step === 'plan') setStep('details')
    else if (step === 'admin') setStep('plan')
    else if (step === 'confirmation') setStep('admin')
  }
  
  const PlansTab = () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div 
        className={`border rounded-lg p-4 hover:border-primary cursor-pointer transition-all ${formData.plan === 'essential' ? 'border-primary ring-1 ring-primary bg-primary/5' : ''}`}
        onClick={() => updateFormData('plan', 'essential')}
      >
        <div className="text-lg font-bold">Essential</div>
        <div className="text-2xl font-bold mt-2 mb-4">$199<span className="text-sm font-normal text-muted-foreground">/mo</span></div>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>• Full platform access</p>
          <p>• Up to 2 team members</p>
          <p>• Access to vetted affiliate network</p>
          <p>• Basic booking management</p>
          <p>• Standard reporting</p>
          <p>• Document exchange</p>
          <p>• Access to VIP services booking</p>
          <p>• Email support</p>
        </div>
      </div>
      
      <div 
        className={`border rounded-lg p-4 hover:border-primary cursor-pointer transition-all ${formData.plan === 'professional' ? 'border-primary ring-1 ring-primary bg-primary/5' : ''}`}
        onClick={() => updateFormData('plan', 'professional')}
      >
        <div className="flex justify-between items-center">
            <div className="text-lg font-bold">Professional</div>
            <Badge variant="default">Most Popular</Badge>
        </div>
        <div className="text-2xl font-bold mt-2 mb-4">$499<span className="text-sm font-normal text-muted-foreground">/mo</span></div>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>• Everything in Essential, plus:</p>
          <p>• Up to 5 team members</p>
          <p>• Advanced booking management</p>
          <p>• Custom booking forms</p>
          <p>• Advanced analytics</p>
          <p>• Bulk booking tools</p>
          <p>• Priority support</p>
        </div>
      </div>
      
      <div 
        className={`border rounded-lg p-4 hover:border-primary cursor-pointer transition-all ${formData.plan === 'business' ? 'border-primary ring-1 ring-primary bg-primary/5' : ''}`}
        onClick={() => updateFormData('plan', 'business')}
      >
        <div className="text-lg font-bold">Business</div>
        <div className="text-2xl font-bold mt-2 mb-4">$999<span className="text-sm font-normal text-muted-foreground">/mo</span></div>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>• Everything in Professional, plus:</p>
          <p>• Up to 10 team members</p>
          <p>• White-label options</p>
          <p>• Advanced integrations</p>
          <p>• Custom analytics dashboard</p>
          <p>• Volume-based incentives</p>
          <p>• Dedicated account manager</p>
          <p>• 24/7 priority support</p>
        </div>
      </div>
    </div>
  )
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create New Organization</DialogTitle>
          <DialogDescription>
            Add a new organization to the platform
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="flex items-center justify-between mb-6">
            <div className="flex gap-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'details' || step === 'plan' || step === 'admin' || step === 'confirmation' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}`}>
                <Building2 className="h-4 w-4" />
              </div>
              <div className="h-0.5 w-6 self-center bg-muted">
                {(step === 'plan' || step === 'admin' || step === 'confirmation') && <div className="h-full bg-primary"></div>}
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'plan' || step === 'admin' || step === 'confirmation' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}`}>
                <CreditCard className="h-4 w-4" />
              </div>
              <div className="h-0.5 w-6 self-center bg-muted">
                {(step === 'admin' || step === 'confirmation') && <div className="h-full bg-primary"></div>}
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'admin' || step === 'confirmation' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}`}>
                <User className="h-4 w-4" />
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              Step {step === 'details' ? '1' : step === 'plan' ? '2' : step === 'admin' ? '3' : '4'} of 4
            </div>
          </div>
          
          {step === 'details' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="org-name">Organization Name</Label>
                <Input 
                  id="org-name" 
                  placeholder="Acme Transportation Co." 
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e?.target?.value)}
                />
                <p className="text-xs text-muted-foreground">The organization or company name</p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="org-domain">Domain Prefix</Label>
                <div className="flex">
                  <Input 
                    id="org-domain" 
                    placeholder="acme" 
                    value={formData.domain}
                    onChange={(e) => updateFormData('domain', e?.target?.value)}
                    className="rounded-r-none"
                  />
                  <div className="flex items-center px-3 border border-l-0 rounded-r-md bg-muted">
                    .transflow.app
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">Will be used for organization URL: {formData.domain ? formData?.domain?.toLowerCase() : 'yourcompany'}.transflow.app</p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="org-industry">Industry</Label>
                <Select value={formData.industry} onValueChange={(value) => updateFormData('industry', value)}>
                  <SelectTrigger id="org-industry">
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="logistics">Logistics</SelectItem>
                    <SelectItem value="transportation">Transportation</SelectItem>
                    <SelectItem value="tourism">Tourism</SelectItem>
                    <SelectItem value="corporate">Corporate</SelectItem>
                    <SelectItem value="events">Events Management</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          
          {step === 'plan' && (
            <div className="space-y-4">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Select a Plan</h3>
                <p className="text-sm text-muted-foreground">Choose the appropriate subscription plan for this organization.</p>
              </div>
              
              <PlansTab />
            </div>
          )}
          
          {step === 'admin' && (
            <div className="space-y-4">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Organization Admin User</h3>
                <p className="text-sm text-muted-foreground">Create the initial admin user for this organization.</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="admin-first-name">First Name</Label>
                  <Input 
                    id="admin-first-name" 
                    placeholder="John" 
                    value={formData.adminFirstName}
                    onChange={(e) => updateFormData('adminFirstName', e?.target?.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="admin-last-name">Last Name</Label>
                  <Input 
                    id="admin-last-name" 
                    placeholder="Doe" 
                    value={formData.adminLastName}
                    onChange={(e) => updateFormData('adminLastName', e?.target?.value)}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="admin-email">Email Address</Label>
                <Input 
                  id="admin-email" 
                  type="email"
                  placeholder="<EMAIL>" 
                  value={formData.adminEmail}
                  onChange={(e) => updateFormData('adminEmail', e?.target?.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="admin-password">Initial Password</Label>
                <Input
                  id="admin-password"
                  type="password"
                  placeholder="••••••••"
                  value={formData.adminPassword}
                  onChange={(e) => updateFormData('adminPassword', e?.target?.value)}
                />
                <p className="text-xs text-muted-foreground">Leave blank to auto-generate. The admin will be prompted to change this password on first login.</p>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Role & Permissions</h4>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="admin-role">Admin Role</Label>
                    <Select
                      value={formData.adminRole || 'ADMIN'}
                      onValueChange={(value) => updateFormData('adminRole', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ADMIN">Administrator</SelectItem>
                        <SelectItem value="MANAGER">Manager</SelectItem>
                        <SelectItem value="MEMBER">Member</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="permission-template">Permission Template</Label>
                    <Select
                      value={formData.permissionTemplate || 'admin'}
                      onValueChange={(value) => updateFormData('permissionTemplate', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select template" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">Full Admin Access</SelectItem>
                        <SelectItem value="manager">Manager Access</SelectItem>
                        <SelectItem value="member">Basic Member Access</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex flex-row items-center space-x-3 space-y-0">
                    <Checkbox
                      checked={formData.sendInvitation !== false}
                      onCheckedChange={(checked) => updateFormData('sendInvitation', checked)}
                    />
                    <div className="space-y-1 leading-none">
                      <Label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                        Send invitation email
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Send login credentials and welcome email to the admin user
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {step === 'confirmation' && (
            <div className="space-y-6">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Confirm Organization Details</h3>
                <p className="text-sm text-muted-foreground">Please review the information before creating the organization.</p>
              </div>
              
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-md">
                  <h4 className="font-medium mb-2">Organization Information</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="text-muted-foreground">Name:</div>
                    <div>{formData.name}</div>
                    
                    <div className="text-muted-foreground">Domain:</div>
                    <div>{formData?.domain?.toLowerCase()}.transflow.app</div>
                    
                    <div className="text-muted-foreground">Industry:</div>
                    <div className="capitalize">{formData.industry}</div>
                    
                    <div className="text-muted-foreground">Plan:</div>
                    <div className="capitalize">{formData.plan}</div>
                  </div>
                </div>
                
                <div className="bg-muted p-4 rounded-md">
                  <h4 className="font-medium mb-2">Admin User</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="text-muted-foreground">Name:</div>
                    <div>{formData.adminFirstName} {formData.adminLastName}</div>
                    
                    <div className="text-muted-foreground">Email:</div>
                    <div>{formData.adminEmail}</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter className="flex justify-between items-center">
          {step !== 'details' ? (
            <Button type="button" variant="ghost" onClick={prevStep}>
              Back
            </Button>
          ) : (
            <div></div>
          )}
          
          {step !== 'confirmation' ? (
            <Button type="button" onClick={nextStep} disabled={!validateStep()}>
              Continue <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button type="button" onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Organization"}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}