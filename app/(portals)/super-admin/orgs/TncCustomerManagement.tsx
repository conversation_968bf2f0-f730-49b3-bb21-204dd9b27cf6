"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Building2, Plus, Users, Eye, Settings, Loader2 } from "lucide-react";

interface TncCustomer {
  id: string;
  name: string;
  slug: string;
  account_type: string;
  parent_tnc_id: string;
  managed_by: string;
  status: string;
  business_type?: string;
  industry?: string;
  primary_contact_name: string;
  primary_contact_email: string;
  created_at: string;
  updated_at: string;
}

interface TncOrganization {
  id: string;
  name: string;
  account_type: string;
}

interface TncCustomerManagementProps {
  tncOrganizations: TncOrganization[];
  onRefresh: () => void;
}

export function TncCustomerManagement({ tncOrganizations, onRefresh }: TncCustomerManagementProps) {
  const [selectedTnc, setSelectedTnc] = useState<string>("");
  const [customers, setCustomers] = useState<TncCustomer[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [creating, setCreating] = useState(false);
  const { toast } = useToast();

  // Form state for creating new TNC customer
  const [newCustomer, setNewCustomer] = useState({
    name: "",
    primary_contact_name: "",
    primary_contact_email: "",
    business_type: "",
    industry: ""
  });

  // Fetch TNC customers when TNC is selected
  const fetchTncCustomers = async (tncId: string) => {
    if (!tncId) {
      setCustomers([]);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/super-admin/tnc-customers?tnc_id=${tncId}`);
      const data = await response.json();

      if (data.success) {
        setCustomers(data.customers || []);
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch TNC customers",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching TNC customers:", error);
      toast({
        title: "Error",
        description: "Failed to fetch TNC customers",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Create new TNC customer
  // Handle portal provisioning
  const handleProvisionPortal = async (customerId: string) => {
    try {
      const response = await fetch('/api/super-admin/tnc-portal-provisioning', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customer_org_id: customerId,
          parent_tnc_id: selectedTnc,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: `Portal provisioned successfully! URL: ${data.portal_url}`,
        });
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to provision portal",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error provisioning portal:", error);
      toast({
        title: "Error",
        description: "Failed to provision portal",
        variant: "destructive",
      });
    }
  };

  // Handle view portal configuration
  const handleViewPortal = async (customerId: string) => {
    try {
      const response = await fetch(`/api/super-admin/tnc-portal-provisioning?customer_org_id=${customerId}`);
      const data = await response.json();

      if (data.success && data.portal_configuration) {
        toast({
          title: "Portal Information",
          description: `Portal URL: ${data.portal_url || 'Not provisioned yet'}`,
        });
      } else {
        toast({
          title: "Info",
          description: "Portal not yet provisioned for this customer",
        });
      }
    } catch (error) {
      console.error("Error fetching portal config:", error);
      toast({
        title: "Error",
        description: "Failed to fetch portal configuration",
        variant: "destructive",
      });
    }
  };

  const createTncCustomer = async () => {
    if (!selectedTnc || !newCustomer.name || !newCustomer.primary_contact_name || !newCustomer.primary_contact_email) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setCreating(true);
    try {
      const response = await fetch('/api/super-admin/tnc-customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newCustomer,
          parent_tnc_id: selectedTnc,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Success",
          description: `TNC customer "${newCustomer.name}" created successfully`,
        });
        
        // Reset form and close dialog
        setNewCustomer({
          name: "",
          primary_contact_name: "",
          primary_contact_email: "",
          business_type: "",
          industry: ""
        });
        setShowCreateDialog(false);
        
        // Refresh customers list
        fetchTncCustomers(selectedTnc);
        onRefresh();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to create TNC customer",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error creating TNC customer:", error);
      toast({
        title: "Error",
        description: "Failed to create TNC customer",
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  // Handle TNC selection change
  const handleTncChange = (tncId: string) => {
    setSelectedTnc(tncId);
    fetchTncCustomers(tncId);
  };

  const getAccountTypeBadge = (accountType: string) => {
    const variants = {
      'tnc_customer': 'secondary',
      'tnc_account': 'default',
      'direct_client': 'outline',
      'transflow_super_admin': 'destructive'
    } as const;
    
    return (
      <Badge variant={variants[accountType as keyof typeof variants] || 'outline'}>
        {accountType?.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            TNC Customer Management
          </CardTitle>
          <CardDescription>
            Manage customer accounts for Transportation Network Companies (TNCs)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* TNC Selection */}
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Label htmlFor="tnc-select">Select TNC Organization</Label>
              <Select value={selectedTnc} onValueChange={handleTncChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a TNC to manage customers..." />
                </SelectTrigger>
                <SelectContent>
                  {tncOrganizations.map((tnc) => (
                    <SelectItem key={tnc.id} value={tnc.id}>
                      {tnc.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {selectedTnc && (
              <Button 
                onClick={() => setShowCreateDialog(true)}
                className="mt-6"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Customer
              </Button>
            )}
          </div>

          {/* Customers Table */}
          {selectedTnc && (
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer Name</TableHead>
                    <TableHead>Account Type</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Industry</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                        <p className="mt-2 text-sm text-muted-foreground">Loading customers...</p>
                      </TableCell>
                    </TableRow>
                  ) : customers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <Building2 className="h-8 w-8 mx-auto text-muted-foreground" />
                        <p className="mt-2 text-sm text-muted-foreground">No customers found</p>
                      </TableCell>
                    </TableRow>
                  ) : (
                    customers.map((customer) => (
                      <TableRow key={customer.id}>
                        <TableCell className="font-medium">{customer.name}</TableCell>
                        <TableCell>{getAccountTypeBadge(customer.account_type)}</TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{customer.primary_contact_name}</div>
                            <div className="text-muted-foreground">{customer.primary_contact_email}</div>
                          </div>
                        </TableCell>
                        <TableCell>{customer.industry || 'N/A'}</TableCell>
                        <TableCell>
                          <Badge variant={customer.status === 'active' ? 'default' : 'secondary'}>
                            {customer.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{new Date(customer.created_at).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleViewPortal(customer.id)}
                              title="View Portal Configuration"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleProvisionPortal(customer.id)}
                              title="Provision Portal"
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create TNC Customer Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create TNC Customer</DialogTitle>
            <DialogDescription>
              Add a new customer account under the selected TNC organization.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="customer-name">Customer Name *</Label>
              <Input
                id="customer-name"
                value={newCustomer.name}
                onChange={(e) => setNewCustomer(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter customer organization name"
              />
            </div>
            
            <div>
              <Label htmlFor="contact-name">Primary Contact Name *</Label>
              <Input
                id="contact-name"
                value={newCustomer.primary_contact_name}
                onChange={(e) => setNewCustomer(prev => ({ ...prev, primary_contact_name: e.target.value }))}
                placeholder="Enter contact person name"
              />
            </div>
            
            <div>
              <Label htmlFor="contact-email">Primary Contact Email *</Label>
              <Input
                id="contact-email"
                type="email"
                value={newCustomer.primary_contact_email}
                onChange={(e) => setNewCustomer(prev => ({ ...prev, primary_contact_email: e.target.value }))}
                placeholder="Enter contact email"
              />
            </div>
            
            <div>
              <Label htmlFor="business-type">Business Type</Label>
              <Select 
                value={newCustomer.business_type} 
                onValueChange={(value) => setNewCustomer(prev => ({ ...prev, business_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select business type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hotel">Hotel</SelectItem>
                  <SelectItem value="event_management">Event Management</SelectItem>
                  <SelectItem value="corporate">Corporate</SelectItem>
                  <SelectItem value="travel_agency">Travel Agency</SelectItem>
                  <SelectItem value="wedding_planner">Wedding Planner</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="industry">Industry</Label>
              <Input
                id="industry"
                value={newCustomer.industry}
                onChange={(e) => setNewCustomer(prev => ({ ...prev, industry: e.target.value }))}
                placeholder="Enter industry (optional)"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={createTncCustomer} disabled={creating}>
              {creating && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Create Customer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}