'use client'

import React from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/app/components/ui/card'
import { EmbeddableFormManager } from '@/app/components/embeddable-forms/EmbeddableFormManager'
import { Badge } from '@/app/components/ui/badge'
import { Button } from '@/app/components/ui/button'
import { ArrowLeft, Settings, Eye, BarChart3 } from 'lucide-react'
import Link from 'next/link'

interface OrgEmbeddableFormsPageProps {
  params: {
    id: string
  }
}

export default function OrgEmbeddableFormsPage({ params }: OrgEmbeddableFormsPageProps) {
  const { id } = params

  // In a real app, you'd fetch org data
  const orgName = "Sample Organization" // This should come from API
  const orgType = "Hotel" // This should come from API

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link 
          href="/super-admin/orgs" 
          className="flex items-center hover:text-foreground transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Organizations
        </Link>
        <span>/</span>
        <span>{orgName}</span>
        <span>/</span>
        <span className="text-foreground">Embeddable Forms</span>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Embeddable Forms</h1>
          <p className="text-muted-foreground mt-2">
            Manage booking forms for {orgName}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">{orgType}</Badge>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Organization Context Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Organization: {orgName}</span>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button variant="outline" size="sm">
                <BarChart3 className="h-4 w-4 mr-2" />
                Analytics
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-primary">12</div>
              <div className="text-sm text-muted-foreground">Active Forms</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-green-600">1,234</div>
              <div className="text-sm text-muted-foreground">Total Submissions</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-blue-600">89%</div>
              <div className="text-sm text-muted-foreground">Conversion Rate</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Embeddable Form Manager */}
      <Card>
        <CardHeader>
          <CardTitle>Form Management</CardTitle>
          <p className="text-sm text-muted-foreground">
            Create and manage embeddable booking forms for this organization
          </p>
        </CardHeader>
        <CardContent>
          <EmbeddableFormManager organizationId={id} />
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <div className="text-2xl mb-2">🎨</div>
            <h3 className="font-semibold mb-1">Customize Branding</h3>
            <p className="text-sm text-muted-foreground">Update colors, logos, and styling</p>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <div className="text-2xl mb-2">📊</div>
            <h3 className="font-semibold mb-1">View Analytics</h3>
            <p className="text-sm text-muted-foreground">Track form performance</p>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <div className="text-2xl mb-2">🔗</div>
            <h3 className="font-semibold mb-1">Integration Guide</h3>
            <p className="text-sm text-muted-foreground">Learn how to embed forms</p>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <div className="text-2xl mb-2">⚙️</div>
            <h3 className="font-semibold mb-1">Advanced Settings</h3>
            <p className="text-sm text-muted-foreground">Configure form behavior</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between py-2 border-b">
              <div>
                <p className="font-medium">New form submission</p>
                <p className="text-sm text-muted-foreground">Hotel Booking Form - 2 minutes ago</p>
              </div>
              <Badge variant="outline">New</Badge>
            </div>
            <div className="flex items-center justify-between py-2 border-b">
              <div>
                <p className="font-medium">Form updated</p>
                <p className="text-sm text-muted-foreground">Event Booking Form - 1 hour ago</p>
              </div>
              <Badge variant="secondary">Updated</Badge>
            </div>
            <div className="flex items-center justify-between py-2">
              <div>
                <p className="font-medium">New form created</p>
                <p className="text-sm text-muted-foreground">Corporate Event Form - 3 hours ago</p>
              </div>
              <Badge variant="outline">Created</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
