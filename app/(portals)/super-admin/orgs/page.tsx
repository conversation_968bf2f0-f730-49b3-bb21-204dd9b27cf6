// ---
// SYSTEM ENTITY SUMMARY
//
// Tenants: Isolated workspaces for each client/affiliate/network. All business data is scoped by organization_id.
// Users: Authenticated individuals. Linked to tenants via tenant_users (can belong to multiple tenants).
// Orgs/Companies: Business entities (e.g., affiliate companies, client companies). May be linked to tenants or used for grouping.
//
// Typical Flow:
// - Super Admin creates a tenant (<PERSON>lient, Affiliate, TNC, White Label, etc.).
// - Super Admin or Tenant Admin invites/creates users and assigns them to the tenant.
// - Users may be further associated with organizations/companies for business logic, reporting, or permissions.
// ---

"use client";

import { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Plus, MoreHorizontal, Building2, Edit, EyeIcon, UserPlus, Shield, CreditCard, BarChart3, FileText, Clock, CheckCircle, AlertTriangle, DollarSign, ArrowUpCircle, ArrowDownCircle, Building, Users, PlusCircle, Briefcase, Search, Filter, SortAsc, SortDesc, RefreshCw, Download, Upload, Trash2, Archive, Settings2, TrendingUp, Activity, Globe, MapPin, Calendar, Loader2 } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { TrialManagementCard } from "@/app/(portals)/super-admin/subscriptions/TrialManagementCard";
import { useToast } from "@/components/ui/use-toast";
import { OrgDetailsDrawer } from "./OrgDetailsDrawer";
import { CreateOrgForm } from "./CreateOrgForm";
import { TncCustomerManagement } from "./TncCustomerManagement";

// Types for real data
interface Organization {
  id: string;
  name: string;
  slug?: string;
  description?: string;
  industry?: string;
  logo_url?: string;
  website?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  phone?: string;
  email: string;
  organization_id: string;
  status: string;
  settings?: any;
  created_at: string;
  updated_at?: string;
  users_count?: number;
  last_active?: string;
  plan?: string;
  // Four-tier architecture fields
  account_type?: 'transflow_super_admin' | 'tnc_account' | 'tnc_customer' | 'direct_client';
  parent_tnc_id?: string;
  managed_by?: 'transflow' | 'tnc';
  organization_type?: 'shared' | 'segregated' | 'isolated';
}

interface Subscription {
  id: string;
  org_id: string;
  org_name: string;
  plan: string;
  status: string;
  billing_cycle: string;
  amount: number;
  start_date: string;
  next_billing_date: string;
  payment_method: string;
}

export default function OrgsAndSubscriptionsPage() {
  const { toast } = useToast();
  const [tab, setTab] = useState("orgs");
  const [searchQuery, setSearchQuery] = useState("");
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [filteredOrgs, setFilteredOrgs] = useState<Organization[]>([]);
  const [filteredSubscriptions, setFilteredSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateOrgDialog, setShowCreateOrgDialog] = useState(false);
  const [showCreateSubscriptionDialog, setShowCreateSubscriptionDialog] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [orgDetailsOpen, setOrgDetailsOpen] = useState(false);

  // Get TNC organizations for customer management
  const tncOrganizations = organizations.filter(org => 
    org.account_type === 'tnc_account' || org.account_type === 'transflow_super_admin'
  ).map(org => ({
    id: org.id,
    name: org.name,
    account_type: org.account_type || 'unknown'
  }));
  
  // Enhanced filtering and sorting
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [industryFilter, setIndustryFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("created_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [selectedOrgs, setSelectedOrgs] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  
  // Analytics state
  const [analytics, setAnalytics] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    growth: 0,
    recentActivity: 0
  });

  // Enhanced fetch organizations with analytics
  const fetchOrganizations = useCallback(async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      
      const response = await fetch('/api/super-admin/organizations');
      if (!response.ok) {
        throw new Error('Failed to fetch organizations');
      }
      const data = await response.json();
      if (data.success) {
        setOrganizations(data.organizations);
        
        // Calculate analytics
        const orgs = data.organizations;
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        setAnalytics({
          total: orgs.length,
          active: orgs.filter((org: Organization) => org.status === 'active').length,
          inactive: orgs.filter((org: Organization) => org.status !== 'active').length,
          growth: orgs.filter((org: Organization) => new Date(org.created_at) > thirtyDaysAgo).length,
          recentActivity: orgs.filter((org: Organization) => 
            org.last_active && new Date(org.last_active) > new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          ).length
        });
      }
    } catch (error) {
      console.error('Error fetching organizations:', error);

      // Fallback: Show recently created organizations from localStorage
      const recentOrgs = getRecentlyCreatedOrgs();
      if (recentOrgs.length > 0) {
        setOrganizations(recentOrgs);
        toast({
          title: "Using Cached Data",
          description: "Showing recently created organizations. Database connection issue detected.",
          variant: "default",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to load organizations. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [toast]);

  // Helper function to get recently created organizations from localStorage
  const getRecentlyCreatedOrgs = (): Organization[] => {
    try {
      const stored = localStorage.getItem('recentlyCreatedOrgs');
      if (stored) {
        const orgs = JSON.parse(stored);
        // Return organizations created in the last 24 hours
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return orgs.filter((org: Organization) => new Date(org.created_at) > oneDayAgo);
      }
    } catch (error) {
      console.error('Error reading from localStorage:', error);
    }
    return [];
  };

  // Helper function to store newly created organization
  const storeRecentlyCreatedOrg = (org: Organization) => {
    try {
      const existing = getRecentlyCreatedOrgs();
      const updated = [org, ...existing.filter(o => o.id !== org.id)].slice(0, 10); // Keep last 10
      localStorage.setItem('recentlyCreatedOrgs', JSON.stringify(updated));
    } catch (error) {
      console.error('Error storing to localStorage:', error);
    }
  };

  // Fetch subscriptions from API (placeholder - implement when subscription API is ready)
  const fetchSubscriptions = async () => {
    try {
      // TODO: Implement subscription API endpoint
      // For now, we'll use empty array
      setSubscriptions([]);
      setFilteredSubscriptions([]);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchOrganizations();
    fetchSubscriptions();
  }, [fetchOrganizations]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchOrganizations(true);
    }, 30000);

    return () => clearInterval(interval);
  }, [fetchOrganizations]);

  // Enhanced filtering and sorting
  useEffect(() => {
    let filtered = [...organizations];

    // Apply search filter
    if (searchQuery.trim() !== "") {
      filtered = filtered.filter(org =>
        org?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (org.industry && org?.industry?.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (org.email && org?.email?.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (org.city && org?.city?.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(org => org.status === statusFilter);
    }

    // Apply industry filter
    if (industryFilter !== "all") {
      filtered = filtered.filter(org => org.industry === industryFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof Organization];
      let bValue: any = b[sortBy as keyof Organization];

      // Handle date sorting
      if (sortBy === 'created_at' || sortBy === 'updated_at') {
        aValue = new Date(aValue || 0).getTime();
        bValue = new Date(bValue || 0).getTime();
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = (bValue || '').toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredOrgs(filtered);

    // Filter subscriptions
    let filteredSubs = [...subscriptions];
    if (searchQuery.trim() !== "") {
      filteredSubs = filteredSubs.filter(sub =>
        sub?.org_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        sub?.plan?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        sub?.status?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    setFilteredSubscriptions(filteredSubs);
  }, [searchQuery, organizations, subscriptions, statusFilter, industryFilter, sortBy, sortOrder]);

  const formatDate = (dateString: string) => new Intl.DateTimeFormat('en-US', { year: 'numeric', month: 'short', day: 'numeric' }).format(new Date(dateString));
  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const handleOrgCreated = (newOrg: Organization) => {
    // Store the new organization in localStorage for fallback
    storeRecentlyCreatedOrg(newOrg);

    // Refresh the organizations list from the API
    fetchOrganizations();
    toast({
      title: "Organization Created",
      description: `${newOrg.name} has been successfully created.`,
    });
  };

  const handleViewOrgDetails = (org: Organization) => {
    setSelectedOrg(org);
    setOrgDetailsOpen(true);
  };

  // Enhanced handlers
  const handleRefresh = () => {
    fetchOrganizations(true);
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const handleSelectOrg = (orgId: string, checked: boolean) => {
    if (checked) {
      setSelectedOrgs([...selectedOrgs, orgId]);
    } else {
      setSelectedOrgs(selectedOrgs.filter(id => id !== orgId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedOrgs(filteredOrgs.map(org => org.id));
    } else {
      setSelectedOrgs([]);
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedOrgs.length === 0) return;

    try {
      const response = await fetch('/api/super-admin/organizations/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          organizationIds: selectedOrgs
        })
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: `Bulk ${action} completed for ${selectedOrgs.length} organizations.`,
        });
        setSelectedOrgs([]);
        fetchOrganizations(true);
      } else {
        throw new Error(`Failed to ${action} organizations`);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${action} organizations. Please try again.`,
        variant: "destructive",
      });
    }
  };

  // Get unique industries for filter
  const uniqueIndustries = Array.from(new Set(organizations.map(org => org.industry).filter(Boolean)));

  // Analytics cards data
  const analyticsCards = [
    {
      title: "Total Organizations",
      value: analytics.total,
      icon: Building2,
      trend: analytics.growth > 0 ? "up" : "stable",
      trendValue: analytics.growth,
      description: `${analytics.growth} new this month`
    },
    {
      title: "Active Organizations",
      value: analytics.active,
      icon: CheckCircle,
      trend: "up",
      trendValue: Math.round((analytics.active / analytics.total) * 100) || 0,
      description: `${Math.round((analytics.active / analytics.total) * 100) || 0}% of total`
    },
    {
      title: "Recent Activity",
      value: analytics.recentActivity,
      icon: Activity,
      trend: "up",
      trendValue: analytics.recentActivity,
      description: "Active in last 7 days"
    },
    {
      title: "Growth Rate",
      value: `${analytics.growth}`,
      icon: TrendingUp,
      trend: analytics.growth > 0 ? "up" : "stable",
      trendValue: analytics.growth,
      description: "Organizations this month"
    }
  ];

  // --- Real Stats Data ---
  const orgStats = {
    totalOrgs: organizations.length,
    activeSubscriptions: subscriptions.filter(sub => sub.status === 'active').length,
    trialOrgs: organizations.filter(org => org.status === 'active').length,
    avgSubValue: subscriptions.length > 0
      ? Math.round(subscriptions.reduce((sum, sub) => sum + sub.amount, 0) / subscriptions.length)
      : 0
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Organizations & Subscriptions</h1>
          <p className="text-muted-foreground">
            Manage tenant organizations and their subscription plans
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
            {refreshing ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <Button onClick={() => setShowCreateOrgDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Organization
          </Button>
        </div>
      </div>

      {/* Enhanced Analytics Dashboard */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {analyticsCards.map((card, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
              <card.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                {card.trend === "up" ? (
                  <ArrowUpCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownCircle className="h-3 w-3 text-gray-500" />
                )}
                <span>{card.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enhanced Search and Filters */}
      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search organizations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e?.target?.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
          </SelectContent>
        </Select>
        <Select value={industryFilter} onValueChange={setIndustryFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by industry" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Industries</SelectItem>
            {uniqueIndustries.map((industry) => (
              <SelectItem key={industry} value={industry}>
                {industry}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Sort
              {sortOrder === 'asc' ? (
                <SortAsc className="ml-2 h-4 w-4" />
              ) : (
                <SortDesc className="ml-2 h-4 w-4" />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Sort by</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handleSort('name')}>
              Name
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleSort('created_at')}>
              Created Date
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleSort('status')}>
              Status
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleSort('industry')}>
              Industry
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Bulk Actions Bar */}
      {selectedOrgs.length > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">
                {selectedOrgs.length} organization{selectedOrgs.length > 1 ? 's' : ''} selected
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('activate')}
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                Activate
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('deactivate')}
              >
                <Archive className="mr-2 h-4 w-4" />
                Deactivate
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('export')}
              >
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedOrgs([])}
              >
                Clear Selection
              </Button>
            </div>
          </div>
        </Card>
      )}

      <Tabs value={tab} onValueChange={setTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="orgs">Organizations ({filteredOrgs.length})</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions ({filteredSubscriptions.length})</TabsTrigger>
          <TabsTrigger value="tnc-customers">TNC Customers ({tncOrganizations.length})</TabsTrigger>
          <TabsTrigger value="trial">Trial Management</TabsTrigger>
        </TabsList>

        <TabsContent value="orgs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Organizations</CardTitle>
              <CardDescription>Manage organizations and their details</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Org</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Plan</TableHead>
                    <TableHead>Users</TableHead>
                    <TableHead>Industry</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Last Active</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">Loading organizations...</TableCell>
                    </TableRow>
                  ) : filteredOrgs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">No organizations found</TableCell>
                    </TableRow>
                  ) : (
                    filteredOrgs.map(org => (
                      <TableRow key={org.id}>
                        <TableCell>{org.name}</TableCell>
                        <TableCell>
                          <Badge variant={org.status === 'active' ? 'default' : org.status === 'pending' ? 'secondary' : 'destructive'}>
                            {org.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{org.settings?.subscription?.plan || 'Not set'}</TableCell>
                        <TableCell>{org.users_count || 0}</TableCell>
                        <TableCell>{org.industry || 'Not specified'}</TableCell>
                        <TableCell>{formatDate(org.created_at)}</TableCell>
                        <TableCell>{org.updated_at ? formatDate(org.updated_at) : 'Never'}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewOrgDetails(org)}>
                                <EyeIcon className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Org
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <UserPlus className="mr-2 h-4 w-4" />
                                Manage Users
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Shield className="mr-2 h-4 w-4" />
                                {org.status === 'suspended' ? 'Activate' : 'Suspend'}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Subscriptions</CardTitle>
              <CardDescription>Manage subscription plans and billing cycles</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Org</TableHead>
                    <TableHead>Plan</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Billing Cycle</TableHead>
                    <TableHead>Next Billing</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSubscriptions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">No subscriptions found</TableCell>
                    </TableRow>
                  ) : (
                    filteredSubscriptions.map(sub => (
                      <TableRow key={sub.id}>
                        <TableCell>{sub.org_name}</TableCell>
                        <TableCell>{sub.plan}</TableCell>
                        <TableCell>{sub.status}</TableCell>
                        <TableCell>{formatCurrency(sub.amount)}</TableCell>
                        <TableCell>{sub.billing_cycle}</TableCell>
                        <TableCell>{formatDate(sub.next_billing_date)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <EyeIcon className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Subscription
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <CreditCard className="mr-2 h-4 w-4" />
                                View Payment History
                              </DropdownMenuItem>
                              {sub.status === 'active' && (
                                <DropdownMenuItem className="text-amber-600">
                                  <Clock className="mr-2 h-4 w-4" />
                                  Change Plan
                                </DropdownMenuItem>
                              )}
                              {sub.status !== 'canceled' && (
                                <DropdownMenuItem className="text-red-600">
                                  <AlertTriangle className="mr-2 h-4 w-4" />
                                  Cancel Subscription
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tnc-customers" className="space-y-4">
          <TncCustomerManagement 
            tncOrganizations={tncOrganizations}
            onRefresh={() => fetchOrganizations(true)}
          />
        </TabsContent>

        <TabsContent value="trial" className="space-y-4">
          <TrialManagementCard />
        </TabsContent>
      </Tabs>

      {/* Drawer and Dialog Components */}
      <OrgDetailsDrawer 
        open={orgDetailsOpen}
        onOpenChange={setOrgDetailsOpen}
        org={selectedOrg}
      />
      
      <CreateOrgForm 
        open={showCreateOrgDialog}
        onOpenChange={setShowCreateOrgDialog}
        onSuccess={handleOrgCreated}
      />
    </div>
  );
} 