"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>etHeader, She<PERSON><PERSON><PERSON><PERSON>, SheetClose } from "@/app/components/ui/sheet"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Button } from "@/app/components/ui/button"
import { User, Users, CreditCard, Settings, LogIn, UserPlus, MoreHorizontal, ArrowRight, Check, Calendar, Clock, AlertTriangle, DollarSign, Download, Code, BarChart3, Loader2, Plus, Shield } from "lucide-react"
import { Card, CardHeader, CardTitle, CardContent } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/app/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/app/components/ui/dropdown-menu"
import { useToast } from "@/app/components/ui/use-toast"
import { ScrollArea } from "@/app/components/ui/scroll-area"
import { Switch } from "@/app/components/ui/switch"
import { Label } from "@/app/components/ui/label"
import { Textarea } from "@/app/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group"
import { useRouter } from "next/navigation"
import { Input } from "@/app/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/app/components/ui/dialog"
import Link from "next/link"
import { OrganizationPermissionsTab } from "@/app/components/admin/organizations/OrganizationPermissionsTab"

interface OrgDetailsDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  org: {
    id: string
    name: string
    slug?: string
    description?: string
    industry?: string
    logo_url?: string
    website?: string
    address?: string
    city?: string
    state?: string
    zip?: string
    country?: string
    phone?: string
    email: string
    organization_id: string
    domain?: string
    status: string
    plan?: string
    created_at: string
    updated_at?: string
    branding?: any
    settings?: any
    users_count?: number
  } | null
}

export function OrgDetailsDrawer({ open, onOpenChange, org }: OrgDetailsDrawerProps) {
  const { toast } = useToast()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [orgData, setOrgData] = useState<any>(null)
  const [users, setUsers] = useState<any[]>([])
  const [subscription, setSubscription] = useState<any>(null)
  const [analytics, setAnalytics] = useState<any>(null)
  const [embeddableForms, setEmbeddableForms] = useState<any>(null)

  // Modal states
  const [showAddUserModal, setShowAddUserModal] = useState(false)
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false)
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false)
  const [showExportModal, setShowExportModal] = useState(false)
  const [showBrandingModal, setShowBrandingModal] = useState(false)

  // Form states
  const [newUserEmail, setNewUserEmail] = useState('')
  const [newUserRole, setNewUserRole] = useState('')
  const [sendInvitation, setSendInvitation] = useState(true)

  // Branding form states
  const [brandingData, setBrandingData] = useState({
    logo_url: '',
    primary_color: '#3b82f6',
    secondary_color: '#64748b',
    background_color: '#ffffff',
    custom_css: ''
  })

  // Fetch detailed organization data when drawer opens
  useEffect(() => {
    if (open && org?.id) {
      fetchOrgDetails()
    }
  }, [open, org?.id])

  const fetchOrgDetails = async () => {
    if (!org?.id) return

    try {
      setLoading(true)
      const response = await fetch(`/api/super-admin/organizations/${org.id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch organization details')
      }
      const result = await response.json()
      // Handle both old and new API response formats
      if (result.success && result.data) {
        setOrgData(result.data.organization)
        setUsers(result.data.users || [])
        setSubscription(result.data.subscription)
        setAnalytics(result.data.analytics)
        setEmbeddableForms(result.data.embeddable_forms)
      } else if (result.organization) {
        // New API format
        setOrgData(result.organization)
        setUsers(result.users || [])
        setSubscription(result.subscription)
        setAnalytics(result.analytics)
        setEmbeddableForms(result.embeddable_forms)
      } else {
        throw new Error('Invalid API response format')
      }
    } catch (error) {
      console.error('Error fetching organization details:', error)
      toast({
        title: "Error",
        description: "Failed to load organization details. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (!org) return null



  const handleAddUser = () => {
    setShowAddUserModal(true)
  }

  const handleViewFullSubscription = () => {
    setShowSubscriptionModal(true)
  }

  const handleChangePlan = () => {
    setShowSubscriptionModal(true)
  }

  const handleViewAnalytics = () => {
    setShowAnalyticsModal(true)
  }

  const handleExportData = () => {
    setShowExportModal(true)
  }

  const handleSaveSettings = async () => {
    try {
      // TODO: Implement settings save API call
      toast({
        title: "Settings Saved",
        description: "Organization settings have been updated successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save settings",
        variant: "destructive",
      })
    }
  }

  const handleEditBranding = async () => {
    // Load current branding data
    try {
      const response = await fetch(`/api/super-admin/organizations/${org.id}/branding`)
      if (response.ok) {
        const data = await response.json()
        setBrandingData({
          logo_url: data.logo_url || '',
          primary_color: data.primary_color || '#3b82f6',
          secondary_color: data.secondary_color || '#64748b',
          background_color: data.background_color || '#ffffff',
          custom_css: data.custom_css || ''
        })
      }
    } catch (error) {
      console.error('Error loading branding data:', error)
    }
    setShowBrandingModal(true)
  }

  const handleSaveBranding = async () => {
    try {
      const response = await fetch(`/api/super-admin/organizations/${org.id}/branding`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(brandingData)
      })

      if (response.ok) {
        toast({
          title: "Branding Updated",
          description: "Organization branding has been updated successfully.",
        })
        setShowBrandingModal(false)
        fetchOrgDetails() // Refresh the organization data
      } else {
        throw new Error('Failed to update branding')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update branding settings",
        variant: "destructive",
      })
    }
  }

  const handleImpersonate = async () => {
    try {
      // TODO: Implement impersonation logic
      toast({
        title: "Impersonation Started",
        description: `Now viewing as ${org.name}`,
      })
      // Redirect to the organization's portal
      router.push('/event-manager')
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to impersonate organization",
        variant: "destructive",
      })
    }
  }

  const handleSelectPlan = async (planName: string) => {
    try {
      // TODO: Implement plan change API call
      const response = await fetch('/api/super-admin/organizations/change-plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          organization_id: org.id,
          new_plan: planName
        })
      })

      if (response.ok) {
        toast({
          title: "Plan Changed",
          description: `Successfully changed to ${planName} plan.`,
        })
        setShowSubscriptionModal(false)
        fetchOrgDetails() // Refresh the organization data
      } else {
        throw new Error('Failed to change plan')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to change subscription plan",
        variant: "destructive",
      })
    }
  }

  const handleToggleUserStatus = async (userId: string, currentStatus: string) => {
    try {
      // TODO: Implement user status toggle API call
      toast({
        title: "User Status Updated",
        description: `User status changed to ${currentStatus === 'Active' ? 'Inactive' : 'Active'}`,
      })
      // Refresh user data
      fetchOrgDetails()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user status",
        variant: "destructive",
      })
    }
  }





  return (
    <>
      <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="!fixed !right-0 !top-0 !h-full !w-[95vw] !max-w-[95vw] !p-0 !m-0 !rounded-none !border-l-4 !border-l-blue-500 !bg-background !shadow-2xl !flex !flex-col !animate-in !data-[state=open]:slide-in-from-right-80">
        <SheetHeader className="flex flex-row items-center justify-between px-6 pt-6 pb-2 border-b">
          <div>
            <SheetTitle className="text-2xl font-bold">{org.name}</SheetTitle>
            <div className="text-sm text-muted-foreground">{org.domain || "No domain set"}</div>
            <div className="flex gap-2 mt-2">
              <span className="px-2 py-0.5 rounded bg-primary/10 text-primary text-xs font-medium">{org.status}</span>
              <span className="px-2 py-0.5 rounded bg-secondary/10 text-secondary text-xs font-medium">{org.plan}</span>
            </div>
          </div>
          <div className="flex gap-2 items-center">
            <Button size="sm" variant="outline" className="gap-1" title="Impersonate Org" onClick={handleImpersonate}>
              <LogIn className="h-4 w-4" /> Impersonate
            </Button>
            <SheetClose asChild>
              <Button size="icon" variant="ghost" className="ml-2">
                <span className="sr-only">Close</span>
                ×
              </Button>
            </SheetClose>
          </div>
        </SheetHeader>
        <div className="flex-1 flex flex-col overflow-hidden">
          <Tabs defaultValue="overview" className="flex-1 flex flex-col">
            <TabsList className="flex gap-2 px-6 pt-4 pb-2 border-b bg-background sticky top-0 z-10">
              <TabsTrigger value="overview" className="flex gap-1 items-center"><User className="h-4 w-4" /> Overview</TabsTrigger>
              <TabsTrigger value="users" className="flex gap-1 items-center"><Users className="h-4 w-4" /> Users</TabsTrigger>
              <TabsTrigger value="permissions" className="flex gap-1 items-center"><Shield className="h-4 w-4" /> Permissions</TabsTrigger>
              <TabsTrigger value="subscription" className="flex gap-1 items-center"><CreditCard className="h-4 w-4" /> Subscription</TabsTrigger>
              <TabsTrigger value="embeddable-forms" className="flex gap-1 items-center"><Code className="h-4 w-4" /> Embeddable Forms</TabsTrigger>
              <TabsTrigger value="settings" className="flex gap-1 items-center"><Settings className="h-4 w-4" /> Settings</TabsTrigger>
            </TabsList>
            <TabsContent value="overview" className="space-y-6">
              <div className="p-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Org Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <dl className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Industry:</dt>
                          <dd className="font-medium">{orgData?.industry || org?.industry || "Not specified"}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Total Users:</dt>
                          <dd className="font-medium">{users?.length || 0}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Subdomain:</dt>
                          <dd className="font-medium flex items-center">
                            {orgData?.domain || org?.domain || "Not set"}
                            {(orgData?.domain || org?.domain) && (
                              <Button variant="ghost" size="icon" className="h-4 w-4 ml-1">
                                <ArrowRight className="h-3 w-3" />
                              </Button>
                            )}
                          </dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Created:</dt>
                          <dd className="font-medium">{new Date(orgData?.created_at || org?.created_at).toLocaleDateString()}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Last Updated:</dt>
                          <dd className="font-medium">{orgData?.updated_at ? new Date(orgData.updated_at).toLocaleDateString() : "N/A"}</dd>
                        </div>
                      </dl>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Subscription</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <dl className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Plan:</dt>
                          <dd>
                            <Badge
                              className={
                                (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise'
                                  ? 'bg-purple-100 text-purple-800'
                                  : (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional'
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-gray-100 text-gray-800'
                              }
                            >
                              {(subscription?.plan_name || orgData?.settings?.plan || org?.plan || 'Not set').charAt(0).toUpperCase() + (subscription?.plan_name || orgData?.settings?.plan || org?.plan || 'Not set').slice(1)}
                            </Badge>
                          </dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Status:</dt>
                          <dd className={`font-medium ${subscription?.status === 'active' ? 'text-green-600' : 'text-red-600'}`}>
                            {subscription?.status ? subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1) : 'Active'}
                          </dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Billing Cycle:</dt>
                          <dd className="font-medium">{subscription?.billing_cycle || 'Annual'}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-muted-foreground">Next Billing:</dt>
                          <dd className="font-medium">
                            {subscription?.current_period_end ? new Date(subscription.current_period_end).toLocaleDateString() : 'May 15, 2024'}
                          </dd>
                        </div>
                        <div>
                          <Button variant="outline" size="sm" className="mt-2 w-full" onClick={handleViewFullSubscription}>
                            <CreditCard className="h-4 w-4 mr-2" />
                            View Full Subscription
                          </Button>
                        </div>
                      </dl>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Activity & Usage</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid gap-4 grid-cols-3">
                        <div className="bg-muted p-3 rounded-md">
                          <div className="text-2xl font-bold">{analytics?.active_users || users?.length || 0}</div>
                          <div className="text-xs text-muted-foreground">Active Users</div>
                        </div>
                        <div className="bg-muted p-3 rounded-md">
                          <div className="text-2xl font-bold">{analytics?.quotes_this_month || 0}</div>
                          <div className="text-xs text-muted-foreground">Quotes This Month</div>
                        </div>
                        <div className="bg-muted p-3 rounded-md">
                          <div className="text-2xl font-bold">{analytics?.trips_completed || 0}</div>
                          <div className="text-xs text-muted-foreground">Trips Completed</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2 flex flex-row items-center justify-between">
                    <CardTitle className="text-sm font-medium">Branding & Customization</CardTitle>
                    <Button variant="outline" size="sm" onClick={handleEditBranding}>Edit</Button>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid gap-4 grid-cols-2">
                        <div>
                          <div className="text-sm font-medium mb-2">Logo</div>
                          <div className="h-24 bg-muted rounded-md flex items-center justify-center">
                            {orgData?.logo_url ? (
                              <img src={orgData.logo_url} alt="Logo" className="max-h-20" />
                            ) : (
                              <div className="text-xs text-muted-foreground">No logo set</div>
                            )}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium mb-2">Colors</div>
                          <div className="flex gap-2">
                            <div className="w-8 h-8 rounded-full bg-primary"></div>
                            <div className="w-8 h-8 rounded-full bg-secondary"></div>
                            <div className="w-8 h-8 rounded-full bg-muted"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="users" className="space-y-6">
              <div className="p-6">
                {loading && (
                  <div className="flex items-center justify-center p-8">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span>Loading users...</span>
                  </div>
                )}

                <div className="flex justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium">User Management</h3>
                    <p className="text-sm text-muted-foreground">{users.length} user{users.length !== 1 ? 's' : ''} in organization</p>
                  </div>
                  <Button size="sm" onClick={handleAddUser}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Add User
                  </Button>
                </div>

                <Card className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Organization Users</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>User</TableHead>
                          <TableHead>Organization Role</TableHead>
                          <TableHead>User Role</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Joined</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {users.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                              No users found in this organization
                            </TableCell>
                          </TableRow>
                        ) : (
                          users.map(user => (
                            <TableRow key={user.id}>
                              <TableCell>
                                <div className="flex items-center gap-3">
                                  <Avatar className="h-8 w-8">
                                    <AvatarImage src={`https://avatars.dicebear.com/api/initials/${(user.first_name?.[0] || '') + (user.last_name?.[0] || '')}.svg`} />
                                    <AvatarFallback>{(user.first_name?.[0] || '') + (user.last_name?.[0] || '')}</AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <div className="font-medium">{user.full_name || `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unknown'}</div>
                                    <div className="text-sm text-muted-foreground">{user.email}</div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline" className="font-normal">
                                  {user.organization_role || 'USER'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="secondary" className="font-normal">
                                  {user.user_role || 'USER'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant={user.status === "Active" ? "default" : "secondary"} className="font-normal">
                                  {user.status}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                              </TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                      <span className="sr-only">Open menu</span>
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuItem>Edit Role</DropdownMenuItem>
                                    <DropdownMenuItem>Reset Password</DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      className={user.status === "Active" ? "text-destructive" : ""}
                                      onClick={() => handleToggleUserStatus(user.id, user.status)}
                                    >
                                      {user.status === "Active" ? "Deactivate" : "Activate"}
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">User Roles & Permissions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="bg-muted p-4 rounded-lg">
                          <h4 className="font-medium mb-2">Admin</h4>
                          <p className="text-sm text-muted-foreground">Full access to all org settings, users, and data.</p>
                        </div>
                        <div className="bg-muted p-4 rounded-lg">
                          <h4 className="font-medium mb-2">Manager</h4>
                          <p className="text-sm text-muted-foreground">Can manage trips, quotes, and events but cannot change org settings.</p>
                        </div>
                        <div className="bg-muted p-4 rounded-lg">
                          <h4 className="font-medium mb-2">User</h4>
                          <p className="text-sm text-muted-foreground">Standard access to create and manage their own resources.</p>
                        </div>
                        <div className="bg-muted p-4 rounded-lg">
                          <h4 className="font-medium mb-2">Viewer</h4>
                          <p className="text-sm text-muted-foreground">Read-only access to view reports and dashboards.</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="permissions" className="space-y-6">
              <div className="p-6">
                <OrganizationPermissionsTab organizationId={org.id} />
              </div>
            </TabsContent>

            <TabsContent value="subscription" className="space-y-6">
              <div className="p-6">
                <div className="flex justify-between mb-4">
                  <h3 className="text-lg font-medium">Subscription Management</h3>
                  <Button size="sm" onClick={handleChangePlan}>
                    <DollarSign className="mr-2 h-4 w-4" />
                    Change Plan
                  </Button>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Current Plan</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="p-4 rounded-lg border bg-card text-card-foreground">
                        <div className="mb-4">
                          <Badge className={`capitalize ${
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise' ? 'bg-purple-100 text-purple-800' :
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' ? 'bg-purple-100 text-purple-800' :
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {subscription?.plan_name || orgData?.settings?.plan || org?.plan || 'Essential'}
                          </Badge>
                        </div>
                        <div className="mb-6">
                          <div className="text-3xl font-bold mb-1">
                            ${(subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise' ? 'Custom' :
                               (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' ? '999' :
                               (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' ? '499' : '199'}
                            {(subscription?.plan_name || orgData?.settings?.plan || org?.plan) !== 'enterprise' && <span className="text-sm font-normal text-muted-foreground"> / month</span>}
                          </div>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center">
                            <Check className="mr-2 h-4 w-4 text-green-500" />
                            {(subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise' ? 'Unlimited team members' :
                             (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' ? 'Up to 10 team members' :
                             (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' ? 'Up to 5 team members' :
                             'Up to 2 team members'}
                          </div>
                          <div className="flex items-center">
                            <Check className="mr-2 h-4 w-4 text-green-500" />
                            {(subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise' ? 'Enterprise-grade booking management' :
                             (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' ? 'Advanced booking management' :
                             (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' ? 'Advanced booking management' :
                             'Basic booking management'}
                          </div>
                          <div className="flex items-center">
                            <Check className="mr-2 h-4 w-4 text-green-500" />
                            {(subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise' ? 'Custom reporting suite' :
                             (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' ? 'Custom dashboard' :
                             (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' ? 'Advanced analytics' :
                             'Standard reporting'}
                          </div>
                          <div className="flex items-center">
                            <Check className="mr-2 h-4 w-4 text-green-500" />
                            {(subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise' ? 'Dedicated support team' :
                             (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' ? '24/7 priority support' :
                             (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' ? 'Priority support' :
                             'Email support'}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Billing Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between mb-1">
                            <div className="text-sm font-medium">Payment Method</div>
                            <Button variant="link" size="sm" className="h-auto p-0">Update</Button>
                          </div>
                          <div className="flex items-center gap-2 p-3 border rounded-md">
                            <CreditCard className="h-5 w-5 text-muted-foreground" />
                            <div>
                              <div className="font-medium">•••• •••• •••• 4242</div>
                              <div className="text-xs text-muted-foreground">Expires 12/2024</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Usage & Limits</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <div className="text-sm font-medium">Team Members</div>
                          <div className="text-sm text-muted-foreground">
                            {users?.length || 0} / {
                              (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise' ? 'Unlimited' :
                              (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' ? '10' :
                              (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' ? '5' : '2'
                            }
                          </div>
                        </div>
                        <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                          <div className={`h-2 ${
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise' ? 'w-[10%] bg-green-500' :
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' && (users?.length || 0) > 9 ? 'w-[100%] bg-red-500' :
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' ? `w-[${Math.min(Math.round(((users?.length || 0) / 10) * 100), 95)}%] bg-green-500` :
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' && (users?.length || 0) > 4 ? 'w-[100%] bg-red-500' :
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' ? `w-[${Math.min(Math.round(((users?.length || 0) / 5) * 100), 95)}%] bg-green-500` :
                            (users?.length || 0) > 2 ? 'w-[100%] bg-red-500' :
                            `w-[${Math.min(Math.round(((users?.length || 0) / 2) * 100), 95)}%] bg-green-500`
                          }`}></div>
                        </div>
                        {(((subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'essential' && (users?.length || 0) > 2) ||
                          ((subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' && (users?.length || 0) > 5) ||
                          ((subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' && (users?.length || 0) > 10)) && (
                          <div className="flex items-center gap-1 mt-1 text-xs text-red-600">
                            <AlertTriangle className="h-3 w-3" />
                            Over limit. Consider upgrading.
                          </div>
                        )}
                      </div>

                      <div>
                        <div className="flex justify-between mb-1">
                          <div className="text-sm font-medium">Features</div>
                          <div className="text-sm text-muted-foreground">{
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise' ? 'All features + Custom development' :
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' ? 'Advanced features' :
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' ? 'Enhanced features' :
                            'Basic features'
                          }</div>
                        </div>
                        <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                          <div className={`h-2 ${
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'enterprise' ? 'w-[100%] bg-green-500' :
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'business' ? 'w-[75%] bg-green-500' :
                            (subscription?.plan_name || orgData?.settings?.plan || org?.plan) === 'professional' ? 'w-[50%] bg-green-500' :
                            'w-[25%] bg-green-500'
                          }`}></div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="embeddable-forms" className="space-y-6">
              <div className="p-6">
                <div className="flex justify-between mb-4">
                  <h3 className="text-lg font-medium">Embeddable Forms</h3>
                  <Button size="sm" asChild>
                    <Link href={`/super-admin/orgs/${org.id}/embeddable-forms`}>
                      <ArrowRight className="mr-2 h-4 w-4" />
                      Manage Forms
                    </Link>
                  </Button>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Form Status</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Active Forms</span>
                          <Badge variant="secondary">{embeddableForms?.active_count || 2}</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Total Submissions</span>
                          <span className="font-medium">{embeddableForms?.total_submissions || 1234}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Conversion Rate</span>
                          <span className="font-medium text-green-600">{embeddableForms?.conversion_rate || 68}%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Quick Actions</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                          <Link href={`/super-admin/orgs/${org.id}/embeddable-forms`}>
                            <Code className="mr-2 h-4 w-4" />
                            Configure Forms
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" className="w-full justify-start" onClick={handleViewAnalytics}>
                          <BarChart3 className="mr-2 h-4 w-4" />
                          View Analytics
                        </Button>
                        <Button variant="outline" size="sm" className="w-full justify-start" onClick={handleExportData}>
                          <Download className="mr-2 h-4 w-4" />
                          Export Data
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Recent Form Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between py-2 border-b">
                        <div>
                          <div className="font-medium text-sm">Main Booking Form</div>
                          <div className="text-xs text-muted-foreground">Last submission: 2 hours ago</div>
                        </div>
                        <Badge variant="outline" className="text-green-600">Active</Badge>
                      </div>
                      <div className="flex items-center justify-between py-2 border-b">
                        <div>
                          <div className="font-medium text-sm">Airport Transfer Form</div>
                          <div className="text-xs text-muted-foreground">Last submission: 1 day ago</div>
                        </div>
                        <Badge variant="outline" className="text-green-600">Active</Badge>
                      </div>
                      <div className="text-center py-4">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/super-admin/orgs/${org.id}/embeddable-forms`}>
                            View All Forms
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <div className="p-6">
                <div className="flex justify-between mb-4">
                  <h3 className="text-lg font-medium">Org Settings</h3>
                  <Button size="sm" onClick={handleSaveSettings}>Save Changes</Button>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Basic Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="org-name">Org Name</Label>
                          <Input id="org-name" defaultValue={orgData?.name || org?.name} />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="org-domain">Domain</Label>
                          <div className="flex">
                            <Input id="org-domain" defaultValue={(orgData?.domain || org?.domain)?.split('.')[0]} className="rounded-r-none" />
                            <div className="flex items-center px-3 border border-l-0 rounded-r-md bg-muted">
                              .transflow.app
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Status & Security</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">Org Status</div>
                            <div className="text-sm text-muted-foreground">Enable or disable this org</div>
                          </div>
                          <div>
                            <RadioGroup defaultValue={orgData?.status || org?.status} className="flex gap-4">
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="active" id="status-active" />
                                <Label htmlFor="status-active">Active</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="suspended" id="status-suspended" />
                                <Label htmlFor="status-suspended">Suspended</Label>
                              </div>
                            </RadioGroup>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">Require 2FA</div>
                            <div className="text-sm text-muted-foreground">Enforce two-factor authentication</div>
                          </div>
                          <Switch defaultChecked={orgData?.settings?.require2FA} />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card className="border-red-200">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-red-600">Danger Zone</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Delete Org</div>
                          <div className="text-sm text-red-600">This action cannot be undone</div>
                        </div>
                        <Button variant="destructive" size="sm">
                          Delete Org
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </SheetContent>
    </Sheet>

    {/* Add User Modal */}
    <Dialog open={showAddUserModal} onOpenChange={setShowAddUserModal}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New User</DialogTitle>
          <DialogDescription>
            Add a new user to {org.name}. They will receive an invitation email.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              Email
            </Label>
            <Input
              id="email"
              placeholder="<EMAIL>"
              className="col-span-3"
              value={newUserEmail}
              onChange={(e) => setNewUserEmail(e?.target?.value)}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="role" className="text-right">
              Role
            </Label>
            <Select value={newUserRole} onValueChange={setNewUserRole}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ADMIN">Admin</SelectItem>
                <SelectItem value="MANAGER">Manager</SelectItem>
                <SelectItem value="CUSTOMER">User</SelectItem>
                <SelectItem value="CUSTOMER_COORDINATOR">Coordinator</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">
              Options
            </Label>
            <div className="col-span-3 flex items-center space-x-2">
              <input
                type="checkbox"
                id="sendInvitation"
                checked={sendInvitation}
                onChange={(e) => setSendInvitation(e?.target?.checked)}
                className="rounded"
              />
              <Label htmlFor="sendInvitation" className="text-sm">
                Send invitation email
              </Label>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => {
            setShowAddUserModal(false)
            setNewUserEmail('')
            setNewUserRole('')
            setSendInvitation(true)
          }}>
            Cancel
          </Button>
          {sendInvitation ? (
            <Button onClick={async () => {
              if (!newUserEmail || !newUserRole) {
                toast({
                  title: "Error",
                  description: "Please fill in all required fields.",
                  variant: "destructive",
                })
                return
              }

              try {
                // TODO: Implement user invitation API call
                const response = await fetch('/api/super-admin/users/invite', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    email: newUserEmail,
                    role: newUserRole,
                    organization_id: org.id,
                    send_invitation: true
                  })
                })

                if (response.ok) {
                  toast({
                    title: "User Invited",
                    description: "Invitation email sent successfully.",
                  })
                  setShowAddUserModal(false)
                  setNewUserEmail('')
                  setNewUserRole('')
                  fetchOrgDetails() // Refresh the user list
                } else {
                  throw new Error('Failed to invite user')
                }
              } catch (error) {
                toast({
                  title: "Error",
                  description: "Failed to send invitation",
                  variant: "destructive",
                })
              }
            }}>
              Send Invitation
            </Button>
          ) : (
            <Button onClick={async () => {
              if (!newUserEmail || !newUserRole) {
                toast({
                  title: "Error",
                  description: "Please fill in all required fields.",
                  variant: "destructive",
                })
                return
              }

              try {
                // TODO: Implement direct user creation API call
                const response = await fetch('/api/super-admin/users/create', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    email: newUserEmail,
                    role: newUserRole,
                    organization_id: org.id,
                    send_invitation: false
                  })
                })

                if (response.ok) {
                  toast({
                    title: "User Created",
                    description: "User added successfully without invitation.",
                  })
                  setShowAddUserModal(false)
                  setNewUserEmail('')
                  setNewUserRole('')
                  fetchOrgDetails() // Refresh the user list
                } else {
                  throw new Error('Failed to create user')
                }
              } catch (error) {
                toast({
                  title: "Error",
                  description: "Failed to create user",
                  variant: "destructive",
                })
              }
            }}>
              Add User
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Subscription Modal */}
    <Dialog open={showSubscriptionModal} onOpenChange={setShowSubscriptionModal}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Subscription Management</DialogTitle>
          <DialogDescription>
            Manage subscription plan and billing for {org.name}.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="grid gap-4">
            <h4 className="font-medium">Current Plan</h4>
            <div className="grid gap-2">
              <div className="flex justify-between">
                <span>Plan:</span>
                <Badge>{subscription?.plan_name || org?.plan || 'Essential'}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Status:</span>
                <span className="text-green-600">Active</span>
              </div>
              <div className="flex justify-between">
                <span>Next Billing:</span>
                <span>{subscription?.current_period_end ? new Date(subscription.current_period_end).toLocaleDateString() : 'May 15, 2024'}</span>
              </div>
            </div>
          </div>
          <div className="grid gap-4">
            <h4 className="font-medium">Available Plans</h4>
            <div className="grid gap-2">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">Professional</div>
                  <div className="text-sm text-muted-foreground">$499/month</div>
                </div>
                <Button variant="outline" size="sm" onClick={() => handleSelectPlan('Professional')}>
                  Select
                </Button>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">Business</div>
                  <div className="text-sm text-muted-foreground">$999/month</div>
                </div>
                <Button variant="outline" size="sm" onClick={() => handleSelectPlan('Business')}>
                  Select
                </Button>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">Enterprise</div>
                  <div className="text-sm text-muted-foreground">Custom pricing</div>
                </div>
                <Button variant="outline" size="sm" onClick={() => handleSelectPlan('Enterprise')}>
                  Contact Sales
                </Button>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setShowSubscriptionModal(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Analytics Modal */}
    <Dialog open={showAnalyticsModal} onOpenChange={setShowAnalyticsModal}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>Form Analytics</DialogTitle>
          <DialogDescription>
            Detailed analytics for embeddable forms from {org.name}.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="grid gap-4 grid-cols-3">
            <div className="bg-muted p-4 rounded-lg text-center">
              <div className="text-2xl font-bold">{embeddableForms?.total_submissions || 1234}</div>
              <div className="text-sm text-muted-foreground">Total Submissions</div>
            </div>
            <div className="bg-muted p-4 rounded-lg text-center">
              <div className="text-2xl font-bold">{embeddableForms?.conversion_rate || 68}%</div>
              <div className="text-sm text-muted-foreground">Conversion Rate</div>
            </div>
            <div className="bg-muted p-4 rounded-lg text-center">
              <div className="text-2xl font-bold">{embeddableForms?.active_count || 2}</div>
              <div className="text-sm text-muted-foreground">Active Forms</div>
            </div>
          </div>
          <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <BarChart3 className="h-8 w-8 mx-auto mb-2" />
              <div className="text-sm">Analytics Chart</div>
              <div className="text-xs">(Chart implementation required)</div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setShowAnalyticsModal(false)}>
            Close
          </Button>
          <Button asChild>
            <Link href={`/super-admin/orgs/${org.id}/embeddable-forms`}>
              View Full Analytics
            </Link>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Export Data Modal */}
    <Dialog open={showExportModal} onOpenChange={setShowExportModal}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Export Data</DialogTitle>
          <DialogDescription>
            Export form submission data for {org.name}.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label>Export Format</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="csv">CSV</SelectItem>
                <SelectItem value="xlsx">Excel (XLSX)</SelectItem>
                <SelectItem value="json">JSON</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label>Date Range</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="all">All time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setShowExportModal(false)}>
            Cancel
          </Button>
          <Button onClick={() => {
            toast({
              title: "Export Started",
              description: "Your data export will be ready shortly.",
            })
            setShowExportModal(false)
          }}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Branding Edit Modal */}
    <Dialog open={showBrandingModal} onOpenChange={setShowBrandingModal}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Branding & Customization</DialogTitle>
          <DialogDescription>
            Customize the appearance and branding for {org.name}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="logo_url" className="text-right">
              Logo URL
            </Label>
            <Input
              id="logo_url"
              placeholder="https://example.com/logo.png"
              className="col-span-3"
              value={brandingData.logo_url}
              onChange={(e) => setBrandingData(prev => ({ ...prev, logo_url: e?.target?.value }))}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="primary_color" className="text-right">
              Primary Color
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="primary_color"
                type="color"
                className="w-16 h-10 p-1 border rounded"
                value={brandingData.primary_color}
                onChange={(e) => setBrandingData(prev => ({ ...prev, primary_color: e?.target?.value }))}
              />
              <Input
                placeholder="#3b82f6"
                className="flex-1"
                value={brandingData.primary_color}
                onChange={(e) => setBrandingData(prev => ({ ...prev, primary_color: e?.target?.value }))}
              />
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="secondary_color" className="text-right">
              Secondary Color
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="secondary_color"
                type="color"
                className="w-16 h-10 p-1 border rounded"
                value={brandingData.secondary_color}
                onChange={(e) => setBrandingData(prev => ({ ...prev, secondary_color: e?.target?.value }))}
              />
              <Input
                placeholder="#64748b"
                className="flex-1"
                value={brandingData.secondary_color}
                onChange={(e) => setBrandingData(prev => ({ ...prev, secondary_color: e?.target?.value }))}
              />
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="background_color" className="text-right">
              Background Color
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="background_color"
                type="color"
                className="w-16 h-10 p-1 border rounded"
                value={brandingData.background_color}
                onChange={(e) => setBrandingData(prev => ({ ...prev, background_color: e?.target?.value }))}
              />
              <Input
                placeholder="#ffffff"
                className="flex-1"
                value={brandingData.background_color}
                onChange={(e) => setBrandingData(prev => ({ ...prev, background_color: e?.target?.value }))}
              />
            </div>
          </div>
          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="custom_css" className="text-right pt-2">
              Custom CSS
            </Label>
            <textarea
              id="custom_css"
              placeholder="/* Custom CSS styles */"
              className="col-span-3 min-h-[100px] p-2 border rounded-md resize-vertical"
              value={brandingData.custom_css}
              onChange={(e) => setBrandingData(prev => ({ ...prev, custom_css: e?.target?.value }))}
            />
          </div>
          <div className="grid grid-cols-4 items-start gap-4">
            <Label className="text-right pt-2">
              Preview
            </Label>
            <div className="col-span-3 p-4 border rounded-md" style={{
              backgroundColor: brandingData.background_color,
              color: brandingData.primary_color
            }}>
              <div className="flex items-center gap-2 mb-2">
                {brandingData.logo_url && (
                  <img src={brandingData.logo_url} alt="Logo" className="h-8 w-auto" />
                )}
                <span className="font-semibold">{org.name}</span>
              </div>
              <div className="text-sm" style={{ color: brandingData.secondary_color }}>
                This is a preview of your branding
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => {
            setShowBrandingModal(false)
            setBrandingData({
              logo_url: '',
              primary_color: '#3b82f6',
              secondary_color: '#64748b',
              background_color: '#ffffff',
              custom_css: ''
            })
          }}>
            Cancel
          </Button>
          <Button onClick={handleSaveBranding}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    </>
  )
}