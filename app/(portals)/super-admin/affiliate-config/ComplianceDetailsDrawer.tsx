"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>lose } from "@/app/components/ui/sheet"
import { Button } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/app/components/ui/tabs"
import { FileText, History, CheckCircle2, AlertTriangle, Upload, Eye } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { Badge } from "@/app/components/ui/badge"
import { Affiliate, ComplianceEntry } from "@/app/lib/types/affiliates"
import { useOrganization } from "@/app/contexts/OrganizationContext"

interface ComplianceDetailsDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  affiliate: Affiliate | null
  complianceData: ComplianceEntry | null
}

// Required document list with real status checking
const getRequiredDocuments = (affiliate: any) => [
  {
    id: 'insurance',
    name: 'Certificate of Insurance (COI)',
    required: true,
    status: affiliate?.insurance_provider ? 'verified' : 'missing',
    details: affiliate?.insurance_provider ? `Provider: ${affiliate.insurance_provider}, Amount: ${affiliate.insurance_amount}` : 'Not provided'
  },
  {
    id: 'license',
    name: 'Business Operating License',
    required: true,
    status: affiliate?.license_number ? 'verified' : 'missing',
    details: affiliate?.license_number ? `License: ${affiliate.license_number}` : 'Not provided'
  },
  {
    id: 'w9',
    name: 'W-9 Form',
    required: true,
    status: affiliate?.federal_tax_id ? 'verified' : 'missing',
    details: affiliate?.federal_tax_id ? `Tax ID: ${affiliate.federal_tax_id}` : 'Not provided'
  },
  {
    id: 'drivers',
    name: 'Driver Documentation (List & Licenses)',
    required: true,
    status: 'pending',
    details: 'Driver verification in progress'
  },
  {
    id: 'vehicles',
    name: 'Vehicle Documentation (Registrations)',
    required: true,
    status: affiliate?.fleet_size ? 'verified' : 'missing',
    details: affiliate?.fleet_size ? `Fleet size: ${affiliate.fleet_size} vehicles` : 'Fleet information not provided'
  },
  {
    id: 'agreement',
    name: 'Signed Partner Agreement',
    required: false,
    status: affiliate?.status === 'active' ? 'verified' : 'pending',
    details: affiliate?.status === 'active' ? 'Agreement signed and active' : 'Pending agreement signature'
  },
];

import { useState, useEffect } from 'react';

export function ComplianceDetailsDrawer({ open, onOpenChange, affiliate, complianceData }: ComplianceDetailsDrawerProps) {
  const { currentOrganization } = useOrganization();
  const [auditLog, setAuditLog] = useState<any[]>([]);
  const [loadingAuditLog, setLoadingAuditLog] = useState(false);

  // Fetch real audit log data
  useEffect(() => {
    if (affiliate?.id && open) {
      setLoadingAuditLog(true);
      fetch(`/api/super-admin/affiliates/${affiliate.id}/audit-log`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            setAuditLog(data.auditLog);
          }
        })
        .catch(error => {
          console.error('Error fetching audit log:', error);
          setAuditLog([]);
        })
        .finally(() => setLoadingAuditLog(false));
    }
  }, [affiliate?.id, open]);

  if (!affiliate) return null

  // Use real affiliate data for compliance information
  const effectiveComplianceData = {
    partnerId: affiliate.id,
    overallStatus: affiliate.verification_status || affiliate.status || 'Pending',
    insuranceStatus: affiliate.insurance_provider ? 'Verified' : 'Pending',
    insuranceExpiry: affiliate.insurance_expiry || 'Not provided',
    licenseStatus: affiliate.license_number ? 'Verified' : 'Pending',
    licenseExpiry: 'Not specified', // This field might not exist in current schema
    lastAudit: affiliate.updated_at || affiliate.created_at || 'Not available'
  }

  // Context-aware logic: adjust UI/fields based on tenant type/branding/feature flags
  const tenantType = currentOrganization?.organization_type;
  const branding = currentOrganization?.branding;
  const isWhiteLabel = tenantType === 'white_label';

  // Example: Show a custom banner or fields for white-label tenants
  const renderWhiteLabelBanner = () => {
    if (!isWhiteLabel) return null;
    return (
      <div className="mb-4 p-3 rounded bg-primary/10 text-primary font-semibold text-center">
        White-Label Tenant: Custom branding and compliance requirements may apply.
      </div>
    );
  };

  const getStatusBadge = (status: string) => {
     switch (status) {
      case 'verified': return <Badge variant="success">Verified</Badge>;
      case 'pending': return <Badge variant="secondary">Pending</Badge>;
      case 'missing': return <Badge variant="destructive">Missing</Badge>;
      case 'expired': return <Badge variant="destructive">Expired</Badge>;
      // Legacy support for old status values
      case 'Verified': return <Badge variant="success">Verified</Badge>;
      case 'Pending': return <Badge variant="secondary">Pending</Badge>;
      case 'Missing': return <Badge variant="destructive">Missing</Badge>;
      case 'Expired': return <Badge variant="destructive">Expired</Badge>;
      default: return <Badge variant="secondary">{status || 'Unknown'}</Badge>;
    }
  };



  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-4xl w-full p-0"> {/* Made wider */}
        <SheetHeader className="px-6 pt-6 pb-4 border-b">
           <div className="flex justify-between items-center">
             <div>
                <SheetTitle className="text-xl font-semibold">Compliance Details: {affiliate.name}</SheetTitle>
                <p className="text-sm text-muted-foreground">Document status and audit log for {affiliate.name}.</p>
             </div>
              <SheetClose asChild>
                <Button size="icon" variant="ghost">
                  <span className="sr-only">Close</span>
                  ×
                </Button>
             </SheetClose>
           </div>
        </SheetHeader>

        <div className="p-6 overflow-y-auto h-[calc(100vh-80px)]"> {/* Adjust height calculation as needed */}
          {renderWhiteLabelBanner()}
           <Tabs defaultValue="documents">
              <TabsList className="mb-4">
                <TabsTrigger value="documents"><FileText className="mr-2 h-4 w-4" /> Documents</TabsTrigger>
                <TabsTrigger value="auditLog"><History className="mr-2 h-4 w-4" /> Audit Log</TabsTrigger>
              </TabsList>

              <TabsContent value="documents">
                 <Card>
                   <CardHeader>
                     <CardTitle>Document Status</CardTitle>
                     <CardDescription>Review and verify uploaded compliance documents for {affiliate.name}.</CardDescription>
                   </CardHeader>
                   <CardContent>
                     <div className="rounded-md border">
                       <Table>
                         <TableHeader>
                           <TableRow>
                             <TableHead>Document Name</TableHead>
                             <TableHead>Status</TableHead>
                             <TableHead>Expiration Date</TableHead>
                             <TableHead className="text-right">Actions</TableHead>
                           </TableRow>
                         </TableHeader>
                         <TableBody>
                           {getRequiredDocuments(affiliate).map(doc => {
                             const isMissing = doc.status === 'missing';
                             const isVerified = doc.status === 'verified';
                             return (
                               <TableRow key={doc.id}>
                                 <TableCell className="font-medium">
                                   {doc.name}
                                   {doc.required && <span className="text-destructive ml-1">*</span>}
                                 </TableCell>
                                 <TableCell>{getStatusBadge(doc.status)}</TableCell>
                                 <TableCell>{doc.details}</TableCell>
                                 <TableCell className="text-right">
                                   {isMissing ? (
                                     <Button variant="outline" size="sm">
                                       <Upload className="mr-2 h-3 w-3" /> Request Upload
                                     </Button>
                                   ) : (
                                     <div className="flex justify-end gap-2">
                                       <Button variant="outline" size="sm">
                                         <Eye className="mr-2 h-3 w-3" /> View
                                       </Button>
                                       <Button
                                         variant={isVerified ? "secondary" : "outline"}
                                         size="sm"
                                         disabled={isVerified}
                                         className={!isVerified ? "text-green-600 hover:text-green-700" : ""}
                                       >
                                         <CheckCircle2 className="mr-2 h-3 w-3" /> {isVerified ? 'Verified' : 'Verify'}
                                       </Button>
                                     </div>
                                   )}
                                 </TableCell>
                               </TableRow>
                             );
                           })}
                         </TableBody>
                       </Table>
                     </div>
                   </CardContent>
                 </Card>
              </TabsContent>

              <TabsContent value="auditLog">
                 <Card>
                   <CardHeader className="flex flex-row items-center justify-between">
                     <div>
                        <CardTitle>Audit Log</CardTitle>
                        <CardDescription>History of compliance-related actions for {affiliate.name}.</CardDescription>
                     </div>
                     <Button variant="outline" size="sm">Export Log</Button>
                   </CardHeader>
                   <CardContent>
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Date & Time</TableHead>
                              <TableHead>Action</TableHead>
                              <TableHead>User</TableHead>
                              <TableHead>Details</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {loadingAuditLog ? (
                              <TableRow><TableCell colSpan={4} className="text-center h-24">Loading audit log...</TableCell></TableRow>
                            ) : auditLog.length === 0 ? (
                              <TableRow><TableCell colSpan={4} className="text-center h-24">No audit history found.</TableCell></TableRow>
                            ) : (
                              auditLog.map((log, index) => (
                                <TableRow key={log.id || index}>
                                  <TableCell>{new Date(log.timestamp).toLocaleString()}</TableCell>
                                  <TableCell>{log.action}</TableCell>
                                  <TableCell title={log.userEmail}>{log.user}</TableCell>
                                  <TableCell>{log.details}</TableCell>
                                </TableRow>
                              ))
                            )}
                          </TableBody>
                        </Table>
                      </div>
                   </CardContent>
                 </Card>
              </TabsContent>
           </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  )
}