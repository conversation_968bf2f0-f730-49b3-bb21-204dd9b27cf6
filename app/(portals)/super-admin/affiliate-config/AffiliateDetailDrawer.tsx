"use client";

import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from "@/app/components/ui/drawer";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import { Separator } from "@/app/components/ui/separator";
import { Progress } from "@/app/components/ui/progress";
import { useToast } from "@/app/components/ui/use-toast";
import {
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Star,
  TrendingUp,
  Car,
  FileText,
  Shield,
  Clock,
  CheckCircle,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Users,
  DollarSign,
  Globe,
  ClipboardCheck,
  X,
  Plus,
} from "lucide-react";
import { Affiliate } from "@/app/lib/types/affiliates";
import { RejectionDialog } from "./RejectionDialog";

interface AffiliateDetailDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  affiliate: Affiliate | null;
}

// This component embeds the full affiliate profile page content in a large drawer
export function AffiliateDetailDrawer({
  open,
  onOpenChange,
  affiliate,
}: AffiliateDetailDrawerProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showRequestUpdatesDialog, setShowRequestUpdatesDialog] = useState(false);

  // Fetch detailed affiliate data including performance metrics, fleet, etc.
  const fetchAffiliateDetails = async (affiliateId: string) => {
    const response = await fetch(`/api/super-admin/affiliates/${affiliateId}/detailed`);
    if (!response.ok) {
      throw new Error("Failed to fetch affiliate details");
    }
    return response.json();
  };

  const {
    data: affiliateData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["affiliate-details", affiliate?.id],
    queryFn: () => fetchAffiliateDetails(affiliate!.id),
    enabled: !!affiliate?.id && open,
  });

  // Approval mutation
  const statusMutation = useMutation({
    mutationFn: async ({
      status,
      verificationStatus,
    }: {
      status: string;
      verificationStatus: string;
    }) => {
      if (!affiliate) throw new Error("No affiliate selected");

      const response = await fetch(
        `/api/super-admin/affiliates/${affiliate.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status,
            verificationStatus,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update affiliate status");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["affiliate-details", affiliate?.id],
      });
      queryClient.invalidateQueries({ queryKey: ["affiliates"] });
      refetch();
    },
  });

  const handleApprove = async () => {
    try {
      await statusMutation.mutateAsync({
        status: "active",
        verificationStatus: "approved",
      });
      toast({
        title: "Success",
        description: "Affiliate approved successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to approve affiliate",
        variant: "destructive",
      });
    }
  };

  const handleReject = () => {
    setShowRejectDialog(true);
  };

  const handleRequestUpdates = () => {
    setShowRequestUpdatesDialog(true);
  };

  const handleRejectConfirm = async (reasons: string[], customReason: string) => {
    try {
      await statusMutation.mutateAsync({
        status: "inactive",
        verificationStatus: "rejected",
      });
      toast({
        title: "Success",
        description: "Affiliate rejected",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject affiliate",
        variant: "destructive",
      });
    }
  };

  const handleRequestUpdatesConfirm = async (reasons: string[], customReason: string) => {
    try {
      // Here you would typically send an email or notification to the affiliate
      // For now, we'll just show a success message
      toast({
        title: "Updates Requested",
        description: "Affiliate has been notified to provide additional information",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send update request",
        variant: "destructive",
      });
    }
  };

  if (!affiliate) return null;

  const affiliateDetails = affiliateData?.affiliate || affiliate;
  const fleet = affiliateData?.fleet || [];
  const rateCards = affiliateData?.rateCards || [];
  const drivers = affiliateData?.drivers || [];

  return (
    <Drawer open={open} onOpenChange={onOpenChange} side="right" size="6xl">
      <DrawerContent className="h-full">
        <DrawerHeader className="border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                <Building2 className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <DrawerTitle className="text-xl">{affiliate.name}</DrawerTitle>
                <p className="text-sm text-muted-foreground">
                  {affiliate.city}, {affiliate.state} • Applied{" "}
                  {new Date(affiliate.created_at || "").toLocaleDateString()}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Badge
                variant={
                  affiliate.status === "approved"
                    ? "default"
                    : affiliate.status === "pending"
                    ? "secondary"
                    : affiliate.status === "in_verification"
                    ? "outline"
                    : "destructive"
                }
              >
                {affiliate.status === "approved"
                  ? "Approved"
                  : affiliate.status === "pending"
                  ? "Pending"
                  : affiliate.status === "in_verification"
                  ? "In Verification"
                  : "Rejected"}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DrawerHeader>

        <div className="flex-1 overflow-auto p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">
                  Loading affiliate details...
                </p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Failed to load affiliate details
                </p>
                <Button
                  onClick={() => refetch()}
                  variant="outline"
                  className="mt-2"
                >
                  Try Again
                </Button>
              </div>
            </div>
          ) : (
            <>
              {/* Action Buttons */}
              <div className="flex items-center gap-3 mb-6">
                <Button
                  variant="outline"
                  onClick={handleRequestUpdates}
                  disabled={statusMutation.isLoading}
                  className="h-10 px-6"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Request Updates
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleReject}
                  disabled={
                    statusMutation.isLoading || affiliate.status === "inactive"
                  }
                  className="h-10 px-6"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  {statusMutation.isLoading
                    ? "Processing..."
                    : "Reject Application"}
                </Button>
                <Button
                  onClick={handleApprove}
                  disabled={
                    statusMutation.isLoading || affiliate.status === "active"
                  }
                  className="h-10 px-6 bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  {statusMutation.isLoading
                    ? "Processing..."
                    : "Approve & Activate"}
                </Button>
              </div>

              {/* Key Metrics Dashboard */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-600" />
                      Response Rate
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Quote response performance
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[120px] bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {affiliateData?.affiliate?.performance?.responseRate?.toFixed(1) || '0'}%
                        </div>
                        <p className="text-sm text-blue-700">Response Rate</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <MapPin className="h-5 w-5 text-green-600" />
                      Service Coverage
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">Cities served</p>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[120px] bg-gradient-to-br from-green-50 to-green-100 rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {affiliateData?.affiliate?.service?.citiesCovered?.length || 0}
                        </div>
                        <p className="text-sm text-green-700">Cities Covered</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Shield className="h-5 w-5 text-purple-600" />
                      Performance
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Overall rating
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[120px] bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {affiliateData?.affiliate?.performance?.rating?.toFixed(1) || '0.0'}
                        </div>
                        <p className="text-sm text-purple-700">Rating Score</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Comprehensive Affiliate Management Tabs */}
              <Tabs defaultValue="approval" className="space-y-6">
                <TabsList className="grid w-full grid-cols-5 lg:grid-cols-9">
                  <TabsTrigger value="approval">Approval</TabsTrigger>
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="contact">Contact</TabsTrigger>
                  <TabsTrigger value="performance">Performance</TabsTrigger>
                  <TabsTrigger value="rates">Rates</TabsTrigger>
                  <TabsTrigger value="fleet">Fleet</TabsTrigger>
                  <TabsTrigger value="service-area">Service Area</TabsTrigger>
                  <TabsTrigger value="documents">Documents</TabsTrigger>
                  <TabsTrigger value="audit">Audit Log</TabsTrigger>
                </TabsList>

                {/* Approval Tab - Current Content */}
                <TabsContent value="approval" className="space-y-6">
                  <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
                    {/* Left Column - Company Overview & Contact */}
                    <div className="xl:col-span-1 space-y-6">
                      <Card>
                        <CardHeader>
                          <div className="flex items-center gap-4">
                            <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center">
                              <Building2 className="h-8 w-8 text-gray-600" />
                            </div>
                            <div>
                              <CardTitle className="text-xl">
                                {affiliateDetails.name ||
                                  affiliateDetails.businessName}
                              </CardTitle>
                              <p className="text-sm text-muted-foreground">
                                Applied{" "}
                                {new Date(
                                  affiliateDetails.createdAt ||
                                    affiliateDetails.created_at ||
                                    ""
                                ).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="space-y-3">
                            <div className="flex items-center gap-3">
                              <Mail className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="text-sm text-muted-foreground">
                                  Email
                                </p>
                                <p className="font-medium">
                                  {affiliateDetails.email || "Not provided"}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <Phone className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="text-sm text-muted-foreground">
                                  Phone
                                </p>
                                <p className="font-medium">
                                  {affiliateDetails.phone || "Not provided"}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <MapPin className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="text-sm text-muted-foreground">
                                  Address
                                </p>
                                <p className="font-medium">
                                  {affiliateDetails.address?.street
                                    ? `${affiliateDetails.address.street}, ${affiliateDetails.address.city}, ${affiliateDetails.address.state} ${affiliateDetails.address.zip}`
                                    : `${
                                        affiliateDetails.city || affiliate.city
                                      }, ${
                                        affiliateDetails.state ||
                                        affiliate.state
                                      }`}
                                </p>
                              </div>
                            </div>
                          </div>

                          <Separator />

                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Business License
                              </p>
                              <p className="font-medium text-sm">
                                {affiliateDetails.businessInfo?.taxId ||
                                  "Not provided"}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Established
                              </p>
                              <p className="font-medium text-sm">
                                {affiliateDetails.businessInfo
                                  ?.yearEstablished || "N/A"}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Rating
                              </p>
                              <p className="font-medium text-sm">
                                {affiliateDetails.metrics?.rating?.toFixed(1) ||
                                  affiliateDetails.rating ||
                                  "N/A"}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Last Active
                              </p>
                              <p className="font-medium text-sm">
                                {affiliateDetails.lastActive
                                  ? new Date(
                                      affiliateDetails.lastActive
                                    ).toLocaleDateString()
                                  : "Never"}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* Application Progress */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg flex items-center gap-2">
                            <ClipboardCheck className="h-5 w-5" />
                            Application Progress
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <Progress value={65} className="w-full" />
                            <div className="flex justify-between text-xs text-muted-foreground">
                              <span>Documents</span>
                              <span>Verification</span>
                              <span>Review</span>
                            </div>
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                                  <span className="text-sm">
                                    Business Registration
                                  </span>
                                </div>
                                <Badge variant="outline" className="text-xs">
                                  Verified
                                </Badge>
                              </div>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <Clock className="h-4 w-4 text-yellow-500" />
                                  <span className="text-sm">
                                    Insurance Documentation
                                  </span>
                                </div>
                                <Badge variant="secondary" className="text-xs">
                                  In Review
                                </Badge>
                              </div>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <AlertTriangle className="h-4 w-4 text-red-500" />
                                  <span className="text-sm">
                                    Fleet Inspection
                                  </span>
                                </div>
                                <Badge variant="outline" className="text-xs">
                                  Pending
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Right Column - Fleet & Rates, Compliance & Audit */}
                    <div className="xl:col-span-2 space-y-6">
                      {/* Fleet & Rates Section */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg flex items-center gap-2">
                            <Car className="h-5 w-5" />
                            Fleet & Rates
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Fleet Overview */}
                            <div className="space-y-4">
                              <h4 className="font-medium">Fleet Overview</h4>
                              <div className="grid grid-cols-2 gap-4">
                                <div className="p-4 bg-blue-50 rounded-lg text-center">
                                  <div className="text-2xl font-bold text-blue-600">
                                    {fleet.length || 12}
                                  </div>
                                  <p className="text-sm text-blue-700">
                                    Total Vehicles
                                  </p>
                                </div>
                                <div className="p-4 bg-green-50 rounded-lg text-center">
                                  <div className="text-2xl font-bold text-green-600">
                                    {drivers.length || 8}
                                  </div>
                                  <p className="text-sm text-green-700">
                                    Active Drivers
                                  </p>
                                </div>
                              </div>
                              <div className="space-y-2">
                                {affiliateData?.affiliate?.fleet?.vehiclesByType && Object.keys(affiliateData.affiliate.fleet.vehiclesByType).length > 0 ? (
                                  Object.entries(affiliateData.affiliate.fleet.vehiclesByType).map(([type, count]) => (
                                    <div key={type} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                                      <span className="text-sm capitalize">{type}</span>
                                      <Badge variant="outline">{count} vehicle{count !== 1 ? 's' : ''}</Badge>
                                    </div>
                                  ))
                                ) : (
                                  <div className="p-3 bg-gray-50 rounded text-center">
                                    <span className="text-sm text-muted-foreground">No vehicles registered</span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Rate Structure */}
                            <div className="space-y-4">
                              <h4 className="font-medium">Rate Structure</h4>
                              <div className="space-y-3">
                                {affiliateData?.affiliate?.service?.rateCards && affiliateData.affiliate.service.rateCards.length > 0 ? (
                                  affiliateData.affiliate.service?.rateCards?.map((rateCard: any, index: number) => (
                                    <div key={index} className="p-4 border rounded-lg">
                                      <div className="flex justify-between items-center mb-2">
                                        <span className="font-medium capitalize">
                                          {rateCard.vehicle_type || 'Standard'} - {rateCard.service_type || 'General'}
                                        </span>
                                        <span className="text-lg font-bold">
                                          {rateCard.base_rate ? `$${rateCard.base_rate}` :
                                           rateCard.per_hour_rate ? `$${rateCard.per_hour_rate}/hr` :
                                           rateCard.per_mile_rate ? `$${rateCard.per_mile_rate}/mi` : 'Quote'}
                                        </span>
                                      </div>
                                      <p className="text-sm text-muted-foreground">
                                        {rateCard.minimum_hours ? `${rateCard.minimum_hours}-hour minimum` :
                                         rateCard.gratuity_percentage ? `${rateCard.gratuity_percentage}% gratuity included` :
                                         'Contact for details'}
                                      </p>
                                    </div>
                                  ))
                                ) : (
                                  <div className="p-4 border rounded-lg text-center">
                                    <span className="text-sm text-muted-foreground">No rate cards configured</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* Compliance & Audit Section */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg flex items-center gap-2">
                            <Shield className="h-5 w-5" />
                            Compliance & Audit
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Documents Status */}
                            <div className="space-y-4">
                              <h4 className="font-medium">
                                Required Documents
                              </h4>
                              <div className="space-y-3">
                                {affiliateData?.affiliate?.compliance?.documents && affiliateData.affiliate.compliance.documents.length > 0 ? (
                                  affiliateData.affiliate.compliance?.documents?.map((doc: any, index: number) => {
                                    const getStatusColor = (status: string) => {
                                      switch (status) {
                                        case 'APPROVED': return { bg: 'bg-green-50', icon: CheckCircle2, iconColor: 'text-green-500', badgeClass: 'bg-green-100 text-green-700' };
                                        case 'PENDING': return { bg: 'bg-yellow-50', icon: Clock, iconColor: 'text-yellow-500', badgeClass: 'bg-yellow-100 text-yellow-700' };
                                        case 'REJECTED': return { bg: 'bg-red-50', icon: AlertTriangle, iconColor: 'text-red-500', badgeClass: 'bg-red-100 text-red-700' };
                                        default: return { bg: 'bg-gray-50', icon: Clock, iconColor: 'text-gray-500', badgeClass: 'bg-gray-100 text-gray-700' };
                                      }
                                    };
                                    const statusConfig = getStatusColor(doc.status);
                                    const StatusIcon = statusConfig.icon;

                                    return (
                                      <div key={index} className={`flex items-center justify-between p-3 ${statusConfig.bg} rounded-lg`}>
                                        <div className="flex items-center gap-2">
                                          <StatusIcon className={`h-4 w-4 ${statusConfig.iconColor}`} />
                                          <span className="text-sm capitalize">
                                            {doc.document_type?.toLowerCase().replace('_', ' ') || 'Document'}
                                          </span>
                                        </div>
                                        <Badge
                                          variant="outline"
                                          className={statusConfig.badgeClass}
                                        >
                                          {doc.status === 'APPROVED' ? 'Verified' :
                                           doc.status === 'PENDING' ? 'In Progress' :
                                           doc.status === 'REJECTED' ? 'Rejected' : 'Unknown'}
                                        </Badge>
                                      </div>
                                    );
                                  })
                                ) : (
                                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                                    <span className="text-sm text-muted-foreground">No documents uploaded</span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Operational Compliance */}
                            <div className="space-y-4">
                              <h4 className="font-medium">
                                Operational Compliance
                              </h4>
                              <div className="space-y-3">
                                <div className="p-3 bg-gray-50 rounded-lg">
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium">
                                      Dispatch Integration
                                    </span>
                                    {affiliateData?.affiliate?.service?.dispatchSoftware ? (
                                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                                    ) : (
                                      <Clock className="h-4 w-4 text-yellow-500" />
                                    )}
                                  </div>
                                  <p className="text-xs text-muted-foreground">
                                    {affiliateData?.affiliate?.service?.dispatchSoftware || 'Not configured'}
                                  </p>
                                </div>
                                <div className="p-3 bg-gray-50 rounded-lg">
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium">
                                      24/7 Support
                                    </span>
                                    {affiliateData?.affiliate?.operations?.livePhoneSupport ? (
                                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                                    ) : (
                                      <XCircle className="h-4 w-4 text-red-500" />
                                    )}
                                  </div>
                                  <p className="text-xs text-muted-foreground">
                                    {affiliateData?.affiliate?.operations?.livePhoneSupport ? 'Available' : 'Not available'}
                                  </p>
                                </div>
                                <div className="p-3 bg-gray-50 rounded-lg">
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium">
                                      Direct Billing
                                    </span>
                                    {affiliateData?.affiliate?.operations?.directBilling ? (
                                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                                    ) : (
                                      <XCircle className="h-4 w-4 text-red-500" />
                                    )}
                                  </div>
                                  <p className="text-xs text-muted-foreground">
                                    {affiliateData?.affiliate?.operations?.directBilling ? 'Supported' : 'Not supported'}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </TabsContent>

                {/* Overview Tab */}
                <TabsContent value="overview" className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Company Information */}
                    <Card className="lg:col-span-2">
                      <CardHeader>
                        <CardTitle>Company Information</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Business Name
                            </p>
                            <p className="font-medium">
                              {affiliateData?.affiliate?.name || affiliate.name}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">
                              DBA Name
                            </p>
                            <p className="font-medium">
                              {affiliateData?.affiliate?.dba || 'Not specified'}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Status
                            </p>
                            <Badge variant="outline">
                              {affiliateData?.affiliate?.status || affiliate.status}
                            </Badge>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Year Established
                            </p>
                            <p className="font-medium">
                              {affiliateData?.affiliate?.year_established || 'Not specified'}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Location
                            </p>
                            <p className="font-medium">
                              {affiliateData?.affiliate?.city || affiliate.city}, {affiliateData?.affiliate?.state || affiliate.state}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Rating
                            </p>
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              <span className="font-medium">
                                {affiliateData?.affiliate?.performance?.rating?.toFixed(1) || affiliate.rating || "N/A"}
                              </span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Quick Actions */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {affiliate.status === "pending" && (
                          <>
                            <Button onClick={handleApprove} className="w-full">
                              <CheckCircle className="h-4 w-4 mr-2" />
                              Approve Application
                            </Button>
                            <Button
                              onClick={handleReject}
                              variant="destructive"
                              className="w-full"
                            >
                              <XCircle className="h-4 w-4 mr-2" />
                              Reject Application
                            </Button>
                          </>
                        )}
                        <Button variant="outline" className="w-full">
                          <Mail className="h-4 w-4 mr-2" />
                          Send Message
                        </Button>
                        <Button variant="outline" className="w-full">
                          <FileText className="h-4 w-4 mr-2" />
                          View Quotes
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Contact Tab */}
                <TabsContent value="contact" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Contact Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div className="flex items-center gap-3">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Email
                              </p>
                              <p className="font-medium">
                                {affiliateData?.affiliate?.primary_contact_email ||
                                  affiliateData?.affiliate?.email ||
                                  affiliate.email ||
                                  "Not provided"}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <Phone className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Phone
                              </p>
                              <p className="font-medium">
                                {affiliateData?.affiliate?.contact_phone ||
                                  affiliateData?.affiliate?.phone ||
                                  affiliate.phone ||
                                  "Not provided"}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Owner Name
                              </p>
                              <p className="font-medium">
                                {affiliateData?.affiliate?.owner_name || "Not provided"}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Federal Tax ID
                              </p>
                              <p className="font-medium">
                                {affiliateData?.affiliate?.federal_tax_id || "Not provided"}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-4">
                          <div className="flex items-center gap-3">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Address
                              </p>
                              <p className="font-medium">
                                {affiliateData?.affiliate?.address
                                  ? `${affiliateData.affiliate.address}${affiliateData.affiliate.address_line_2 ? ', ' + affiliateData.affiliate.address_line_2 : ''}`
                                  : `${affiliate.address || 'Not provided'}`}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {affiliateData?.affiliate?.city || affiliate.city}, {affiliateData?.affiliate?.state || affiliate.state} {affiliateData?.affiliate?.zip}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {affiliateData?.affiliate?.country || 'USA'}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <Globe className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Website
                              </p>
                              <p className="font-medium">
                                {affiliateData?.affiliate?.website || "Not provided"}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Performance Tab */}
                <TabsContent value="performance" className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">Total Offers</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {affiliateData?.affiliate?.performance?.totalOffers || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Quote offers received
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">
                          Acceptance Rate
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {affiliateData?.affiliate?.performance?.acceptanceRate?.toFixed(1) || 0}%
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {affiliateData?.affiliate?.performance?.acceptedOffers || 0} accepted
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">Response Rate</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {affiliateData?.affiliate?.performance?.responseRate?.toFixed(1) || 0}%
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Avg: {affiliateData?.affiliate?.performance?.avgResponseTime?.toFixed(1) || 0} min
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">
                          Average Rate
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          ${affiliateData?.affiliate?.performance?.avgRate?.toFixed(0) || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Per trip average
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">
                          Completion Rate
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {affiliateData?.affiliate?.performance?.completionRate?.toFixed(1) || 0}%
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Trip completion success
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">
                          Fleet Size
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {affiliateData?.affiliate?.fleet?.totalVehicles || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {affiliateData?.affiliate?.fleet?.vehicleTypes?.length || 0} vehicle types
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">
                          Service Coverage
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {affiliateData?.affiliate?.service?.citiesCovered?.length || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Cities served
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">
                          Counter Offers
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {affiliateData?.affiliate?.performance?.counterOffers || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Negotiation attempts
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">
                          Documents Status
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {affiliateData?.affiliate?.compliance?.verifiedDocuments || 0}/{affiliateData?.affiliate?.compliance?.totalDocuments || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Verified documents
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Rates Tab */}
                <TabsContent value="rates" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Rate Structure</CardTitle>
                      <CardDescription>
                        Current pricing for different vehicle types and services
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {rateCards.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {rateCards.map((rate: any) => (
                            <div
                              key={rate.id}
                              className="p-4 border rounded-lg"
                            >
                              <div className="flex justify-between items-center mb-2">
                                <span className="font-medium">
                                  {rate.vehicle_type}
                                </span>
                                <Badge
                                  variant="outline"
                                  className="bg-green-100 text-green-700"
                                >
                                  {rate.status}
                                </Badge>
                              </div>
                              <div className="space-y-2 text-sm">
                                {rate.airport_transfer_flat_rate && (
                                  <p>
                                    Airport Transfer: $
                                    {rate.airport_transfer_flat_rate}
                                  </p>
                                )}
                                {rate.per_hour_rate && (
                                  <p>Hourly Rate: ${rate.per_hour_rate}/hr</p>
                                )}
                                {rate.p2p_point_to_point_rate && (
                                  <p>
                                    Point-to-Point: $
                                    {rate.p2p_point_to_point_rate}/mi
                                  </p>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="p-4 border rounded-lg text-center">
                          <span className="text-sm text-muted-foreground">No rate cards configured</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Fleet Tab */}
                <TabsContent value="fleet" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Car className="h-5 w-5" />
                        Fleet Overview
                      </CardTitle>
                      <CardDescription>
                        Vehicle inventory and specifications
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {fleet.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <h4 className="font-medium">Fleet Statistics</h4>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="p-4 bg-blue-50 rounded-lg text-center">
                                <div className="text-2xl font-bold text-blue-600">
                                  {fleet.length}
                                </div>
                                <p className="text-sm text-blue-700">
                                  Total Vehicles
                                </p>
                              </div>
                              <div className="p-4 bg-green-50 rounded-lg text-center">
                                <div className="text-2xl font-bold text-green-600">
                                  {drivers.length}
                                </div>
                                <p className="text-sm text-green-700">
                                  Active Drivers
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-4">
                            <h4 className="font-medium">Recent Vehicles</h4>
                            <div className="space-y-3">
                              {fleet.slice(0, 3).map((vehicle: any) => (
                                <div
                                  key={vehicle.id}
                                  className="p-4 border rounded-lg"
                                >
                                  <div className="flex justify-between items-center mb-2">
                                    <span className="font-medium">
                                      {vehicle.year} {vehicle.make}{" "}
                                      {vehicle.model}
                                    </span>
                                    <Badge
                                      variant={
                                        vehicle.status === "active"
                                          ? "default"
                                          : "secondary"
                                      }
                                    >
                                      {vehicle.status}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-muted-foreground">
                                    {vehicle.type} • License:{" "}
                                    {vehicle.license_plate}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <h4 className="font-medium">Fleet Statistics</h4>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="p-4 bg-blue-50 rounded-lg text-center">
                              <div className="text-2xl font-bold text-blue-600">
                                {affiliateData?.affiliate?.fleet?.totalVehicles || 0}
                              </div>
                              <p className="text-sm text-blue-700">
                                Total Vehicles
                              </p>
                            </div>
                            <div className="p-4 bg-green-50 rounded-lg text-center">
                              <div className="text-2xl font-bold text-green-600">
                                {affiliateData?.affiliate?.staff?.totalDrivers || 0}
                              </div>
                              <p className="text-sm text-green-700">
                                Active Drivers
                              </p>
                            </div>
                          </div>
                          {affiliateData?.affiliate?.fleet?.vehiclesByType && Object.keys(affiliateData.affiliate.fleet.vehiclesByType).length > 0 ? (
                            <div className="space-y-2">
                              {Object.entries(affiliateData.affiliate.fleet.vehiclesByType).map(([type, count]) => (
                                <div key={type} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                                  <span className="text-sm capitalize">{type}</span>
                                  <Badge variant="outline">{count} vehicle{count !== 1 ? 's' : ''}</Badge>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="p-3 bg-gray-50 rounded text-center">
                              <span className="text-sm text-muted-foreground">No vehicles registered</span>
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Service Area Tab */}
                <TabsContent value="service-area" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <MapPin className="h-5 w-5" />
                        Service Coverage
                      </CardTitle>
                      <CardDescription>
                        Geographic coverage and service capabilities
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <h4 className="font-medium">Coverage Areas</h4>
                          <div className="h-[200px] bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg flex items-center justify-center">
                            <div className="text-center">
                              <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                              <p className="text-sm text-gray-600">
                                Interactive coverage map
                              </p>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Primary Hub
                              </p>
                              <p className="font-medium">
                                {affiliateDetails?.address?.city ||
                                  affiliate.city}
                                ,{" "}
                                {affiliateDetails?.address?.state ||
                                  affiliate.state}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Coverage Radius
                              </p>
                              <p className="font-medium">50 miles</p>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <h4 className="font-medium">Service Capabilities</h4>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                              <span className="text-sm">Airport Transfers</span>
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            </div>
                            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                              <span className="text-sm">Corporate Events</span>
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            </div>
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <span className="text-sm">Wedding Services</span>
                              <XCircle className="h-4 w-4 text-gray-400" />
                            </div>
                            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                              <span className="text-sm">24/7 Availability</span>
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Documents Tab */}
                <TabsContent value="documents" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Shield className="h-5 w-5" />
                        Documents & Compliance
                      </CardTitle>
                      <CardDescription>
                        Required documents and compliance status
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <h4 className="font-medium">Required Documents</h4>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                              <div className="flex items-center gap-2">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                <span className="text-sm">
                                  Business License
                                </span>
                              </div>
                              <Badge
                                variant="outline"
                                className="bg-green-100 text-green-700"
                              >
                                Verified
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                              <div className="flex items-center gap-2">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                <span className="text-sm">
                                  Commercial Insurance
                                </span>
                              </div>
                              <Badge
                                variant="outline"
                                className="bg-green-100 text-green-700"
                              >
                                Verified
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-yellow-500" />
                                <span className="text-sm">
                                  Vehicle Inspections
                                </span>
                              </div>
                              <Badge variant="secondary">In Progress</Badge>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <h4 className="font-medium">
                            Operational Compliance
                          </h4>
                          <div className="space-y-3">
                            <div className="p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium">
                                  Dispatch Integration
                                </span>
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              </div>
                              <p className="text-xs text-muted-foreground">
                                System connected
                              </p>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium">
                                  24/7 Support
                                </span>
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              </div>
                              <p className="text-xs text-muted-foreground">
                                Confirmed availability
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Audit Log Tab */}
                <TabsContent value="audit" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Audit Log
                      </CardTitle>
                      <CardDescription>
                        Activity history and changes
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {affiliateData?.affiliate?.auditLogs && affiliateData.affiliate.auditLogs.length > 0 ? (
                          affiliateData.affiliate?.auditLogs?.map((log: any, index: number) => {
                            const getLogIcon = (action: string) => {
                              switch (action?.toLowerCase()) {
                                case 'approved': case 'approve': return { icon: CheckCircle, color: 'green' };
                                case 'rejected': case 'reject': return { icon: XCircle, color: 'red' };
                                case 'submitted': case 'submit': return { icon: Clock, color: 'yellow' };
                                case 'updated': case 'update': return { icon: FileText, color: 'blue' };
                                case 'created': case 'create': return { icon: Plus, color: 'purple' };
                                default: return { icon: FileText, color: 'gray' };
                              }
                            };

                            const logConfig = getLogIcon(log.action);
                            const LogIcon = logConfig.icon;

                            return (
                              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                                <div className="flex items-center gap-3">
                                  <div className={`w-8 h-8 bg-${logConfig.color}-100 rounded-full flex items-center justify-center`}>
                                    <LogIcon className={`h-4 w-4 text-${logConfig.color}-600`} />
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium capitalize">
                                      {log.action || 'Activity'} {log.table_name ? `- ${log.table_name.replace('_', ' ')}` : ''}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                      {log.details || 'No details available'} • {' '}
                                      {log.created_at ? new Date(log.created_at).toLocaleDateString() : 'Unknown date'}
                                    </p>
                                  </div>
                                </div>
                                <Badge variant="outline" className={`bg-${logConfig.color}-100 text-${logConfig.color}-700`}>
                                  {log.action || 'Activity'}
                                </Badge>
                              </div>
                            );
                          })
                        ) : (
                          <>
                            {/* Show status-based audit entries when no audit logs exist */}
                            {affiliateData?.affiliate?.status?.applicationStatus === 'approved' && (
                              <div className="flex items-center justify-between p-4 border rounded-lg">
                                <div className="flex items-center gap-3">
                                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium">Application Approved</p>
                                    <p className="text-xs text-muted-foreground">
                                      Status: {affiliateData.affiliate.status.applicationStatus} • {' '}
                                      {affiliateData.affiliate.status.applicationSubmittedAt
                                        ? new Date(affiliateData.affiliate.status.applicationSubmittedAt).toLocaleDateString()
                                        : 'Date unknown'}
                                    </p>
                                  </div>
                                </div>
                                <Badge variant="outline" className="bg-green-100 text-green-700">
                                  Approved
                                </Badge>
                              </div>
                            )}

                            {affiliateData?.affiliate?.compliance?.totalDocuments > 0 && (
                              <div className="flex items-center justify-between p-4 border rounded-lg">
                                <div className="flex items-center gap-3">
                                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <FileText className="h-4 w-4 text-blue-600" />
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium">Documents Status</p>
                                    <p className="text-xs text-muted-foreground">
                                      {affiliateData.affiliate.compliance.verifiedDocuments} verified, {' '}
                                      {affiliateData.affiliate.compliance.pendingDocuments} pending
                                    </p>
                                  </div>
                                </div>
                                <Badge variant="outline">
                                  {affiliateData.affiliate.compliance.verifiedDocuments > 0 ? 'Verified' : 'Pending'}
                                </Badge>
                              </div>
                            )}

                            {!affiliateData?.affiliate?.auditLogs?.length && (
                              <div className="p-4 border rounded-lg text-center">
                                <p className="text-sm text-muted-foreground">No audit logs available</p>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </>
          )}
        </div>
      </DrawerContent>

      {/* Rejection Dialog */}
      <RejectionDialog
        open={showRejectDialog}
        onOpenChange={setShowRejectDialog}
        onReject={handleRejectConfirm}
        affiliateName={affiliate?.name || ""}
      />

      {/* Request Updates Dialog (reusing RejectionDialog with different title) */}
      <RejectionDialog
        open={showRequestUpdatesDialog}
        onOpenChange={setShowRequestUpdatesDialog}
        onReject={handleRequestUpdatesConfirm}
        affiliateName={affiliate?.name || ""}
      />
    </Drawer>
  );
}
