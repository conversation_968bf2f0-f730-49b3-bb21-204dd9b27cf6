"use client"

import React, { useEffect, useState } from 'react'
import { useForm, use<PERSON><PERSON>A<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON><PERSON>er, Sheet<PERSON>itle, SheetClose } from "@/app/components/ui/sheet"
import { Button } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Car, Clock, MapPin, DollarSign, Plus, Edit, Trash2, Loader2 } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { Input } from "@/app/components/ui/input"
import { Switch } from "@/app/components/ui/switch"
import { Textarea } from "@/app/components/ui/textarea"
import { Badge } from "@/app/components/ui/badge"
import { ScrollArea } from "@/app/components/ui/scroll-area"
import { toast } from 'sonner'
import { useOrganization } from "@/app/contexts/OrganizationContext"

// TODO: Reuse types from main page
interface Affiliate {
  id: string
  name: string
}

// Mock vehicle types (should ideally come from main page definitions)
const vehicleTypes = [
  { id: "sedan", label: "SEDAN" }, { id: "suv", label: "SUV" }, /* ... add others */
];

// Mock fleet data
const mockFleet = [
  { id: 'f1', make: 'Cadillac', model: 'Escalade', year: '2023', capacity: '6', status: 'active' },
  { id: 'f2', make: 'Mercedes-Benz', model: 'S-Class', year: '2022', capacity: '4', status: 'active' },
  { id: 'f3', make: 'Ford', model: 'Transit', year: '2021', capacity: '14', status: 'maintenance' },
];

// Zod Schema for a single vehicle rate configuration
const vehicleRateSchema = z.object({
  vehicleTypeId: z.string(),
  vehicleTypeLabel: z.string(),
  enabled: z.boolean().default(false),
  useDistanceTime: z.boolean().default(false), // false = P2P, true = Distance + Time
  p2pRate: z.number().optional().nullable(),
  baseDistance: z.number().optional().nullable(), // For D+T
  perMileRate: z.number().optional().nullable(), // For D+T
  perHourRateDT: z.number().optional().nullable(), // For D+T (Depot-to-Depot hourly)
  minMiles: z.number().optional().nullable(), // For D+T
  extraHourRate: z.number().optional().nullable(), // For both? Check definition
  airportRate: z.number().optional().nullable(),
  hourlyRate: z.number().optional().nullable(), // Standard Hourly
  minHours: z.number().optional().nullable(), // Standard Hourly
  cancellationPolicy: z.string().optional().nullable(),
});

// Zod Schema for the entire rates form
const ratesFormSchema = z.object({
  rates: z.array(vehicleRateSchema)
});

type RatesFormValues = z.infer<typeof ratesFormSchema>;

// --- Fleet Types/Schema ---
const vehicleStatusSchema = z.enum(['active', 'maintenance', 'inactive']);
type VehicleStatus = z.infer<typeof vehicleStatusSchema>;

const vehicleSchema = z.object({
  id: z.string().uuid(),
  make: z.string().min(1, "Make is required"),
  model: z.string().min(1, "Model is required"),
  year: z.number().int().min(1980, "Invalid year").max(new Date().getFullYear() + 1, "Invalid year"),
  capacity: z.number().int().min(1, "Capacity must be at least 1"),
  vin: z.string().optional().nullable(), // Vehicle Identification Number
  licensePlate: z.string().optional().nullable(),
  status: vehicleStatusSchema.default('active'),
});

type Vehicle = z.infer<typeof vehicleSchema>;

// Real API function to fetch affiliate fleet
const fetchAffiliateFleet = async (affiliateId: string): Promise<Vehicle[]> => {
  console.log(`Fetching fleet for affiliate: ${affiliateId}`);

  try {
    const response = await fetch(`/api/super-admin/affiliates/${affiliateId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch affiliate details');
    }

    const data = await response.json();

    // Transform fleet data to match Vehicle interface
    const vehicles: Vehicle[] = (data.fleet || []).map((vehicle: any) => ({
      id: vehicle.id,
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      capacity: vehicle.capacity,
      vin: vehicle.vin || '',
      licensePlate: vehicle.license_plate || '',
      status: vehicle.status || 'active'
    }));

    return vehicles;
  } catch (error) {
    console.error('Error fetching affiliate fleet:', error);
    throw error;
  }
};

interface RatesFleetDetailsDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  affiliate: Affiliate | null
}

// Real API function to fetch affiliate rates
const fetchAffiliateRates = async (affiliateId: string): Promise<RatesFormValues> => {
  console.log(`Fetching rates for affiliate: ${affiliateId}`);

  try {
    const response = await fetch(`/api/super-admin/affiliates/${affiliateId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch affiliate details');
    }

    const data = await response.json();

    // Create a map of existing rate cards by vehicle type
    const rateCardsMap = new Map();
    (data.rateCards || []).forEach((card: any) => {
      rateCardsMap.set(card.vehicle_type, card);
    });

    // Transform rate cards to match the form structure
    const existingRates = vehicleTypes.map(vt => {
      const rateCard = rateCardsMap.get(vt.id);

      return {
        vehicleTypeId: vt.id,
        vehicleTypeLabel: vt.label,
        enabled: !!rateCard,
        useDistanceTime: rateCard?.pricing_model_type === 'DT',
        p2pRate: rateCard?.p2p_point_to_point_rate || null,
        baseDistance: rateCard?.dt_base_fee || null,
        perMileRate: rateCard?.dt_per_mile_rate || null,
        perHourRateDT: rateCard?.dt_per_hour_rate || null,
        minMiles: rateCard?.dt_min_miles || null,
        extraHourRate: rateCard?.p2p_extra_hour_rate || null,
        airportRate: rateCard?.airport_transfer_flat_rate || null,
        hourlyRate: rateCard?.per_hour_rate || null,
        minHours: rateCard?.minimum_hours || null,
        cancellationPolicy: null, // This field might need to be added to the database
      };
    });

    return { rates: existingRates };
  } catch (error) {
    console.error('Error fetching affiliate rates:', error);
    throw error;
  }
};

export function RatesFleetDetailsDrawer({ open, onOpenChange, affiliate }: RatesFleetDetailsDrawerProps) {
  const { currentOrganization } = useOrganization();
  const [isLoadingRates, setIsLoadingRates] = useState(false);
  const [isLoadingFleet, setIsLoadingFleet] = useState(false);
  const [fleetData, setFleetData] = useState<Vehicle[]>([]);
  const [currentTab, setCurrentTab] = useState("rates");

  // State for Modals/Dialogs (placeholders)
  const [isAddVehicleModalOpen, setIsAddVehicleModalOpen] = useState(false);
  const [isEditVehicleModalOpen, setIsEditVehicleModalOpen] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState<Vehicle | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [deletingVehicleId, setDeletingVehicleId] = useState<string | null>(null);

  const ratesForm = useForm<RatesFormValues>({
    resolver: zodResolver(ratesFormSchema),
    defaultValues: {
      rates: []
    }
  });

  const { fields: rateFields, replace: replaceRates } = useFieldArray({
    control: ratesForm.control,
    name: "rates"
  });

  useEffect(() => {
    if (affiliate?.id) {
      // Fetch Rates
      setIsLoadingRates(true);
      fetchAffiliateRates(affiliate.id)
        .then(data => ratesForm.reset({ rates: data.rates }))
        .catch(error => {
          console.error("Error fetching affiliate rates:", error);
          toast.error("Failed to load affiliate rates.");
        })
        .finally(() => setIsLoadingRates(false));

      // Fetch Fleet
      setIsLoadingFleet(true);
      fetchAffiliateFleet(affiliate.id)
        .then(setFleetData)
        .catch(error => {
          console.error("Error fetching affiliate fleet:", error);
          toast.error("Failed to load affiliate fleet.");
          setFleetData([]); // Clear on error
        })
        .finally(() => setIsLoadingFleet(false));

    } else {
      // Clear form and fleet data if affiliate is null
      ratesForm.reset({ rates: [] });
      setFleetData([]);
    }
  }, [affiliate, ratesForm.reset]);

  const handleRatesSubmit = (values: RatesFormValues) => {
    console.log("Submitting Rates:", values);
    return new Promise(resolve => setTimeout(() => {
       toast.success(`Rates for ${affiliate?.name} saved successfully!`);
       resolve(true);
    }, 1000));
  };

  const handleAddVehicle = () => {
    console.log("Open Add Vehicle Modal");
    setIsAddVehicleModalOpen(true);
    // TODO: Implement Add Vehicle Modal Component & Logic
  };

  const handleEditVehicle = (vehicle: Vehicle) => {
    console.log("Open Edit Vehicle Modal for:", vehicle.id);
    setEditingVehicle(vehicle);
    setIsEditVehicleModalOpen(true);
    // TODO: Implement Edit Vehicle Modal Component & Logic
  };

  const handleDeleteVehicle = (vehicleId: string) => {
    console.log("Open Delete Confirmation for:", vehicleId);
    setDeletingVehicleId(vehicleId);
    setIsDeleteConfirmOpen(true);
    // TODO: Implement Delete Confirmation Dialog & Logic
  };

  if (!affiliate) return null

  const tenantType = currentOrganization?.organization_type;
  const isWhiteLabel = tenantType === 'white_label';

  const renderWhiteLabelBanner = () => {
    if (!isWhiteLabel) return null;
    return (
      <div className="mb-4 p-3 rounded bg-primary/10 text-primary font-semibold text-center">
        White-Label Tenant: Custom branding and rate/fleet fields may apply.
      </div>
    );
  };

  const renderVehicleStatusBadge = (status: VehicleStatus) => {
    switch (status) {
      case 'active': return <Badge variant="success">Active</Badge>;
      case 'maintenance': return <Badge variant="secondary">Maintenance</Badge>;
      case 'inactive': return <Badge variant="secondary">Inactive</Badge>;
      default: return <Badge variant="outline">Unknown</Badge>;
    }
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-6xl w-full p-0 flex flex-col">
        <SheetHeader className="px-6 pt-6 pb-4 border-b sticky top-0 bg-background z-10">
          <div className="flex justify-between items-center">
             <div>
               <SheetTitle className="text-xl font-semibold">Rates & Fleet Details: {affiliate.name}</SheetTitle>
               <p className="text-sm text-muted-foreground">Detailed configuration for {affiliate.name}.</p>
              </div>
              <SheetClose asChild>
                <Button size="icon" variant="ghost">
                  <span className="sr-only">Close</span>
                  ×
                </Button>
             </SheetClose>
          </div>
        </SheetHeader>
        <div className="p-6 overflow-y-auto h-[calc(100vh-80px)]">
          {renderWhiteLabelBanner()}
          <Tabs value={currentTab} onValueChange={setCurrentTab} className="relative">
             <TabsList className="sticky top-0 mb-4 z-10 bg-background py-2">
               <TabsTrigger value="rates"><DollarSign className="mr-2 h-4 w-4" /> Rates</TabsTrigger>
               <TabsTrigger value="fleet"><Car className="mr-2 h-4 w-4" /> Fleet</TabsTrigger>
               <TabsTrigger value="serviceArea"><MapPin className="mr-2 h-4 w-4" /> Service Area</TabsTrigger>
               <TabsTrigger value="hours"><Clock className="mr-2 h-4 w-4" /> Operating Hours</TabsTrigger>
             </TabsList>

             <TabsContent value="rates">
                <Card>
                  <CardHeader>
                    <CardTitle>Vehicle Type Rates</CardTitle>
                    <CardDescription>Configure rates for each vehicle type and pricing model for {affiliate.name}.</CardDescription>
                  </CardHeader>
                  <CardContent>
                     {isLoadingRates ? (
                       <div className="flex justify-center items-center h-60">
                          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                          <p className="ml-2">Loading rates...</p>
                       </div>
                     ) : (
                        <form onSubmit={ratesForm.handleSubmit(handleRatesSubmit)}>
                         <div className="overflow-x-auto border rounded-md">
                           <Table>
                             <TableHeader>
                               <TableRow>
                                 <TableHead className="sticky left-0 bg-background z-10 whitespace-nowrap w-[150px]">Vehicle Type</TableHead>
                                 <TableHead className="w-[80px]">Enabled</TableHead>
                                 <TableHead className="w-[120px]">Pricing</TableHead>
                                 <TableHead className="whitespace-nowrap">P2P Rate ($)</TableHead>
                                 <TableHead className="whitespace-nowrap">Base Dist. (mi)</TableHead>
                                 <TableHead className="whitespace-nowrap">Per Mile ($)</TableHead>
                                 <TableHead className="whitespace-nowrap">Per Hour (D+T $)</TableHead>
                                 <TableHead className="whitespace-nowrap">Min Miles (D+T)</TableHead>
                                 <TableHead className="whitespace-nowrap">Extra Hour ($)</TableHead>
                                 <TableHead className="whitespace-nowrap">Airport Rate ($)</TableHead>
                                 <TableHead className="whitespace-nowrap">Hourly Rate ($)</TableHead>
                                 <TableHead className="whitespace-nowrap">Min Hours</TableHead>
                                 <TableHead className="whitespace-nowrap min-w-[250px]">Cancellation Policy</TableHead>
                               </TableRow>
                             </TableHeader>
                             <TableBody>
                               {rateFields.map((field, index) => {
                                 const isEnabled = ratesForm.watch(`rates.${index}.enabled`);
                                 const useDistanceTime = ratesForm.watch(`rates.${index}.useDistanceTime`);
                                 return (
                                   <TableRow key={field.id}>
                                     <TableCell className="font-medium sticky left-0 bg-background z-10 whitespace-nowrap w-[150px]">
                                         {field.vehicleTypeLabel}
                                         <input type="hidden" {...ratesForm.register(`rates.${index}.vehicleTypeId`)} />
                                         <input type="hidden" {...ratesForm.register(`rates.${index}.vehicleTypeLabel`)} />
                                     </TableCell>
                                     <TableCell className="w-[80px]">
                                       <Controller
                                         control={ratesForm.control}
                                         name={`rates.${index}.enabled`}
                                         render={({ field: controllerField }) => (
                                           <Switch
                                             checked={controllerField.value}
                                             onCheckedChange={controllerField.onChange}
                                           />
                                         )}
                                       />
                                     </TableCell>
                                     <TableCell className="w-[120px]">
                                       <div className="flex items-center gap-1">
                                         <Controller
                                             control={ratesForm.control}
                                             name={`rates.${index}.useDistanceTime`}
                                             render={({ field: controllerField }) => (
                                                 <Switch
                                                   checked={controllerField.value}
                                                   onCheckedChange={controllerField.onChange}
                                                   disabled={!isEnabled}
                                                 />
                                             )}
                                         />
                                         <span className="text-xs whitespace-nowrap">{useDistanceTime ? "D+T" : "P2P"}</span>
                                       </div>
                                     </TableCell>
                                     <TableCell><Input type="number" step="0.01" className="w-24" disabled={!isEnabled || useDistanceTime} {...ratesForm.register(`rates.${index}.p2pRate`, { valueAsNumber: true })} /></TableCell>
                                     <TableCell><Input type="number" step="0.1" className="w-24" disabled={!isEnabled || !useDistanceTime} {...ratesForm.register(`rates.${index}.baseDistance`, { valueAsNumber: true })} /></TableCell>
                                     <TableCell><Input type="number" step="0.01" className="w-24" disabled={!isEnabled || !useDistanceTime} {...ratesForm.register(`rates.${index}.perMileRate`, { valueAsNumber: true })} /></TableCell>
                                     <TableCell><Input type="number" step="0.01" className="w-24" disabled={!isEnabled || !useDistanceTime} {...ratesForm.register(`rates.${index}.perHourRateDT`, { valueAsNumber: true })} /></TableCell>
                                     <TableCell><Input type="number" step="1" className="w-24" disabled={!isEnabled || !useDistanceTime} {...ratesForm.register(`rates.${index}.minMiles`, { valueAsNumber: true })} /></TableCell>
                                     <TableCell><Input type="number" step="0.01" className="w-24" disabled={!isEnabled} {...ratesForm.register(`rates.${index}.extraHourRate`, { valueAsNumber: true })} /></TableCell>
                                     <TableCell><Input type="number" step="0.01" className="w-24" disabled={!isEnabled} {...ratesForm.register(`rates.${index}.airportRate`, { valueAsNumber: true })} /></TableCell>
                                     <TableCell><Input type="number" step="0.01" className="w-24" disabled={!isEnabled} {...ratesForm.register(`rates.${index}.hourlyRate`, { valueAsNumber: true })} /></TableCell>
                                     <TableCell><Input type="number" step="1" className="w-24" disabled={!isEnabled} {...ratesForm.register(`rates.${index}.minHours`, { valueAsNumber: true })} /></TableCell>
                                     <TableCell><Input type="text" className="min-w-[250px]" disabled={!isEnabled} {...ratesForm.register(`rates.${index}.cancellationPolicy`)} /></TableCell>
                                   </TableRow>
                                 );
                               })}
                             </TableBody>
                           </Table>
                         </div>
                         {rateFields.length === 0 && !isLoadingRates && (
                            <p className="text-center text-muted-foreground py-4">No vehicle types found or rates not configured.</p>
                         )}
                         <div className="flex justify-end mt-6 sticky bottom-0 bg-background py-4 border-t">
                           <Button type="submit" disabled={ratesForm.formState.isSubmitting || isLoadingRates}>
                             {ratesForm.formState.isSubmitting ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...</> : "Save Rates"}
                           </Button>
                         </div>
                       </form>
                     )}
                  </CardContent>
                </Card>
             </TabsContent>

             <TabsContent value="fleet">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle>Fleet Management</CardTitle>
                      <CardDescription>Manage vehicles in {affiliate.name}'s fleet.</CardDescription>
                    </div>
                    <Button size="sm" onClick={handleAddVehicle} disabled={isLoadingFleet}>
                        <Plus className="mr-2 h-4 w-4" /> Add Vehicle
                    </Button>
                  </CardHeader>
                  <CardContent>
                     {isLoadingFleet ? (
                       <div className="flex justify-center items-center h-40 border rounded-md">
                          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                          <p className="ml-2">Loading fleet...</p>
                       </div>
                     ) : (
                       <div className="rounded-md border">
                         <Table>
                           <TableHeader>
                             <TableRow>
                               <TableHead>Make</TableHead>
                               <TableHead>Model</TableHead>
                               <TableHead>Year</TableHead>
                               <TableHead>Capacity</TableHead>
                               <TableHead>License Plate</TableHead>
                               <TableHead>Status</TableHead>
                               <TableHead className="text-right">Actions</TableHead>
                             </TableRow>
                           </TableHeader>
                           <TableBody>
                             {fleetData.length === 0 ? (
                               <TableRow>
                                 <TableCell colSpan={7} className="h-24 text-center text-muted-foreground">
                                   No vehicles found for this affiliate.
                                 </TableCell>
                               </TableRow>
                             ) : (
                               fleetData.map(vehicle => (
                                 <TableRow key={vehicle.id}>
                                   <TableCell>{vehicle.make}</TableCell>
                                   <TableCell>{vehicle.model}</TableCell>
                                   <TableCell>{vehicle.year}</TableCell>
                                   <TableCell>{vehicle.capacity}</TableCell>
                                   <TableCell>{vehicle.licensePlate || '-'}</TableCell>
                                   <TableCell>{renderVehicleStatusBadge(vehicle.status)}</TableCell>
                                   <TableCell className="text-right">
                                     <Button variant="ghost" size="icon" className="mr-1 h-7 w-7" onClick={() => handleEditVehicle(vehicle)}>
                                         <Edit className="h-4 w-4" />
                                         <span className="sr-only">Edit</span>
                                     </Button>
                                     <Button variant="ghost" size="icon" className="text-destructive hover:text-destructive h-7 w-7" onClick={() => handleDeleteVehicle(vehicle.id)}>
                                         <Trash2 className="h-4 w-4" />
                                         <span className="sr-only">Delete</span>
                                     </Button>
                                   </TableCell>
                                 </TableRow>
                               ))
                             )}
                           </TableBody>
                         </Table>
                       </div>
                     )}
                  </CardContent>
                </Card>
             </TabsContent>

             <TabsContent value="serviceArea">
                <Card>
                  <CardHeader>
                    <CardTitle>Service Area</CardTitle>
                    <CardDescription>Define the geographical areas served (e.g., ZIP codes, radius).</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Textarea
                      placeholder="Enter service areas, e.g., ZIP codes (90210, 90211), radius (15 miles from LAX), or description..."
                      rows={10}
                    />
                    <div className="flex justify-end mt-4">
                       <Button>Save Service Area</Button>
                    </div>
                  </CardContent>
                </Card>
             </TabsContent>

             <TabsContent value="hours">
                <Card>
                  <CardHeader>
                     <CardTitle>Operating Hours</CardTitle>
                     <CardDescription>Set standard operating hours for the affiliate.</CardDescription>
                  </CardHeader>
                  <CardContent>
                     {/* Placeholder: Implement hours configuration UI */}
                     <div className="p-6 border rounded-md bg-muted/50 text-center text-muted-foreground">
                       Operating Hours configuration UI (e.g., Mon-Sun time inputs) will go here.
                     </div>
                  </CardContent>
                </Card>
             </TabsContent>
          </Tabs>
        </div>
      </SheetContent>
      {/* TODO: Add Modals/Dialogs for Add/Edit/Delete Vehicle */}
      {/* Placeholder: AddVehicleModal... */}
      {/* Placeholder: EditVehicleModal... */}
      {/* Placeholder: ConfirmDeleteDialog... */}
    </Sheet>
  )
}