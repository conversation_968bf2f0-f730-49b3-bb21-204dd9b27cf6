"use client"

import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, She<PERSON><PERSON><PERSON><PERSON>, SheetClose } from "@/app/components/ui/sheet"
import { Button } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/app/components/ui/tabs"
import { Badge } from "@/app/components/ui/badge"
import { Separator } from "@/app/components/ui/separator"
import { 
  Building2, 
  MapPin, 
  Phone, 
  Mail, 
  Calendar, 
  Star, 
  Users, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Info
} from "lucide-react"
import { Affiliate } from "@/app/lib/types/affiliates"
import { useOrganization } from "@/app/contexts/OrganizationContext"

interface GeneralDetailsDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  affiliate: Affiliate | null
}

export function GeneralDetailsDrawer({ open, onOpenChange, affiliate }: GeneralDetailsDrawerProps) {
  const { currentOrganization } = useOrganization();
  if (!affiliate) return null;

  const tenantType = currentOrganization?.organization_type;
  const isWhiteLabel = tenantType === 'white_label';

  const renderWhiteLabelBanner = () => {
    if (!isWhiteLabel) return null;
    return (
      <div className="mb-4 p-3 rounded bg-primary/10 text-primary font-semibold text-center">
        White-Label Tenant: Custom branding and profile fields may apply.
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getVerificationStatusBadge = (status: string) => {
    switch (status) {
      case 'verified': return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="mr-1 h-3 w-3" />Verified</Badge>
      case 'pending': return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><Clock className="mr-1 h-3 w-3" />Pending</Badge>
      case 'Draft': return <Badge className="bg-blue-100 text-blue-800 border-blue-200"><Info className="mr-1 h-3 w-3" />Draft</Badge>
      default: return <Badge className="bg-red-100 text-red-800 border-red-200"><AlertCircle className="mr-1 h-3 w-3" />Unverified</Badge>
    }
  }

  const renderRatingStars = (rating: number) => {
    return (
      <div className="flex items-center">
        <div className="mr-2 font-semibold">{rating.toFixed(1)}</div>
        <div className="flex">
          {[1, 2, 3, 4, 5].map(star => (
            <Star
              key={star}
              className={`h-4 w-4 ${star <= Math.round(rating) ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
            />
          ))}
        </div>
      </div>
    )
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-4xl w-full p-0">
        <SheetHeader className="px-6 pt-6 pb-4 border-b">
          <div className="flex justify-between items-center">
            <div>
              <SheetTitle className="text-xl font-semibold">General Details: {affiliate.name}</SheetTitle>
              <p className="text-sm text-muted-foreground">Complete profile information for {affiliate.name}</p>
            </div>
            <SheetClose asChild>
              <Button size="icon" variant="ghost">
                <span className="sr-only">Close</span>
                ×
              </Button>
            </SheetClose>
          </div>
        </SheetHeader>
        
        <div className="p-6 overflow-y-auto h-[calc(100vh-80px)]">
          {renderWhiteLabelBanner()}
          <Tabs defaultValue="overview">
            <TabsList className="mb-4">
              <TabsTrigger value="overview"><Building2 className="mr-2 h-4 w-4" />Overview</TabsTrigger>
              <TabsTrigger value="contact"><Phone className="mr-2 h-4 w-4" />Contact Info</TabsTrigger>
              <TabsTrigger value="metrics"><TrendingUp className="mr-2 h-4 w-4" />Performance</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Company Name</label>
                      <p className="text-lg font-semibold">{affiliate.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Business Name</label>
                      <p className="text-lg">{affiliate.businessName || affiliate.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Status</label>
                      <div className="mt-1">
                        <Badge className={getStatusBadgeClass(affiliate.status)}>
                          {affiliate.status.charAt(0).toUpperCase() + affiliate.status.slice(1)}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Verification Status</label>
                      <div className="mt-1">
                        {getVerificationStatusBadge(affiliate.verificationStatus)}
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Member Since</label>
                      <p className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        {formatDate(affiliate.createdAt)}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                      <p className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        {formatDate(affiliate.updatedAt)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Business Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Business Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Tax ID</label>
                      <p>{affiliate.businessInfo?.taxId || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Business Type</label>
                      <p>{affiliate.businessInfo?.businessType || 'Not specified'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Year Established</label>
                      <p>{affiliate.businessInfo?.yearEstablished || 'Not provided'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="contact" className="space-y-6">
              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Phone className="h-5 w-5" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Email Address</label>
                      <p className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        {affiliate.email}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Phone Number</label>
                      <p className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        {affiliate.phone}
                      </p>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Business Address</label>
                    <div className="flex items-start gap-2 mt-1">
                      <MapPin className="h-4 w-4 mt-1" />
                      <div>
                        <p>{affiliate.street_address || affiliate.address?.street}</p>
                        <p>{affiliate.city || affiliate.address?.city}, {affiliate.state || affiliate.address?.state} {affiliate.zip || affiliate.address?.zip}</p>
                        <p>{affiliate.country || affiliate.address?.country}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Persons */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Contact Persons
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {affiliate.contactPersons && affiliate?.contactPersons?.length > 0 ? (
                    <div className="space-y-3">
                      {affiliate?.contactPersons?.map((contact, index) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <p className="font-medium">{contact.name}</p>
                          <p className="text-sm text-muted-foreground">{contact.role}</p>
                          <p className="text-sm">{contact.email}</p>
                          <p className="text-sm">{contact.phone}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No contact persons added</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="metrics" className="space-y-6">
              {/* Performance Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Performance Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{affiliate.metrics?.rating || 0}</div>
                      <div className="text-sm text-muted-foreground">Average Rating</div>
                      <div className="mt-2">
                        {renderRatingStars(affiliate.metrics?.rating || 0)}
                      </div>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{affiliate.metrics?.completionRate || 0}%</div>
                      <div className="text-sm text-muted-foreground">Completion Rate</div>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{affiliate.metrics?.tripCount || 0}</div>
                      <div className="text-sm text-muted-foreground">Total Trips</div>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">{affiliate.metrics?.reviewCount || 0}</div>
                      <div className="text-sm text-muted-foreground">Reviews</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Additional Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle>Additional Performance Data</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">On-Time Rate</label>
                      <p className="text-lg font-semibold">{affiliate.metrics?.onTimeRate || 0}%</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Response Time</label>
                      <p className="text-lg font-semibold">{affiliate.metrics?.responseTime || 0} min</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Last Active</label>
                      <p className="text-lg">{affiliate.lastActive ? formatDate(affiliate.lastActive) : 'N/A'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  )
}
