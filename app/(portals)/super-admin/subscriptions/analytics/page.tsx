"use client"

import React, { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { But<PERSON> } from "@/app/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/app/components/ui/tabs"
import { Progress } from "@/app/components/ui/progress"
import { ArrowUpRight, ArrowDownRight, ArrowRight, Download, ChevronRight, Filter, RefreshCw } from "lucide-react"
import { Badge } from "@/app/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { AuthCheck } from "@/app/components/auth-check"
import { Input } from "@/app/components/ui/input"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieC<PERSON>,
  Pie,
  Cell,
  <PERSON><PERSON><PERSON>,
  Area
} from "recharts"
import { cn } from "@/lib/utils"
import { DateRangePicker } from "@/app/components/ui/date-range-picker"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { DollarSign, TrendingUp, TrendingDown, Users, BarChart3, Layers, CalendarDays } from "lucide-react"
import { subDays } from 'date-fns'
import type { DateRange } from 'react-day-picker'

// Mock data for the analytics dashboard
const mockRevenueData = [
  { month: 'Jan', mrr: 12000, newMrr: 1500, churnedMrr: 500 },
  { month: 'Feb', mrr: 13000, newMrr: 1800, churnedMrr: 800 },
  { month: 'Mar', mrr: 13500, newMrr: 1200, churnedMrr: 700 },
  { month: 'Apr', mrr: 14500, newMrr: 2100, churnedMrr: 1100 },
  { month: 'May', mrr: 15200, newMrr: 1700, churnedMrr: 1000 },
  { month: 'Jun', mrr: 16500, newMrr: 2300, churnedMrr: 1000 },
  { month: 'Jul', mrr: 17800, newMrr: 2000, churnedMrr: 700 },
  { month: 'Aug', mrr: 19100, newMrr: 2200, churnedMrr: 900 },
  { month: 'Sep', mrr: 21000, newMrr: 2600, churnedMrr: 700 },
  { month: 'Oct', mrr: 22400, newMrr: 2100, churnedMrr: 700 },
  { month: 'Nov', mrr: 24300, newMrr: 2700, churnedMrr: 800 },
  { month: 'Dec', mrr: 26200, newMrr: 2800, churnedMrr: 900 },
];

const mockPlanDistribution = [
  { name: 'Essential', value: 38, color: '#94a3b8' },
  { name: 'Professional', value: 45, color: '#3b82f6' },
  { name: 'Business', value: 15, color: '#8b5cf6' },
  { name: 'Enterprise', value: 2, color: '#6366f1' },
];

const mockChurnData = [
  { month: 'Jan', rate: 2.1 },
  { month: 'Feb', rate: 2.3 },
  { month: 'Mar', rate: 2.0 },
  { month: 'Apr', rate: 1.8 },
  { month: 'May', rate: 1.9 },
  { month: 'Jun', rate: 1.7 },
  { month: 'Jul', rate: 1.5 },
  { month: 'Aug', rate: 1.6 },
  { month: 'Sep', rate: 1.4 },
  { month: 'Oct', rate: 1.3 },
  { month: 'Nov', rate: 1.2 },
  { month: 'Dec', rate: 1.1 },
];

const mockUpgradeDowngradeData = [
  { month: 'Jan', upgrades: 12, downgrades: 5 },
  { month: 'Feb', upgrades: 15, downgrades: 7 },
  { month: 'Mar', upgrades: 18, downgrades: 4 },
  { month: 'Apr', upgrades: 14, downgrades: 6 },
  { month: 'May', upgrades: 16, downgrades: 8 },
  { month: 'Jun', upgrades: 21, downgrades: 5 },
  { month: 'Jul', upgrades: 24, downgrades: 4 },
  { month: 'Aug', upgrades: 19, downgrades: 9 },
  { month: 'Sep', upgrades: 22, downgrades: 6 },
  { month: 'Oct', upgrades: 25, downgrades: 7 },
  { month: 'Nov', upgrades: 28, downgrades: 4 },
  { month: 'Dec', upgrades: 30, downgrades: 5 },
];

const mockCustomerLifetimeValue = [
  { month: 'Jan', ltv: 2400 },
  { month: 'Feb', ltv: 2450 },
  { month: 'Mar', ltv: 2500 },
  { month: 'Apr', ltv: 2550 },
  { month: 'May', ltv: 2600 },
  { month: 'Jun', ltv: 2650 },
  { month: 'Jul', ltv: 2700 },
  { month: 'Aug', ltv: 2750 },
  { month: 'Sep', ltv: 2800 },
  { month: 'Oct', ltv: 2900 },
  { month: 'Nov', ltv: 3000 },
  { month: 'Dec', ltv: 3100 },
];

const mockAcquisitionCost = [
  { month: 'Jan', cac: 380 },
  { month: 'Feb', cac: 390 },
  { month: 'Mar', cac: 385 },
  { month: 'Apr', cac: 400 },
  { month: 'May', cac: 410 },
  { month: 'Jun', cac: 405 },
  { month: 'Jul', cac: 395 },
  { month: 'Aug', cac: 390 },
  { month: 'Sep', cac: 385 },
  { month: 'Oct', cac: 380 },
  { month: 'Nov', cac: 375 },
  { month: 'Dec', cac: 370 },
];

const mockPlans = [
  { value: "all", label: "All Plans" },
  { value: "enterprise", label: "Enterprise" },
  { value: "premium", label: "Premium" },
  { value: "standard", label: "Standard" },
  { value: "basic", label: "Basic" },
]

function formatCurrency(amount: number) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}

function SubscriptionAnalyticsDashboard() {
  const [timeframe, setTimeframe] = useState<string>("yearly")
  const [isRefreshing, setIsRefreshing] = useState(false)
  
  // Simple state for filter values
  const [filterPlan, setFilterPlan] = useState<string>("all")
  
  // Calculate summary metrics
  const summaryMetrics = useMemo(() => {
    const currentMonth = new Date().getMonth();
    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    
    const currentMRR = mockRevenueData[currentMonth].mrr;
    const prevMRR = mockRevenueData[prevMonth].mrr;
    const mrrGrowth = ((currentMRR - prevMRR) / prevMRR) * 100;
    
    const currentChurn = mockChurnData[currentMonth].rate;
    const prevChurn = mockChurnData[prevMonth].rate;
    const churnChange = ((currentChurn - prevChurn) / prevChurn) * 100;
    
    const currentLTV = mockCustomerLifetimeValue[currentMonth].ltv;
    const prevLTV = mockCustomerLifetimeValue[prevMonth].ltv;
    const ltvGrowth = ((currentLTV - prevLTV) / prevLTV) * 100;
    
    return {
      mrr: currentMRR,
      arr: currentMRR * 12,
      mrrGrowth,
      customers: 256, // Mock total customers
      churnRate: currentChurn,
      churnChange,
      ltv: currentLTV,
      ltvGrowth,
      avgRevenuePerUser: Math.round(currentMRR / 256), // Mock ARPU
    };
  }, []);
  
  const handleRefresh = () => {
    setIsRefreshing(true);
    // Simulate data refresh
    setTimeout(() => setIsRefreshing(false), 1000);
  };
  
  const [date, setDate] = useState<DateRange | undefined>({
    from: subDays(new Date(), 29),
    to: new Date(),
  })
  const [plan, setPlan] = useState("all")

  // TODO: Replace with Supabase data fetching
  const metrics = {
    mrr: 243000,
    churn: 2.3,
    activeSubs: 157,
    upgrades: 12,
    downgrades: 3,
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Subscription Analytics</h2>
          <p className="text-muted-foreground">Analyze subscription revenue, churn, and plan performance</p>
        </div>
        <div className="flex items-center gap-2">
          <DateRangePicker date={date} onDateChange={setDate} />
          <Select value={plan} onValueChange={setPlan}>
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Plan" />
            </SelectTrigger>
            <SelectContent>
              {mockPlans.map(p => (
                <SelectItem key={p.value} value={p.value}>{p.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">MRR</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${metrics?.mrr?.toLocaleString()}</div>
            <div className="flex items-center pt-1 text-xs text-green-600">
              <TrendingUp className="mr-1 h-3.5 w-3.5" />
              <span>+12.5% from last month</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.churn}%</div>
            <div className="flex items-center pt-1 text-xs text-red-600">
              <TrendingDown className="mr-1 h-3.5 w-3.5" />
              <span>-0.8% from last month</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeSubs}</div>
            <div className="flex items-center pt-1 text-xs text-green-600">
              <TrendingUp className="mr-1 h-3.5 w-3.5" />
              <span>+8 new this month</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upgrades / Downgrades</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.upgrades} / {metrics.downgrades}</div>
            <div className="flex items-center pt-1 text-xs text-green-600">
              <TrendingUp className="mr-1 h-3.5 w-3.5" />
              <span>Upgrades</span>
            </div>
            <div className="flex items-center pt-1 text-xs text-amber-600">
              <TrendingDown className="mr-1 h-3.5 w-3.5" />
              <span>Downgrades</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section (placeholders) */}
      <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>MRR Trend</CardTitle>
            <CardDescription>Monthly recurring revenue over time</CardDescription>
          </CardHeader>
          <CardContent className="px-2">
            <div className="h-[260px] w-full flex items-center justify-center bg-muted/50 rounded-md">
              <BarChart3 className="h-8 w-8 text-muted-foreground mr-2" />
              <span className="text-muted-foreground">[MRR Trend Chart Placeholder]</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Churn Rate Trend</CardTitle>
            <CardDescription>Churn rate by month</CardDescription>
          </CardHeader>
          <CardContent className="px-2">
            <div className="h-[260px] w-full flex items-center justify-center bg-muted/50 rounded-md">
              <TrendingDown className="h-8 w-8 text-muted-foreground mr-2" />
              <span className="text-muted-foreground">[Churn Rate Chart Placeholder]</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Subscription Events Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Subscription Events</CardTitle>
          <CardDescription>Latest changes in subscription status</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Org</TableHead>
                <TableHead>Event</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>User</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* TODO: Replace with real data */}
              <TableRow>
                <TableCell>2024-06-28</TableCell>
                <TableCell>Acme Transportation</TableCell>
                <TableCell>Upgrade</TableCell>
                <TableCell>Enterprise</TableCell>
                <TableCell><EMAIL></TableCell>
              </TableRow>
              <TableRow>
                <TableCell>2024-06-27</TableCell>
                <TableCell>Global Logistics</TableCell>
                <TableCell>Churn</TableCell>
                <TableCell>Premium</TableCell>
                <TableCell><EMAIL></TableCell>
              </TableRow>
              <TableRow>
                <TableCell>2024-06-26</TableCell>
                <TableCell>City Tours LLC</TableCell>
                <TableCell>Downgrade</TableCell>
                <TableCell>Standard</TableCell>
                <TableCell><EMAIL></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

export default function SubscriptionAnalyticsPage() {
  return (
    <AuthCheck requiredRoles={['SUPER_ADMIN']}>
      <SubscriptionAnalyticsDashboard />
    </AuthCheck>
  )
} 