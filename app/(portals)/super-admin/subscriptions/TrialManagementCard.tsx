"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Clock, AlertCircle, BellRing, ChevronRight, Save } from "lucide-react"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"

// Define the form schema for trial settings
const trialSettingsSchema = z.object({
  trialDuration: z.number().int().min(1).max(90),
  trialDurationUnit: z.enum(["days", "weeks", "months"]),
  enableAutoConversion: z.boolean(),
  sendReminders: z.boolean(),
  reminderDaysBefore: z.number().int().min(1).max(30),
  enableGracePeriod: z.boolean(),
  gracePeriodDays: z.number().int().min(1).max(30),
})

type TrialSettingsFormValues = z.infer<typeof trialSettingsSchema>

// Trial management components
export function TrialManagementCard() {
  const { toast } = useToast()
  const [isEditing, setIsEditing] = useState(false)
  
  // Default values for the form
  const defaultValues: TrialSettingsFormValues = {
    trialDuration: 14,
    trialDurationUnit: "days",
    enableAutoConversion: true,
    sendReminders: true,
    reminderDaysBefore: 3,
    enableGracePeriod: true,
    gracePeriodDays: 3,
  }
  
  const form = useForm<TrialSettingsFormValues>({
    resolver: zodResolver(trialSettingsSchema),
    defaultValues,
  })
  
  function onSubmit(data: TrialSettingsFormValues) {
    // In a real app, this would save to your backend
    console.log("Form data submitted:", data)
    
    toast({
      title: "Trial settings updated",
      description: "Your changes have been saved successfully.",
    })
    
    setIsEditing(false)
  }
  
  // Trial subscription metrics for summary
  const trialMetrics = {
    totalTrials: 12,
    endingSoon: 4,
    conversionRate: 68,
  }
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Trial Subscription Management</CardTitle>
            <CardDescription>Configure trial periods and conversion settings</CardDescription>
          </div>
          <Badge variant="outline" className="gap-1">
            <Clock className="h-3 w-3" />
            <span>{trialMetrics.totalTrials} Active Trials</span>
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent>
        {!isEditing ? (
          <div className="space-y-4">
            <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
              <div className="bg-muted/50 p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-2">Trial Duration</h3>
                <p className="text-2xl font-bold">{defaultValues.trialDuration} {defaultValues.trialDurationUnit}</p>
                <p className="text-xs text-muted-foreground mt-1">Free access period for new tenants</p>
              </div>
              
              <div className="bg-muted/50 p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-2">Ending Soon</h3>
                <p className="text-2xl font-bold">{trialMetrics.endingSoon}</p>
                <p className="text-xs text-muted-foreground mt-1">Trials ending in next 3 days</p>
              </div>
              
              <div className="bg-muted/50 p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-2">Conversion Rate</h3>
                <p className="text-2xl font-bold">{trialMetrics.conversionRate}%</p>
                <p className="text-xs text-muted-foreground mt-1">Trial to paid conversion</p>
              </div>
            </div>
            
            <div className="space-y-2 pt-4">
              <h3 className="text-sm font-medium">Current Settings</h3>
              <div className="grid gap-3 grid-cols-1 md:grid-cols-2">
                <div className="flex justify-between items-center border p-3 rounded-lg">
                  <div>
                    <p className="font-medium">Auto-Conversion</p>
                    <p className="text-xs text-muted-foreground">Automatically convert trials to paid plans</p>
                  </div>
                  <Badge variant={defaultValues.enableAutoConversion ? "default" : "outline"}>
                    {defaultValues.enableAutoConversion ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
                
                <div className="flex justify-between items-center border p-3 rounded-lg">
                  <div>
                    <p className="font-medium">Trial Reminders</p>
                    <p className="text-xs text-muted-foreground">Send notifications before trial ends</p>
                  </div>
                  <Badge variant={defaultValues.sendReminders ? "default" : "outline"}>
                    {defaultValues.sendReminders ? `${defaultValues.reminderDaysBefore} days before` : "Disabled"}
                  </Badge>
                </div>
                
                <div className="flex justify-between items-center border p-3 rounded-lg">
                  <div>
                    <p className="font-medium">Grace Period</p>
                    <p className="text-xs text-muted-foreground">Additional days after trial expires</p>
                  </div>
                  <Badge variant={defaultValues.enableGracePeriod ? "default" : "outline"}>
                    {defaultValues.enableGracePeriod ? `${defaultValues.gracePeriodDays} days` : "Disabled"}
                  </Badge>
                </div>
                
                <div className="flex justify-between items-center border p-3 rounded-lg">
                  <div>
                    <p className="font-medium">Trial Health</p>
                    <p className="text-xs text-muted-foreground">Current conversion metrics</p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">Good</Badge>
                </div>
              </div>
            </div>
            
            <div className="flex items-center mt-4 pt-4 border-t">
              <AlertCircle className="h-4 w-4 text-muted-foreground mr-2" />
              <span className="text-sm text-muted-foreground">Changes to trial settings will affect new trials only</span>
            </div>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="flex gap-4">
                    <FormField
                      control={form.control}
                      name="trialDuration"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Trial Duration</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={90}
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e?.target?.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="trialDurationUnit"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Unit</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select unit" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="days">Days</SelectItem>
                              <SelectItem value="weeks">Weeks</SelectItem>
                              <SelectItem value="months">Months</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="enableAutoConversion"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between p-3 rounded-lg border">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Auto-Conversion</FormLabel>
                          <FormDescription>
                            Automatically convert trials to paid plans
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="sendReminders"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between p-3 rounded-lg border">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Trial Reminders</FormLabel>
                          <FormDescription>
                            Send notifications before trial ends
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch("sendReminders") && (
                    <FormField
                      control={form.control}
                      name="reminderDaysBefore"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Days Before Trial End</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={30}
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e?.target?.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                  
                  <FormField
                    control={form.control}
                    name="enableGracePeriod"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between p-3 rounded-lg border">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Grace Period</FormLabel>
                          <FormDescription>
                            Additional days after trial expires
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch("enableGracePeriod") && (
                    <FormField
                      control={form.control}
                      name="gracePeriodDays"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Grace Period (Days)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={30}
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e?.target?.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </div>

              <div className="border-t pt-4 mt-4 flex justify-end gap-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsEditing(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </form>
          </Form>
        )}
      </CardContent>

      <CardFooter className="flex justify-between border-t pt-4">
        {!isEditing ? (
          <>
            <Button variant="outline" size="sm" className="gap-1">
              <BellRing className="h-4 w-4" />
              <span>View Upcoming Expirations</span>
            </Button>
            <Button onClick={() => setIsEditing(true)}>Edit Trial Settings</Button>
          </>
        ) : null}
      </CardFooter>
    </Card>
  )
}