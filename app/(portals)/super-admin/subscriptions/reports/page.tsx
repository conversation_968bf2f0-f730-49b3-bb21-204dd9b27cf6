"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/app/components/ui/card"
import { DateRangePicker } from "@/app/components/ui/date-range-picker"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { Button } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { useToast } from "@/app/components/ui/use-toast"
import { Download, FileText, CalendarDays, RefreshCw, TrendingUp, Users, DollarSign } from "lucide-react"
import { subDays, format } from 'date-fns'
import type { DateRange } from 'react-day-picker'

const reportTypes = [
  { value: "overview", label: "System Overview" },
  { value: "quotes", label: "Quote Analytics" },
  { value: "affiliates", label: "Affiliate Performance" },
  { value: "financial", label: "Financial Reports" },
  { value: "performance", label: "Performance Metrics" },
]

interface ReportData {
  stats?: {
    totalQuotes: number;
    totalAffiliates: number;
    activeAffiliates: number;
    totalRevenue: number;
    conversionRate: number;
  };
  quotes?: any[];
  affiliatePerformance?: any[];
  monthlyRevenue?: any[];
  responseMetrics?: any[];
}

interface GeneratedReport {
  id: string;
  type: string;
  generated: string;
  range: string;
  status: 'generating' | 'ready' | 'failed';
  url?: string;
  size?: string;
}

export default function SubscriptionsReportsPage() {
  const { toast } = useToast();
  const [date, setDate] = useState<DateRange | undefined>({
    from: subDays(new Date(), 29),
    to: new Date(),
  })
  const [reportType, setReportType] = useState("overview")
  const [reportData, setReportData] = useState<ReportData>({})
  const [generatedReports, setGeneratedReports] = useState<GeneratedReport[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)

  // Fetch report data
  useEffect(() => {
    fetchReportData();
  }, [date, reportType]);

  const fetchReportData = async () => {
    if (!date?.from || !date?.to) return;

    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        reportType,
        startDate: date.from.toISOString(),
        endDate: date.to.toISOString()
      });

      console.log("Frontend: Making request to:", `/api/admin/reports?${params}`);
      const response = await fetch(`/api/admin/reports?${params}`);
      console.log("Frontend: Response status:", response.status);
      console.log("Frontend: Response ok:", response.ok);
      
      const data = await response.json();
      console.log("Frontend: Response data:", data);

      if (data.success) {
        setReportData(data.data);
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch report data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const generateReport = async () => {
    if (!date?.from || !date?.to) {
      toast({
        title: 'Error',
        description: 'Please select a date range.',
        variant: 'destructive',
      });
      return;
    }

    setIsGenerating(true);
    try {
      const newReport: GeneratedReport = {
        id: Date.now().toString(),
        type: reportTypes.find(rt => rt.value === reportType)?.label || reportType,
        generated: format(new Date(), 'yyyy-MM-dd HH:mm'),
        range: `${format(date.from, 'yyyy-MM-dd')} to ${format(date.to, 'yyyy-MM-dd')}`,
        status: 'generating'
      };

      setGeneratedReports(prev => [newReport, ...prev]);

      // Simulate report generation
      setTimeout(() => {
        setGeneratedReports(prev =>
          prev.map(report =>
            report.id === newReport.id
              ? {
                  ...report,
                  status: 'ready' as const,
                  url: `/downloads/${reportType}_${newReport.id}.csv`,
                  size: '2.4 MB'
                }
              : report
          )
        );

        toast({
          title: 'Report Generated',
          description: 'Your report has been generated successfully.',
        });
      }, 3000);

    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to generate report. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Reports & Exports</h2>
          <p className="text-muted-foreground">Generate and download reports for organizations and subscriptions</p>
        </div>
        <div className="flex items-center gap-2">
          <DateRangePicker date={date} onDateChange={setDate} />
          <Select value={reportType} onValueChange={setReportType}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Report Type" />
            </SelectTrigger>
            <SelectContent>
              {reportTypes.map(rt => (
                <SelectItem key={rt.value} value={rt.value}>{rt.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={generateReport} disabled={isGenerating}>
            {isGenerating ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <FileText className="h-4 w-4 mr-2" />
            )}
            {isGenerating ? 'Generating...' : 'Generate Report'}
          </Button>
        </div>
      </div>

      {/* Real-time Analytics Cards */}
      {reportData.stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Quotes</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{reportData.stats.totalQuotes}</div>
              <p className="text-xs text-muted-foreground">
                {reportData.stats.conversionRate}% conversion rate
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Affiliates</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{reportData.stats.activeAffiliates}</div>
              <p className="text-xs text-muted-foreground">
                of {reportData.stats.totalAffiliates} total affiliates
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${reportData.stats?.totalRevenue?.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Selected period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Performance</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{reportData.stats.conversionRate}%</div>
              <p className="text-xs text-muted-foreground">
                Quote conversion rate
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Generated Reports</CardTitle>
          <CardDescription>Download completed reports below</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Type</TableHead>
                <TableHead>Date Generated</TableHead>
                <TableHead>Date Range</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Download</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {generatedReports.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center text-muted-foreground">
                    No reports generated yet. Click "Generate Report" to create your first report.
                  </TableCell>
                </TableRow>
              ) : (
                generatedReports.map(report => (
                  <TableRow key={report.id}>
                    <TableCell>{report.type}</TableCell>
                    <TableCell>{report.generated}</TableCell>
                    <TableCell>{report.range}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          report.status === 'ready' ? 'default' :
                          report.status === 'generating' ? 'secondary' :
                          'destructive'
                        }
                      >
                        {report.status === 'generating' && <RefreshCw className="h-3 w-3 mr-1 animate-spin" />}
                        {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {report.status === 'ready' && report.url ? (
                        <Button asChild variant="ghost" size="icon" title="Download">
                          <a href={report.url} download>
                            <Download className="h-4 w-4" />
                          </a>
                        </Button>
                      ) : (
                        <Button variant="ghost" size="icon" disabled>
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter>
          <Button variant="outline" className="gap-1">
            <Download className="h-4 w-4" />
            Export All
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
} 