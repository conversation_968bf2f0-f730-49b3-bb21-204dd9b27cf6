"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { Textarea } from "@/app/components/ui/textarea";
import { Badge } from "@/app/components/ui/badge";
import { useToast } from "@/app/components/ui/use-toast";
import { Mail, Edit, Save, Plus, Eye, Settings } from "lucide-react";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";

interface EmailTemplate {
  id: string;
  type: string;
  name: string;
  subject: string;
  content: string;
  category: string;
  variables: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export default function EmailTemplatesPage() {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/email-templates');
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates || []);
      } else {
        throw new Error('Failed to fetch templates');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch email templates",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const saveTemplate = async (template: Partial<EmailTemplate>) => {
    try {
      const response = await fetch('/api/email-templates', {
        method: template.id ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(template)
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: `Template ${template.id ? 'updated' : 'created'} successfully`,
          variant: "default"
        });
        fetchTemplates();
        setIsDialogOpen(false);
        setEditingTemplate(null);
      } else {
        throw new Error('Failed to save template');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${template.id ? 'update' : 'create'} template`,
        variant: "destructive"
      });
    }
  };

  const previewTemplate = (template: EmailTemplate) => {
    // Open preview in new window
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    if (previewWindow) {
      previewWindow.document.write(`
        <html>
          <head>
            <title>Email Template Preview - ${template.name}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { background: #f5f5f5; padding: 10px; margin-bottom: 20px; }
              .content { border: 1px solid #ddd; padding: 20px; }
            </style>
          </head>
          <body>
            <div class="header">
              <h2>${template.name}</h2>
              <p><strong>Subject:</strong> ${template.subject}</p>
              <p><strong>Type:</strong> ${template.type}</p>
              <p><strong>Variables:</strong> ${template.variables.join(', ')}</p>
            </div>
            <div class="content">
              ${template.content}
            </div>
          </body>
        </html>
      `);
      previewWindow.document.close();
    }
  };

  const openEditDialog = (template?: EmailTemplate) => {
    setEditingTemplate(template || {
      id: '',
      type: '',
      name: '',
      subject: '',
      content: '',
      category: 'quote',
      variables: [],
      is_active: true,
      created_at: '',
      updated_at: ''
    });
    setIsDialogOpen(true);
  };

  if (loading) {
    return <div className="container mx-auto p-6">Loading templates...</div>;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Email Templates</h1>
          <p className="text-gray-600 mt-2">
            Manage email templates for quote workflow communications
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={() => window.open('/super-admin/communication/test', '_blank')}
            variant="outline"
          >
            <Settings className="h-4 w-4 mr-2" />
            Test Communication
          </Button>
          <Button onClick={() => openEditDialog()}>
            <Plus className="h-4 w-4 mr-2" />
            New Template
          </Button>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid gap-6">
        {templates.map((template) => (
          <Card key={template.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Type: {template.type} | Category: {template.category}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={template.is_active ? "default" : "secondary"}>
                    {template.is_active ? "Active" : "Inactive"}
                  </Badge>
                  <Button
                    onClick={() => previewTemplate(template)}
                    variant="outline"
                    size="sm"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    onClick={() => openEditDialog(template)}
                    variant="outline"
                    size="sm"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>Subject:</strong> {template.subject}</p>
                <p><strong>Variables:</strong> {template.variables.join(', ')}</p>
                <div className="text-sm text-gray-500">
                  Last updated: {new Date(template.updated_at).toLocaleDateString()}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingTemplate?.id ? 'Edit Template' : 'Create New Template'}
            </DialogTitle>
          </DialogHeader>
          {editingTemplate && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Template Name</label>
                  <Input
                    value={editingTemplate.name}
                    onChange={(e) => setEditingTemplate({
                      ...editingTemplate,
                      name: e?.target?.value
                    })}
                    placeholder="Template name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Template Type</label>
                  <Input
                    value={editingTemplate.type}
                    onChange={(e) => setEditingTemplate({
                      ...editingTemplate,
                      type: e?.target?.value
                    })}
                    placeholder="e.g., quote_created"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Subject Line</label>
                <Input
                  value={editingTemplate.subject}
                  onChange={(e) => setEditingTemplate({
                    ...editingTemplate,
                    subject: e?.target?.value
                  })}
                  placeholder="Email subject with {{variables}}"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Email Content (HTML)</label>
                <Textarea
                  value={editingTemplate.content}
                  onChange={(e) => setEditingTemplate({
                    ...editingTemplate,
                    content: e?.target?.value
                  })}
                  placeholder="HTML email content with {{variables}}"
                  rows={15}
                  className="font-mono text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Variables (comma-separated)</label>
                <Input
                  value={editingTemplate.variables.join(', ')}
                  onChange={(e) => setEditingTemplate({
                    ...editingTemplate,
                    variables: e?.target?.value.split(',').map(v => v.trim()).filter(v => v)
                  })}
                  placeholder="customerName, quoteReference, etc."
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  onClick={() => setIsDialogOpen(false)}
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => saveTemplate(editingTemplate)}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Template
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
