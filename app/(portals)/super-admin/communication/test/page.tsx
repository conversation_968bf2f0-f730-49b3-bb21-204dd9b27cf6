"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { Textarea } from "@/app/components/ui/textarea";
import { Badge } from "@/app/components/ui/badge";
import { useToast } from "@/app/components/ui/use-toast";
import { Mail, MessageSquare, Phone, Send, CheckCircle, XCircle, Clock, Settings } from "lucide-react";

interface TestResult {
  type: string;
  status: 'idle' | 'sending' | 'success' | 'error';
  message?: string;
  timestamp?: string;
}

export default function CommunicationTestPage() {
  const { toast } = useToast();
  const [testEmail, setTestEmail] = useState("<EMAIL>");
  const [testPhone, setTestPhone] = useState("+1234567890");
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  const updateTestResult = (type: string, status: TestResult['status'], message?: string) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.type === type);
      if (existing) {
        return prev.map(r => r.type === type 
          ? { ...r, status, message, timestamp: new Date().toLocaleTimeString() }
          : r
        );
      } else {
        return [...prev, { type, status, message, timestamp: new Date().toLocaleTimeString() }];
      }
    });
  };

  const testEmailTemplate = async (templateType: string) => {
    updateTestResult(`email_${templateType}`, 'sending');
    
    try {
      const response = await fetch('/api/emails', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: testEmail,
          from: 'TransFlow Test <<EMAIL>>',
          type: templateType,
          templateVariables: {
            customerName: 'Test Customer',
            quoteReference: 'TEST-001',
            pickupLocation: 'Test Pickup Location',
            dropoffLocation: 'Test Dropoff Location',
            tripDate: '2024-01-15',
            tripTime: '10:00 AM',
            affiliateName: 'Test Affiliate',
            offerAmount: 150,
            currency: 'USD'
          }
        })
      });

      const result = await response.json();
      
      if (result.success || response.ok) {
        updateTestResult(`email_${templateType}`, 'success', 'Email sent successfully');
        toast({
          title: "Email Test Success",
          description: `${templateType} email template test completed successfully`,
          variant: "default"
        });
      } else {
        throw new Error(result.error || 'Email sending failed');
      }
    } catch (error) {
      updateTestResult(`email_${templateType}`, 'error', error instanceof Error ? error.message : 'Unknown error');
      toast({
        title: "Email Test Failed",
        description: `Failed to send ${templateType} email: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
    }
  };

  const testSMS = async () => {
    updateTestResult('sms', 'sending');
    
    try {
      const response = await fetch('/api/sms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: testPhone,
          message: 'Test SMS from TransFlow communication system. Quote TEST-001 submitted successfully.',
          priority: 'normal',
          systemCall: true
        })
      });

      const result = await response.json();
      
      if (result.success) {
        updateTestResult('sms', 'success', result.mock ? 'SMS test completed (mock mode)' : 'SMS sent successfully');
        toast({
          title: "SMS Test Success",
          description: result.mock ? 'SMS test completed in mock mode (Twilio not configured)' : 'SMS sent successfully',
          variant: "default"
        });
      } else {
        throw new Error(result.error || 'SMS sending failed');
      }
    } catch (error) {
      updateTestResult('sms', 'error', error instanceof Error ? error.message : 'Unknown error');
      toast({
        title: "SMS Test Failed",
        description: `Failed to send SMS: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
    }
  };

  const testQuoteWorkflow = async () => {
    updateTestResult('quote_workflow', 'sending');
    
    try {
      const response = await fetch('/api/test-communication', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          testType: 'quote_submitted',
          customerEmail: testEmail,
          customerPhone: testPhone,
          mockData: {
            quote: {
              id: 'test-quote-001',
              reference_number: 'TEST-001',
              pickup_location: 'Test Pickup',
              dropoff_location: 'Test Dropoff',
              date: '2024-01-15',
              time: '10:00',
              passenger_count: 2,
              vehicle_type: 'sedan'
            },
            customer: {
              name: 'Test Customer',
              email: testEmail,
              phone: testPhone
            },
            affiliates: [
              { name: 'Test Affiliate 1' },
              { name: 'Test Affiliate 2' }
            ]
          }
        })
      });

      const result = await response.json();
      
      if (result.success) {
        updateTestResult('quote_workflow', 'success', 'Quote workflow communication test completed');
        toast({
          title: "Quote Workflow Test Success",
          description: 'Full quote workflow communication test completed successfully',
          variant: "default"
        });
      } else {
        throw new Error(result.error || 'Quote workflow test failed');
      }
    } catch (error) {
      updateTestResult('quote_workflow', 'error', error instanceof Error ? error.message : 'Unknown error');
      toast({
        title: "Quote Workflow Test Failed",
        description: `Quote workflow test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'sending':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'sending':
        return <Badge variant="secondary">Sending...</Badge>;
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">✅ Success</Badge>;
      case 'error':
        return <Badge variant="destructive">❌ Failed</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Communication System Test</h1>
          <p className="text-gray-600 mt-2">
            Test email templates, SMS notifications, and the complete communication workflow
          </p>
        </div>
        <Button 
          onClick={() => window.open('/super-admin/communication/templates', '_blank')}
          variant="outline"
        >
          <Settings className="h-4 w-4 mr-2" />
          Manage Templates
        </Button>
      </div>

      {/* Test Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Test Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Test Email Address</label>
            <Input
              value={testEmail}
              onChange={(e) => setTestEmail(e?.target?.value)}
              placeholder="<EMAIL>"
              type="email"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Test Phone Number</label>
            <Input
              value={testPhone}
              onChange={(e) => setTestPhone(e?.target?.value)}
              placeholder="+1234567890"
              type="tel"
            />
          </div>
        </CardContent>
      </Card>

      {/* Email Template Tests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Template Tests
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              'quote_created',
              'quote_updated',
              'quote_accepted',
              'quote_rejected',
              'quote_cancelled'
            ].map((templateType) => (
              <Button
                key={templateType}
                onClick={() => testEmailTemplate(templateType)}
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2"
              >
                <Mail className="h-6 w-6" />
                <span className="text-sm font-medium">{templateType.replace('_', ' ')}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* SMS Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            SMS Notification Test
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={testSMS} variant="outline" className="w-full">
            <MessageSquare className="h-4 w-4 mr-2" />
            Test SMS Notification
          </Button>
        </CardContent>
      </Card>

      {/* Quote Workflow Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Quote Workflow Communication Test
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={testQuoteWorkflow} variant="default" className="w-full">
            <Send className="h-4 w-4 mr-2" />
            Test Complete Quote Workflow
          </Button>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <div className="font-medium">{result.type.replace('_', ' ')}</div>
                      {result.message && (
                        <div className="text-sm text-gray-600">{result.message}</div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {result.timestamp && (
                      <span className="text-xs text-gray-500">{result.timestamp}</span>
                    )}
                    {getStatusBadge(result.status)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
