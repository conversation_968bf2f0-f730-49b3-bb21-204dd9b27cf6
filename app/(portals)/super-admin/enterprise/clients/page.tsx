"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/app/components/ui/tabs"
import { APIKeyManagement } from "@/app/components/enterprise/APIKeyManagement"
import { UsageAnalyticsDashboard } from "@/app/components/enterprise/UsageAnalyticsDashboard"
import { BillingManagement } from "@/app/components/enterprise/BillingManagement"
import { Building2, Key, BarChart3, CreditCard, ArrowLeft, Plus } from "lucide-react"
import { useToast } from "@/app/components/ui/use-toast"

interface EnterpriseClient {
  id: string
  name: string
  api_key: string
  organization_id: string
  rate_limit: number
  status: 'active' | 'suspended' | 'inactive'
  webhook_url?: string
  created_at: string
  updated_at: string
}

export default function EnterpriseClientsPage() {
  const [clients, setClients] = useState<EnterpriseClient[]>([])
  const [selectedClient, setSelectedClient] = useState<EnterpriseClient | null>(null)
  const [activeTab, setActiveTab] = useState("api-keys")
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    fetchClients()
  }, [])

  const fetchClients = async () => {
    try {
      const response = await fetch('/api/super-admin/enterprise/clients')
      if (!response.ok) {
        throw new Error('Failed to fetch clients')
      }
      const data = await response.json()
      setClients(data.clients || [])
    } catch (error) {
      console.error('Error fetching clients:', error)
      toast({
        title: "Error",
        description: "Failed to load enterprise clients",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'suspended': return 'bg-yellow-100 text-yellow-800'
      case 'inactive': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading clients...</p>
        </div>
      </div>
    )
  }

  if (selectedClient) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => setSelectedClient(null)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Clients
          </Button>
          <div className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-primary" />
            <div>
              <h1 className="text-3xl font-bold">{selectedClient.name}</h1>
              <p className="text-muted-foreground">Enterprise Client Management</p>
            </div>
            <Badge className={getStatusColor(selectedClient.status)}>
              {selectedClient?.status?.toUpperCase()}
            </Badge>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="api-keys" className="flex items-center gap-2">
              <Key className="h-4 w-4" />
              API Keys
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="billing" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Billing
            </TabsTrigger>
          </TabsList>

          <TabsContent value="api-keys" className="space-y-6">
            <APIKeyManagement 
              clientId={selectedClient.id} 
              clientName={selectedClient.name} 
            />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <UsageAnalyticsDashboard 
              clientId={selectedClient.id} 
              clientName={selectedClient.name} 
            />
          </TabsContent>

          <TabsContent value="billing" className="space-y-6">
            <BillingManagement 
              clientId={selectedClient.id} 
              clientName={selectedClient.name} 
            />
          </TabsContent>
        </Tabs>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Enterprise Clients</h1>
          <p className="text-muted-foreground">Manage enterprise API clients and their configurations</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Client
        </Button>
      </div>

      {clients.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Enterprise Clients</h3>
              <p className="text-muted-foreground mb-4">
                No enterprise clients have been created yet. Approve applications to create new clients.
              </p>
              <Button variant="outline">
                View Applications
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {clients.map((client) => (
            <Card key={client.id} className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Building2 className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">{client.name}</CardTitle>
                  </div>
                  <Badge className={getStatusColor(client.status)}>
                    {client.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium">Rate Limit</p>
                    <p className="text-sm text-muted-foreground">{client?.rate_limit?.toLocaleString()} req/min</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">API Key</p>
                    <p className="text-sm text-muted-foreground font-mono">
                      {client.api_key.substring(0, 12)}...
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Created</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(client.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  {client.webhook_url && (
                    <div>
                      <p className="text-sm font-medium">Webhook</p>
                      <p className="text-sm text-muted-foreground">Configured</p>
                    </div>
                  )}
                </div>
                
                <div className="mt-4 pt-4 border-t">
                  <Button 
                    className="w-full" 
                    onClick={() => setSelectedClient(client)}
                  >
                    Manage Client
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
