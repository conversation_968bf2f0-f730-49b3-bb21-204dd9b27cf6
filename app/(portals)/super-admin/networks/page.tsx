/**
 * GUG-30: Affiliate Network Management - Super Admin Networks Management
 * 
 * Allows super admins to create, manage, and monitor affiliate networks
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { Badge } from '@/app/components/ui/badge';
import { Textarea } from '@/app/components/ui/textarea';
import { useToast } from '@/app/components/ui/use-toast';
import { 
  Network, 
  Plus, 
  Edit,
  Trash2,
  Users,
  Globe,
  Shield,
  Star,
  DollarSign,
  Settings,
  BarChart3,
  Eye
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/app/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';

interface AffiliateNetwork {
  id: string;
  name: string;
  slug: string;
  description?: string;
  network_type: 'regional' | 'specialty' | 'premium' | 'global';
  coverage_area: Record<string, any>;
  auto_approval: boolean;
  min_rating_required: number;
  max_affiliates?: number;
  requires_insurance: boolean;
  requires_background_check: boolean;
  commission_rate: number;
  membership_fee: number;
  fee_frequency: 'monthly' | 'annual' | 'per_trip';
  brand_color: string;
  logo_url?: string;
  is_active: boolean;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export default function SuperAdminNetworksPage() {
  const [networks, setNetworks] = useState<AffiliateNetwork[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingNetwork, setEditingNetwork] = useState<AffiliateNetwork | null>(null);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    network_type: 'regional' as const,
    auto_approval: false,
    min_rating_required: 0.0,
    max_affiliates: '',
    requires_insurance: true,
    requires_background_check: true,
    commission_rate: 0.0,
    membership_fee: 0.0,
    fee_frequency: 'monthly' as const,
    brand_color: '#3B82F6',
    logo_url: '',
    is_public: true
  });

  useEffect(() => {
    loadNetworks();
  }, []);

  const loadNetworks = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/networks?include_private=true');
      if (response.ok) {
        const data = await response.json();
        setNetworks(data.networks || []);
      }

    } catch (error) {
      console.error('Error loading networks:', error);
      toast({
        title: "Error",
        description: "Failed to load networks",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const url = editingNetwork 
        ? `/api/networks/${editingNetwork.id}`
        : '/api/networks';
      
      const method = editingNetwork ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          max_affiliates: formData.max_affiliates ? parseInt(formData.max_affiliates) : null
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save network');
      }

      toast({
        title: "Success",
        description: `Network ${editingNetwork ? 'updated' : 'created'} successfully`,
      });

      setIsDialogOpen(false);
      resetForm();
      loadNetworks();

    } catch (error) {
      console.error('Error saving network:', error);
      toast({
        title: "Error",
        description: "Failed to save network",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (network: AffiliateNetwork) => {
    setEditingNetwork(network);
    setFormData({
      name: network.name,
      slug: network.slug,
      description: network.description || '',
      network_type: network.network_type,
      auto_approval: network.auto_approval,
      min_rating_required: network.min_rating_required,
      max_affiliates: network.max_affiliates?.toString() || '',
      requires_insurance: network.requires_insurance,
      requires_background_check: network.requires_background_check,
      commission_rate: network.commission_rate,
      membership_fee: network.membership_fee,
      fee_frequency: network.fee_frequency,
      brand_color: network.brand_color,
      logo_url: network.logo_url || '',
      is_public: network.is_public
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (networkId: string) => {
    if (!confirm('Are you sure you want to delete this network?')) {
      return;
    }

    try {
      const response = await fetch(`/api/networks/${networkId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete network');
      }

      toast({
        title: "Success",
        description: "Network deleted successfully",
      });

      loadNetworks();

    } catch (error) {
      console.error('Error deleting network:', error);
      toast({
        title: "Error",
        description: "Failed to delete network",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setEditingNetwork(null);
    setFormData({
      name: '',
      slug: '',
      description: '',
      network_type: 'regional',
      auto_approval: false,
      min_rating_required: 0.0,
      max_affiliates: '',
      requires_insurance: true,
      requires_background_check: true,
      commission_rate: 0.0,
      membership_fee: 0.0,
      fee_frequency: 'monthly',
      brand_color: '#3B82F6',
      logo_url: '',
      is_public: true
    });
  };

  const getNetworkTypeIcon = (type: string) => {
    switch (type) {
      case 'global':
        return <Globe className="w-5 h-5" />;
      case 'premium':
        return <Star className="w-5 h-5" />;
      case 'specialty':
        return <Shield className="w-5 h-5" />;
      default:
        return <Network className="w-5 h-5" />;
    }
  };

  const getNetworkTypeColor = (type: string) => {
    switch (type) {
      case 'global':
        return 'bg-blue-100 text-blue-800';
      case 'premium':
        return 'bg-purple-100 text-purple-800';
      case 'specialty':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading networks...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Network Management</h1>
          <p className="text-gray-600">Create and manage affiliate networks</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="w-4 h-4 mr-2" />
              Create Network
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingNetwork ? 'Edit' : 'Create'} Network
              </DialogTitle>
              <DialogDescription>
                Configure affiliate network settings and requirements
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Network Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e?.target?.value})}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="slug">Slug</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => setFormData({...formData, slug: e?.target?.value})}
                    placeholder="network-slug"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e?.target?.value})}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="network_type">Network Type</Label>
                  <Select 
                    value={formData.network_type} 
                    onValueChange={(value: any) => setFormData({...formData, network_type: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="regional">Regional</SelectItem>
                      <SelectItem value="specialty">Specialty</SelectItem>
                      <SelectItem value="premium">Premium</SelectItem>
                      <SelectItem value="global">Global</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="brand_color">Brand Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="brand_color"
                      type="color"
                      value={formData.brand_color}
                      onChange={(e) => setFormData({...formData, brand_color: e?.target?.value})}
                      className="w-16 h-10"
                    />
                    <Input
                      value={formData.brand_color}
                      onChange={(e) => setFormData({...formData, brand_color: e?.target?.value})}
                      placeholder="#3B82F6"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="min_rating_required">Min Rating Required</Label>
                  <Input
                    id="min_rating_required"
                    type="number"
                    step="0.1"
                    min="0"
                    max="5"
                    value={formData.min_rating_required}
                    onChange={(e) => setFormData({...formData, min_rating_required: parseFloat(e?.target?.value)})}
                  />
                </div>
                
                <div>
                  <Label htmlFor="max_affiliates">Max Affiliates (optional)</Label>
                  <Input
                    id="max_affiliates"
                    type="number"
                    min="1"
                    value={formData.max_affiliates}
                    onChange={(e) => setFormData({...formData, max_affiliates: e?.target?.value})}
                    placeholder="Unlimited"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="commission_rate">Commission Rate (%)</Label>
                  <Input
                    id="commission_rate"
                    type="number"
                    step="0.1"
                    min="0"
                    max="100"
                    value={formData.commission_rate}
                    onChange={(e) => setFormData({...formData, commission_rate: parseFloat(e?.target?.value)})}
                  />
                </div>
                
                <div>
                  <Label htmlFor="membership_fee">Membership Fee</Label>
                  <Input
                    id="membership_fee"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.membership_fee}
                    onChange={(e) => setFormData({...formData, membership_fee: parseFloat(e?.target?.value)})}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="fee_frequency">Fee Frequency</Label>
                  <Select 
                    value={formData.fee_frequency} 
                    onValueChange={(value: any) => setFormData({...formData, fee_frequency: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="annual">Annual</SelectItem>
                      <SelectItem value="per_trip">Per Trip</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="logo_url">Logo URL (optional)</Label>
                  <Input
                    id="logo_url"
                    value={formData.logo_url}
                    onChange={(e) => setFormData({...formData, logo_url: e?.target?.value})}
                    placeholder="https://example.com/logo.png"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="auto_approval"
                    checked={formData.auto_approval}
                    onChange={(e) => setFormData({...formData, auto_approval: e?.target?.checked})}
                    className="rounded"
                  />
                  <Label htmlFor="auto_approval">Auto-approve applications</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="requires_insurance"
                    checked={formData.requires_insurance}
                    onChange={(e) => setFormData({...formData, requires_insurance: e?.target?.checked})}
                    className="rounded"
                  />
                  <Label htmlFor="requires_insurance">Requires insurance</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="requires_background_check"
                    checked={formData.requires_background_check}
                    onChange={(e) => setFormData({...formData, requires_background_check: e?.target?.checked})}
                    className="rounded"
                  />
                  <Label htmlFor="requires_background_check">Requires background check</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="is_public"
                    checked={formData.is_public}
                    onChange={(e) => setFormData({...formData, is_public: e?.target?.checked})}
                    className="rounded"
                  />
                  <Label htmlFor="is_public">Public (discoverable by affiliates)</Label>
                </div>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  {editingNetwork ? 'Update' : 'Create'} Network
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6">
        {networks.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Network className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Networks</h3>
              <p className="text-gray-600 mb-4">Create your first affiliate network</p>
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Network
              </Button>
            </CardContent>
          </Card>
        ) : (
          networks.map((network) => (
            <Card key={network.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {getNetworkTypeIcon(network.network_type)}
                      {network.name}
                      {!network.is_active && (
                        <Badge variant="secondary">Inactive</Badge>
                      )}
                      {!network.is_public && (
                        <Badge variant="outline">Private</Badge>
                      )}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge className={getNetworkTypeColor(network.network_type)}>
                        {network.network_type}
                      </Badge>
                      {network.auto_approval && (
                        <Badge variant="outline" className="text-green-600">
                          Auto Approval
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {/* View analytics */}}
                    >
                      <BarChart3 className="w-4 h-4 mr-2" />
                      Analytics
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(network)}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(network.id)}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">{network.description}</p>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-700">Min Rating</p>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm text-gray-600">{network.min_rating_required}</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Commission</p>
                    <p className="text-sm text-gray-600 mt-1">{network.commission_rate}%</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Membership Fee</p>
                    <p className="text-sm text-gray-600 mt-1">
                      ${network.membership_fee}/{network.fee_frequency}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Max Affiliates</p>
                    <p className="text-sm text-gray-600 mt-1">
                      {network.max_affiliates || 'Unlimited'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 mt-4 pt-4 border-t">
                  {network.requires_insurance && (
                    <div className="flex items-center gap-2 text-sm">
                      <Shield className="w-4 h-4 text-blue-600" />
                      <span>Insurance Required</span>
                    </div>
                  )}
                  {network.requires_background_check && (
                    <div className="flex items-center gap-2 text-sm">
                      <Shield className="w-4 h-4 text-green-600" />
                      <span>Background Check Required</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}