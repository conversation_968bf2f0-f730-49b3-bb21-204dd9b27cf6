"use client"

import { useState } from "react"
import { useToast } from "@/app/components/ui/use-toast"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/app/components/ui/dialog"
import { Input } from "@/app/components/ui/input"
import { But<PERSON> } from "@/app/components/ui/button"
import { Label } from "@/app/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Separator } from "@/app/components/ui/separator"
import { Building2, Globe, CreditCard, User, ArrowRight, ArrowLeft } from "lucide-react"
import { Badge } from "@/app/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"

interface UnifiedTenantCreationFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: (tenantData: any) => void
}

interface TenantFormData {
  // Basic Info
  name: string
  slug: string
  domain: string
  organization_type: 'shared' | 'segregated' | 'white_label'
  industry: string
  
  // Subscription
  plan: 'essential' | 'professional' | 'business'
  
  // Admin User
  adminEmail: string
  adminFirstName: string
  adminLastName: string
  adminPassword: string
  
  // Branding
  branding: {
    logo_url?: string
    primary_color?: string
    secondary_color?: string
  }
  
  // Settings
  settings: Record<string, any>
}

export function UnifiedTenantCreationForm({ open, onOpenChange, onSuccess }: UnifiedTenantCreationFormProps) {
  const { toast } = useToast()
  const [step, setStep] = useState<'details' | 'plan' | 'admin' | 'confirmation'>('details')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<TenantFormData>({
    name: '',
    slug: '',
    domain: '',
    organization_type: 'shared',
    industry: 'transportation',
    plan: 'professional',
    adminEmail: '',
    adminFirstName: '',
    adminLastName: '',
    adminPassword: '',
    branding: {},
    settings: {}
  })

  const updateFormData = (key: keyof TenantFormData, value: any) => {
    setFormData(prev => ({ ...prev, [key]: value }))
  }

  const updateBranding = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      branding: { ...prev.branding, [key]: value }
    }))
  }

  // Auto-generate slug from name
  const handleNameChange = (name: string) => {
    updateFormData('name', name)
    if (!formData.slug || formData.slug === generateSlug(formData.name)) {
      updateFormData('slug', generateSlug(name))
    }
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    
    try {
      // Create tenant via API
      const response = await fetch('/api/super-admin/tenants', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: formData.name,
          slug: formData.slug,
          domain: formData.domain || null,
          organization_type: formData.organization_type,
          branding: formData.branding,
          settings: {
            ...formData.settings,
            industry: formData.industry,
            plan: formData.plan
          }
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create tenant')
      }

      const result = await response.json()
      
      toast({
        title: "Tenant Created Successfully",
        description: `${formData.name} has been created with ${formData.plan} plan.`,
      })
      
      if (onSuccess) {
        onSuccess({
          ...result.tenant,
          plan: formData.plan,
          industry: formData.industry,
          adminUser: {
            email: formData.adminEmail,
            firstName: formData.adminFirstName,
            lastName: formData.adminLastName
          }
        })
      }
      
      // Reset form and close
      resetForm()
      onOpenChange(false)
      
    } catch (error) {
      console.error('Error creating tenant:', error)
      toast({
        title: "Error Creating Tenant",
        description: error instanceof Error ? error.message : "Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      domain: '',
      organization_type: 'shared',
      industry: 'transportation',
      plan: 'professional',
      adminEmail: '',
      adminFirstName: '',
      adminLastName: '',
      adminPassword: '',
      branding: {},
      settings: {}
    })
    setStep('details')
  }

  const validateStep = () => {
    switch (step) {
      case 'details':
        return formData.name.trim() && formData.slug.trim() && formData.organization_type
      case 'plan':
        return formData.plan
      case 'admin':
        return formData.adminEmail.trim() && formData.adminFirstName.trim() && formData.adminLastName.trim()
      default:
        return true
    }
  }

  const nextStep = () => {
    if (step === 'details') setStep('plan')
    else if (step === 'plan') setStep('admin')
    else if (step === 'admin') setStep('confirmation')
  }

  const prevStep = () => {
    if (step === 'plan') setStep('details')
    else if (step === 'admin') setStep('plan')
    else if (step === 'confirmation') setStep('admin')
  }

  const getStepNumber = () => {
    switch (step) {
      case 'details': return 1
      case 'plan': return 2
      case 'admin': return 3
      case 'confirmation': return 4
      default: return 1
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Create New Tenant
          </DialogTitle>
          <DialogDescription>
            Set up a new tenant with complete configuration including subscription plan and admin user.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Indicator */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                getStepNumber() >= 1 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
              }`}>
                <Building2 className="h-4 w-4" />
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                getStepNumber() >= 2 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
              }`}>
                <CreditCard className="h-4 w-4" />
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                getStepNumber() >= 3 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
              }`}>
                <User className="h-4 w-4" />
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                getStepNumber() >= 4 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
              }`}>
                ✓
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              Step {getStepNumber()} of 4
            </div>
          </div>

          {/* Step Content */}
          {step === 'details' && (
            <div className="space-y-4">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Tenant Details</h3>
                <p className="text-sm text-muted-foreground">Basic information about the tenant organization.</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tenant-name">Tenant Name *</Label>
                  <Input 
                    id="tenant-name" 
                    placeholder="Acme Transportation Co." 
                    value={formData.name}
                    onChange={(e) => handleNameChange(e?.target?.value)}
                  />
                  <p className="text-xs text-muted-foreground">The organization or company name</p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="tenant-slug">URL Slug *</Label>
                  <div className="flex items-center space-x-2">
                    <Input 
                      id="tenant-slug" 
                      placeholder="acme-transport" 
                      value={formData.slug}
                      onChange={(e) => updateFormData('slug', e?.target?.value)}
                    />
                    <span className="text-sm text-muted-foreground">.transflow.app</span>
                  </div>
                  <p className="text-xs text-muted-foreground">URL-friendly identifier</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tenant-type">Tenant Type *</Label>
                  <Select value={formData.organization_type} onValueChange={(value) => updateFormData('organization_type', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="shared">Shared SaaS</SelectItem>
                      <SelectItem value="segregated">TNC Network</SelectItem>
                      <SelectItem value="white_label">White Label</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="industry">Industry</Label>
                  <Select value={formData.industry} onValueChange={(value) => updateFormData('industry', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="transportation">Transportation</SelectItem>
                      <SelectItem value="logistics">Logistics</SelectItem>
                      <SelectItem value="hospitality">Hospitality</SelectItem>
                      <SelectItem value="events">Events</SelectItem>
                      <SelectItem value="corporate">Corporate</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="domain">Custom Domain (Optional)</Label>
                <Input 
                  id="domain" 
                  placeholder="app.acmetransport.com" 
                  value={formData.domain}
                  onChange={(e) => updateFormData('domain', e?.target?.value)}
                />
                <p className="text-xs text-muted-foreground">For white-label deployments</p>
              </div>
            </div>
          )}

          {step === 'plan' && (
            <div className="space-y-4">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Select a Plan</h3>
                <p className="text-sm text-muted-foreground">Choose the appropriate subscription plan for this tenant.</p>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <Card className={`cursor-pointer transition-all ${formData.plan === 'essential' ? 'ring-2 ring-primary' : ''}`}
                      onClick={() => updateFormData('plan', 'essential')}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      Essential
                      <Badge variant="outline">$199/mo</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <ul className="text-sm space-y-1">
                      <li>• Full platform access</li>
                      <li>• Up to 2 team members</li>
                      <li>• Access to vetted affiliate network</li>
                      <li>• Basic booking management</li>
                      <li>• Standard reporting</li>
                      <li>• Email support</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card className={`cursor-pointer transition-all ${formData.plan === 'professional' ? 'ring-2 ring-primary' : ''}`}
                      onClick={() => updateFormData('plan', 'professional')}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      Professional
                      <Badge>Most Popular</Badge>
                    </CardTitle>
                    <CardDescription>$499/mo</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <ul className="text-sm space-y-1">
                      <li>• Everything in Essential, plus:</li>
                      <li>• Up to 5 team members</li>
                      <li>• Advanced booking management</li>
                      <li>• Custom booking forms</li>
                      <li>• Advanced analytics</li>
                      <li>• Bulk booking tools</li>
                      <li>• Priority support</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card className={`cursor-pointer transition-all ${formData.plan === 'business' ? 'ring-2 ring-primary' : ''}`}
                      onClick={() => updateFormData('plan', 'business')}>
                  <CardHeader>
                    <CardTitle>Business</CardTitle>
                    <CardDescription>$999/mo</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <ul className="text-sm space-y-1">
                      <li>• Everything in Professional, plus:</li>
                      <li>• Up to 10 team members</li>
                      <li>• White-label options</li>
                      <li>• Advanced integrations</li>
                      <li>• Custom analytics dashboard</li>
                      <li>• Volume-based incentives</li>
                      <li>• Dedicated account manager</li>
                      <li>• 24/7 priority support</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {step === 'admin' && (
            <div className="space-y-4">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Tenant Admin User</h3>
                <p className="text-sm text-muted-foreground">Create the initial admin user for this tenant.</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="admin-first-name">First Name *</Label>
                  <Input
                    id="admin-first-name"
                    placeholder="John"
                    value={formData.adminFirstName}
                    onChange={(e) => updateFormData('adminFirstName', e?.target?.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="admin-last-name">Last Name *</Label>
                  <Input
                    id="admin-last-name"
                    placeholder="Doe"
                    value={formData.adminLastName}
                    onChange={(e) => updateFormData('adminLastName', e?.target?.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="admin-email">Email Address *</Label>
                <Input
                  id="admin-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.adminEmail}
                  onChange={(e) => updateFormData('adminEmail', e?.target?.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="admin-password">Initial Password *</Label>
                <Input
                  id="admin-password"
                  type="password"
                  placeholder="••••••••"
                  value={formData.adminPassword}
                  onChange={(e) => updateFormData('adminPassword', e?.target?.value)}
                />
                <p className="text-xs text-muted-foreground">The admin will be prompted to change this password on first login.</p>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Branding & Customization</h4>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primary-color">Primary Color</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="primary-color"
                        type="color"
                        value={formData.branding.primary_color || '#000000'}
                        onChange={(e) => updateBranding('primary_color', e?.target?.value)}
                        className="w-16 h-10"
                      />
                      <Input
                        placeholder="#000000"
                        value={formData.branding.primary_color || ''}
                        onChange={(e) => updateBranding('primary_color', e?.target?.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="logo-url">Logo URL</Label>
                    <Input
                      id="logo-url"
                      placeholder="https://example.com/logo.png"
                      value={formData.branding.logo_url || ''}
                      onChange={(e) => updateBranding('logo_url', e?.target?.value)}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {step === 'confirmation' && (
            <div className="space-y-4">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Confirm Tenant Creation</h3>
                <p className="text-sm text-muted-foreground">Review the details before creating the tenant.</p>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Tenant Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Name:</span>
                      <span className="text-sm font-medium">{formData.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Slug:</span>
                      <span className="text-sm font-medium">{formData.slug}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Type:</span>
                      <Badge variant="outline">{formData.organization_type.replace('_', ' ')}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Industry:</span>
                      <span className="text-sm font-medium">{formData.industry}</span>
                    </div>
                    {formData.domain && (
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Domain:</span>
                        <span className="text-sm font-medium">{formData.domain}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Subscription & Admin</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Plan:</span>
                      <Badge>{formData.plan}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Admin:</span>
                      <span className="text-sm font-medium">{formData.adminFirstName} {formData.adminLastName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Email:</span>
                      <span className="text-sm font-medium">{formData.adminEmail}</span>
                    </div>
                    {formData.branding.primary_color && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Brand Color:</span>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-4 h-4 rounded border"
                            style={{ backgroundColor: formData.branding.primary_color }}
                          />
                          <span className="text-sm font-medium">{formData.branding.primary_color}</span>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <Button 
            variant="outline" 
            onClick={step === 'details' ? () => onOpenChange(false) : prevStep}
          >
            {step === 'details' ? 'Cancel' : (
              <>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </>
            )}
          </Button>
          
          {step === 'confirmation' ? (
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Tenant'}
            </Button>
          ) : (
            <Button onClick={nextStep} disabled={!validateStep()}>
              Continue
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
