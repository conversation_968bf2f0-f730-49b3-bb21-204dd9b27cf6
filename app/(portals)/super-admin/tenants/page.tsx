// ---
// REDIRECT NOTICE
//
// This page has been consolidated with the Organizations page.
// All tenant management functionality is now available at /super-admin/orgs
// which provides a more comprehensive interface with advanced features.
// ---

'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Button } from "@/app/components/ui/button"
import { ArrowRight, Building2, Info } from "lucide-react"

export default function TenantsRedirectPage() {
  const router = useRouter()

  useEffect(() => {
    // Auto-redirect after 3 seconds
    const timer = setTimeout(() => {
      router.push('/super-admin/orgs')
    }, 3000)

    return () => clearTimeout(timer)
  }, [router])

  const handleRedirect = () => {
    router.push('/super-admin/orgs')
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
              <Info className="h-6 w-6 text-blue-600" />
            </div>
            <CardTitle className="text-2xl">Page Moved</CardTitle>
            <CardDescription>
              Tenant management has been consolidated with the Organizations page
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center space-x-4">
                <div className="flex items-center space-x-2 text-muted-foreground">
                  <Building2 className="h-4 w-4" />
                  <span>/super-admin/tenants</span>
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
                <div className="flex items-center space-x-2 text-primary font-medium">
                  <Building2 className="h-4 w-4" />
                  <span>/super-admin/orgs</span>
                </div>
              </div>
              
              <p className="text-sm text-muted-foreground">
                The Organizations page now provides all tenant management features plus:
              </p>
              
              <ul className="text-sm text-left space-y-1 bg-muted/50 p-4 rounded-lg">
                <li>• Advanced 4-step organization creation wizard</li>
                <li>• Integrated user creation with role assignment</li>
                <li>• Granular permission template selection</li>
                <li>• Subscription plan management</li>
                <li>• Real-time user invitation system</li>
              </ul>
            </div>

            <div className="flex justify-center space-x-3">
              <Button onClick={handleRedirect} className="flex items-center space-x-2">
                <span>Go to Organizations</span>
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>

            <p className="text-xs text-center text-muted-foreground">
              Redirecting automatically in 3 seconds...
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
