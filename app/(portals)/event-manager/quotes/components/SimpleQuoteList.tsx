"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Button } from "@/app/components/ui/button"
import { Avatar, AvatarFallback } from "@/app/components/ui/avatar"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog"
import {
  Clock,
  MapPin,
  Users,
  Car,
  Calendar,
  Eye,
  MoreHorizontal,
  CheckCircle,
  AlertTriangle,
  X
} from "lucide-react"

interface Quote {
  id: string
  reference_number: string
  pickup_location: string
  dropoff_location: string
  city: string
  pickup_date: string
  pickup_time: string
  passenger_count: number
  vehicle_type_preference?: string
  service_type: string
  status: string
  created_at: string
  client_name?: string
  total_price?: number
  affiliate_responses?: Array<{
    affiliate_name: string
    status: string
    price: number
    tier: string
  }>
}

interface SimpleQuoteListProps {
  statusFilter?: string
}

export function SimpleQuoteList({ statusFilter }: SimpleQuoteListProps) {
  const [quotes, setQuotes] = useState<Quote[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    fetchQuotes()
  }, [statusFilter])

  const fetchQuotes = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (statusFilter && statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      const response = await fetch(`/api/event-manager/quotes?${params}`)
      console.log('SimpleQuoteList - API Response Status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('SimpleQuoteList - API Response Data:', data)
        setQuotes(data.quotes || [])
      } else {
        console.error('SimpleQuoteList - API Error:', response.status, response.statusText)
        const errorText = await response.text()
        console.error('SimpleQuoteList - Error Details:', errorText)
        // Fallback to mock data for development
        setQuotes(getMockQuotes())
      }
    } catch (error) {
      console.error('Error fetching quotes:', error)
      setQuotes(getMockQuotes())
    } finally {
      setLoading(false)
    }
  }

  const getMockQuotes = (): Quote[] => [
    {
      id: '1ae47a90-535f-465d-b701-cdcc902bc663',
      reference_number: 'Q1735689600-ABC123',
      pickup_location: '123 Austin Street',
      dropoff_location: '456 Downtown Ave',
      city: 'Austin',
      pickup_date: '2025-06-07',
      pickup_time: '11:00',
      passenger_count: 1,
      vehicle_type_preference: 'SUV',
      service_type: 'point_to_point',
      status: 'pending',
      created_at: '2025-05-31T10:00:00Z',
      client_name: 'Unknown Customer',
      affiliate_responses: [
        { affiliate_name: 'Elite Transport Co', status: 'pending', price: 150, tier: 'Elite' },
        { affiliate_name: 'Premium Rides LLC', status: 'pending', price: 125, tier: 'Premium' }
      ]
    },
    {
      id: '2ae47a90-535f-465d-b701-cdcc902bc664',
      reference_number: 'Q1735689601-DEF456',
      pickup_location: '789 Business District',
      dropoff_location: '321 Airport Terminal',
      city: 'Austin',
      pickup_date: '2025-06-08',
      pickup_time: '14:30',
      passenger_count: 3,
      vehicle_type_preference: 'Luxury Sedan',
      service_type: 'airport',
      status: 'accepted',
      created_at: '2025-05-30T15:30:00Z',
      client_name: 'Corporate Client',
      total_price: 180,
      affiliate_responses: [
        { affiliate_name: 'Elite Transport Co', status: 'accepted', price: 180, tier: 'Elite' }
      ]
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case 'accepted':
        return <Badge variant="default" className="bg-green-100 text-green-800">Accepted</Badge>
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>
      case 'completed':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Completed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'accepted':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'rejected':
        return <X className="h-4 w-4 text-red-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-blue-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />
    }
  }

  const handleViewDetails = (quote: Quote) => {
    setSelectedQuote(quote)
    setShowDetails(true)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-32"></div>
                    <div className="h-3 bg-gray-200 rounded w-24"></div>
                  </div>
                </div>
                <div className="h-6 bg-gray-200 rounded w-16"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (quotes.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Car className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No Quotes Found</h3>
          <p className="text-muted-foreground mb-4">
            {statusFilter && statusFilter !== 'all' 
              ? `No ${statusFilter} quotes at the moment.`
              : 'You haven\'t created any transportation quotes yet.'
            }
          </p>
          <Button onClick={() => window.location.href = '/event-manager/quotes/new'}>
            Create Your First Quote
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className="space-y-3">
        {quotes.map((quote) => (
          <Card key={quote.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 flex-1">
                  {/* Customer Avatar */}
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>
                      {quote.client_name?.charAt(0) || 'U'}
                    </AvatarFallback>
                  </Avatar>

                  {/* Quote Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-sm truncate">
                        {quote.client_name || 'Unknown Customer'}
                      </h3>
                      {getStatusIcon(quote.status)}
                      {getStatusBadge(quote.status)}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        <span className="truncate max-w-32">{quote.city}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(quote.pickup_date)} at {formatTime(quote.pickup_time)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        <span>{quote.passenger_count} passenger{quote.passenger_count !== 1 ? 's' : ''}</span>
                      </div>
                      {quote.vehicle_type_preference && (
                        <div className="flex items-center gap-1">
                          <Car className="h-3 w-3" />
                          <span>{quote.vehicle_type_preference}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  {quote.total_price && (
                    <div className="text-right mr-3">
                      <div className="font-semibold">${quote.total_price}</div>
                      <div className="text-xs text-muted-foreground">Total</div>
                    </div>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleViewDetails(quote)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quote Details Modal */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Quote #{selectedQuote?.reference_number}</DialogTitle>
            <DialogDescription>
              Transportation request details and affiliate responses
            </DialogDescription>
          </DialogHeader>
          
          {selectedQuote && (
            <div className="space-y-6">
              {/* Customer & Trip Info */}
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-3">Customer Information</h4>
                  <div className="space-y-2 text-sm">
                    <div>👤 {selectedQuote.client_name || 'Unknown'}</div>
                    <div>📅 No Event</div>
                    <div>👥 {selectedQuote.passenger_count} passengers</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Trip Details</h4>
                  <div className="space-y-2 text-sm">
                    <div>📍 {selectedQuote.city}</div>
                    <div>🚗 {selectedQuote.vehicle_type_preference || 'Any Vehicle'}</div>
                    <div>📅 {formatDate(selectedQuote.pickup_date)} at {formatTime(selectedQuote.pickup_time)}</div>
                  </div>
                </div>
              </div>

              {/* Route Information */}
              <div>
                <h4 className="font-medium mb-3">Route Information</h4>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <div className="font-medium">Pickup Location</div>
                      <div className="text-sm text-muted-foreground">{selectedQuote.pickup_location}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div>
                      <div className="font-medium">Dropoff Location</div>
                      <div className="text-sm text-muted-foreground">{selectedQuote.dropoff_location}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Affiliate Responses */}
              {selectedQuote.affiliate_responses && selectedQuote?.affiliate_responses?.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Affiliate Responses</h4>
                  <div className="space-y-2">
                    {selectedQuote?.affiliate_responses?.map((response, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Badge variant="outline">{response.tier}</Badge>
                          <span className="font-medium">{response.affiliate_name}</span>
                          {getStatusBadge(response.status)}
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">${response.price}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button variant="outline" onClick={() => setShowDetails(false)}>
                  Cancel
                </Button>
                <Button>
                  Process Quote
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
