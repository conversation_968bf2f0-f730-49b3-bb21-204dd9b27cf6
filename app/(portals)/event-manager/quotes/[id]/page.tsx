"use client";

import { Suspense, useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card } from "@/app/components/ui/card";
import { ArrowLeft, Pencil, Send, MessageSquare } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/app/components/ui/badge";
import { createClient } from "@supabase/supabase-js";
import { Textarea } from "@/app/components/ui/textarea";
import { QuoteActionPanel } from "@/app/components/features/quotes/panels/quote-action-panel";
import {
  getAccountPermissions,
  determineAccountType,
  determineWorkflowType,
  type AccountContext,
} from "@/lib/utils/account-types";

function QuoteDetail({ quoteId }: { quoteId: string }) {
  const [quote, setQuote] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchQuote = async () => {
      try {
        setLoading(true);

        // Create client-side Supabase client
        const supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        );

        // Fetch quote data with affiliate offers
        const { data, error } = await supabase
          .from("quotes")
          .select(
            `
            *,
            quote_affiliate_offers (
              id,
              company_id,
              status,
              rate_amount,
              notes,
              created_at,
              affiliate_companies (
                id,
                name,
                city,
                state
              )
            )
          `
          )
          .eq("id", quoteId)
          .single();

        if (error) {
          setError(error.message || "Unknown error occurred");
          return;
        }

        if (!data) {
          setError("Quote not found");
          return;
        }

        setQuote(data);
      } catch (err) {
        setError("Failed to fetch quote");
        console.error("Error fetching quote:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchQuote();
  }, [quoteId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading quote: {error}</p>
      </div>
    );
  }

  if (!quote) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Quote not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <Link
          href="/event-manager/quotes"
          className="flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Quotes
        </Link>
        <Badge variant={quote.status === "accepted" ? "default" : "secondary"}>
          {quote.status}
        </Badge>
      </div>

      {/* Quote Action Panel for Client Interface */}
      <QuoteActionPanel
        quote={quote}
        onClose={() => {}} // No close needed for full page
        onAction={async (action: string, data?: any) => {
          console.log("Quote action:", action, data);
          // Handle client actions like accept/reject affiliate offers
          if (action === "accept_response" || action === "reject_response") {
            // Redirect to API endpoint for client actions
            const response = await fetch("/api/event-manager/quotes", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                action:
                  action === "accept_response"
                    ? "accept_offer"
                    : "reject_offer",
                quoteId: quote.id,
                affiliateOfferId: data?.responseId,
              }),
            });

            if (response.ok) {
              // Refresh the page to show updated status
              window.location.reload();
            }
          }
        }}
        userType="customer" // Important: Set as customer for client interface
        userContext={{
          userRoles: ["CUSTOMER"],
          tenantType: "shared",
          quoteStatus: quote.status,
          userType: "customer",
          isQuoteOwner: true,
          organizationId: quote.company_id,
          workflowType: "pure_saas",
          clientSelectedAffiliates: !!(quote as any).quote_affiliate_offers
            ?.length,
          // Enhanced account type context
          accountType: "regular_client",
          permissions: getAccountPermissions({
            accountType: "regular_client",
            workflowType: "pure_saas",
            organizationId: quote.company_id,
            isQuoteOwner: true,
          }),
        }}
      />
    </div>
  );
}

export default function QuoteDetailPage({
  params,
}: {
  params: { id: string };
}) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <QuoteDetail quoteId={params.id} />
    </Suspense>
  );
}
