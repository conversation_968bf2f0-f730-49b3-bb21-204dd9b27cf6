"use client"

import React, { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/app/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/app/components/ui/avatar"
import { Input } from "@/app/components/ui/input"
import {
  Search,
  Calendar,
  MapPin,
  Users,
  Clock,
  AlertCircle,
  Car,
  History,
  Star,
  TrendingUp,
  Filter,
  Map as MapIcon,
  Plane,
  Crown,
  Building,
  CalendarClock,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Phone,
  MessageSquare,
  Mail,
  Edit,
  MoreVertical,
  ChevronUp,
  ChevronDown,
  Shield,
  FileText,
  UserCheck,
  Stethoscope,
  Plus,
  ArrowLeft
} from "lucide-react"
import Link from "next/link"

// Define Trip type
interface Trip {
  id: string
  status: "SCHEDULED" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED"
  priority: "standard" | "high" | "urgent"
  eventName: string
  eventId: string
  pickupLocation: string
  dropoffLocation: string
  scheduledPickupTime: string
  scheduledDropoffTime: string
  passengerCount: number
  vehicleType: string
  driverName: string
  driverPhone: string
  securityLevel: "standard" | "vip" | "vvip"
  notes?: string
}

export default function EventTripsPage() {
  const params = useParams()
  const eventId = params.id as string
  const [trips, setTrips] = useState<Trip[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // In a real app, fetch trips for this specific event
    // For now, using mock data
    setIsLoading(true)
    setTimeout(() => {
      setTrips([
        {
          id: "trip-1",
          status: "SCHEDULED",
          priority: "high",
          eventName: "Annual Corporate Retreat",
          eventId: eventId,
          pickupLocation: "JFK Airport Terminal 4",
          dropoffLocation: "Hilton Midtown Manhattan",
          scheduledPickupTime: "2023-06-15T14:30:00",
          scheduledDropoffTime: "2023-06-15T15:45:00",
          passengerCount: 3,
          vehicleType: "Executive Sedan",
          driverName: "Michael Johnson",
          driverPhone: "+****************",
          securityLevel: "vip",
          notes: "VIP passengers, provide water and refreshments"
        },
        {
          id: "trip-2",
          status: "COMPLETED",
          priority: "standard",
          eventName: "Annual Corporate Retreat",
          eventId: eventId,
          pickupLocation: "LaGuardia Airport",
          dropoffLocation: "Hilton Midtown Manhattan",
          scheduledPickupTime: "2023-06-15T12:15:00",
          scheduledDropoffTime: "2023-06-15T13:30:00",
          passengerCount: 2,
          vehicleType: "Standard SUV",
          driverName: "Sarah Williams",
          driverPhone: "+****************",
          securityLevel: "standard",
          notes: ""
        },
        {
          id: "trip-3",
          status: "IN_PROGRESS",
          priority: "urgent",
          eventName: "Annual Corporate Retreat",
          eventId: eventId,
          pickupLocation: "Newark Liberty International Airport",
          dropoffLocation: "Hilton Midtown Manhattan",
          scheduledPickupTime: "2023-06-15T16:00:00",
          scheduledDropoffTime: "2023-06-15T17:30:00",
          passengerCount: 1,
          vehicleType: "Executive Sedan",
          driverName: "Robert Davis",
          driverPhone: "+****************",
          securityLevel: "vvip",
          notes: "CEO arrival, highest priority"
        }
      ])
      setIsLoading(false)
    }, 1000)
  }, [eventId])

  const filteredTrips = trips.filter(trip => {
    if (!searchQuery) return true
    
    const query = searchQuery.toLowerCase()
    return (
      trip?.pickupLocation?.toLowerCase().includes(query) ||
      trip?.dropoffLocation?.toLowerCase().includes(query) ||
      trip?.driverName?.toLowerCase().includes(query) ||
      trip?.vehicleType?.toLowerCase().includes(query)
    )
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "SCHEDULED":
        return "bg-blue-100 text-blue-800"
      case "IN_PROGRESS":
        return "bg-amber-100 text-amber-800"
      case "COMPLETED":
        return "bg-green-100 text-green-800"
      case "CANCELLED":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800"
      case "high":
        return "bg-amber-100 text-amber-800"
      case "standard":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href={`/event-manager/events/${eventId}`}>
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <h1 className="text-2xl font-semibold">Event Transportation</h1>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add New Trip
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Trips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{trips.length}</div>
            <p className="text-xs text-muted-foreground">
              {trips.filter(t => t.status === "COMPLETED").length} completed
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {trips.filter(t => t.status === "IN_PROGRESS").length}
            </div>
            <p className="text-xs text-muted-foreground">
              {trips.filter(t => t.status === "SCHEDULED").length} scheduled
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">VIP Transfers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {trips.filter(t => t.securityLevel === "vip" || t.securityLevel === "vvip").length}
            </div>
            <p className="text-xs text-muted-foreground">
              {trips.filter(t => t.securityLevel === "vvip").length} VVIP
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search trips by location, driver, or vehicle..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e?.target?.value)}
          />
        </div>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Trips</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="in-progress">In Progress</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="space-y-4">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : filteredTrips.length > 0 ? (
            <div className="grid grid-cols-1 gap-4">
              {filteredTrips.map(trip => (
                <Card key={trip.id} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(trip.status)}>
                            {trip.status.replace("_", " ")}
                          </Badge>
                          <Badge className={getPriorityColor(trip.priority)}>
                            {trip.priority.charAt(0).toUpperCase() + trip.priority.slice(1)} Priority
                          </Badge>
                        </div>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <div className="flex items-start gap-2 mb-2">
                            <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">Pickup</p>
                              <p className="text-sm text-muted-foreground">{trip.pickupLocation}</p>
                              <p className="text-xs text-muted-foreground">
                                {new Date(trip.scheduledPickupTime).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex items-start gap-2 mb-2">
                            <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">Dropoff</p>
                              <p className="text-sm text-muted-foreground">{trip.dropoffLocation}</p>
                              <p className="text-xs text-muted-foreground">
                                {new Date(trip.scheduledDropoffTime).toLocaleString()}
                              </p>
                            </div>
                          </div>
                        </div>
                        
                        <div>
                          <div className="flex items-start gap-2 mb-2">
                            <Users className="h-4 w-4 mt-0.5 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">Passengers</p>
                              <p className="text-sm text-muted-foreground">{trip.passengerCount} passengers</p>
                              <p className="text-xs text-muted-foreground">
                                {trip.securityLevel === "vvip" ? "VVIP" : trip.securityLevel === "vip" ? "VIP" : "Standard"} security
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex items-start gap-2">
                            <Car className="h-4 w-4 mt-0.5 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">Vehicle & Driver</p>
                              <p className="text-sm text-muted-foreground">{trip.vehicleType}</p>
                              <p className="text-xs text-muted-foreground">{trip.driverName} • {trip.driverPhone}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {trip.notes && (
                        <div className="mt-2 p-2 bg-muted rounded-md">
                          <p className="text-xs">{trip.notes}</p>
                        </div>
                      )}
                    </div>
                    
                    <div className="border-t px-4 py-2 bg-muted/50 flex justify-end gap-2">
                      <Button variant="outline" size="sm">
                        <Phone className="h-3 w-3 mr-1" />
                        Contact
                      </Button>
                      <Button variant="outline" size="sm">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        Message
                      </Button>
                      <Button size="sm">
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-8">
              <AlertCircle className="h-8 w-8 text-muted-foreground mb-2" />
              <h3 className="font-medium">No trips found</h3>
              <p className="text-sm text-muted-foreground">Try adjusting your search or filters</p>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="scheduled" className="space-y-4">
          {filteredTrips
            .filter(trip => trip.status === "SCHEDULED")
            .map(trip => (
              /* Same card component as above */
              <Card key={trip.id} className="overflow-hidden">
                {/* Card content same as above */}
              </Card>
            ))}
        </TabsContent>
        
        <TabsContent value="in-progress" className="space-y-4">
          {filteredTrips
            .filter(trip => trip.status === "IN_PROGRESS")
            .map(trip => (
              /* Same card component as above */
              <Card key={trip.id} className="overflow-hidden">
                {/* Card content same as above */}
              </Card>
            ))}
        </TabsContent>
        
        <TabsContent value="completed" className="space-y-4">
          {filteredTrips
            .filter(trip => trip.status === "COMPLETED")
            .map(trip => (
              /* Same card component as above */
              <Card key={trip.id} className="overflow-hidden">
                {/* Card content same as above */}
              </Card>
            ))}
        </TabsContent>
      </Tabs>
    </div>
  )
} 