"use client"

import React, { useState, useEffect, Fragment } from "react"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/app/components/ui/avatar"
import { cn } from "@/lib/utils"
import {
  Calendar,
  MapPin,
  Users,
  ArrowLeft,
  Plus,
  RefreshCcw,
  Truck,
  MoreVertical,
  Filter,
  Download,
  Printer,
  Mail,
  Copy,
  AlertCircle,
  Search,
  Loader2,
  ArrowUpDown,
  Clock,
  CarFront,
  Route,
  Phone,
  UserPlus,
  X,
  Pin,
  LayoutGrid,
  Map as MapIcon,
  ChevronRight,
  AlertTriangle,
  Plane,
  ArrowRight,
  RefreshCw,
  FileText,
  Bell,
  Clock4,
  MessageSquare,
  Shield,
  Crown,
  Car,
  CloudRain,
  Edit,
  Trash2,
  Navigation,
  DollarSign,
  Zap,
  CheckCircle
} from "lucide-react"
import Link from "next/link"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { formatDate } from "@/lib/utils"
import { Input } from "@/app/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { Checkbox } from "@/app/components/ui/checkbox"
import Map, { Marker, Source, Layer, NavigationControl, FullscreenControl, ScaleControl } from 'react-map-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from "@/app/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"
import { Separator } from "@/app/components/ui/separator"
import { Progress } from "@/app/components/ui/progress"
import { ScrollArea } from "@/app/components/ui/scroll-area"
import { RealTimeStatus, DetailedRealTimeStatus, QuoteStatusTimeline } from "@/components/quotes/real-time-status"
import { useAllQuoteStatus } from "@/lib/hooks/useQuoteStatus"

type EventData = {
  id: string
  title: string
  description: string
  startDate: Date
  endDate: Date
  location: string
  status: "draft" | "published" | "cancelled"
  totalPassengers: number
  trips: Array<{
    id: string
    vehicle: string
    passengerCount: number
    passengers?: Array<{
      id: string
      name: string
      group?: string
      phone?: string
      email?: string
      isVIP?: boolean
    }>
    pickupLocation: string
    dropoffLocation: string
    time: string
    status: "scheduled" | "in_progress" | "completed" | "cancelled"
    type: "arrival" | "departure" | "transfer"
    notes?: string
    driver?: {
      name: string
      phone: string
    }
    stage?: TripStage
    tripType?: string
    startTime?: string
    endTime?: string
    estimatedDuration?: number
  }>
  quotes: Array<{
    id: string
    vehicle: string
    passengerCount: number
    pickupLocation: string
    dropoffLocation: string
    date: string
    time: string
    status: "pending" | "rate_requested" | "quote_ready" | "changes_requested" | "accepted" | "rejected"
    price: {
      baseRate: number
      gratuity: number
      adminFee: number
      total: number
    }
    notes?: string
    affiliateRate?: number
    markup?: number
    vehicleDetails?: {
      type: string
      capacity: number
    }
    amount?: number
    pickupTime?: string
    guestGroup?: string
    eventActivity?: string
    eventRelation?: string
    relatedQuotes?: Array<{
      type: string
      vehicle: string
      passengerCount: number
      pickupLocation: string
      dropoffLocation: string
      date: string
      time: string
      status: "pending" | "rate_requested" | "quote_ready" | "changes_requested" | "accepted" | "rejected"
      price: {
        baseRate: number
        gratuity: number
        adminFee: number
        total: number
      }
      notes?: string
      affiliateRate?: number
      markup?: number
      vehicleDetails?: {
        type: string
        capacity: number
      }
      amount?: number
      pickupTime?: string
    }>
  }>
}

// Add new types for map data
interface Coordinates {
  lat: number
  lng: number
}

interface RouteData {
  type: string
  properties: {}
  geometry: {
    type: string
    coordinates: [number, number][]
  }
}

// Add new state and types at the top
type Driver = {
  id: string
  name: string
  phone: string
  available: boolean
  vehicle?: string
}

type Vehicle = {
  id: string
  name: string
  type: string
  capacity: number
  available: boolean
}

// Add mock data
const availableDrivers: Driver[] = [
  { id: "D1", name: "John Smith", phone: "+****************", available: true, vehicle: "Tesla Model Y" },
  { id: "D2", name: "Sarah Johnson", phone: "+****************", available: true, vehicle: "BMW 7 Series" },
  { id: "D3", name: "Mike Wilson", phone: "+****************", available: true, vehicle: "Mercedes Sprinter" }
]

const availableVehicles: Vehicle[] = [
  { id: "V1", name: "Tesla Model Y", type: "Sedan", capacity: 4, available: true },
  { id: "V2", name: "Mercedes Sprinter", type: "Van", capacity: 12, available: true },
  { id: "V3", name: "BMW 7 Series", type: "Luxury", capacity: 3, available: true }
]

// Add new trip status type
type TripStage = "on_the_way" | "waiting_for_passenger" | "passenger_on_board" | "dropped_off"

// Add a function to get stage color
const getTripStageAbbr = (stage: TripStage) => {
  switch (stage) {
    case "on_the_way": return "OTW"
    case "waiting_for_passenger": return "OLC"
    case "passenger_on_board": return "POB"
    case "dropped_off": return "DONE"
    default: return ""
  }
}

const getTripStageColor = (stage: TripStage) => {
  switch (stage) {
    case "on_the_way": return "bg-blue-500"
    case "waiting_for_passenger": return "bg-yellow-500"
    case "passenger_on_board": return "bg-green-500"
    case "dropped_off": return "bg-purple-500"
    default: return "bg-gray-500"
  }
}

// Update getCoordinates to be synchronous
const getCoordinates = (location: string): Coordinates => {
  const mockCoordinates: { [key: string]: Coordinates } = {
    "San Francisco International Airport (SFO)": { lat: 37.6213, lng: -122.3790 },
    "Moscone Center": { lat: 37.7845, lng: -122.4008 },
    "Union Square": { lat: 37.7879, lng: -122.4075 },
    "Golden Gate Bridge Welcome Center": { lat: 37.8077, lng: -122.4750 },
    "Fisherman's Wharf": { lat: 37.8080, lng: -122.4177 },
    "Palace of Fine Arts": { lat: 37.8029, lng: -122.4484 },
    "Pier 39": { lat: 37.8087, lng: -122.4098 },
    "Chinatown Gate": { lat: 37.7905, lng: -122.4067 },
    "Oracle Park": { lat: 37.7786, lng: -122.3893 },
    "California Academy of Sciences": { lat: 37.7699, lng: -122.4661 }
  }
  return mockCoordinates[location] || { lat: 37.7749, lng: -122.4194 }
}

// Real API call to fetch event details
const getEventDetails = async (id: string): Promise<EventData> => {
  try {
    const response = await fetch(`/api/event-manager/events/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch event details')
    }
    const data = await response.json()
    return data.event
  } catch (error) {
    console.error('Error fetching event details:', error)
    // Fallback to mock data if API fails
    return {
      id,
      title: "Tech Conference Austin 2024",
      description: "Annual Technology Conference in Austin",
      startDate: new Date("2024-04-15"),
      endDate: new Date("2024-04-17"),
      location: "Austin Convention Center, Austin, TX",
      status: "published",
      totalPassengers: 150,
      trips: [
        {
          id: "T1",
          vehicle: "Tesla Model Y",
        passengerCount: 4,
        passengers: [
          { id: "P1", name: "John Doe", group: "VIP" },
          { id: "P2", name: "Jane Smith", group: "VIP" },
          { id: "P3", name: "Bob Wilson", group: "VIP" },
          { id: "P4", name: "Alice Brown", group: "VIP" }
        ],
        pickupLocation: "San Francisco International Airport (SFO)",
        dropoffLocation: "Moscone Center",
        time: "09:00 AM",
        status: "in_progress",
        stage: "on_the_way",
        type: "arrival",
        notes: "VIP pickup - Meet at Terminal 2",
        driver: {
          name: "John Smith",
          phone: "+****************"
        }
      },
      {
        id: "T2",
        vehicle: "Mercedes Sprinter",
        passengerCount: 8,
        pickupLocation: "Union Square",
        dropoffLocation: "Golden Gate Bridge Welcome Center",
        time: "02:00 PM",
        status: "in_progress",
        stage: "waiting_for_passenger",
        type: "transfer",
        notes: "Group tour transfer",
        driver: {
          name: "Mike Wilson",
          phone: "+****************"
        }
      },
      {
        id: "T3",
        vehicle: "BMW 7 Series",
        passengerCount: 3,
        pickupLocation: "Moscone Center",
        dropoffLocation: "Fisherman's Wharf",
        time: "01:30 PM",
        status: "in_progress",
        stage: "passenger_on_board",
        type: "transfer",
        driver: {
          name: "Sarah Johnson",
          phone: "+****************"
        }
      },
      {
        id: "T4",
        vehicle: "Cadillac Escalade",
        passengerCount: 5,
        pickupLocation: "Palace of Fine Arts",
        dropoffLocation: "Chinatown Gate",
        time: "02:30 PM",
        status: "in_progress",
        stage: "dropped_off",
        type: "transfer",
        driver: {
          name: "David Lee",
          phone: "+****************"
        }
      },
      {
        id: "T5",
        vehicle: "Lincoln Navigator",
        passengerCount: 6,
        pickupLocation: "Oracle Park",
        dropoffLocation: "California Academy of Sciences",
        time: "03:00 PM",
        status: "in_progress",
        type: "transfer",
        driver: {
          name: "Emily Chen",
          phone: "+****************"
        }
      },
      // Keep existing scheduled and completed trips
      {
        id: "T6",
        vehicle: "BMW 7 Series",
        passengerCount: 3,
        pickupLocation: "Moscone Center",
        dropoffLocation: "San Francisco International Airport (SFO)",
        time: "05:00 PM",
        status: "scheduled",
        type: "departure",
        driver: {
          name: "Sarah Johnson",
          phone: "+****************"
        }
      }
    ],
    quotes: [
      {
        id: "Q1",
        vehicle: "Mercedes Sprinter",
        passengerCount: 8,
        pickupLocation: "San Francisco International Airport (SFO)",
        dropoffLocation: "Moscone Center",
        date: "2024-03-15",
        time: "10:00 AM",
        status: "pending",
        price: {
          baseRate: 180,
          gratuity: 36,
          adminFee: 15,
          total: 231
        },
        notes: "WiFi required for all vehicles"
      },
      {
        id: "Q2",
        vehicle: "Tesla Model Y",
        passengerCount: 4,
        pickupLocation: "Union Square",
        dropoffLocation: "Golden Gate Bridge Welcome Center",
        date: "2024-03-16",
        time: "02:00 PM",
        status: "rate_requested",
        price: {
          baseRate: 120,
          gratuity: 24,
          adminFee: 15,
          total: 159
        }
      },
      {
        id: "Q3",
        vehicle: "Motor Coach",
        passengerCount: 45,
        pickupLocation: "Moscone Center",
        dropoffLocation: "SFO Airport",
        date: "2024-03-17",
        time: "03:00 PM",
        status: "quote_ready",
        price: {
          baseRate: 800,
          gratuity: 160,
          adminFee: 50,
          total: 1010
        },
        affiliateRate: 700,
        markup: 15
      },
      {
        id: "Q4",
        vehicle: "Executive Van",
        passengerCount: 12,
        pickupLocation: "Fisherman's Wharf",
        dropoffLocation: "Oracle Park",
        date: "2024-03-16",
        time: "05:00 PM",
        status: "changes_requested",
        price: {
          baseRate: 250,
          gratuity: 50,
          adminFee: 25,
          total: 325
        },
        notes: "Client requested vehicle upgrade"
      }
    ]
    }
  }
}

// Add sorting type
type SortOption = {
  field: 'time' | 'passengerCount' | 'status' | 'type'
  direction: 'asc' | 'desc'
}

interface QuoteCardProps {
  quote: EventData['quotes'][0]
  onAction: (action: string, quoteId: string) => void
}

function QuoteCard({ quote, onAction }: QuoteCardProps) {
  return (
    <Card className="p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <div>
            <div className="font-medium">{quote.vehicle}</div>
            <div className="text-sm text-muted-foreground">{quote.passengerCount} passengers</div>
          </div>
          <RealTimeStatus
            quoteId={quote.id}
            showNotifications={false}
            size="sm"
          />
        </div>
        <div className="text-lg font-bold">
          ${quote.price?.total?.toFixed(2)}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div>
          <div className="text-muted-foreground">Pickup</div>
          <div>{quote.pickupLocation}</div>
        </div>
        <div>
          <div className="text-muted-foreground">Dropoff</div>
          <div>{quote.dropoffLocation}</div>
        </div>
        <div>
          <div className="text-muted-foreground">Date</div>
          <div>{quote.date}</div>
        </div>
        <div>
          <div className="text-muted-foreground">Time</div>
          <div>{quote.time}</div>
        </div>
      </div>

      {quote.status === 'quote_ready' && (
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={() => onAction('request_changes', quote.id)}
          >
            Request Changes
          </Button>
          <Button
            variant="outline"
            onClick={() => onAction('reject', quote.id)}
          >
            Reject
          </Button>
          <Button
            onClick={() => onAction('accept', quote.id)}
          >
            Accept
          </Button>
        </div>
      )}

      {quote.notes && (
        <div className="mt-4 text-sm text-muted-foreground">
          <div className="font-medium">Notes</div>
          <div>{quote.notes}</div>
        </div>
      )}
    </Card>
  )
}

interface TripCardProps {
  trip: EventData['trips'][0]
  onClick: () => void
  isSelected: boolean
}

function TripCard({ trip, onClick, isSelected }: TripCardProps) {
  return (
    <Card
      className={cn(
        "p-4 cursor-pointer transition-all hover:shadow-md",
        isSelected && "ring-2 ring-primary"
      )}
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <div>
            <div className="font-medium">{trip.vehicle}</div>
            <div className="text-sm text-muted-foreground">{trip.passengerCount} passengers</div>
          </div>
          <Badge variant={
            trip.status === 'scheduled' ? 'secondary' :
              trip.status === 'in_progress' ? 'default' :
                trip.status === 'completed' ? 'outline' :
                  'destructive'
          }>
            {trip.status.replace('_', ' ').toUpperCase()}
          </Badge>
          <Badge variant="outline">
            {trip?.type?.toUpperCase()}
          </Badge>
        </div>
        <div className="text-sm text-muted-foreground">
          {trip.time}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div>
          <div className="text-muted-foreground">Pickup</div>
          <div>{trip.pickupLocation}</div>
        </div>
        <div>
          <div className="text-muted-foreground">Dropoff</div>
          <div>{trip.dropoffLocation}</div>
        </div>
      </div>

      {trip.driver && (
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarFallback>{trip.driver.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{trip.driver.name}</div>
              <div className="text-muted-foreground">{trip.driver.phone}</div>
            </div>
          </div>
          <Button variant="ghost" size="sm">
            <Phone className="h-4 w-4 mr-2" />
            Contact
          </Button>
        </div>
      )}

      {trip.notes && (
        <div className="mt-4 text-sm text-muted-foreground">
          <div className="font-medium">Notes</div>
          <div>{trip.notes}</div>
        </div>
      )}

      {trip.passengers && trip?.passengers?.length > 0 && (
        <div className="mt-4">
          <div className="text-sm font-medium mb-2">Passengers</div>
          <div className="flex -space-x-2">
            {trip?.passengers?.map((passenger) => (
              <Avatar key={passenger.id} className="h-6 w-6 border-2 border-background">
                <AvatarFallback>{passenger.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
            ))}
          </div>
        </div>
      )}
    </Card>
  )
}

// Add new types
interface EventCoordinator {
  id: string
  name: string
  email: string
  phone: string
  role: "primary" | "secondary"
  assignedTrips: string[]
  lastActive?: string
  isOnline?: boolean
}

interface EventAlert {
  id: string
  type: "high" | "medium" | "low"
  message: string
  timestamp: string
  category: "vip" | "schedule" | "traffic" | "weather"
  relatedTrips?: string[]
}

// Add mock data
const mockCoordinators: EventCoordinator[] = [
  {
    id: "c1",
    name: "John Smith",
    email: "<EMAIL>",
    phone: "+****************",
    role: "primary",
    assignedTrips: ["T1", "T2", "T3"],
    lastActive: new Date().toISOString(),
    isOnline: true
  },
  {
    id: "c2",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+****************",
    role: "secondary",
    assignedTrips: ["T4", "T5"],
    lastActive: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 mins ago
    isOnline: false
  }
]

const mockAlerts: EventAlert[] = [
  {
    id: "a1",
    type: "high",
    message: "VIP arrival delayed - John Smith's flight ETA updated to 10:30 AM",
    timestamp: new Date().toISOString(),
    category: "vip",
    relatedTrips: ["T1"]
  },
  {
    id: "a2",
    type: "medium",
    message: "Heavy traffic reported on route to Moscone Center",
    timestamp: new Date().toISOString(),
    category: "traffic",
    relatedTrips: ["T2", "T3"]
  },
  {
    id: "a3",
    type: "low",
    message: "Light rain expected around venue area",
    timestamp: new Date().toISOString(),
    category: "weather"
  }
]

export default function EventDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const eventId = params.id as string
  const [event, setEvent] = useState<EventData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedTrips, setSelectedTrips] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState("scheduled")
  const [mapViewport, setMapViewport] = useState({
    latitude: 37.7749,
    longitude: -122.4194,
    zoom: 11,
    bearing: 0,
    pitch: 0,
    padding: { top: 0, bottom: 0, left: 0, right: 0 }
  })
  const [routeData, setRouteData] = useState<RouteData | null>(null)
  const [selectedTrip, setSelectedTrip] = useState<EventData['trips'][0] | null>(null)
  const [tripDetailsOpen, setTripDetailsOpen] = useState(false)
  const [selectedTripType, setSelectedTripType] = useState<string>("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [isMapLoading, setIsMapLoading] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [tripFilters, setTripFilters] = useState({
    timeRange: "all", // all, today, tomorrow, thisWeek
    passengerCount: "all", // all, 1-4, 5-8, 9+
    hasDriver: "all" // all, assigned, unassigned
  })
  const [sortConfig, setSortConfig] = useState<SortOption>({ field: 'time', direction: 'asc' })
  const [hoveredTripId, setHoveredTripId] = useState<string | null>(null)
  const [quoteSearchTerm, setQuoteSearchTerm] = useState("")
  const [selectedLiveTrips, setSelectedLiveTrips] = useState<string[]>([])
  const [liveSearchTerm, setLiveSearchTerm] = useState("")
  const [pinnedTrips, setPinnedTrips] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<"list" | "map">("map")
  const [showPassengerDetails, setShowPassengerDetails] = useState<string | null>(null)
  const [isFullScreen, setIsFullScreen] = useState(false)
  const [timelineView, setTimelineView] = useState<"day" | "week" | "month">("week")
  const [expandedEvents, setExpandedEvents] = useState<string[]>([])
  const [expandedQuoteId, setExpandedQuoteId] = useState<string | null>(null);
  const [searchPassenger, setSearchPassenger] = useState("")
  const [coordinators] = useState<EventCoordinator[]>(mockCoordinators)
  const [alerts] = useState<EventAlert[]>(mockAlerts)
  const [showAlerts, setShowAlerts] = useState(true)
  const [messageDialogOpen, setMessageDialogOpen] = useState(false)
  const [selectedCoordinator, setSelectedCoordinator] = useState<EventCoordinator | null>(null)

  // Add this near the top of the component
  const getQuoteAction = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Review Quote';
      case 'rate_requested':
        return 'View Rate Request';
      case 'quote_ready':
        return 'Review & Accept';
      case 'changes_requested':
        return 'View Requested Changes';
      default:
        return 'View Details';
    }
  }

  // Add the modal component
  const QuoteDetailsModal = ({ quote, isOpen }: { quote: EventData['quotes'][0], isOpen: boolean }) => {
    if (!quote) return null;

    return (
      <Dialog open={isOpen} onOpenChange={() => setExpandedQuoteId(null)}>
        <DialogContent className="max-w-3xl">
          {/* Header with Event Name and Date */}
          <div className="flex items-center gap-2 mb-6">
            <div>
              <h2 className="text-xl font-semibold flex items-center gap-2">
                {event?.title} <span className="text-muted-foreground">•</span>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>{quote.date}</span>
                </div>
              </h2>
            </div>
            <div className="ml-auto flex items-center gap-2">
              <Badge variant="secondary" className="text-yellow-600 bg-yellow-100">Pending Quote</Badge>
              <Badge variant="outline">VIP</Badge>
              <Badge variant="secondary" className="bg-red-100 text-red-800">high priority</Badge>
            </div>
          </div>

          {/* Passenger Section */}
          <div className="mb-6">
            <div className="text-sm text-muted-foreground">Passenger</div>
            <div className="font-medium">Emma Wilson</div>
          </div>

          {/* Vehicle and Passenger Count */}
          <div className="flex items-center gap-2 mb-4">
            <CarFront className="h-4 w-4 text-muted-foreground" />
            <span>{quote.vehicleDetails?.type || quote.vehicle}</span>
            <span className="text-muted-foreground">•</span>
            <Users className="h-4 w-4 text-muted-foreground" />
            <span>{quote.passengerCount} passengers</span>
          </div>

          {/* Route */}
          <div className="flex items-center gap-2 text-muted-foreground mb-8">
            <MapPin className="h-4 w-4" />
            <span>{quote.pickupLocation} → {quote.dropoffLocation}</span>
          </div>

          <div className="grid grid-cols-2 gap-8">
            {/* Left Column */}
            <div>
              {/* Timeline */}
              <div className="mb-8">
                <h3 className="font-medium mb-4">Timeline</h3>
                <div className="space-y-6">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-primary mt-2" />
                    <div>
                      <div className="font-medium">Quote Created</div>
                      <div className="text-sm text-muted-foreground">
                        {quote.date} by Emma Wilson
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Initial quote request submitted
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-primary mt-2" />
                    <div>
                      <div className="font-medium">Rate Requested</div>
                      <div className="text-sm text-muted-foreground">
                        {quote.date} by System Manager
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Rate request sent to preferred affiliates
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div>
              {/* Communications */}
              <div className="mb-8">
                <h3 className="font-medium mb-4">Communications</h3>
                <div className="space-y-4">
                  <div>
                    <div className="font-medium">System Manager</div>
                    <div className="text-sm text-muted-foreground">
                      {quote.date}
                    </div>
                    <div className="text-sm mt-1">
                      Quote received. Processing your request.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Cancellation Policy and Terms */}
          <div className="mb-8 p-4 border rounded-lg bg-muted/30">
            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">Cancellation Policy</h3>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>Free cancellation up to 48 hours before pickup</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    <span>50% charge for cancellations within 48 hours</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <X className="h-4 w-4" />
                    <span>100% charge for no-shows or same-day cancellations</span>
                  </div>
                </div>
              </div>
              <Separator />
              <div className="text-sm">
                By accepting this quote, you agree to our{" "}
                <Link href="/terms" className="text-primary hover:underline">
                  Terms & Conditions
                </Link>
              </div>
            </div>
          </div>

          {/* Total Amount */}
          <div>
            <h3 className="font-medium mb-4">Total Amount</h3>
            <div className="flex justify-between items-start">
              <div>
                <div className="text-2xl font-bold mb-2">$231</div>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <div>Base Fare: $180</div>
                  <div>Additional Services: $15</div>
                  <div>Gratuity: $36</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline">Request Changes</Button>
                <Button variant="destructive">Reject</Button>
                <Button>Accept Quote</Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  // Add state for live trips data
  const [liveTripsData, setLiveTripsData] = useState<any[]>([])
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null)

  // Function to fetch live trips data
  const fetchLiveTrips = async () => {
    try {
      const response = await fetch(`/api/event-manager/events/${eventId}/live-trips`)
      if (response.ok) {
        const data = await response.json()
        setLiveTripsData(data.trips || [])
        setPerformanceMetrics(data.metrics || null)
      }
    } catch (error) {
      console.error('Error fetching live trips:', error)
    }
  }

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        const data = await getEventDetails(eventId)
        setEvent(data)
        // Also fetch live trips data
        await fetchLiveTrips()
      } catch (error) {
        console.error("Failed to fetch event:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchEvent()
  }, [eventId])

  // Set up polling for live trips data
  useEffect(() => {
    const interval = setInterval(fetchLiveTrips, 30000) // Poll every 30 seconds
    return () => clearInterval(interval)
  }, [eventId])

  // Use real live trips data from API
  const liveTrips = liveTripsData.length > 0 ? liveTripsData : (event?.trips.filter(trip => trip.status === "in_progress") || [])

  const filteredLiveTrips = liveTrips.filter(trip => {
    const matchesSearch =
      trip?.pickupLocation?.toLowerCase().includes(liveSearchTerm.toLowerCase()) ||
      trip?.dropoffLocation?.toLowerCase().includes(liveSearchTerm.toLowerCase()) ||
      trip?.vehicle?.toLowerCase().includes(liveSearchTerm.toLowerCase()) ||
      (trip.driver?.name.toLowerCase() || "").includes(liveSearchTerm.toLowerCase())
    return matchesSearch
  })

  const handleLiveTripSelect = (tripId: string) => {
    setSelectedLiveTrips(prev => {
      if (prev.includes(tripId)) {
        return prev.filter(id => id !== tripId)
      }
      return [...prev, tripId]
    })
  }

  // Enhanced trip filtering
  const getFilteredTrips = () => {
    if (!event?.trips) return []

    return event?.trips?.filter(trip => {
      const matchesSearch =
        trip?.pickupLocation?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trip?.dropoffLocation?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trip?.vehicle?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trip.driver?.name.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesType = selectedTripType === "all" || trip.type === selectedTripType
      const matchesStatus = statusFilter === "all" || trip.status === statusFilter

      // Time range filtering
      const tripDate = new Date(trip.time)
      const today = new Date()
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)
      const weekEnd = new Date(today)
      weekEnd.setDate(weekEnd.getDate() + 7)

      const matchesTimeRange = tripFilters.timeRange === "all" ||
        (tripFilters.timeRange === "today" && tripDate.toDateString() === today.toDateString()) ||
        (tripFilters.timeRange === "tomorrow" && tripDate.toDateString() === tomorrow.toDateString()) ||
        (tripFilters.timeRange === "thisWeek" && tripDate <= weekEnd)

      // Passenger count filtering
      const matchesPassengerCount = tripFilters.passengerCount === "all" ||
        (tripFilters.passengerCount === "1-4" && trip.passengerCount <= 4) ||
        (tripFilters.passengerCount === "5-8" && trip.passengerCount > 4 && trip.passengerCount <= 8) ||
        (tripFilters.passengerCount === "9+" && trip.passengerCount > 8)

      // Driver assignment filtering
      const matchesDriverStatus = tripFilters.hasDriver === "all" ||
        (tripFilters.hasDriver === "assigned" && trip.driver) ||
        (tripFilters.hasDriver === "unassigned" && !trip.driver)

      return matchesSearch && matchesType && matchesStatus &&
        matchesTimeRange && matchesPassengerCount && matchesDriverStatus
    })
  }

  // Add sorting function
  const getSortedTrips = (trips: EventData['trips']) => {
    return [...trips].sort((a, b) => {
      switch (sortConfig.field) {
        case 'time':
          return sortConfig.direction === 'asc'
            ? new Date(a.time).getTime() - new Date(b.time).getTime()
            : new Date(b.time).getTime() - new Date(a.time).getTime()
        case 'passengerCount':
          return sortConfig.direction === 'asc'
            ? a.passengerCount - b.passengerCount
            : b.passengerCount - a.passengerCount
        case 'status':
          return sortConfig.direction === 'asc'
            ? a.status.localeCompare(b.status)
            : b.status.localeCompare(a.status)
        case 'type':
          return sortConfig.direction === 'asc'
            ? a.type.localeCompare(b.type)
            : b.type.localeCompare(a.type)
        default:
          return 0
      }
    })
  }

  // Update filtered trips to include sorting
  const filteredAndSortedTrips = getSortedTrips(getFilteredTrips())

  // Handle map marker click
  const handleMarkerClick = (tripId: string) => {
    const trip = event?.trips.find(t => t.id === tripId)
    if (trip) {
      setSelectedTrip(trip)
      setTripDetailsOpen(true)
    }
  }

  // Update map effect with loading state
  useEffect(() => {
    if (selectedTrip) {
      setIsMapLoading(true)
      const pickup = getCoordinates(selectedTrip.pickupLocation)
      const dropoff = getCoordinates(selectedTrip.dropoffLocation)

      // Center the map between pickup and dropoff
      setMapViewport(prev => ({
        ...prev,
        latitude: (pickup.lat + dropoff.lat) / 2,
        longitude: (pickup.lng + dropoff.lng) / 2,
        zoom: 11
      }))

      // Create a route line between points
      setRouteData({
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates: [
            [pickup.lng, pickup.lat],
            [dropoff.lng, dropoff.lat]
          ]
        }
      })
      setIsMapLoading(false)
    }
  }, [selectedTrip])

  // Bulk actions
  const handleBulkAction = (action: string) => {
    switch (action) {
      case "export":
        console.log("Exporting selected trips:", selectedTrips)
        break
      case "print":
        console.log("Printing selected trips:", selectedTrips)
        break
      case "email":
        console.log("Emailing selected trips:", selectedTrips)
        break
      case "clone":
        console.log("Cloning selected trips:", selectedTrips)
        break
      default:
        break
    }
  }

  // Update the TabsContent for pending quotes
  const handleQuoteAction = (action: string, quoteId?: string) => {
    // Implementation of handleQuoteAction
  }

  const fitMapToSelectedTrips = () => {
    if (selectedLiveTrips.length === 0) return;

    const selectedTripsData = filteredLiveTrips.filter(trip => selectedLiveTrips.includes(trip.id));
    const coordinates: [number, number][] = [];

    selectedTripsData.forEach(trip => {
      const pickup = getCoordinates(trip.pickupLocation);
      const dropoff = getCoordinates(trip.dropoffLocation);
      coordinates.push([pickup.lng, pickup.lat]);
      coordinates.push([dropoff.lng, dropoff.lat]);
    });

    // Calculate bounds
    const lngs = coordinates.map(coord => coord[0]);
    const lats = coordinates.map(coord => coord[1]);
    const minLng = Math.min(...lngs);
    const maxLng = Math.max(...lngs);
    const minLat = Math.min(...lats);
    const maxLat = Math.max(...lats);

    // Add padding
    const padding = 0.1;
    const newViewport = {
      ...mapViewport,
      latitude: (minLat + maxLat) / 2,
      longitude: (minLng + maxLng) / 2,
      zoom: Math.min(
        Math.log2(360 / (maxLng - minLng + padding)) - 1,
        Math.log2(180 / (maxLat - minLat + padding)) - 1,
        11 // max zoom level
      )
    };

    setMapViewport(newViewport);
  };

  // Update useEffect to call fitMapToSelectedTrips when selected trips change
  useEffect(() => {
    fitMapToSelectedTrips();
  }, [selectedLiveTrips]);

  // Full-screen detection
  useEffect(() => {
    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullScreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullScreenChange);
  }, []);

  const handlePinTrip = (tripId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    setPinnedTrips(prev => {
      if (prev.includes(tripId)) {
        return prev.filter(id => id !== tripId)
      }
      return [...prev, tripId]
    })
  }

  const sortedLiveTrips = [...filteredLiveTrips].sort((a, b) => {
    const aIsPinned = pinnedTrips.includes(a.id)
    const bIsPinned = pinnedTrips.includes(b.id)
    if (aIsPinned && !bIsPinned) return -1
    if (!aIsPinned && bIsPinned) return 1
    return 0
  })

  if (isLoading) {
    return (
      <div className="container space-y-6 p-6">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/events">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div className="h-6 w-48 animate-pulse bg-muted rounded" />
        </div>
        <div className="h-[600px] animate-pulse bg-muted rounded-lg" />
      </div>
    )
  }

  if (!event) {
    return (
      <div className="container space-y-6 p-6">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/events">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Event Not Found</h2>
        </div>
        <Card>
          <CardContent className="p-6">
            <p className="text-muted-foreground">The requested event could not be found.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100/50">
      <div className="container mx-auto p-6 space-y-6">
        {/* Enhanced Header with Status Badge */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-start justify-between">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <h1 className="text-3xl font-bold text-slate-900">{event?.title || "Tech Conference 2024"}</h1>
                <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200">
                  Active Event
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2 text-slate-600">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">March 15-17, 2024</span>
                </div>
                <div className="flex items-center gap-2 text-slate-600">
                  <MapPin className="h-4 w-4 text-red-500" />
                  <span className="font-medium">Moscone Center, San Francisco</span>
                </div>
                <div className="flex items-center gap-2 text-slate-600">
                  <Users className="h-4 w-4 text-purple-500" />
                  <span className="font-medium">150 Expected Attendees</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                className="shadow-sm"
                onClick={() => {
                  // Scroll to quotes tab and activate it
                  const quotesTab = document.querySelector('[value="quotes"]') as HTMLElement
                  quotesTab?.click()
                  setTimeout(() => {
                    quotesTab?.scrollIntoView({ behavior: 'smooth' })
                  }, 100)
                }}
              >
                <FileText className="h-4 w-4 mr-2" />
                View All Quotes
              </Button>
              <Button
                className="bg-blue-600 hover:bg-blue-700 shadow-sm"
                onClick={() => {
                  window.location.href = `/event-manager/quotes/new?event=${event?.id}`
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Transportation
              </Button>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Transportation Coverage */}
          <Card className="bg-white shadow-sm border-0 ring-1 ring-slate-200/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Car className="h-5 w-5 text-blue-600" />
                </div>
                <Badge variant="outline" className="text-xs">85% Complete</Badge>
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold text-slate-900">Transportation Coverage</h3>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold text-slate-900">120</span>
                  <span className="text-sm text-slate-500">of 140 guests</span>
                </div>
                <Progress value={85} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* Active Trips */}
          <Card className="bg-white shadow-sm border-0 ring-1 ring-slate-200/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Navigation className="h-5 w-5 text-green-600" />
                </div>
                <Badge variant="outline" className="text-xs text-green-600">Live</Badge>
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold text-slate-900">Active Trips</h3>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold text-slate-900">8</span>
                  <span className="text-sm text-slate-500">in progress</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-green-600">
                  <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>All on schedule</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pending Quotes */}
          <Card className="bg-white shadow-sm border-0 ring-1 ring-slate-200/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="h-5 w-5 text-yellow-600" />
                </div>
                <Badge variant="outline" className="text-xs text-yellow-600">Needs Attention</Badge>
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold text-slate-900">Pending Quotes</h3>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold text-slate-900">3</span>
                  <span className="text-sm text-slate-500">awaiting approval</span>
                </div>
                <div className="text-sm text-yellow-600">2 VIP arrivals pending</div>
              </div>
            </CardContent>
          </Card>

          {/* Budget Status */}
          <Card className="bg-white shadow-sm border-0 ring-1 ring-slate-200/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <DollarSign className="h-5 w-5 text-purple-600" />
                </div>
                <Badge variant="outline" className="text-xs text-green-600">On Budget</Badge>
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold text-slate-900">Transportation Budget</h3>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold text-slate-900">$12.4K</span>
                  <span className="text-sm text-slate-500">of $15K</span>
                </div>
                <Progress value={83} className="h-2" />
              </div>
            </CardContent>
          </Card>
        </div>

      {/* Enhanced Event Timeline Section */}
      <Card className="mb-6 bg-white shadow-sm border-0 ring-1 ring-slate-200/50">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Calendar className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <CardTitle className="text-xl">Event Timeline</CardTitle>
                <p className="text-sm text-slate-500 mt-1">Real-time transportation schedule and progress</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="bg-slate-100 rounded-lg p-1">
                <Button
                  variant={timelineView === "day" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setTimelineView("day")}
                  className="h-8 px-3"
                >
                  Today
                </Button>
                <Button
                  variant={timelineView === "week" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setTimelineView("week")}
                  className="h-8 px-3"
                >
                  Week
                </Button>
                <Button
                  variant={timelineView === "month" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setTimelineView("month")}
                  className="h-8 px-3"
                >
                  Month
                </Button>
              </div>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Enhanced Status Legend */}
          <div className="flex items-center justify-between mb-6 p-4 bg-slate-50 rounded-lg">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-green-500" />
                <span className="text-sm font-medium">Completed</span>
                <Badge variant="outline" className="ml-1 text-xs">2</Badge>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-blue-500" />
                <span className="text-sm font-medium">In Progress</span>
                <Badge variant="outline" className="ml-1 text-xs">5</Badge>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-yellow-500" />
                <span className="text-sm font-medium">Scheduled</span>
                <Badge variant="outline" className="ml-1 text-xs">3</Badge>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-red-500" />
                <span className="text-sm font-medium">Delayed</span>
                <Badge variant="outline" className="ml-1 text-xs">0</Badge>
              </div>
            </div>
            <div className="text-sm text-slate-500">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>

          {/* Modern Timeline Content */}
          <div className="space-y-4">
            {timelineView === "day" && (
              <div className="relative">
                {/* Time header */}
                <div className="flex ml-[280px] mb-4 px-2">
                  {Array.from({ length: 12 }).map((_, i) => {
                    const hour = i + 6 // Start from 6 AM
                    return (
                      <div key={i} className="flex-1 text-center">
                        <div className="text-xs font-medium text-slate-600">
                          {hour.toString().padStart(2, '0')}:00
                        </div>
                        <div className="text-xs text-slate-400">
                          {hour < 12 ? 'AM' : 'PM'}
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* Grid lines */}
                <div className="absolute inset-0 ml-[280px] grid grid-cols-12 gap-0 pointer-events-none">
                  {Array.from({ length: 12 }).map((_, i) => (
                    <div key={i} className="border-l border-slate-200 h-full" />
                  ))}
                </div>

                {/* Trip rows */}
                <div className="relative space-y-3">
                  {event?.trips.map((trip, index) => {
                    const startHour = parseInt(trip.time.split(':')[0])
                    const startMinute = parseInt(trip.time.split(':')[1]) || 0
                    const duration = trip.type === 'transfer' ? 1.5 : 2
                    const startPercentage = ((startHour - 6) + startMinute / 60) / 12 * 100
                    const widthPercentage = (duration / 12) * 100

                    return (
                      <div key={trip.id} className="flex items-center h-20 relative group">
                        {/* Trip details on the left */}
                        <div className="w-[280px] pr-4">
                          <Card className="p-3 h-16 flex items-center justify-between hover:shadow-md transition-shadow">
                            <div className="flex items-center gap-3">
                              <div className={cn(
                                "h-4 w-4 rounded-full shrink-0 ring-2 ring-white shadow-sm",
                                trip.status === 'completed' ? 'bg-green-500' :
                                  trip.status === 'in_progress' ? 'bg-blue-500' :
                                    trip.status === 'scheduled' ? 'bg-yellow-500' :
                                      'bg-slate-400'
                              )} />
                              <div className="min-w-0">
                                <div className="text-sm font-semibold truncate">
                                  {trip.passengers?.[0]?.name || 'Group Transfer'}
                                </div>
                                <div className="flex items-center gap-1 text-xs text-slate-500">
                                  <Car className="h-3 w-3" />
                                  <span>{trip.vehicle}</span>
                                  <span>•</span>
                                  <span>{trip.passengerCount} pax</span>
                                </div>
                              </div>
                            </div>
                            <div className="text-xs text-slate-500 text-right">
                              <div className="font-medium">{trip.time}</div>
                              <div className="text-xs">{trip.type}</div>
                            </div>
                          </Card>
                        </div>

                        {/* Trip block on the timeline */}
                        {startHour >= 6 && startHour <= 18 && (
                          <div
                            className={cn(
                              "absolute h-14 rounded-lg border-2 flex items-center px-3 cursor-pointer transition-all hover:shadow-lg hover:scale-[1.02] group-hover:z-10",
                              trip.status === 'completed' ? 'bg-green-100 border-green-400 text-green-800' :
                                trip.status === 'in_progress' ? 'bg-blue-100 border-blue-400 text-blue-800' :
                                  trip.status === 'scheduled' ? 'bg-yellow-100 border-yellow-400 text-yellow-800' :
                                    'bg-slate-100 border-slate-400 text-slate-800'
                            )}
                            style={{
                              left: `calc(${Math.max(0, Math.min(startPercentage, 85))}% + 280px)`,
                              width: `${Math.min(widthPercentage, 100 - Math.max(0, startPercentage))}%`,
                              minWidth: '120px'
                            }}
                            onClick={() => {
                              setSelectedTrip(trip)
                              setTripDetailsOpen(true)
                            }}
                          >
                            <div className="flex items-center gap-2 w-full min-w-0">
                              <div className="truncate text-sm font-medium">
                                {trip.passengers?.[0]?.name?.split(' ')[0] || 'Group'}
                              </div>
                              <ArrowRight className="h-3 w-3 shrink-0" />
                              <div className="truncate text-xs">
                                {trip.dropoffLocation.split(',')[0]}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            )}

            {/* Week/Month views */}
            {timelineView === "week" && (
              <div className="grid grid-cols-7 gap-4">
                {Array.from({ length: 7 }).map((_, dayIndex) => {
                  const date = new Date()
                  date.setDate(date.getDate() + dayIndex)
                  // For demo purposes, distribute trips across the week
                  const dayTrips = event?.trips.filter((_, index) =>
                    index % 7 === dayIndex
                  ) || []

                  return (
                    <Card key={dayIndex} className="p-4">
                      <div className="text-center mb-3">
                        <div className="text-sm font-medium">{date.toLocaleDateString('en', { weekday: 'short' })}</div>
                        <div className="text-lg font-bold">{date.getDate()}</div>
                      </div>
                      <div className="space-y-2">
                        {dayTrips.slice(0, 3).map(trip => (
                          <div key={trip.id} className="p-2 bg-slate-50 rounded text-xs">
                            <div className="font-medium truncate">{trip.passengers?.[0]?.name || 'Group'}</div>
                            <div className="text-slate-500">{trip.time}</div>
                          </div>
                        ))}
                        {dayTrips.length > 3 && (
                          <div className="text-xs text-slate-500 text-center">+{dayTrips.length - 3} more</div>
                        )}
                      </div>
                    </Card>
                  )
                })}
              </div>
            )}

            {timelineView === "month" && (
              <div className="text-center py-8 text-slate-500">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Month view coming soon</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Keep only the Alerts Panel */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
            <Button variant="ghost" size="sm" onClick={() => setShowAlerts(!showAlerts)}>
              <Bell className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        {showAlerts && (
          <CardContent>
            <ScrollArea className="h-[120px]">
              <div className="space-y-3">
                {alerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`p-3 rounded-lg border ${alert.type === "high"
                      ? "bg-red-50/50 border-red-200"
                      : alert.type === "medium"
                        ? "bg-yellow-50/50 border-yellow-200"
                        : "bg-blue-50/50 border-blue-200"
                      }`}
                  >
                    <div className="flex items-start gap-2">
                      {alert.category === "vip" ? (
                        <Shield className="h-4 w-4 text-red-500 mt-0.5" />
                      ) : alert.category === "traffic" ? (
                        <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5" />
                      ) : alert.category === "weather" ? (
                        <AlertTriangle className="h-4 w-4 text-blue-500 mt-0.5" />
                      ) : (
                        <Clock4 className="h-4 w-4 text-gray-500 mt-0.5" />
                      )}
                      <div>
                        <p className="text-sm">{alert.message}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-muted-foreground">
                            {new Date(alert.timestamp).toLocaleTimeString()}
                          </span>
                          {alert.relatedTrips && (
                            <Badge variant="secondary" className="text-xs">
                              {alert?.relatedTrips?.length} affected trips
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        )}
      </Card>

        {/* Enhanced Main Content Tabs */}
        <Card className="bg-white shadow-sm border-0 ring-1 ring-slate-200/50">
          <Tabs defaultValue="overview" className="w-full">
            <div className="border-b border-slate-200">
              <TabsList className="h-auto p-1 bg-transparent w-full justify-start">
                <TabsTrigger
                  value="overview"
                  className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 px-6 py-3 rounded-lg"
                >
                  <LayoutGrid className="h-4 w-4 mr-2" />
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="live"
                  className="data-[state=active]:bg-green-50 data-[state=active]:text-green-700 data-[state=active]:border-green-200 px-6 py-3 rounded-lg"
                >
                  <Navigation className="h-4 w-4 mr-2" />
                  Live Trips
                  <Badge variant="outline" className="ml-2 bg-green-100 text-green-700 border-green-300">8</Badge>
                </TabsTrigger>
                <TabsTrigger
                  value="quotes"
                  className="data-[state=active]:bg-yellow-50 data-[state=active]:text-yellow-700 data-[state=active]:border-yellow-200 px-6 py-3 rounded-lg"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Quotes
                  <Badge variant="outline" className="ml-2 bg-yellow-100 text-yellow-700 border-yellow-300">3</Badge>
                </TabsTrigger>
                <TabsTrigger
                  value="schedule"
                  className="data-[state=active]:bg-purple-50 data-[state=active]:text-purple-700 data-[state=active]:border-purple-200 px-6 py-3 rounded-lg"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Overview Tab Content */}
            <TabsContent value="overview" className="p-6 space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Quick Actions */}
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-500" />
                    Quick Actions
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-blue-50 hover:border-blue-200"
                      onClick={() => {
                        window.location.href = `/event-manager/quotes/new?event=${event?.id}`
                      }}
                    >
                      <Plus className="h-5 w-5" />
                      <span className="text-sm">Add Quote</span>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-green-50 hover:border-green-200"
                      onClick={() => {
                        // Scroll to live trips tab and activate it
                        const liveTab = document.querySelector('[value="live"]') as HTMLElement
                        liveTab?.click()
                        setTimeout(() => {
                          liveTab?.scrollIntoView({ behavior: 'smooth' })
                        }, 100)
                      }}
                    >
                      <Navigation className="h-5 w-5" />
                      <span className="text-sm">Track Live Trips</span>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-purple-50 hover:border-purple-200"
                      onClick={() => {
                        // Scroll to schedule tab and activate it
                        const scheduleTab = document.querySelector('[value="schedule"]') as HTMLElement
                        scheduleTab?.click()
                        setTimeout(() => {
                          scheduleTab?.scrollIntoView({ behavior: 'smooth' })
                        }, 100)
                      }}
                    >
                      <Users className="h-5 w-5" />
                      <span className="text-sm">Manage Guests</span>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-yellow-50 hover:border-yellow-200"
                      onClick={() => {
                        // Show notification/alert functionality
                        alert('Send Updates feature coming soon!')
                      }}
                    >
                      <Bell className="h-5 w-5" />
                      <span className="text-sm">Send Updates</span>
                    </Button>
                  </div>
                </Card>

                {/* Recent Activity */}
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Clock className="h-5 w-5 text-green-500" />
                    Recent Activity
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                      <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Trip T1 started</p>
                        <p className="text-xs text-slate-500">John Doe pickup from SFO</p>
                      </div>
                      <span className="text-xs text-slate-500">2 min ago</span>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                      <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Quote Q3 approved</p>
                        <p className="text-xs text-slate-500">Motor Coach for 45 passengers</p>
                      </div>
                      <span className="text-xs text-slate-500">15 min ago</span>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                      <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">New quote request</p>
                        <p className="text-xs text-slate-500">Executive Van needed</p>
                      </div>
                      <span className="text-xs text-slate-500">1 hour ago</span>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Event Timeline Preview */}
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-purple-500" />
                    Today's Schedule
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Scroll to schedule tab and activate it
                      const scheduleTab = document.querySelector('[value="schedule"]') as HTMLElement
                      scheduleTab?.click()
                      setTimeout(() => {
                        scheduleTab?.scrollIntoView({ behavior: 'smooth' })
                      }, 100)
                    }}
                  >
                    View Full Schedule
                  </Button>
                </div>
                <div className="space-y-3">
                  {event?.trips.slice(0, 4).map((trip) => (
                    <div key={trip.id} className="flex items-center gap-4 p-3 border rounded-lg">
                      <div className="text-sm font-medium text-slate-600">{trip.time}</div>
                      <div className={cn(
                        "h-3 w-3 rounded-full",
                        trip.status === 'completed' ? 'bg-green-500' :
                        trip.status === 'in_progress' ? 'bg-blue-500' :
                        'bg-slate-300'
                      )}></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{trip.passengers?.[0]?.name || 'Group Transfer'}</p>
                        <p className="text-xs text-slate-500">{trip.pickupLocation} → {trip.dropoffLocation}</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {trip.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  ))}
                </div>
              </Card>
            </TabsContent>

        {/* Enhanced Live Trips Tab Content */}
        <TabsContent value="live" className="p-0">
          <div className="relative h-[calc(100vh-200px)]">
            {/* Map Container */}
            <div className="absolute inset-0">
              <Map
                {...mapViewport}
                onMove={evt => setMapViewport(evt.viewState)}
                style={{ width: '100%', height: '100%' }}
                mapStyle="mapbox://styles/mapbox/light-v11"
                mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_TOKEN}
              >
                {/* Map Controls */}
                <NavigationControl position="top-right" />
                <FullscreenControl position="top-right" />
                <ScaleControl position="bottom-right" />

                {/* Enhanced Route Lines for Each Trip */}
                {sortedLiveTrips.map((trip, index) => {
                  const pickup = getCoordinates(trip.pickupLocation)
                  const dropoff = getCoordinates(trip.dropoffLocation)

                  // Create more realistic curved route line
                  const createCurvedRoute = (start: any, end: any) => {
                    const midLat = (start.lat + end.lat) / 2
                    const midLng = (start.lng + end.lng) / 2

                    // Add some curve to make it look more like a real route
                    const offset = 0.005 // Adjust this for more/less curve
                    const curveLat = midLat + offset
                    const curveLng = midLng + offset

                    return [
                      [start.lng, start.lat],
                      [curveLng, curveLat],
                      [end.lng, end.lat]
                    ]
                  }

                  const routeData = {
                    type: 'Feature' as const,
                    geometry: {
                      type: 'LineString' as const,
                      coordinates: createCurvedRoute(pickup, dropoff)
                    }
                  }

                  const routeColors = ['#4B83E8', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']
                  const routeColor = routeColors[index % routeColors.length]

                  return (
                    <Source key={`route-${trip.id}`} type="geojson" data={routeData}>
                      <Layer
                        id={`route-${trip.id}`}
                        type="line"
                        paint={{
                          'line-color': selectedLiveTrips.includes(trip.id) ? routeColor : '#CBD5E1',
                          'line-width': selectedLiveTrips.includes(trip.id) ? 6 : 3,
                          'line-opacity': selectedLiveTrips.includes(trip.id) ? 0.9 : 0.4,
                          'line-dasharray': trip.stage === 'dropped_off' ? [1, 0] : undefined
                        }}
                      />
                    </Source>
                  )
                })}

                {/* Enhanced Trip Markers */}
                {sortedLiveTrips.map((trip, index) => {
                  const pickup = getCoordinates(trip.pickupLocation)
                  const dropoff = getCoordinates(trip.dropoffLocation)

                  // Calculate vehicle position based on trip stage
                  const progress = trip.stage === 'on_the_way' ? 0.3 :
                    trip.stage === 'waiting_for_passenger' ? 0 :
                      trip.stage === 'passenger_on_board' ? 0.7 : 1

                  const vehiclePosition = {
                    lat: pickup.lat + (dropoff.lat - pickup.lat) * progress,
                    lng: pickup.lng + (dropoff.lng - pickup.lng) * progress
                  }

                  const routeColors = ['#4B83E8', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']
                  const routeColor = routeColors[index % routeColors.length]

                  return (
                    <Fragment key={trip.id}>
                      {/* Enhanced Vehicle Position with Driver Info */}
                      <Marker
                        longitude={vehiclePosition.lng}
                        latitude={vehiclePosition.lat}
                      >
                        <div className="relative group">
                          <div
                            className="w-8 h-8 rounded-full border-3 border-white shadow-lg flex items-center justify-center cursor-pointer transform transition-transform hover:scale-110"
                            style={{ backgroundColor: routeColor }}
                            onClick={() => handleLiveTripSelect(trip.id)}
                          >
                            <Car className="h-4 w-4 text-white" />
                          </div>
                          {/* Enhanced Vehicle Info Popup */}
                          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-white p-4 rounded-lg shadow-xl border opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-20">
                            <div className="text-sm font-semibold">{trip.vehicle}</div>
                            <div className="text-xs text-muted-foreground">{trip.driver?.name}</div>
                            <div className="text-xs text-muted-foreground">
                              Stage: {trip.stage?.replace('_', ' ') || 'In Progress'}
                            </div>
                            <div className="text-xs text-blue-600 font-medium">
                              ETA: {trip.stage === 'on_the_way' ? '10 mins' :
                                trip.stage === 'waiting_for_passenger' ? 'Arrived' :
                                  trip.stage === 'passenger_on_board' ? '15 mins' : 'Completed'}
                            </div>
                          </div>
                        </div>
                      </Marker>

                      {/* Enhanced Pickup Point - Only show when trip is selected */}
                      {selectedLiveTrips.includes(trip.id) && (
                        <Marker longitude={pickup.lng} latitude={pickup.lat}>
                          <div className="relative group">
                            <div className="flex items-center gap-2">
                              <div className="bg-green-500 text-white px-3 py-2 rounded-lg shadow-lg text-sm font-bold border-2 border-white cursor-pointer hover:bg-green-600 transition-colors">
                                A
                              </div>
                              <div className="bg-white px-4 py-3 rounded-lg shadow-lg border">
                                <div className="text-sm font-semibold">
                                  {trip.passengers?.[0]?.name || 'Passenger'}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {trip.passengerCount} passenger{trip.passengerCount !== 1 ? 's' : ''}
                                </div>
                              </div>
                            </div>
                            {/* Enhanced Pickup Location Popup */}
                            <div className="absolute bottom-10 left-0 bg-white p-4 rounded-lg shadow-xl border opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-20 max-w-xs">
                              <div className="text-sm font-semibold text-green-600">Pickup Location</div>
                              <div className="text-xs text-muted-foreground">{trip.pickupLocation}</div>
                              <div className="text-xs text-muted-foreground">Time: {trip.time}</div>
                              {trip.passengers && trip?.passengers?.length > 0 && (
                                <div className="mt-2 space-y-1">
                                  <div className="text-xs font-semibold">Passengers:</div>
                                  {trip.passengers.slice(0, 3).map((passenger: any, i: number) => (
                                    <div key={i} className="text-xs text-muted-foreground flex items-center gap-1">
                                      <span>{passenger.name}</span>
                                      {passenger.isVIP && <Crown className="h-3 w-3 text-yellow-500" />}
                                    </div>
                                  ))}
                                  {trip?.passengers?.length > 3 && (
                                    <div className="text-xs text-muted-foreground">
                                      +{trip?.passengers?.length - 3} more
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </Marker>
                      )}

                      {/* Enhanced Dropoff Point - Only show when trip is selected */}
                      {selectedLiveTrips.includes(trip.id) && (
                        <Marker longitude={dropoff.lng} latitude={dropoff.lat}>
                          <div className="relative group">
                            <div className="flex items-center gap-2">
                              <div className="bg-red-500 text-white px-3 py-2 rounded-lg shadow-lg text-sm font-bold border-2 border-white cursor-pointer hover:bg-red-600 transition-colors">
                                B
                              </div>
                              <div className="bg-white px-4 py-3 rounded-lg shadow-lg border">
                                <div className="text-sm font-semibold">
                                  {trip.dropoffLocation.split(',')[0]}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  ETA: {trip.estimatedDuration || 15} min
                                </div>
                              </div>
                            </div>
                            {/* Enhanced Dropoff Location Popup */}
                            <div className="absolute bottom-10 right-0 bg-white p-4 rounded-lg shadow-xl border opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-20">
                              <div className="text-sm font-semibold text-red-600">Dropoff Location</div>
                              <div className="text-xs text-muted-foreground">{trip.dropoffLocation}</div>
                              <div className="text-xs text-muted-foreground">
                                ETA: {trip.estimatedDuration || 15} minutes
                              </div>
                            </div>
                          </div>
                        </Marker>
                      )}
                    </Fragment>
                  )
                })}
              </Map>

              {/* Map Overlay Controls - Only show in normal mode, not full-screen */}
              {!isFullScreen && (
                <div className="absolute top-4 left-4 z-50 space-y-4">
                {/* Live Trips Control Panel */}
                <Card className="w-80 bg-white/95 backdrop-blur-sm">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Live Trips Control</CardTitle>
                      <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300">
                        {sortedLiveTrips.length} Active
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Search and Filters */}
                    <div className="space-y-3">
                      <div className="relative">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Search trips, drivers, passengers..."
                          className="pl-8 h-9"
                          value={liveSearchTerm}
                          onChange={(e) => setLiveSearchTerm(e?.target?.value)}
                        />
                      </div>

                      {/* Trip Stage Filter */}
                      <Select defaultValue="all">
                        <SelectTrigger className="h-9">
                          <SelectValue placeholder="Filter by stage" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Stages</SelectItem>
                          <SelectItem value="on_the_way">On The Way</SelectItem>
                          <SelectItem value="waiting_for_passenger">Waiting for Passenger</SelectItem>
                          <SelectItem value="passenger_on_board">Passenger On Board</SelectItem>
                          <SelectItem value="dropped_off">Completed</SelectItem>
                        </SelectContent>
                      </Select>

                      {/* Vehicle Type Filter */}
                      <Select defaultValue="all">
                        <SelectTrigger className="h-9">
                          <SelectValue placeholder="Filter by vehicle" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Vehicles</SelectItem>
                          <SelectItem value="sedan">Sedan</SelectItem>
                          <SelectItem value="suv">SUV</SelectItem>
                          <SelectItem value="van">Van</SelectItem>
                          <SelectItem value="luxury">Luxury</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Quick Actions */}
                    <div className="grid grid-cols-2 gap-2">
                      <Button size="sm" variant="outline" onClick={() => window.location.reload()}>
                        <RefreshCw className="h-4 w-4 mr-1" />
                        Refresh
                      </Button>
                      <Button size="sm" variant="outline" onClick={fitMapToSelectedTrips}>
                        <Zap className="h-4 w-4 mr-1" />
                        Auto-fit
                      </Button>
                      <Button size="sm" variant="outline" className="col-span-2">
                        <AlertTriangle className="h-4 w-4 mr-1" />
                        Emergency Overview
                      </Button>
                    </div>

                    {/* God's View Features */}
                    <div className="border-t pt-3 space-y-2">
                      <div className="text-xs font-medium text-muted-foreground">Event Overview</div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div className="bg-blue-50 p-2 rounded">
                          <div className="font-medium text-blue-700">Coverage</div>
                          <div className="text-blue-600">85% Complete</div>
                        </div>
                        <div className="bg-green-50 p-2 rounded">
                          <div className="font-medium text-green-700">On Time</div>
                          <div className="text-green-600">92% Rate</div>
                        </div>
                        <div className="bg-yellow-50 p-2 rounded">
                          <div className="font-medium text-yellow-700">Delays</div>
                          <div className="text-yellow-600">3 Active</div>
                        </div>
                        <div className="bg-purple-50 p-2 rounded">
                          <div className="font-medium text-purple-700">VIP</div>
                          <div className="text-purple-600">6 Tracked</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Trip List Panel */}
                <Card className="w-80 bg-white/95 backdrop-blur-sm max-h-96 overflow-hidden">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Active Trips</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <ScrollArea className="h-80">
                      <div className="space-y-2 p-4">
                        {sortedLiveTrips.map((trip, index) => {
                          const routeColors = ['#4B83E8', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']
                          const routeColor = routeColors[index % routeColors.length]

                          return (
                            <Card
                              key={trip.id}
                              className={cn(
                                "p-3 cursor-pointer transition-all hover:shadow-md",
                                selectedLiveTrips.includes(trip.id) ? "ring-2 ring-primary bg-primary/5" : "hover:bg-muted/50"
                              )}
                              onClick={() => handleLiveTripSelect(trip.id)}
                            >
                              <div className="space-y-2">
                                {/* Header */}
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <div
                                      className="w-3 h-3 rounded-full border border-white"
                                      style={{ backgroundColor: routeColor }}
                                    />
                                    <span className="font-medium text-sm">
                                      {trip.passengers?.[0]?.name.split(' ')[0] || 'Passenger'}
                                    </span>
                                    <span className="text-xs text-muted-foreground">• {trip.vehicle}</span>
                                  </div>
                                  <div className={cn(
                                    "px-2 py-0.5 rounded text-xs font-medium text-white",
                                    getTripStageColor(trip.stage || "on_the_way")
                                  )}>
                                    {getTripStageAbbr(trip.stage || "on_the_way")}
                                  </div>
                                </div>

                                {/* Route */}
                                <div className="text-xs text-muted-foreground space-y-1">
                                  <div className="flex items-center gap-1">
                                    <span className="font-medium text-green-600">A</span>
                                    <span className="truncate">{trip.pickupLocation}</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <span className="font-medium text-red-600">B</span>
                                    <span className="truncate">{trip.dropoffLocation}</span>
                                  </div>
                                </div>

                                {/* ETA */}
                                <div className="flex justify-between items-center text-xs">
                                  <span className="text-muted-foreground">
                                    {trip.passengerCount} passenger{trip.passengerCount !== 1 ? 's' : ''}
                                  </span>
                                  <span className="font-medium text-blue-600">
                                    ETA: {trip.stage === 'on_the_way' ? '10 mins' :
                                      trip.stage === 'waiting_for_passenger' ? 'Arrived' :
                                        trip.stage === 'passenger_on_board' ? '15 mins' : 'Completed'}
                                  </span>
                                </div>
                              </div>
                            </Card>
                          )
                        })}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
              )}

              {/* Map Legend - Only show in normal mode, not full-screen */}
              {!isFullScreen && (
                <div className="absolute bottom-4 left-4 z-50">
                <Card className="bg-white/95 backdrop-blur-sm">
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Trip Stages</div>
                      <div className="space-y-1 text-xs">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <span>On The Way</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                          <span>Waiting for Passenger</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span>Passenger On Board</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                          <span>Completed</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              )}

              {/* Enhanced Real-time Stats Panel - Only show in normal mode, not full-screen */}
              {!isFullScreen && (
                <div className="absolute top-4 right-20 z-50 space-y-4">
                  {/* Main Stats */}
                  <Card className="bg-white/95 backdrop-blur-sm">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="text-sm font-medium text-center">Live Transportation</div>
                        <div className="grid grid-cols-2 gap-3 text-center">
                          <div>
                            <div className="text-2xl font-bold text-blue-600">{sortedLiveTrips.filter(t => t.stage === 'on_the_way').length}</div>
                            <div className="text-xs text-muted-foreground">En Route</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold text-yellow-600">{sortedLiveTrips.filter(t => t.stage === 'waiting_for_passenger').length}</div>
                            <div className="text-xs text-muted-foreground">Waiting</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold text-green-600">{sortedLiveTrips.filter(t => t.stage === 'passenger_on_board').length}</div>
                            <div className="text-xs text-muted-foreground">In Transit</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold text-gray-600">{sortedLiveTrips.filter(t => t.stage === 'dropped_off').length}</div>
                            <div className="text-xs text-muted-foreground">Completed</div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Event Performance */}
                  <Card className="bg-white/95 backdrop-blur-sm">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="text-sm font-medium text-center">Event Performance</div>
                        <div className="space-y-2 text-xs">
                          <div className="flex justify-between">
                            <span>Guest Coverage:</span>
                            <span className="font-medium text-green-600">
                              {performanceMetrics?.guestCoverage ?
                                `${performanceMetrics.guestCoverage.covered}/${performanceMetrics.guestCoverage.total} (${performanceMetrics.guestCoverage.percentage}%)` :
                                '120/140 (86%)'
                              }
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>On-Time Rate:</span>
                            <span className="font-medium text-blue-600">
                              {performanceMetrics?.onTimeRate ? `${performanceMetrics.onTimeRate}%` : '92%'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Active Delays:</span>
                            <span className="font-medium text-yellow-600">
                              {performanceMetrics?.delayedTrips ? `${performanceMetrics.delayedTrips} trips` : '3 trips'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>VIP Tracking:</span>
                            <span className="font-medium text-purple-600">
                              {performanceMetrics?.vipTrips ? `${performanceMetrics.vipTrips} active` : '6 active'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Quick Actions */}
                  <Card className="bg-white/95 backdrop-blur-sm">
                    <CardContent className="p-3">
                      <div className="space-y-2">
                        <Button size="sm" variant="outline" className="w-full text-xs">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          Emergency Alert
                        </Button>
                        <Button size="sm" variant="outline" className="w-full text-xs">
                          <MessageSquare className="h-3 w-3 mr-1" />
                          Broadcast Update
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Enhanced Quotes Tab Content */}
        <TabsContent value="quotes">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle>Transportation Quotes</CardTitle>
                  <p className="text-sm text-muted-foreground">Manage and track all transportation requests</p>
                </div>
                <div className="flex items-center gap-3">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                  <Button
                    onClick={() => {
                      window.location.href = `/event-manager/quotes/new?event=${event?.id}`
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Request New Quote
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Enhanced Filters Section */}
              <div className="flex flex-wrap gap-4 mb-8">
                {/* Search by Passenger Name */}
                <div className="w-[250px] relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search passenger name..."
                    className="pl-8"
                    value={searchPassenger}
                    onChange={(e) => setSearchPassenger(e?.target?.value)}
                  />
                </div>

                {/* Date Filter */}
                <div className="flex items-center space-x-2">
                  <Select defaultValue="all-days">
                    <SelectTrigger className="w-[200px]">
                      <Calendar className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="View Schedule By Day" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-days">Full Event Schedule</SelectItem>
                      <SelectItem value="day-1">Day 1 - Guest Arrivals</SelectItem>
                      <SelectItem value="day-2">Day 2 - Event Day</SelectItem>
                      <SelectItem value="day-3">Day 3 - Departures</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Guest Groups Filter */}
                <div className="flex items-center space-x-2">
                  <Select defaultValue="all-groups">
                    <SelectTrigger className="w-[160px]">
                      <Users className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="Guest Groups" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-groups">All Groups</SelectItem>
                      <SelectItem value="vip">VIP Guests (6)</SelectItem>
                      <SelectItem value="staff">Staff (12)</SelectItem>
                      <SelectItem value="attendees">Attendees (122)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Status Filter */}
                <div className="flex items-center space-x-2">
                  <Select defaultValue="all-statuses">
                    <SelectTrigger className="w-[160px]">
                      <AlertCircle className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="Quote Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-statuses">All Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="accepted">Accepted</SelectItem>
                      <SelectItem value="rate_requested">Rate Requested</SelectItem>
                      <SelectItem value="changes_requested">Changes Requested</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Sort Options */}
                <div className="flex items-center space-x-2">
                  <Select defaultValue="pickup-time">
                    <SelectTrigger className="w-[160px]">
                      <ArrowUpDown className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="Sort By" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pickup-time">Pickup Time</SelectItem>
                      <SelectItem value="passenger-name">Passenger Name</SelectItem>
                      <SelectItem value="price">Price</SelectItem>
                      <SelectItem value="status">Status</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-4 gap-4 mb-6">
                <Card className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FileText className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{event?.quotes.length || 0}</div>
                      <div className="text-xs text-slate-500">Total Quotes</div>
                    </div>
                  </div>
                </Card>
                <Card className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <Clock className="h-4 w-4 text-yellow-600" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">
                        {event?.quotes.filter(q => q.status === 'pending' || q.status === 'rate_requested').length || 0}
                      </div>
                      <div className="text-xs text-slate-500">Pending</div>
                    </div>
                  </div>
                </Card>
                <Card className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">
                        {event?.quotes.filter(q => q.status === 'accepted' || q.status === 'quote_ready').length || 0}
                      </div>
                      <div className="text-xs text-slate-500">Ready/Accepted</div>
                    </div>
                  </div>
                </Card>
                <Card className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <DollarSign className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">
                        ${event?.quotes.reduce((sum, q) => sum + q.price.total, 0).toLocaleString() || '0'}
                      </div>
                      <div className="text-xs text-slate-500">Total Value</div>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Enhanced Streamlined Quote Cards */}
              <div className="space-y-4">
                {event?.quotes.map((quote) => (
                  <Card key={quote.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="p-3 bg-slate-100 rounded-lg">
                            <Car className="h-6 w-6 text-slate-600" />
                          </div>
                          <div>
                            <div className="flex items-center gap-3 mb-2">
                              <h4 className="font-semibold text-slate-900">{quote.vehicle}</h4>
                              <RealTimeStatus
                                quoteId={quote.id}
                                showNotifications={true}
                                size="sm"
                              />
                            </div>
                            <div className="grid grid-cols-3 gap-6 text-sm text-slate-600">
                              <div>
                                <span className="text-slate-500">Route:</span>
                                <div className="font-medium">{quote.pickupLocation} → {quote.dropoffLocation}</div>
                              </div>
                              <div>
                                <span className="text-slate-500">Date & Time:</span>
                                <div className="font-medium">{quote.date} at {quote.time}</div>
                              </div>
                              <div>
                                <span className="text-slate-500">Passengers:</span>
                                <div className="font-medium">{quote.passengerCount} passengers</div>
                                <div className="text-xs text-blue-600">{quote.guestGroup || 'General Attendees'}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-slate-900">${quote.price?.total?.toFixed(2)}</div>
                          <div className="text-sm text-slate-500">Total Cost</div>
                          <div className="flex items-center gap-2 mt-3">
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                            <Button size="sm" onClick={() => setExpandedQuoteId(quote.id)}>
                              View Details
                            </Button>
                          </div>
                        </div>
                      </div>
                      {/* Event Activity Context */}
                      <div className="mt-4 flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <span className="text-slate-500">Activity:</span>
                          <Badge variant="outline" className="text-xs">
                            {quote.eventActivity || 'General Transportation'}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-slate-500">Relation:</span>
                          <span className="font-medium">{quote.eventRelation || 'Event Transport'}</span>
                        </div>
                      </div>

                      {quote.notes && (
                        <div className="mt-4 p-3 bg-slate-50 rounded-lg">
                          <div className="text-sm text-slate-600">
                            <span className="font-medium">Notes:</span> {quote.notes}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Add New Quote CTA */}
              <Card className="border-dashed border-2 border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors mt-6">
                <CardContent className="p-8 text-center">
                  <div className="space-y-3">
                    <div className="p-3 bg-muted/50 rounded-full w-fit mx-auto">
                      <Plus className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <div>
                      <h4 className="font-medium">Need Additional Transportation?</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Add more quotes to complete your event transportation plan
                      </p>
                    </div>
                    <Button
                      onClick={() => {
                        window.location.href = `/event-manager/quotes/new?event=${event?.id}`
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Request New Quote
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scheduled Trips Tab */}
        <TabsContent value="trips">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle>Scheduled Trips</CardTitle>
                  <p className="text-sm text-muted-foreground">View and manage all scheduled trips for this event</p>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Search and Filter Row */}
                <div className="flex items-center gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Search trips..." className="pl-8" />
                  </div>
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Trip Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="arrival">Airport Arrivals</SelectItem>
                      <SelectItem value="departure">Airport Departures</SelectItem>
                      <SelectItem value="transfer">Hotel Transfers</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Trip List */}
                <div className="space-y-4">
                  {event?.trips?.map((trip) => (
                    <div key={trip.id} className="border rounded-lg">
                      {/* Main Row - Always Visible */}
                      <div className="p-4">
                        {/* City, Date, Event Row */}
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="text-lg font-semibold">New York City</h3>
                              <span className="text-sm text-muted-foreground">2024-03-15</span>
                            </div>
                            <div className="text-sm text-muted-foreground">Annual Conference 2024</div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setExpandedEvents(prev =>
                              prev.includes(trip.id)
                                ? prev.filter(id => id !== trip.id)
                                : [...prev, trip.id]
                            )}
                          >
                            <ChevronRight className={cn(
                              "h-4 w-4 transition-transform",
                              expandedEvents.includes(trip.id) && "transform rotate-90"
                            )} />
                          </Button>
                        </div>

                        {/* Passenger Info Row */}
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">Alice Johnson</span>
                            <Crown className="h-4 w-4 text-yellow-500" />
                            <Badge variant="outline" className="ml-2">VVIP</Badge>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <Car className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm text-muted-foreground">Luxury SUV</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm text-muted-foreground">3 passengers</span>
                            </div>
                          </div>
                        </div>

                        {/* Time and Location Row */}
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">14:00</span>
                          </div>
                          <div className="flex items-center gap-2 flex-1">
                            <div className="h-6 w-6 rounded-full bg-green-100 flex items-center justify-center">
                              <span className="text-green-700 text-sm font-medium">A</span>
                            </div>
                            <span className="text-sm text-muted-foreground">JFK Airport Terminal 4</span>
                            <div className="w-12 h-px bg-muted-foreground/30 mx-2" />
                            <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                              <span className="text-blue-700 text-sm font-medium">B</span>
                            </div>
                            <span className="text-sm text-muted-foreground">Marriott Hotel Manhattan</span>
                          </div>
                        </div>

                        {/* Status and Actions Row */}
                        <div className="flex items-center justify-between mt-4">
                          <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-0">Scheduled</Badge>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="sm">
                              <Phone className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MessageSquare className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MapPin className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Expandable Area */}
                      {expandedEvents.includes(trip.id) && (
                        <div className="border-t bg-muted/30">
                          <div className="p-4">
                            <Tabs defaultValue="timeline">
                              <TabsList>
                                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                                <TabsTrigger value="passengers">Passengers</TabsTrigger>
                                <TabsTrigger value="driver">Driver & Vehicle</TabsTrigger>
                                <TabsTrigger value="live-map">Live Map</TabsTrigger>
                                <TabsTrigger value="security">Security</TabsTrigger>
                                <TabsTrigger value="admin">Admin</TabsTrigger>
                              </TabsList>

                              {/* Timeline Tab Content */}
                              <TabsContent value="timeline" className="mt-4">
                                <div className="space-y-4">
                                  <div className="flex items-center gap-2">
                                    <div className="h-2 w-2 rounded-full bg-green-500" />
                                    <span className="text-sm font-medium">Pickup</span>
                                    <span className="text-sm text-muted-foreground">JFK Airport Terminal 4</span>
                                    <span className="text-sm font-medium ml-auto">14:00</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <div className="h-2 w-2 rounded-full bg-blue-500" />
                                    <span className="text-sm font-medium">Dropoff</span>
                                    <span className="text-sm text-muted-foreground">Marriott Hotel Manhattan</span>
                                    <span className="text-sm font-medium ml-auto">15:30</span>
                                  </div>
                                </div>
                                <div className="mt-4 grid grid-cols-4 gap-4">
                                  <div>
                                    <div className="text-sm font-medium">Duration</div>
                                    <div className="text-sm text-muted-foreground">90 min</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">Distance</div>
                                    <div className="text-sm text-muted-foreground">12.5 km</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">Status</div>
                                    <div className="text-sm text-green-600">On Time</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">Weather</div>
                                    <div className="text-sm text-muted-foreground">Clear Sky</div>
                                  </div>
                                </div>
                                <div className="mt-4">
                                  <div className="text-sm font-medium mb-2">Active Alerts</div>
                                  <div className="space-y-2">
                                    <div className="flex items-center gap-2 text-sm text-blue-600">
                                      <AlertCircle className="h-4 w-4" />
                                      <span>Driver en route to pickup location</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm text-blue-600">
                                      <CloudRain className="h-4 w-4" />
                                      <span>Light rain expected</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm text-orange-600">
                                      <AlertTriangle className="h-4 w-4" />
                                      <span>Moderate traffic on route</span>
                                    </div>
                                  </div>
                                </div>
                              </TabsContent>

                              {/* Passengers Tab Content */}
                              <TabsContent value="passengers" className="mt-4">
                                <div className="space-y-6">
                                  <div className="flex items-center justify-between">
                                    <div className="space-y-1">
                                      <h4 className="text-sm font-medium">Passenger List</h4>
                                      <p className="text-sm text-muted-foreground">Total: 3 passengers</p>
                                    </div>
                                    <Button variant="outline" size="sm">
                                      <UserPlus className="h-4 w-4 mr-2" />
                                      Add Passenger
                                    </Button>
                                  </div>
                                  <div className="space-y-4">
                                    {trip.passengers?.map((passenger) => (
                                      <div key={passenger.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex items-center gap-4">
                                          <Avatar>
                                            <AvatarFallback>{passenger.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                                          </Avatar>
                                          <div>
                                            <div className="flex items-center gap-2">
                                              <span className="font-medium">{passenger.name}</span>
                                              {passenger.isVIP && (
                                                <Badge variant="secondary" className="bg-yellow-100 text-yellow-700 border-0">VIP</Badge>
                                              )}
                                            </div>
                                            <div className="text-sm text-muted-foreground">{passenger.group || 'No group assigned'}</div>
                                          </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          {passenger.phone && (
                                            <Button variant="ghost" size="sm">
                                              <Phone className="h-4 w-4" />
                                            </Button>
                                          )}
                                          {passenger.email && (
                                            <Button variant="ghost" size="sm">
                                              <Mail className="h-4 w-4" />
                                            </Button>
                                          )}
                                          <Button variant="ghost" size="sm">
                                            <MoreVertical className="h-4 w-4" />
                                          </Button>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </TabsContent>

                              {/* Driver & Vehicle Tab Content */}
                              <TabsContent value="driver" className="mt-4">
                                <div className="space-y-6">
                                  <div className="grid grid-cols-2 gap-6">
                                    {/* Driver Information */}
                                    <div className="space-y-4">
                                      <h4 className="text-sm font-medium">Driver Information</h4>
                                      {trip.driver ? (
                                        <div className="p-4 border rounded-lg">
                                          <div className="flex items-center gap-4 mb-4">
                                            <Avatar>
                                              <AvatarFallback>{trip.driver.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                                            </Avatar>
                                            <div>
                                              <div className="font-medium">{trip.driver.name}</div>
                                              <div className="text-sm text-muted-foreground">{trip.driver.phone}</div>
                                            </div>
                                          </div>
                                          <div className="space-y-2">
                                            <div className="flex items-center gap-2">
                                              <Badge variant="outline" className="bg-green-100 text-green-700 border-0">Available</Badge>
                                              <Badge variant="outline">4.9 ★</Badge>
                                            </div>
                                            <div className="text-sm text-muted-foreground">Last active: 5 minutes ago</div>
                                          </div>
                                        </div>
                                      ) : (
                                        <div className="p-4 border rounded-lg">
                                          <div className="text-sm text-muted-foreground">No driver assigned</div>
                                          <Button variant="outline" size="sm" className="mt-2">
                                            <UserPlus className="h-4 w-4 mr-2" />
                                            Assign Driver
                                          </Button>
                                        </div>
                                      )}
                                    </div>

                                    {/* Vehicle Information */}
                                    <div className="space-y-4">
                                      <h4 className="text-sm font-medium">Vehicle Information</h4>
                                      <div className="p-4 border rounded-lg">
                                        <div className="flex items-center gap-4 mb-4">
                                          <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                                            <Car className="h-6 w-6 text-primary" />
                                          </div>
                                          <div>
                                            <div className="font-medium">{trip.vehicle}</div>
                                            <div className="text-sm text-muted-foreground">License: NYC-123</div>
                                          </div>
                                        </div>
                                        <div className="space-y-2">
                                          <div className="grid grid-cols-2 gap-2 text-sm">
                                            <div>
                                              <span className="text-muted-foreground">Capacity:</span>
                                              <span className="ml-2">4 passengers</span>
                                            </div>
                                            <div>
                                              <span className="text-muted-foreground">Type:</span>
                                              <span className="ml-2">Luxury SUV</span>
                                            </div>
                                            <div>
                                              <span className="text-muted-foreground">Color:</span>
                                              <span className="ml-2">Black</span>
                                            </div>
                                            <div>
                                              <span className="text-muted-foreground">Year:</span>
                                              <span className="ml-2">2023</span>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </TabsContent>

                              {/* Live Map Tab Content */}
                              <TabsContent value="live-map" className="mt-4">
                                <div className="space-y-4">
                                  <div className="h-[400px] rounded-lg border relative">
                                    <Map
                                      initialViewState={{
                                        latitude: 40.7128,
                                        longitude: -74.0060,
                                        zoom: 12
                                      }}
                                      style={{ width: '100%', height: '100%', borderRadius: '8px' }}
                                      mapStyle="mapbox://styles/mapbox/streets-v11"
                                    >
                                      <NavigationControl position="top-right" />
                                      <Marker latitude={40.7128} longitude={-74.0060}>
                                        <div className="h-4 w-4 bg-primary rounded-full ring-4 ring-primary/20" />
                                      </Marker>
                                      {/* Add route line and other markers here */}
                                    </Map>
                                    <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg">
                                      <div className="text-sm font-medium">ETA: 15 minutes</div>
                                      <div className="text-xs text-muted-foreground">Current speed: 35 mph</div>
                                    </div>
                                  </div>
                                </div>
                              </TabsContent>

                              {/* Security Tab Content */}
                              <TabsContent value="security" className="mt-4">
                                <div className="space-y-6">
                                  <div className="grid grid-cols-2 gap-6">
                                    {/* Security Requirements */}
                                    <div className="space-y-4">
                                      <h4 className="text-sm font-medium">Security Requirements</h4>
                                      <div className="p-4 border rounded-lg space-y-4">
                                        <div className="flex items-center gap-2">
                                          <Shield className="h-4 w-4 text-green-500" />
                                          <span className="text-sm">Background Check Verified</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <Shield className="h-4 w-4 text-green-500" />
                                          <span className="text-sm">Insurance Documentation Complete</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <Shield className="h-4 w-4 text-green-500" />
                                          <span className="text-sm">Vehicle Safety Inspection Passed</span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Security Protocols */}
                                    <div className="space-y-4">
                                      <h4 className="text-sm font-medium">Security Protocols</h4>
                                      <div className="p-4 border rounded-lg space-y-4">
                                        <div className="flex items-center gap-2">
                                          <Badge variant="outline">VIP Protocol Active</Badge>
                                        </div>
                                        <div className="text-sm text-muted-foreground">
                                          <ul className="list-disc list-inside space-y-2">
                                            <li>Secure route planning completed</li>
                                            <li>Backup vehicle on standby</li>
                                            <li>Direct communication line established</li>
                                          </ul>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </TabsContent>

                              {/* Admin Tab Content */}
                              <TabsContent value="admin" className="mt-4">
                                <div className="space-y-6">
                                  {/* Trip Details */}
                                  <div className="space-y-4">
                                    <h4 className="text-sm font-medium">Administrative Details</h4>
                                    <div className="grid grid-cols-3 gap-4">
                                      <div className="p-4 border rounded-lg">
                                        <div className="text-sm font-medium mb-2">Trip ID</div>
                                        <div className="text-sm text-muted-foreground">#{trip.id}</div>
                                      </div>
                                      <div className="p-4 border rounded-lg">
                                        <div className="text-sm font-medium mb-2">Created By</div>
                                        <div className="text-sm text-muted-foreground">John Admin</div>
                                      </div>
                                      <div className="p-4 border rounded-lg">
                                        <div className="text-sm font-medium mb-2">Last Modified</div>
                                        <div className="text-sm text-muted-foreground">2024-03-14 09:30 AM</div>
                                      </div>
                                    </div>
                                  </div>

                                  {/* Activity Log */}
                                  <div className="space-y-4">
                                    <h4 className="text-sm font-medium">Activity Log</h4>
                                    <div className="border rounded-lg divide-y">
                                      <div className="p-3">
                                        <div className="flex items-center justify-between">
                                          <div className="text-sm">Trip created</div>
                                          <div className="text-xs text-muted-foreground">2024-03-14 09:00 AM</div>
                                        </div>
                                      </div>
                                      <div className="p-3">
                                        <div className="flex items-center justify-between">
                                          <div className="text-sm">Driver assigned</div>
                                          <div className="text-xs text-muted-foreground">2024-03-14 09:15 AM</div>
                                        </div>
                                      </div>
                                      <div className="p-3">
                                        <div className="flex items-center justify-between">
                                          <div className="text-sm">Vehicle details updated</div>
                                          <div className="text-xs text-muted-foreground">2024-03-14 09:30 AM</div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  {/* Administrative Actions */}
                                  <div className="flex items-center justify-end gap-2">
                                    <Button variant="outline" size="sm">
                                      <Printer className="h-4 w-4 mr-2" />
                                      Print Details
                                    </Button>
                                    <Button variant="outline" size="sm">
                                      <Copy className="h-4 w-4 mr-2" />
                                      Duplicate Trip
                                    </Button>
                                    <Button variant="destructive" size="sm">
                                      <X className="h-4 w-4 mr-2" />
                                      Cancel Trip
                                    </Button>
                                  </div>
                                </div>
                              </TabsContent>
                            </Tabs>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>



            {/* Enhanced Schedule Tab Content */}
            <TabsContent value="schedule" className="p-6">
              <div className="space-y-6">
                {/* Header with Actions */}
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900">Event Schedule</h3>
                    <p className="text-sm text-slate-500 mt-1">Complete timeline of all transportation activities</p>
                  </div>
                  <div className="flex items-center gap-3">
                    <Button variant="outline" size="sm">
                      <Calendar className="h-4 w-4 mr-2" />
                      Export Schedule
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Print
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => {
                        window.location.href = `/event-manager/quotes/new?event=${event?.id}`
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Trip
                    </Button>
                  </div>
                </div>

                {/* Schedule Stats */}
                <div className="grid grid-cols-5 gap-4">
                  <Card className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Car className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="text-xl font-bold">{event?.trips.length || 0}</div>
                        <div className="text-xs text-slate-500">Total Trips</div>
                      </div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <div className="text-xl font-bold">
                          {event?.trips.filter(t => t.status === 'completed').length || 0}
                        </div>
                        <div className="text-xs text-slate-500">Completed</div>
                      </div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Navigation className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="text-xl font-bold">
                          {event?.trips.filter(t => t.status === 'in_progress').length || 0}
                        </div>
                        <div className="text-xs text-slate-500">In Progress</div>
                      </div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-yellow-100 rounded-lg">
                        <Clock className="h-4 w-4 text-yellow-600" />
                      </div>
                      <div>
                        <div className="text-xl font-bold">
                          {event?.trips.filter(t => t.status === 'scheduled').length || 0}
                        </div>
                        <div className="text-xs text-slate-500">Scheduled</div>
                      </div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <Users className="h-4 w-4 text-purple-600" />
                      </div>
                      <div>
                        <div className="text-xl font-bold">
                          {event?.trips.reduce((sum, t) => sum + t.passengerCount, 0) || 0}
                        </div>
                        <div className="text-xs text-slate-500">Total Passengers</div>
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Enhanced Timeline View */}
                <Card>
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Daily Schedule</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {new Date().toLocaleDateString('en', { weekday: 'long', month: 'long', day: 'numeric' })}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {event?.trips
                        .sort((a, b) => a.time.localeCompare(b.time))
                        .map((trip) => (
                        <Card key={trip.id} className="p-4 hover:shadow-md transition-shadow border-l-4"
                              style={{
                                borderLeftColor:
                                  trip.status === 'completed' ? '#10b981' :
                                  trip.status === 'in_progress' ? '#3b82f6' :
                                  trip.status === 'scheduled' ? '#f59e0b' : '#6b7280'
                              }}>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className="text-center">
                                <div className="text-lg font-bold text-slate-900">{trip.time}</div>
                                <div className="text-xs text-slate-500 uppercase tracking-wide">{trip.type}</div>
                              </div>
                              <div className={cn(
                                "h-4 w-4 rounded-full ring-2 ring-white shadow-sm",
                                trip.status === 'completed' ? 'bg-green-500' :
                                trip.status === 'in_progress' ? 'bg-blue-500' :
                                trip.status === 'scheduled' ? 'bg-yellow-500' : 'bg-slate-400'
                              )} />
                              <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                  <h4 className="font-semibold text-slate-900">
                                    {trip.passengers?.[0]?.name || 'Group Transfer'}
                                  </h4>
                                  <Badge variant="outline" className="text-xs">
                                    {trip.vehicle}
                                  </Badge>
                                  <Badge variant={
                                    trip.status === 'completed' ? 'default' :
                                    trip.status === 'in_progress' ? 'secondary' :
                                    'outline'
                                  } className="text-xs">
                                    {trip.status.replace('_', ' ').toUpperCase()}
                                  </Badge>
                                </div>
                                <div className="grid grid-cols-2 gap-4 text-sm text-slate-600">
                                  <div>
                                    <span className="text-slate-500">Route:</span>
                                    <div className="font-medium">{trip.pickupLocation} → {trip.dropoffLocation}</div>
                                  </div>
                                  <div>
                                    <span className="text-slate-500">Passengers:</span>
                                    <div className="font-medium">{trip.passengerCount} passengers</div>
                                  </div>
                                </div>
                                {trip.driver && (
                                  <div className="mt-2 text-sm text-slate-600">
                                    <span className="text-slate-500">Driver:</span>
                                    <span className="font-medium ml-1">{trip.driver.name}</span>
                                    <span className="text-slate-400 ml-2">{trip.driver.phone}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button variant="outline" size="sm">
                                <Edit className="h-4 w-4 mr-1" />
                                Edit
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedTrip(trip)
                                  setTripDetailsOpen(true)
                                }}
                              >
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          {trip.notes && (
                            <div className="mt-3 p-3 bg-slate-50 rounded-lg">
                              <div className="text-sm text-slate-600">
                                <span className="font-medium">Notes:</span> {trip.notes}
                              </div>
                            </div>
                          )}
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </Card>

        {/* Trip Details Dialog */}
        <Dialog open={tripDetailsOpen} onOpenChange={setTripDetailsOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Trip Details</DialogTitle>
            <DialogDescription>
              View and manage trip information
            </DialogDescription>
          </DialogHeader>
          {selectedTrip && (
            <div className="space-y-6">
              {/* Status Section */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Current Status</p>
                  <Badge
                    variant={
                      selectedTrip?.status === "completed"
                        ? "default"
                        : selectedTrip?.status === "in_progress"
                          ? "secondary"
                          : selectedTrip?.status === "cancelled"
                            ? "destructive"
                            : "outline"
                    }
                  >
                    {selectedTrip?.status?.replace("_", " ").toUpperCase()}
                  </Badge>
                </div>
                <Select defaultValue={selectedTrip?.status}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Update status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Tabs defaultValue="timeline" className="w-full">
                <TabsList>
                  <TabsTrigger value="timeline">Timeline</TabsTrigger>
                  <TabsTrigger value="passengers">Passengers</TabsTrigger>
                  <TabsTrigger value="driver">Driver & Vehicle</TabsTrigger>
                  <TabsTrigger value="live-map">Live Map</TabsTrigger>
                  <TabsTrigger value="security">Security</TabsTrigger>
                  <TabsTrigger value="admin">Admin</TabsTrigger>
                </TabsList>

                {/* Timeline Tab Content */}
                <TabsContent value="timeline" className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-green-500" />
                      <span className="text-sm font-medium">Pickup</span>
                      <span className="text-sm text-muted-foreground">{selectedTrip?.pickupLocation}</span>
                      <span className="text-sm font-medium ml-auto">{selectedTrip?.time}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-blue-500" />
                      <span className="text-sm font-medium">Dropoff</span>
                      <span className="text-sm text-muted-foreground">{selectedTrip?.dropoffLocation}</span>
                      <span className="text-sm font-medium ml-auto">{selectedTrip?.endTime || 'TBD'}</span>
                    </div>
                  </div>
                  <div className="mt-4 grid grid-cols-4 gap-4">
                    <div>
                      <div className="text-sm font-medium">Duration</div>
                      <div className="text-sm text-muted-foreground">{selectedTrip?.estimatedDuration || '90'} min</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium">Distance</div>
                      <div className="text-sm text-muted-foreground">12.5 km</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium">Status</div>
                      <div className="text-sm text-green-600">On Time</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium">Weather</div>
                      <div className="text-sm text-muted-foreground">Clear Sky</div>
                    </div>
                  </div>
                  <div className="mt-4">
                    <div className="text-sm font-medium mb-2">Active Alerts</div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-blue-600">
                        <AlertCircle className="h-4 w-4" />
                        <span>Driver en route to pickup location</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-blue-600">
                        <CloudRain className="h-4 w-4" />
                        <span>Light rain expected</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-orange-600">
                        <AlertTriangle className="h-4 w-4" />
                        <span>Moderate traffic on route</span>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Passengers Tab Content */}
                <TabsContent value="passengers" className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h4 className="text-sm font-medium">Passenger List</h4>
                      <p className="text-sm text-muted-foreground">Total: {selectedTrip?.passengerCount || 0} passengers</p>
                    </div>
                    <Button variant="outline" size="sm">
                      <UserPlus className="h-4 w-4 mr-2" />
                      Add Passenger
                    </Button>
                  </div>
                  <div className="space-y-4">
                    {selectedTrip?.passengers && selectedTrip.passengers.length > 0 ? (
                      selectedTrip?.passengers?.map((passenger) => (
                        <div key={passenger.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center gap-4">
                            <Avatar>
                              <AvatarFallback>{passenger.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{passenger.name}</span>
                                {passenger.isVIP && (
                                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-700 border-0">VIP</Badge>
                                )}
                              </div>
                              <div className="text-sm text-muted-foreground">{passenger.group || 'No group assigned'}</div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {passenger.phone && (
                              <Button variant="ghost" size="sm">
                                <Phone className="h-4 w-4" />
                              </Button>
                            )}
                            {passenger.email && (
                              <Button variant="ghost" size="sm">
                                <Mail className="h-4 w-4" />
                              </Button>
                            )}
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-sm text-muted-foreground border rounded-lg">
                        No passengers assigned yet
                      </div>
                    )}
                  </div>
                </TabsContent>

                {/* Driver & Vehicle Tab Content */}
                <TabsContent value="driver" className="space-y-6">
                  <div className="grid grid-cols-2 gap-6">
                    {/* Driver Information */}
                    <div className="space-y-4">
                      <h4 className="text-sm font-medium">Driver Information</h4>
                      {selectedTrip?.driver ? (
                        <div className="p-4 border rounded-lg">
                          <div className="flex items-center gap-4 mb-4">
                            <Avatar>
                              <AvatarFallback>{selectedTrip.driver.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{selectedTrip.driver.name}</div>
                              <div className="text-sm text-muted-foreground">{selectedTrip.driver.phone}</div>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="bg-green-100 text-green-700 border-0">Available</Badge>
                              <Badge variant="outline">4.9 ★</Badge>
                            </div>
                            <div className="text-sm text-muted-foreground">Last active: 5 minutes ago</div>
                          </div>
                        </div>
                      ) : (
                        <div className="p-4 border rounded-lg">
                          <div className="text-sm text-muted-foreground">No driver assigned</div>
                          <Button variant="outline" size="sm" className="mt-2">
                            <UserPlus className="h-4 w-4 mr-2" />
                            Assign Driver
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Vehicle Information */}
                    <div className="space-y-4">
                      <h4 className="text-sm font-medium">Vehicle Information</h4>
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-center gap-4 mb-4">
                          <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                            <Car className="h-6 w-6 text-primary" />
                          </div>
                          <div>
                            <div className="font-medium">{selectedTrip?.vehicle}</div>
                            <div className="text-sm text-muted-foreground">License: NYC-123</div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span className="text-muted-foreground">Capacity:</span>
                              <span className="ml-2">4 passengers</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Type:</span>
                              <span className="ml-2">Luxury SUV</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Color:</span>
                              <span className="ml-2">Black</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Year:</span>
                              <span className="ml-2">2023</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Live Map Tab Content */}
                <TabsContent value="live-map" className="space-y-4">
                  <div className="space-y-4">
                    <div className="h-[400px] rounded-lg border relative bg-muted/30 flex items-center justify-center">
                      <div className="text-center space-y-2">
                        <MapPin className="h-12 w-12 mx-auto text-muted-foreground" />
                        <div className="text-sm font-medium">Live Map</div>
                        <div className="text-xs text-muted-foreground">Real-time tracking will appear here</div>
                      </div>
                      <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg">
                        <div className="text-sm font-medium">ETA: 15 minutes</div>
                        <div className="text-xs text-muted-foreground">Current speed: 35 mph</div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Security Tab Content */}
                <TabsContent value="security" className="space-y-6">
                  <div className="grid grid-cols-2 gap-6">
                    {/* Security Requirements */}
                    <div className="space-y-4">
                      <h4 className="text-sm font-medium">Security Requirements</h4>
                      <div className="p-4 border rounded-lg space-y-4">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Background Check Verified</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Insurance Documentation Complete</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Vehicle Safety Inspection Passed</span>
                        </div>
                      </div>
                    </div>

                    {/* Security Protocols */}
                    <div className="space-y-4">
                      <h4 className="text-sm font-medium">Security Protocols</h4>
                      <div className="p-4 border rounded-lg space-y-4">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">VIP Protocol Active</Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <ul className="list-disc list-inside space-y-2">
                            <li>Secure route planning completed</li>
                            <li>Backup vehicle on standby</li>
                            <li>Direct communication line established</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Admin Tab Content */}
                <TabsContent value="admin" className="space-y-6">
                  {/* Trip Details */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">Administrative Details</h4>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="p-4 border rounded-lg">
                        <div className="text-sm font-medium mb-2">Trip ID</div>
                        <div className="text-sm text-muted-foreground">#{selectedTrip?.id}</div>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <div className="text-sm font-medium mb-2">Created By</div>
                        <div className="text-sm text-muted-foreground">John Admin</div>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <div className="text-sm font-medium mb-2">Last Modified</div>
                        <div className="text-sm text-muted-foreground">2024-03-14 09:30 AM</div>
                      </div>
                    </div>
                  </div>

                  {/* Activity Log */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">Activity Log</h4>
                    <div className="border rounded-lg divide-y">
                      <div className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="text-sm">Trip created</div>
                          <div className="text-xs text-muted-foreground">2024-03-14 09:00 AM</div>
                        </div>
                      </div>
                      <div className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="text-sm">Driver assigned</div>
                          <div className="text-xs text-muted-foreground">2024-03-14 09:15 AM</div>
                        </div>
                      </div>
                      <div className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="text-sm">Vehicle details updated</div>
                          <div className="text-xs text-muted-foreground">2024-03-14 09:30 AM</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Administrative Actions */}
                  <div className="flex items-center justify-end gap-2">
                    <Button variant="outline" size="sm">
                      <Printer className="h-4 w-4 mr-2" />
                      Print Details
                    </Button>
                    <Button variant="outline" size="sm">
                      <Copy className="h-4 w-4 mr-2" />
                      Duplicate Trip
                    </Button>
                    <Button variant="destructive" size="sm">
                      <X className="h-4 w-4 mr-2" />
                      Cancel Trip
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex items-center justify-end gap-2">
                <Button variant="outline" onClick={() => setTripDetailsOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setTripDetailsOpen(false)}>
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
        </Dialog>

        {/* Render the modal */}
        {expandedQuoteId && event && (
          <QuoteDetailsModal
            quote={event.quotes.find(q => q.id === expandedQuoteId)!}
            isOpen={!!expandedQuoteId}
          />
        )}
      </div>
    </div>
  )
}