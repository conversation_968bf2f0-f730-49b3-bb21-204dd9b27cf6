"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Badge } from "@/app/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/app/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/app/components/ui/dialog"
import { Label } from "@/app/components/ui/label"
import { Textarea } from "@/app/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import {
  Plus,
  Search,
  Users,
  UserPlus,
  Mail,
  Phone,
  Building,
  AlertCircle,
  Edit,
  Trash2,
  Download,
  Filter
} from "lucide-react"
import { toast } from "sonner"

interface Passenger {
  id: string
  first_name: string
  last_name: string
  email: string
  phone_number?: string
  passenger_type: 'guest' | 'vip' | 'staff' | 'adult' | 'child'
  company?: string
  dietary_restrictions?: string
  special_requirements?: string
  created_at: string
  updated_at: string
}

export default function PassengersPage() {
  const [passengers, setPassengers] = useState<Passenger[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState<string>("all")
  const [showNewPassengerDialog, setShowNewPassengerDialog] = useState(false)
  const [editingPassenger, setEditingPassenger] = useState<Passenger | null>(null)
  const [newPassenger, setNewPassenger] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    passenger_type: 'guest' as const,
    company: '',
    dietary_restrictions: '',
    special_requirements: ''
  })

  // Mock stats - should come from API
  const stats = {
    totalPassengers: passengers.length,
    vipPassengers: passengers.filter(p => p.passenger_type === 'vip').length,
    staffPassengers: passengers.filter(p => p.passenger_type === 'staff').length,
    guestPassengers: passengers.filter(p => p.passenger_type === 'guest').length,
    recentlyAdded: passengers.filter(p => {
      const createdDate = new Date(p.created_at)
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      return createdDate > weekAgo
    }).length
  }

  useEffect(() => {
    loadPassengers()
  }, [])

  const loadPassengers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/event-manager/passengers')
      if (response.ok) {
        const data = await response.json()
        setPassengers(data.passengers || [])
      } else {
        toast.error('Failed to load passengers')
      }
    } catch (error) {
      console.error('Failed to load passengers:', error)
      toast.error('Failed to load passengers')
    } finally {
      setLoading(false)
    }
  }

  const handleCreatePassenger = async () => {
    if (!newPassenger.first_name || !newPassenger.last_name || !newPassenger.email) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      const response = await fetch('/api/event-manager/passengers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newPassenger),
      })

      if (response.ok) {
        const data = await response.json()
        setPassengers(prev => [data.passenger, ...prev])
        
        // Reset form
        setNewPassenger({
          first_name: '',
          last_name: '',
          email: '',
          phone_number: '',
          passenger_type: 'guest',
          company: '',
          dietary_restrictions: '',
          special_requirements: ''
        })
        
        setShowNewPassengerDialog(false)
        toast.success('Passenger created successfully')
      } else {
        toast.error('Failed to create passenger')
      }
    } catch (error) {
      console.error('Failed to create passenger:', error)
      toast.error('Failed to create passenger')
    }
  }

  const filteredPassengers = passengers.filter(passenger => {
    const matchesSearch = 
      passenger?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      passenger?.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      passenger?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      passenger.company?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterType === 'all' || passenger.passenger_type === filterType
    
    return matchesSearch && matchesFilter
  })

  const getPassengerTypeColor = (type: string) => {
    switch (type) {
      case 'vip': return 'bg-purple-100 text-purple-800'
      case 'staff': return 'bg-blue-100 text-blue-800'
      case 'adult': return 'bg-green-100 text-green-800'
      case 'child': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="container p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Passenger Management</h1>
          <p className="text-sm text-muted-foreground">Manage your passenger database and profiles</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Dialog open={showNewPassengerDialog} onOpenChange={setShowNewPassengerDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Passenger
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Add New Passenger</DialogTitle>
                <DialogDescription>
                  Create a new passenger profile for your events and trips.
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="first_name">First Name *</Label>
                    <Input
                      id="first_name"
                      value={newPassenger.first_name}
                      onChange={(e) => setNewPassenger(prev => ({ ...prev, first_name: e?.target?.value }))}
                      placeholder="Enter first name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="last_name">Last Name *</Label>
                    <Input
                      id="last_name"
                      value={newPassenger.last_name}
                      onChange={(e) => setNewPassenger(prev => ({ ...prev, last_name: e?.target?.value }))}
                      placeholder="Enter last name"
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newPassenger.email}
                    onChange={(e) => setNewPassenger(prev => ({ ...prev, email: e?.target?.value }))}
                    placeholder="Enter email address"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone_number">Phone Number</Label>
                    <Input
                      id="phone_number"
                      value={newPassenger.phone_number}
                      onChange={(e) => setNewPassenger(prev => ({ ...prev, phone_number: e?.target?.value }))}
                      placeholder="Enter phone number"
                    />
                  </div>
                  <div>
                    <Label htmlFor="passenger_type">Passenger Type</Label>
                    <Select 
                      value={newPassenger.passenger_type} 
                      onValueChange={(value: 'guest' | 'vip' | 'staff' | 'adult' | 'child') => 
                        setNewPassenger(prev => ({ ...prev, passenger_type: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="guest">Guest</SelectItem>
                        <SelectItem value="vip">VIP</SelectItem>
                        <SelectItem value="staff">Staff</SelectItem>
                        <SelectItem value="adult">Adult</SelectItem>
                        <SelectItem value="child">Child</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    value={newPassenger.company}
                    onChange={(e) => setNewPassenger(prev => ({ ...prev, company: e?.target?.value }))}
                    placeholder="Enter company name"
                  />
                </div>
                
                <div>
                  <Label htmlFor="dietary_restrictions">Dietary Restrictions</Label>
                  <Textarea
                    id="dietary_restrictions"
                    value={newPassenger.dietary_restrictions}
                    onChange={(e) => setNewPassenger(prev => ({ ...prev, dietary_restrictions: e?.target?.value }))}
                    placeholder="Any dietary restrictions or allergies"
                    rows={2}
                  />
                </div>
                
                <div>
                  <Label htmlFor="special_requirements">Special Requirements</Label>
                  <Textarea
                    id="special_requirements"
                    value={newPassenger.special_requirements}
                    onChange={(e) => setNewPassenger(prev => ({ ...prev, special_requirements: e?.target?.value }))}
                    placeholder="Any special requirements or accessibility needs"
                    rows={2}
                  />
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowNewPassengerDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreatePassenger}>
                  Create Passenger
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{stats.totalPassengers}</div>
              <div className="text-xs text-muted-foreground">Total Passengers</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <UserPlus className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{stats.vipPassengers}</div>
              <div className="text-xs text-muted-foreground">VIP Passengers</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{stats.staffPassengers}</div>
              <div className="text-xs text-muted-foreground">Staff</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Users className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{stats.guestPassengers}</div>
              <div className="text-xs text-muted-foreground">Guests</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Plus className="h-4 w-4 text-yellow-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{stats.recentlyAdded}</div>
              <div className="text-xs text-muted-foreground">Added This Week</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search passengers by name, email, or company..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e?.target?.value)}
            className="pl-9"
          />
        </div>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-[180px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="vip">VIP</SelectItem>
            <SelectItem value="staff">Staff</SelectItem>
            <SelectItem value="guest">Guest</SelectItem>
            <SelectItem value="adult">Adult</SelectItem>
            <SelectItem value="child">Child</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Passengers List */}
      <Card>
        <CardHeader>
          <CardTitle>Passengers ({filteredPassengers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8 text-gray-500">Loading passengers...</div>
          ) : filteredPassengers.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || filterType !== 'all' ? 'No passengers found matching your criteria.' : 'No passengers found. Create your first passenger to get started.'}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredPassengers.map((passenger) => (
                <div
                  key={passenger.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {passenger.first_name} {passenger.last_name}
                        </span>
                        <Badge className={getPassengerTypeColor(passenger.passenger_type)}>
                          {passenger?.passenger_type?.toUpperCase()}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 flex items-center gap-4">
                        <span className="flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {passenger.email}
                        </span>
                        {passenger.phone_number && (
                          <span className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {passenger.phone_number}
                          </span>
                        )}
                        {passenger.company && (
                          <span className="flex items-center gap-1">
                            <Building className="h-3 w-3" />
                            {passenger.company}
                          </span>
                        )}
                      </div>
                      {(passenger.dietary_restrictions || passenger.special_requirements) && (
                        <div className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {passenger.dietary_restrictions && `Dietary: ${passenger.dietary_restrictions}`}
                          {passenger.dietary_restrictions && passenger.special_requirements && ' • '}
                          {passenger.special_requirements && `Special: ${passenger.special_requirements}`}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
