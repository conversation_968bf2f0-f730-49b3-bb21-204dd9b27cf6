/**
 * TNC Customer Branding Inheritance System
 * Handles branding inheritance from parent TNC to customer organizations
 */

interface BrandingConfig {
  logo_url?: string;
  primary_color?: string;
  secondary_color?: string;
  brand_name?: string;
  custom_css?: string;
  favicon_url?: string;
}

interface Organization {
  id: string;
  name: string;
  account_type: string;
  parent_tnc_id?: string;
  branding?: BrandingConfig;
}

/**
 * Get effective branding for a TNC customer organization
 * Implements the branding inheritance logic from migration 111
 */
export async function getTNCCustomerBranding(
  customerId: string,
  supabaseClient: any
): Promise<BrandingConfig> {
  try {
    // Get customer organization details
    const { data: customer, error: customerError } = await supabaseClient
      .from('organizations')
      .select(`
        id,
        name,
        account_type,
        parent_tnc_id,
        branding
      `)
      .eq('id', customerId)
      .single();

    if (customerError || !customer) {
      throw new Error('Customer organization not found');
    }

    // If not a TNC customer, return own branding
    if (customer.account_type !== 'tnc_customer' || !customer.parent_tnc_id) {
      return customer.branding || {};
    }

    // Get parent TNC branding
    const { data: parentTNC, error: tncError } = await supabaseClient
      .from('organizations')
      .select(`
        id,
        name,
        branding
      `)
      .eq('id', customer.parent_tnc_id)
      .single();

    if (tncError || !parentTNC) {
      console.warn('Parent TNC not found, using customer branding');
      return customer.branding || {};
    }

    // Build effective branding with inheritance
    const effectiveBranding: BrandingConfig = {
      logo_url: customer.branding?.logo_url || parentTNC.branding?.logo_url,
      primary_color: customer.branding?.primary_color || parentTNC.branding?.primary_color || '#1f2937',
      secondary_color: customer.branding?.secondary_color || parentTNC.branding?.secondary_color || '#3b82f6',
      brand_name: customer.branding?.brand_name || parentTNC.branding?.brand_name || parentTNC.name,
      custom_css: customer.branding?.custom_css || parentTNC.branding?.custom_css,
      favicon_url: customer.branding?.favicon_url || parentTNC.branding?.favicon_url
    };

    return effectiveBranding;

  } catch (error) {
    console.error('Error getting TNC customer branding:', error);
    // Fallback to default branding
    return {
      primary_color: '#1f2937',
      secondary_color: '#3b82f6',
      brand_name: 'TransFlow'
    };
  }
}

/**
 * Apply TNC customer branding to the current page
 */
export function applyTNCCustomerBranding(branding: BrandingConfig): void {
  try {
    // Apply CSS custom properties
    const root = document.documentElement;
    
    if (branding.primary_color) {
      root.style.setProperty('--tnc-primary', branding.primary_color);
      root.style.setProperty('--primary', branding.primary_color);
    }
    
    if (branding.secondary_color) {
      root.style.setProperty('--tnc-secondary', branding.secondary_color);
      root.style.setProperty('--secondary', branding.secondary_color);
    }

    // Apply custom CSS if available
    if (branding.custom_css) {
      const existingStyle = document.getElementById('tnc-custom-styles');
      if (existingStyle) {
        existingStyle.remove();
      }
      
      const style = document.createElement('style');
      style.id = 'tnc-custom-styles';
      style.textContent = branding.custom_css;
      document.head.appendChild(style);
    }

  } catch (error) {
    console.warn('Failed to apply TNC customer branding:', error);
  }
}