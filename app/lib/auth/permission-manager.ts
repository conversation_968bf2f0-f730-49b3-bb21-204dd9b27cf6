/**
 * Permission Manager - Centralized permission checking system
 * Implements the complete SUPER_ADMIN control architecture
 */

import { getSupabaseClient } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';
import { UserRole } from './roles';

export interface UserPermissions {
  userId: string;
  organizationId: string;
  userRole: UserRole;
  isSuperAdmin: boolean;
  organizationType: 'shared' | 'segregated' | 'isolated';
  subscriptionPlan: 'free_trial' | 'starter' | 'professional' | 'enterprise';
  permissionTemplate: 'basic_client' | 'premium_client' | 'tnc_enterprise';
  granularPermissions: string[];
  featureFlags: Record<string, boolean>;
  organizationSettings: Record<string, any>;
  whiteLabel: {
    hasWhiteLabeling: boolean;
    hasCustomDomain: boolean;
    hasCustomBranding: boolean;
  };
}

export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
  requiredPermission?: string;
  requiredSubscription?: string;
  requiredFeatureFlag?: string;
}

class PermissionManager {
  private cache = new Map<string, { data: UserPermissions; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get complete user permissions with caching
   */
  async getUserPermissions(userId: string, organizationId: string): Promise<UserPermissions> {
    const cacheKey = `${userId}:${organizationId}`;
    const now = Date.now();

    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && now - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }

    // Try to get browser client first, fallback to service role client for server-side
    let supabase = getSupabaseClient();
    
    if (!supabase) {
      // Server-side fallback: create service role client
      try {
        const { createClient } = await import('@supabase/supabase-js');
        supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!
        );
      } catch (error) {
        console.warn('Failed to create service role client:', error);
        throw new Error('Supabase client not available');
      }
    }

    try {
      // Get user profile and role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('roles')
        .eq('id', userId)
        .single();

      if (profileError) throw profileError;

      const userRoles = profile?.roles || [];
      const isSuperAdmin = userRoles.includes('SUPER_ADMIN');
      const userRole = userRoles[0] as UserRole || 'CLIENT';

      // Get organization details
      const { data: organization, error: orgError } = await supabase
        .from('organizations')
        .select(`
          id,
          organization_type,
          subscription_plan,
          permission_template,
          feature_flags,
          settings,
          has_white_labeling,
          has_custom_domain,
          has_custom_branding
        `)
        .eq('id', organizationId)
        .single();

      if (orgError) throw orgError;

      // Get organization-specific permissions
      const { data: permissions, error: permError } = await supabase
        .from('organization_permissions')
        .select('permission_key, permission_value')
        .eq('organization_id', organizationId)
        .eq('permission_value', true);

      if (permError) throw permError;

      const granularPermissions = permissions?.map(p => p.permission_key) || [];

      // SUPER_ADMIN gets all permissions
      const finalPermissions = isSuperAdmin
        ? ['*', ...granularPermissions]
        : granularPermissions;

      const userPermissions: UserPermissions = {
        userId,
        organizationId,
        userRole,
        isSuperAdmin,
        organizationType: organization.organization_type || 'shared',
        subscriptionPlan: organization.subscription_plan || 'free_trial',
        permissionTemplate: organization.permission_template || 'basic_client',
        granularPermissions: finalPermissions,
        featureFlags: organization.feature_flags || {},
        organizationSettings: organization.settings || {},
        whiteLabel: {
          hasWhiteLabeling: organization.has_white_labeling || false,
          hasCustomDomain: organization.has_custom_domain || false,
          hasCustomBranding: organization.has_custom_branding || false,
        }
      };

      // Cache the result
      this.cache.set(cacheKey, { data: userPermissions, timestamp: now });

      return userPermissions;
    } catch (error) {
      console.error('Error loading user permissions:', error);
      throw new Error('Failed to load user permissions');
    }
  }

  /**
   * Check if user has specific granular permission
   */
  async hasPermission(
    userId: string,
    organizationId: string,
    permission: string
  ): Promise<PermissionCheckResult> {
    try {
      const userPermissions = await this.getUserPermissions(userId, organizationId);

      // SUPER_ADMIN has all permissions
      if (userPermissions.isSuperAdmin) {
        return { allowed: true, reason: 'SUPER_ADMIN override' };
      }

      // Check granular permissions
      if (userPermissions.granularPermissions.includes(permission)) {
        return { allowed: true };
      }

      return {
        allowed: false,
        reason: 'Missing required permission',
        requiredPermission: permission
      };
    } catch (error) {
      return {
        allowed: false,
        reason: 'Permission check failed'
      };
    }
  }

  /**
   * Check if user has access to feature flag
   */
  async hasFeatureFlag(
    userId: string,
    organizationId: string,
    featureFlag: string
  ): Promise<PermissionCheckResult> {
    try {
      const userPermissions = await this.getUserPermissions(userId, organizationId);

      // SUPER_ADMIN has all features
      if (userPermissions.isSuperAdmin) {
        return { allowed: true, reason: 'SUPER_ADMIN override' };
      }

      // Check feature flag
      if (userPermissions.featureFlags[featureFlag] === true) {
        return { allowed: true };
      }

      return {
        allowed: false,
        reason: 'Feature not enabled for organization',
        requiredFeatureFlag: featureFlag
      };
    } catch (error) {
      return {
        allowed: false,
        reason: 'Feature flag check failed'
      };
    }
  }

  /**
   * Check subscription-based access
   */
  async hasSubscriptionAccess(
    userId: string,
    organizationId: string,
    requiredPlan: 'free_trial' | 'starter' | 'professional' | 'enterprise'
  ): Promise<PermissionCheckResult> {
    try {
      const userPermissions = await this.getUserPermissions(userId, organizationId);

      // SUPER_ADMIN bypasses subscription limits
      if (userPermissions.isSuperAdmin) {
        return { allowed: true, reason: 'SUPER_ADMIN override' };
      }

      const planHierarchy = {
        'free_trial': 0,
        'starter': 1,
        'professional': 2,
        'enterprise': 3
      };

      const userPlanLevel = planHierarchy[userPermissions.subscriptionPlan];
      const requiredPlanLevel = planHierarchy[requiredPlan];

      if (userPlanLevel >= requiredPlanLevel) {
        return { allowed: true };
      }

      return {
        allowed: false,
        reason: 'Insufficient subscription plan',
        requiredSubscription: requiredPlan
      };
    } catch (error) {
      return {
        allowed: false,
        reason: 'Subscription check failed'
      };
    }
  }

  /**
   * Comprehensive permission check combining all factors
   */
  async checkAccess(
    userId: string,
    organizationId: string,
    requirements: {
      permission?: string;
      featureFlag?: string;
      subscriptionPlan?: 'free_trial' | 'starter' | 'professional' | 'enterprise';
      organizationType?: 'shared' | 'segregated' | 'isolated';
    }
  ): Promise<PermissionCheckResult> {
    try {
      const userPermissions = await this.getUserPermissions(userId, organizationId);

      // SUPER_ADMIN bypasses all checks
      if (userPermissions.isSuperAdmin) {
        return { allowed: true, reason: 'SUPER_ADMIN override' };
      }

      // Check granular permission
      if (requirements.permission) {
        if (!userPermissions.granularPermissions.includes(requirements.permission)) {
          return {
            allowed: false,
            reason: 'Missing required permission',
            requiredPermission: requirements.permission
          };
        }
      }

      // Check feature flag
      if (requirements.featureFlag) {
        if (!userPermissions.featureFlags[requirements.featureFlag]) {
          return {
            allowed: false,
            reason: 'Feature not enabled',
            requiredFeatureFlag: requirements.featureFlag
          };
        }
      }

      // Check subscription plan
      if (requirements.subscriptionPlan) {
        const subscriptionCheck = await this.hasSubscriptionAccess(
          userId,
          organizationId,
          requirements.subscriptionPlan
        );
        if (!subscriptionCheck.allowed) {
          return subscriptionCheck;
        }
      }

      // Check organization type
      if (requirements.organizationType) {
        if (userPermissions.organizationType !== requirements.organizationType) {
          return {
            allowed: false,
            reason: `Feature requires ${requirements.organizationType} organization type`
          };
        }
      }

      return { allowed: true };
    } catch (error) {
      return {
        allowed: false,
        reason: 'Access check failed'
      };
    }
  }

  /**
   * Get subscription limits for organization
   */
  async getSubscriptionLimits(
    userId: string,
    organizationId: string
  ): Promise<{
    maxQuotesPerMonth: number;
    maxUsersPerOrg: number;
    hasApiAccess: boolean;
    hasAdvancedAnalytics: boolean;
    hasWhiteLabeling: boolean;
  }> {
    try {
      const userPermissions = await this.getUserPermissions(userId, organizationId);

      // SUPER_ADMIN has no limits
      if (userPermissions.isSuperAdmin) {
        return {
          maxQuotesPerMonth: Infinity,
          maxUsersPerOrg: Infinity,
          hasApiAccess: true,
          hasAdvancedAnalytics: true,
          hasWhiteLabeling: true,
        };
      }

      // Default limits by subscription plan
      const defaultLimits = {
        free_trial: {
          maxQuotesPerMonth: 5,
          maxUsersPerOrg: 3,
          hasApiAccess: false,
          hasAdvancedAnalytics: false,
          hasWhiteLabeling: false,
        },
        starter: {
          maxQuotesPerMonth: 25,
          maxUsersPerOrg: 10,
          hasApiAccess: false,
          hasAdvancedAnalytics: true,
          hasWhiteLabeling: false,
        },
        professional: {
          maxQuotesPerMonth: 100,
          maxUsersPerOrg: 25,
          hasApiAccess: true,
          hasAdvancedAnalytics: true,
          hasWhiteLabeling: false,
        },
        enterprise: {
          maxQuotesPerMonth: 1000,
          maxUsersPerOrg: 100,
          hasApiAccess: true,
          hasAdvancedAnalytics: true,
          hasWhiteLabeling: true,
        }
      };

      const baseLimits = defaultLimits[userPermissions.subscriptionPlan] || defaultLimits.free_trial;

      // Override with organization settings
      const customLimits = {
        maxQuotesPerMonth: userPermissions.organizationSettings.maxQuotesPerMonth || baseLimits.maxQuotesPerMonth,
        maxUsersPerOrg: userPermissions.organizationSettings.maxUsersPerOrg || baseLimits.maxUsersPerOrg,
        hasApiAccess: userPermissions.featureFlags.apiAccess ?? baseLimits.hasApiAccess,
        hasAdvancedAnalytics: userPermissions.featureFlags.advancedAnalytics ?? baseLimits.hasAdvancedAnalytics,
        hasWhiteLabeling: userPermissions.whiteLabel.hasWhiteLabeling || baseLimits.hasWhiteLabeling,
      };

      return customLimits;
    } catch (error) {
      console.error('Error getting subscription limits:', error);
      // Return most restrictive limits on error
      return {
        maxQuotesPerMonth: 5,
        maxUsersPerOrg: 3,
        hasApiAccess: false,
        hasAdvancedAnalytics: false,
        hasWhiteLabeling: false,
      };
    }
  }

  /**
   * Clear permission cache for user
   */
  clearCache(userId: string, organizationId?: string): void {
    if (organizationId) {
      this.cache.delete(`${userId}:${organizationId}`);
    } else {
      // Clear all cache entries for user
      for (const [key] of this.cache.entries()) {
        if (key.startsWith(`${userId}:`)) {
          this.cache.delete(key);
        }
      }
    }
  }

  /**
   * Clear all permission cache
   */
  clearAllCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const permissionManager = new PermissionManager();

// Utility functions for easy access
export const hasPermission = (userId: string, organizationId: string, permission: string) =>
  permissionManager.hasPermission(userId, organizationId, permission);

export const hasFeatureFlag = (userId: string, organizationId: string, featureFlag: string) =>
  permissionManager.hasFeatureFlag(userId, organizationId, featureFlag);

export const hasSubscriptionAccess = (userId: string, organizationId: string, requiredPlan: any) =>
  permissionManager.hasSubscriptionAccess(userId, organizationId, requiredPlan);

export const checkAccess = (userId: string, organizationId: string, requirements: any) =>
  permissionManager.checkAccess(userId, organizationId, requirements);

export const getUserPermissions = (userId: string, organizationId: string) =>
  permissionManager.getUserPermissions(userId, organizationId);

export const getSubscriptionLimits = (userId: string, organizationId: string) =>
  permissionManager.getSubscriptionLimits(userId, organizationId);