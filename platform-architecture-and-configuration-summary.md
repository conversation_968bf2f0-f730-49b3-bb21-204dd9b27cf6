# Platform Architecture & Configuration Summary

## 1. Executive Summary

The TransFlow platform is built on a sophisticated, adaptable architecture designed to serve a wide range of clients in the ground transportation industry. Its core strength lies in a flexible, multi-tenant foundation that allows for precise configuration to meet diverse business needs, from small independent operators to large, multi-location enterprise networks.

This document summarizes the key architectural principles and the granular configuration options available to tailor the platform for any client scenario.

---

## 2. Core Architecture: Four-Tier & Multi-Tenant

The platform is designed around a **four-tier account structure**, which enables complex B2B2C relationships and provides a clear hierarchy for management and data isolation.

1.  **TransFlow Super Admin**: The platform owners with ultimate control over the entire system, including tenant creation and system-wide settings.
2.  **TNC (Transportation Network Company) Accounts**: Network operators who use the platform as a "mini-SaaS" to manage their own portfolio of clients and affiliate vendors.
3.  **TNC Customers**: The clients managed by a TNC, operating within the TNC's branded environment and network rules.
4.  **Direct Clients**: Independent clients managed directly by the TransFlow Super Admin, typically utilizing the shared affiliate network.

This structure is underpinned by a robust, `organization_id`-based multi-tenancy model that ensures strict data isolation and security between all entities.

---

## 3. Granular Configuration Options

The platform's ground-breaking adaptability comes from its extensive set of configuration options. These settings allow administrators to precisely define how each client organization interacts with the platform, what features they can access, and how their data is managed.

The following table provides a comprehensive overview of the available configuration levers.

### TransFlow Client Configuration Options Table

| Configuration Category | Configuration Option | Description | Available Values / Type | Example Use Case |
| :--- | :--- | :--- | :--- | :--- |
| **Account Structure** | `account_type` | Defines the client's fundamental relationship with the platform. | `direct_client`, `tnc_account`, `tnc_customer` | A hotel chain like **Marriott Worldwide** is set up as a `tnc_account` to manage its properties. |
| | `parent_network_id` | Links a `tnc_customer` account to its parent `tnc_account`. | UUID | The **Marriott Boston** account has its `parent_network_id` set to the ID of the Marriott Worldwide TNC. |
| **Data Isolation** | `organization_type` | Controls the level of data isolation for an organization. | `shared`, `segregated`, `isolated` | A large enterprise client is set to `isolated` to ensure their data is stored in a completely separate schema for maximum security. |
| | `organization_type` (within a TNC) | Determines if a TNC's customers share data (like passenger lists) or are siloed. | `shared`, `segregated` | A TNC can set all its hotel customers to `shared` so a guest registered at one hotel is recognized at another. |
| **Feature Access** | `subscription_plan` | A high-level plan that gates access to major features like white-labeling. | `free_trial`, `professional`, `enterprise` | A client on the `enterprise` plan gains access to advanced compliance features and higher API rate limits. |
| | `permission_template` | A preset collection of granular permissions for rapid role configuration. | `basic_client`, `premium_client`, `tnc_enterprise` | A new "Fully Managed" client is assigned the `basic_client` template, disabling all financial and affiliate management views. |
| | `organization_permissions` | A granular JSONB object to enable or disable specific features (e.g., fixed-rate quotes, HIPAA compliance). | JSONB (e.g., `{"quotes.fixed_rate": true}`) | A medical transport client has the `compliance.hipaa` permission enabled to activate HIPAA-compliant data handling. |
| **Branding & UI** | `has_white_labeling` | A master switch to enable or disable all white-labeling capabilities for an account. | `boolean` | A TNC that wants to resell the platform under its own brand has `has_white_labeling` set to `true`. |
| | `has_custom_domain` | Allows an organization to use its own subdomain (e.g., `booking.mycompany.com`). | `boolean` | **Elite Corporate Travel** enables this to provide its clients with a fully branded portal URL. |
| | `organization_branding` | Stores the specific branding assets like logos, primary colors, and fonts. | Table Record | The **City Tours** organization has its primary color set to green (`#059669`) and its logo URL stored here. |
| **Network Model** | `affiliate_network_type` | For TNCs, determines if they use TransFlow's shared affiliate network or build their own private one. | `isolated`, `shared` | A TNC specializing in high-security transport chooses an `isolated` network to personally vet and manage all their own affiliates. |
| **Pricing & Rates** | `rate_cards` | A comprehensive system to define pricing models for services. | Table Records | An affiliate creates a `distance_based` rate card for airport transfers with a specific `base_rate` and `per_mile_rate`. |
| | `pricing_model` | The specific algorithm used to calculate a price within a rate card. | `flat_rate`, `distance_based`, `hourly`, etc. | A client sets up an `hourly` pricing model for a "City Tour" service type. |
| | `time_based_multipliers` | Surcharges applied to a rate card for specific times. | `peak_hour_multiplier`, `weekend_multiplier` | A company sets a `peak_hour_multiplier` of `1.5` to automatically increase prices by 50% during rush hour. |

---

## 4. Conclusion

The combination of a flexible four-tier account model and a rich set of granular configuration options makes the TransFlow platform uniquely capable of adapting to nearly any business model in the transportation industry. This architectural approach is our core competitive advantage, enabling us to serve a diverse client base and scale effectively.

