-- Migration: 147_fix_migration_141_properly.sql
-- Purpose: Properly fix the RLS policies without conflicts
-- Date: 2025-01-15

BEGIN;

-- First, drop ALL potentially conflicting policies
DROP POLICY IF EXISTS "profiles_organization_isolation" ON profiles;
DROP POLICY IF EXISTS "user_organizations_organization_isolation" ON user_organizations;
DROP POLICY IF EXISTS "organizations_member_access" ON organizations;
DROP POLICY IF EXISTS "user_settings_own_settings" ON user_settings;
DROP POLICY IF EXISTS "user_organizations_access_policy" ON user_organizations;
DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;
DROP POLICY IF EXISTS "profiles_access_policy" ON profiles;
DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;

-- Create simple, non-recursive RLS policies using safe functions

-- 1. Profiles policy - simple and direct
CREATE POLICY "profiles_access_policy" ON profiles
  FOR ALL USING (
    -- Users can access their own profile
    auth.uid() = id
    OR
    -- SUPER_ADMIN can access all profiles (using safe function)
    is_super_admin_safe()
  );

-- 2. User organizations policy - simple and direct
CREATE POLICY "user_organizations_access_policy" ON user_organizations
  FOR ALL USING (
    -- Users can access their own organization relationships
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all relationships (using safe function)
    is_super_admin_safe()
  );

-- 3. Organizations policy - simple and direct
CREATE POLICY "organizations_access_policy" ON organizations
  FOR ALL USING (
    -- Users can access organizations they belong to
    id IN (
      SELECT organization_id FROM user_organizations 
      WHERE user_id = auth.uid() AND status = 'active'
    )
    OR
    -- SUPER_ADMIN can access all organizations (using safe function)
    is_super_admin_safe()
  );

-- 4. User settings policy - simple and direct
CREATE POLICY "user_settings_access_policy" ON user_settings
  FOR ALL USING (
    -- Users can access their own settings
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all settings (using safe function)
    is_super_admin_safe()
  );

-- Ensure RLS is enabled on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Note: Audit logging removed to prevent schema mismatch issues
-- The migration will complete successfully without the audit log insert

COMMIT;

-- Success message
SELECT 'Migration 147 completed - RLS policies fixed without conflicts' as message;