-- Migration: 135_fix_rls_dependencies_properly.sql
-- Purpose: Properly fix RLS policies by handling existing function dependencies
-- Date: 2025-01-14

BEGIN;

-- REVERT EMERGENCY FIX: Re-enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Drop existing problematic policies first
DROP POLICY IF EXISTS "profiles_access_policy" ON profiles;
DROP POLICY IF EXISTS "user_organizations_access_policy" ON user_organizations;
DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;

-- Use the existing is_super_admin_safe() function instead of creating a new one
-- This avoids dependency conflicts

-- PROFILES TABLE: Simple, non-recursive policy
CREATE POLICY "profiles_production_policy" ON profiles
  FOR ALL USING (
    -- Users can access their own profile
    auth.uid() = id
    OR
    -- SUPER_ADMIN can access all profiles
    is_super_admin_safe()
  );

-- USER_ORGANIZATIONS TABLE: Non-recursive policy
CREATE POLICY "user_organizations_production_policy" ON user_organizations
  FOR ALL USING (
    -- Users can access their own organization relationships
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all user-organization relationships
    is_super_admin_safe()
  );

-- ORGANIZATIONS TABLE: Break the circular dependency with direct subquery
CREATE POLICY "organizations_production_policy" ON organizations
  FOR ALL USING (
    -- SUPER_ADMIN can access all organizations
    is_super_admin_safe()
    OR
    -- Users can access organizations they belong to (direct query, no policy dependency)
    id IN (
      SELECT uo.organization_id 
      FROM user_organizations uo
      WHERE uo.user_id = auth.uid()
    )
  );

-- Add comprehensive logging
DO $$
BEGIN
  RAISE NOTICE 'PRODUCTION RLS POLICIES IMPLEMENTED (CORRECTED)';
  RAISE NOTICE '==============================================';
  RAISE NOTICE 'Re-enabled RLS on all core tables';
  RAISE NOTICE 'Created production-ready RLS policies with:';
  RAISE NOTICE '- Multi-tenant isolation maintained';
  RAISE NOTICE '- SUPER_ADMIN override capabilities';
  RAISE NOTICE '- No circular dependencies';
  RAISE NOTICE '- Uses existing is_super_admin_safe() function';
  RAISE NOTICE '';
  RAISE NOTICE 'Tables with production RLS policies:';
  RAISE NOTICE '- profiles (own profile + SUPER_ADMIN access)';
  RAISE NOTICE '- user_organizations (own relationships + SUPER_ADMIN access)';
  RAISE NOTICE '- organizations (member access + SUPER_ADMIN access)';
  RAISE NOTICE '';
  RAISE NOTICE 'Circular dependency eliminated in organizations policy';
END $$;

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- DROP POLICY "profiles_production_policy" ON profiles;
-- DROP POLICY "user_organizations_production_policy" ON user_organizations;
-- DROP POLICY "organizations_production_policy" ON organizations;