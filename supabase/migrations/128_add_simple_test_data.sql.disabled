-- Migration: 128_add_simple_test_data.sql
-- Purpose: Add simple test data for four-tier architecture testing (conflict-free)
-- Date: 2025-01-13

BEGIN;

-- 1. Add a few simple test organizations (skip if exists)
INSERT INTO organizations (
  id, name, slug, account_type, organization_type, managed_by, status, 
  subscription_plan, permission_template, feature_flags, settings, created_at
) 
SELECT 
  gen_random_uuid(),
  'Test TNC Network',
  'test-tnc',
  'tnc_account',
  'isolated',
  'transflow',
  'active',
  'enterprise',
  'tnc_enterprise',
  '{"tncCustomerManagement": true, "customPortalProvisioning": true}',
  '{"maxCustomers": 100}',
  NOW()
WHERE NOT EXISTS (
  SELECT 1 FROM organizations WHERE name = 'Test TNC Network'
);

-- 2. Add a TNC customer organization
INSERT INTO organizations (
  id, name, slug, account_type, organization_type, parent_tnc_id, managed_by, status,
  subscription_plan, permission_template, feature_flags, settings, created_at
)
SELECT 
  gen_random_uuid(),
  'Test TNC Customer',
  'test-tnc-customer',
  'tnc_customer',
  'segregated',
  tnc.id,
  'tnc',
  'active',
  'enterprise',
  'premium_client',
  '{"advancedAnalytics": true}',
  '{"inheritFromParent": true}',
  NOW()
FROM organizations tnc
WHERE tnc.name = 'Test TNC Network'
AND NOT EXISTS (
  SELECT 1 FROM organizations WHERE name = 'Test TNC Customer'
);

-- 3. Add a direct client organization
INSERT INTO organizations (
  id, name, slug, account_type, organization_type, managed_by, status,
  subscription_plan, permission_template, feature_flags, settings, created_at
) 
SELECT 
  gen_random_uuid(),
  'Test Direct Client',
  'test-direct-client',
  'direct_client',
  'shared',
  'transflow',
  'active',
  'professional',
  'premium_client',
  '{"advancedAnalytics": true}',
  '{"allowGlobalNetwork": true}',
  NOW()
WHERE NOT EXISTS (
  SELECT 1 FROM organizations WHERE name = 'Test Direct Client'
);

-- 4. Add test users (only if they don't exist)
INSERT INTO profiles (id, email, full_name, first_name, last_name, roles, created_at)
SELECT 
  gen_random_uuid(),
  '<EMAIL>',
  'Test TNC Admin',
  'Test',
  'Admin',
  ARRAY['TNC_ADMIN'::user_role],
  NOW()
WHERE NOT EXISTS (
  SELECT 1 FROM profiles WHERE email = '<EMAIL>'
);

INSERT INTO profiles (id, email, full_name, first_name, last_name, roles, created_at)
SELECT 
  gen_random_uuid(),
  '<EMAIL>',
  'Test Customer User',
  'Test',
  'Customer',
  ARRAY['CLIENT'::user_role],
  NOW()
WHERE NOT EXISTS (
  SELECT 1 FROM profiles WHERE email = '<EMAIL>'
);

INSERT INTO profiles (id, email, full_name, first_name, last_name, roles, created_at)
SELECT 
  gen_random_uuid(),
  '<EMAIL>',
  'Test Direct Client',
  'Test',
  'Direct',
  ARRAY['CLIENT'::user_role],
  NOW()
WHERE NOT EXISTS (
  SELECT 1 FROM profiles WHERE email = '<EMAIL>'
);

-- 5. Create user-organization associations
WITH user_org_mapping AS (
  SELECT 
    p.id as user_id,
    o.id as org_id,
    p.roles[1] as user_role,
    CASE 
      WHEN p.roles[1] = 'TNC_ADMIN' THEN ARRAY['tnc.manage_customers', 'manage_users']
      WHEN p.roles[1] = 'CLIENT' THEN ARRAY['quotes.create', 'events.create']
      ELSE ARRAY['quotes.create']
    END as permissions
  FROM profiles p
  JOIN organizations o ON (
    (p.email = '<EMAIL>' AND o.name = 'Test TNC Network') OR
    (p.email = '<EMAIL>' AND o.name = 'Test TNC Customer') OR
    (p.email = '<EMAIL>' AND o.name = 'Test Direct Client')
  )
  WHERE p.email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
)

INSERT INTO user_organizations (user_id, organization_id, role, permissions, created_at)
SELECT user_id, org_id, user_role, permissions, NOW()
FROM user_org_mapping
WHERE NOT EXISTS (
  SELECT 1 FROM user_organizations uo 
  WHERE uo.user_id = user_org_mapping.user_id 
  AND uo.organization_id = user_org_mapping.org_id
);

-- 6. Add a few test quotes
WITH test_orgs AS (
  SELECT id, name FROM organizations 
  WHERE name IN ('Test TNC Customer', 'Test Direct Client')
)

INSERT INTO quotes (
  id, organization_id, pickup_location, dropoff_location, pickup_datetime,
  passenger_count, status, contact_info, created_at
)
SELECT 
  gen_random_uuid(),
  to.id,
  'Airport',
  'Downtown Hotel',
  NOW() + INTERVAL '1 day',
  2,
  'pending',
  jsonb_build_object(
    'name', 'Test Contact',
    'phone', '******-0123',
    'email', '<EMAIL>'
  ),
  NOW()
FROM test_orgs to
WHERE NOT EXISTS (
  SELECT 1 FROM quotes WHERE organization_id = to.id AND pickup_location = 'Airport'
);

-- 7. Add audit log entry
INSERT INTO audit_logs (action, details, created_at)
VALUES (
  'simple_test_data_added',
  jsonb_build_object(
    'migration', '128_add_simple_test_data',
    'purpose', 'Add conflict-free test data for four-tier architecture testing'
  ),
  NOW()
)
WHERE NOT EXISTS (
  SELECT 1 FROM audit_logs WHERE action = 'simple_test_data_added'
);

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- DELETE FROM audit_logs WHERE action = 'simple_test_data_added';
-- DELETE FROM quotes WHERE pickup_location = 'Airport' AND dropoff_location = 'Downtown Hotel';
-- DELETE FROM user_organizations WHERE user_id IN (SELECT id FROM profiles WHERE email LIKE '%@test.com');
-- DELETE FROM profiles WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');
-- DELETE FROM organizations WHERE name IN ('Test TNC Network', 'Test TNC Customer', 'Test Direct Client');