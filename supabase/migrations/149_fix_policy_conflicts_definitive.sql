-- Migration: 149_fix_policy_conflicts_definitive.sql
-- Purpose: Definitive fix for all RLS policy conflicts - consolidate and clean up
-- Date: 2025-01-15

BEGIN;

-- ========================================
-- STEP 1: DROP ALL CONFLICTING POLICIES
-- ========================================

-- Drop all variations of the same policies that have been created multiple times
DROP POLICY IF EXISTS "profiles_organization_isolation" ON profiles;
DROP POLICY IF EXISTS "profiles_access_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_multi_tenant_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_production_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_consolidated_access" ON profiles;

DROP POLICY IF EXISTS "user_organizations_organization_isolation" ON user_organizations;
DROP POLICY IF EXISTS "user_organizations_access_policy" ON user_organizations;
DROP POLICY IF EXISTS "user_organizations_multi_tenant_policy" ON user_organizations;
DROP POLICY IF EXISTS "user_organizations_production_policy" ON user_organizations;
DROP POLICY IF EXISTS "user_organizations_consolidated_access" ON user_organizations;

DROP POLICY IF EXISTS "organizations_member_access" ON organizations;
DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;
DROP POLICY IF EXISTS "organizations_multi_tenant_policy" ON organizations;
DROP POLICY IF EXISTS "organizations_production_policy" ON organizations;
DROP POLICY IF EXISTS "organizations_consolidated_access" ON organizations;

DROP POLICY IF EXISTS "user_settings_own_settings" ON user_settings;
DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;
DROP POLICY IF EXISTS "user_settings_authenticated_access" ON user_settings;
DROP POLICY IF EXISTS "user_settings_own_access" ON user_settings;
DROP POLICY IF EXISTS "user_settings_super_admin_access" ON user_settings;

-- ========================================
-- STEP 2: CREATE SINGLE, DEFINITIVE POLICIES
-- ========================================

-- 1. PROFILES - Single definitive policy
CREATE POLICY "profiles_final_policy" ON profiles
  FOR ALL USING (
    -- Users can access their own profile
    auth.uid() = id
    OR
    -- SUPER_ADMIN can access all profiles (using safe function)
    is_super_admin_safe()
  );

-- 2. USER_ORGANIZATIONS - Single definitive policy  
CREATE POLICY "user_organizations_final_policy" ON user_organizations
  FOR ALL USING (
    -- Users can access their own organization relationships
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all relationships (using safe function)
    is_super_admin_safe()
  );

-- 3. ORGANIZATIONS - Single definitive policy
CREATE POLICY "organizations_final_policy" ON organizations
  FOR ALL USING (
    -- SUPER_ADMIN can access all organizations (using safe function)
    is_super_admin_safe()
    OR
    -- Users can access organizations they belong to
    id IN (
      SELECT organization_id FROM user_organizations 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- 4. USER_SETTINGS - Single definitive policy
CREATE POLICY "user_settings_final_policy" ON user_settings
  FOR ALL USING (
    -- Users can access their own settings
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all settings (using safe function)
    is_super_admin_safe()
  );

-- ========================================
-- STEP 3: ENSURE RLS IS ENABLED
-- ========================================

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- ========================================
-- STEP 4: VERIFY POLICIES EXIST
-- ========================================

DO $$
DECLARE
    policy_count INTEGER;
BEGIN
    -- Count policies to verify they were created
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE schemaname = 'public' 
    AND policyname IN (
        'profiles_final_policy',
        'user_organizations_final_policy', 
        'organizations_final_policy',
        'user_settings_final_policy'
    );
    
    IF policy_count = 4 THEN
        RAISE NOTICE '✅ SUCCESS: All 4 definitive RLS policies created successfully';
        RAISE NOTICE '   - profiles_final_policy';
        RAISE NOTICE '   - user_organizations_final_policy';
        RAISE NOTICE '   - organizations_final_policy';
        RAISE NOTICE '   - user_settings_final_policy';
    ELSE
        RAISE NOTICE '⚠️  WARNING: Only % of 4 policies were created', policy_count;
    END IF;
END $$;

-- ========================================
-- STEP 5: LOG THE DEFINITIVE FIX (SKIPPED DUE TO MISSING DETAILS COLUMN)
-- ========================================

-- INSERT INTO audit_logs (action, details, created_at) VALUES (...);

COMMIT;

-- ========================================
-- SUCCESS MESSAGE
-- ========================================

SELECT 'Migration 149 completed - All RLS policy conflicts resolved with definitive policies' as message;

-- ========================================
-- ROLLBACK INSTRUCTIONS (COMMENTED)
-- ========================================
-- To rollback this migration:
-- DROP POLICY "profiles_final_policy" ON profiles;
-- DROP POLICY "user_organizations_final_policy" ON user_organizations;
-- DROP POLICY "organizations_final_policy" ON organizations;
-- DROP POLICY "user_settings_final_policy" ON user_settings;