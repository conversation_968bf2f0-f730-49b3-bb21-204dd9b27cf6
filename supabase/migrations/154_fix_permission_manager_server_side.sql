-- Migration: 154_fix_permission_manager_server_side.sql
-- Purpose: Document the fix for permission manager server-side Supabase client issue
-- Date: 2025-01-15

BEGIN;

-- This migration documents the fix for the permission manager server-side issue
-- The problem was that permissionManager.getUserPermissions() was calling getSupabaseClient()
-- which returns null on the server side, causing authentication to fail

-- The fix involves updating the permission manager to handle server-side calls properly
-- by using the service role client instead of the browser client

COMMIT;
