-- Migration: 144_create_missing_frontend_functions.sql
-- Purpose: Create the missing get_user_organizations function that the frontend needs
-- Date: 2024-01-15

-- Create the get_user_organizations function that returns organizations for a user
CREATE OR REPLACE FUNCTION get_user_organizations(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    name VARCHA<PERSON>(255),
    slug VARCHAR(100),
    organization_type TEXT,
    subscription_plan TEXT,
    created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    is_super_admin BOOLEAN;
BEGIN
    -- Check if user is SUPER_ADMIN using the safe function
    is_super_admin := is_super_admin_safe();
    
    -- If SUPER_ADMIN, return all organizations
    IF is_super_admin THEN
        RETURN QUERY
        SELECT 
            o.id,
            o.name,
            o.slug,
            o.organization_type,
            o.subscription_plan,
            o.created_at
        FROM organizations o
        ORDER BY o.name;
    ELSE
        -- For regular users, return only their organizations
        RETURN QUERY
        SELECT 
            o.id,
            o.name,
            o.slug,
            o.organization_type,
            o.subscription_plan,
            o.created_at
        FROM organizations o
        JOIN user_organizations uo ON uo.organization_id = o.id
        WHERE uo.user_id = user_uuid
        ORDER BY o.name;
    END IF;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_organizations(UUID) TO authenticated;

-- Log the function creation (skip if audit_logs doesn't have details column)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'audit_logs' AND column_name = 'details') THEN
        INSERT INTO audit_logs (action, details, created_at) 
        VALUES ('function_created', 
                jsonb_build_object('function', 'get_user_organizations', 'purpose', 'frontend_network_switcher'),
                NOW());
    END IF;
END $$;