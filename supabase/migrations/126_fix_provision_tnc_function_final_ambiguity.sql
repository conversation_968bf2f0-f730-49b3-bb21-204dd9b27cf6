-- Migration: Final fix for provision_tnc_customer_portal function parameter ambiguity
-- Purpose: Fix the remaining ambiguous parent_tnc_id reference in the validation query
-- Date: 2025-01-08

BEGIN;

-- Drop and recreate the provision_tnc_customer_portal function with ALL ambiguity issues resolved
DROP FUNCTION IF EXISTS provision_tnc_customer_portal(UUID, UUID, VARCHAR, VARCHAR);

CREATE OR REPLACE FUNCTION provision_tnc_customer_portal(
    p_customer_org_id UUID,
    p_parent_tnc_id UUID,
    p_portal_subdomain VARCHAR(100) DEFAULT NULL,
    p_brand_name VARCHAR(255) DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    portal_config_id UUID;
    customer_org_name VARCHAR(255);
    generated_subdomain VARCHAR(100);
BEGIN
    -- Validate that the customer organization exists and belongs to the TNC
    -- Use explicit table alias to avoid ambiguity
    IF NOT EXISTS (
        SELECT 1 FROM organizations o
        WHERE o.id = p_customer_org_id 
        AND o.account_type = 'tnc_customer'
        AND o.parent_tnc_id = p_parent_tnc_id
    ) THEN
        RAISE EXCEPTION 'Invalid TNC customer organization or hierarchy';
    END IF;
    
    -- Get customer organization name for defaults
    SELECT o.name INTO customer_org_name 
    FROM organizations o
    WHERE o.id = p_customer_org_id;
    
    -- Generate subdomain if not provided
    generated_subdomain := COALESCE(
        p_portal_subdomain, 
        LOWER(REGEXP_REPLACE(customer_org_name, '[^a-zA-Z0-9]', '-', 'g'))
    );
    
    -- Generate unique portal config ID
    portal_config_id := gen_random_uuid();
    
    -- Insert portal configuration with correct column names
    INSERT INTO tnc_customer_portal_configs (
        id,
        tnc_customer_org_id,
        parent_tnc_org_id,  -- Correct column name
        portal_subdomain,
        brand_name,
        status,
        created_at
    ) VALUES (
        portal_config_id,
        p_customer_org_id,
        p_parent_tnc_id,
        generated_subdomain,
        COALESCE(p_brand_name, customer_org_name),
        'active',
        NOW()
    );
    
    -- Copy permissions from parent TNC
    PERFORM inherit_permissions_from_parent_tnc(p_customer_org_id, p_parent_tnc_id);
    
    RETURN portal_config_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION provision_tnc_customer_portal(UUID, UUID, VARCHAR, VARCHAR) TO authenticated;

COMMIT;