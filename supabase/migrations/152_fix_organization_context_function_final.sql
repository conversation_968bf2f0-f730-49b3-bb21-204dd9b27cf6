-- Migration: 152_fix_organization_context_function_final.sql
-- Purpose: Final fix for the get_user_current_organization_context function to properly extract UUID from JSON string
-- Date: 2025-01-15

BEGIN;

-- Drop the existing function
DROP FUNCTION IF EXISTS get_user_current_organization_context(UUID);

-- Recreate the function with the correct JSON string handling
CREATE OR REPLACE FUNCTION get_user_current_organization_context(user_uuid UUID) 
RETURNS TABLE(
    current_organization_id UUID, 
    current_organization_name TEXT, 
    current_organization_type TEXT, 
    available_organizations_count INTEGER, 
    can_switch_organizations BOOLEAN
) AS $$
DECLARE
    is_super_admin BOOLEAN := FALSE;
    current_org_id UUID;
    current_org_name TEXT;
    current_org_type TEXT;
    org_count INTEGER;
BEGIN
    -- Check if user is SUPER_ADMIN
    SELECT 'SUPER_ADMIN' = ANY(p.roles) INTO is_super_admin
    FROM profiles p
    WHERE p.id = user_uuid;
    
    -- Get current organization from user settings or default to shared network
    -- The setting_value is stored as a JSON string, so we need to extract it properly
    SELECT COALESCE(
        (us.setting_value#>>'{}')::UUID,
        '11111111-1111-1111-1111-111111111111'::UUID
    ) INTO current_org_id
    FROM user_settings us
    WHERE us.user_id = user_uuid
        AND us.setting_name = 'app.current_organization_id'
    LIMIT 1;
    
    -- Get current organization details
    SELECT name::TEXT, organization_type::TEXT 
    INTO current_org_name, current_org_type
    FROM organizations
    WHERE id = current_org_id;
    
    -- Count available organizations
    SELECT COUNT(*)::INTEGER INTO org_count
    FROM get_user_accessible_organizations(user_uuid);
    
    RETURN QUERY SELECT
        current_org_id,
        COALESCE(current_org_name, 'Unknown Organization')::TEXT,
        COALESCE(current_org_type, 'shared')::TEXT,
        org_count,
        (is_super_admin OR org_count > 1);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Test the function to ensure it works correctly
DO $$
DECLARE
    test_result RECORD;
BEGIN
    -- Test with a SUPER_ADMIN user
    SELECT * INTO test_result
    FROM get_user_current_organization_context('d0c88ab6-28e2-4f59-a7ac-f89b8070d279');
    
    RAISE NOTICE 'Function test result: org_id=%, org_name=%, org_type=%, count=%, can_switch=%',
        test_result.current_organization_id,
        test_result.current_organization_name,
        test_result.current_organization_type,
        test_result.available_organizations_count,
        test_result.can_switch_organizations;
        
    IF test_result.current_organization_id IS NOT NULL THEN
        RAISE NOTICE '✅ SUCCESS: Function is working correctly';
    ELSE
        RAISE NOTICE '⚠️  WARNING: Function may not be working as expected';
    END IF;
END $$;

COMMIT;

-- ========================================
-- SUCCESS MESSAGE
-- ========================================

SELECT 'Migration 152 completed - Organization context function finally fixed' as message;
