-- Migration: 137_fix_network_switcher_simple.sql
-- Purpose: Simple fix for network switcher and user_settings 409 conflicts
-- Date: 2025-01-14

BEGIN;

-- Fix user_settings 409 conflicts by handling duplicates properly
-- Remove any duplicate user_settings entries (keeping the oldest)
WITH duplicates AS (
  SELECT user_id, setting_name, MIN(created_at) as keep_created_at
  FROM user_settings 
  GROUP BY user_id, setting_name
  HAVING COUNT(*) > 1
)
DELETE FROM user_settings us
WHERE EXISTS (
  SELECT 1 FROM duplicates d 
  WHERE d.user_id = us.user_id 
  AND d.setting_name = us.setting_name 
  AND us.created_at > d.keep_created_at
);

-- Add unique constraint to prevent future duplicates
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'user_settings_user_setting_unique'
  ) THEN
    ALTER TABLE user_settings 
    ADD CONSTRAINT user_settings_user_setting_unique 
    UNIQUE (user_id, setting_name);
  END IF;
END $$;

-- Ensure the SUPER_ADMIN user has proper current organization setting
DO $$
DECLARE
    super_admin_user_id UUID := '7165a1a3-bbf2-40dd-a84f-8c0902abc82f';
    current_org_setting TEXT;
    default_org_id UUID;
BEGIN
    -- Check if user has current_organization_id setting
    SELECT setting_value INTO current_org_setting
    FROM user_settings 
    WHERE user_id = super_admin_user_id 
    AND setting_name = 'app.current_organization_id';
    
    IF current_org_setting IS NULL THEN
        -- Get the first organization this user has access to
        SELECT organization_id INTO default_org_id
        FROM user_organizations 
        WHERE user_id = super_admin_user_id 
        ORDER BY created_at ASC 
        LIMIT 1;
        
        IF default_org_id IS NOT NULL THEN
            -- Insert the setting
            INSERT INTO user_settings (user_id, setting_name, setting_value)
            VALUES (super_admin_user_id, 'app.current_organization_id', to_json(default_org_id::text))
            ON CONFLICT (user_id, setting_name) DO UPDATE 
            SET setting_value = EXCLUDED.setting_value;
            
            RAISE NOTICE 'Set default organization for SUPER_ADMIN: %', default_org_id;
        END IF;
    ELSE
        RAISE NOTICE 'SUPER_ADMIN already has current organization: %', current_org_setting;
    END IF;
END $$;

-- Verify network switcher data availability
DO $$
DECLARE
    super_admin_user_id UUID := '7165a1a3-bbf2-40dd-a84f-8c0902abc82f';
    total_orgs INTEGER;
    user_orgs INTEGER;
    current_org TEXT;
BEGIN
    -- Count total organizations
    SELECT COUNT(*) INTO total_orgs FROM organizations;
    
    -- Count user's organization access
    SELECT COUNT(*) INTO user_orgs 
    FROM user_organizations 
    WHERE user_id = super_admin_user_id;
    
    -- Get current organization name
    SELECT o.name INTO current_org
    FROM organizations o
    JOIN user_settings us ON us.setting_value::text = '"' || o.id::text || '"'
    WHERE us.user_id = super_admin_user_id 
    AND us.setting_name = 'app.current_organization_id';
    
    RAISE NOTICE 'NETWORK SWITCHER STATUS CHECK:';
    RAISE NOTICE '==============================';
    RAISE NOTICE 'Total organizations: %', total_orgs;
    RAISE NOTICE 'User has access to: %', user_orgs;
    RAISE NOTICE 'Current organization: %', COALESCE(current_org, 'NOT SET');
    RAISE NOTICE '';
    
    IF user_orgs >= total_orgs THEN
        RAISE NOTICE '✅ SUPER_ADMIN has full organization access';
        RAISE NOTICE '✅ Network switcher should show all networks';
    ELSE
        RAISE NOTICE '⚠️  SUPER_ADMIN has limited organization access';
        RAISE NOTICE '⚠️  Network switcher may show limited networks';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'EXPECTED RESULTS:';
    RAISE NOTICE '- No more 409 conflicts on user_settings updates';
    RAISE NOTICE '- Network switcher should populate with organizations';
    RAISE NOTICE '- SUPER_ADMIN should see all available networks';
    RAISE NOTICE '';
    RAISE NOTICE 'NEXT STEPS:';
    RAISE NOTICE '1. Hard refresh browser (Cmd+Shift+R)';
    RAISE NOTICE '2. Check network switcher dropdown';
    RAISE NOTICE '3. Verify no console errors';
END $$;

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- ALTER TABLE user_settings DROP CONSTRAINT IF EXISTS user_settings_user_setting_unique;