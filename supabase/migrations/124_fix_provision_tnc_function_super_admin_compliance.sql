-- Migration: Fix provision_tnc_customer_portal function for complete SUPER_ADMIN compliance
-- Purpose: Add missing SUPER_ADMIN override logic, permission validation, and audit logging
-- Date: 2025-01-08

BEGIN;

-- Drop and recreate the provision_tnc_customer_portal function with full SUPER_ADMIN compliance
DROP FUNCTION IF EXISTS provision_tnc_customer_portal(UUID, UUID, VARCHAR, VARCHAR);

CREATE OR REPLACE FUNCTION provision_tnc_customer_portal(
    p_customer_org_id UUID,
    p_parent_tnc_id UUID,
    p_portal_subdomain VARCHAR(100) DEFAULT NULL,
    p_brand_name VARCHAR(255) DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    portal_config_id UUID;
    customer_org_name VARCHAR(255);
    generated_subdomain VARCHAR(100);
    is_super_admin BOOLEAN;
BEGIN
    -- Check if user is SUPER_ADMIN
    is_super_admin := is_super_admin_safe();
    
    -- Validate subscription plan requirements (with SUPER_ADMIN override)
    IF NOT is_super_admin THEN
        IF NOT EXISTS (
            SELECT 1 FROM organizations
            WHERE id = p_parent_tnc_id
            AND subscription_plan IN ('enterprise', 'tnc_enterprise')
        ) THEN
            RAISE EXCEPTION 'SUBSCRIPTION_REQUIRED: Enterprise subscription required for customer portal provisioning';
        END IF;
    END IF;
    
    -- Validate permission template requirements (with SUPER_ADMIN override)
    IF NOT is_super_admin THEN
        IF NOT EXISTS (
            SELECT 1 FROM organizations
            WHERE id = p_parent_tnc_id
            AND permission_template IN ('premium_client', 'tnc_enterprise')
        ) THEN
            RAISE EXCEPTION 'PERMISSION_DENIED: Parent TNC permission template insufficient for customer portal provisioning';
        END IF;
    END IF;
    
    -- Validate feature flags (with SUPER_ADMIN override)
    IF NOT is_super_admin THEN
        IF NOT EXISTS (
            SELECT 1 FROM organizations
            WHERE id = p_parent_tnc_id
            AND (feature_flags->>'customPortalProvisioning' = 'true' OR feature_flags->>'tnc_customer_management' = 'true')
        ) THEN
            RAISE EXCEPTION 'FEATURE_DISABLED: Custom portal provisioning feature not enabled for parent TNC';
        END IF;
    END IF;
    
    -- Validate granular permissions (with SUPER_ADMIN override)
    IF NOT is_super_admin THEN
        IF NOT EXISTS (
            SELECT 1 FROM organization_permissions
            WHERE organization_id = p_parent_tnc_id
            AND permission_key IN ('tnc.manage_customers', 'manage_tnc_customers', 'provision_customer_portals')
            AND permission_value = true
        ) THEN
            RAISE EXCEPTION 'PERMISSION_DENIED: Parent TNC lacks customer management permissions';
        END IF;
    END IF;
    
    -- Validate TNC hierarchy and account types (with SUPER_ADMIN override)
    IF NOT is_super_admin THEN
        -- Validate parent TNC account type
        IF NOT EXISTS (
            SELECT 1 FROM organizations
            WHERE id = p_parent_tnc_id
            AND account_type IN ('tnc_admin', 'tnc_account')
        ) THEN
            RAISE EXCEPTION 'HIERARCHY_INVALID: Parent organization is not a valid TNC account';
        END IF;
        
        -- Validate customer organization hierarchy
        IF NOT EXISTS (
            SELECT 1 FROM organizations o
            WHERE o.id = p_customer_org_id 
            AND o.account_type = 'tnc_customer'
            AND o.parent_tnc_id = p_parent_tnc_id
        ) THEN
            RAISE EXCEPTION 'HIERARCHY_INVALID: Invalid TNC customer organization or hierarchy';
        END IF;
    ELSE
        -- SUPER_ADMIN can provision portals even with invalid hierarchy, but log it
        IF NOT EXISTS (
            SELECT 1 FROM organizations o
            WHERE o.id = p_customer_org_id 
            AND o.account_type = 'tnc_customer'
            AND o.parent_tnc_id = p_parent_tnc_id
        ) THEN
            -- Log SUPER_ADMIN override action
            INSERT INTO audit_logs (action, user_id, organization_id, details, created_at)
            VALUES (
                'super_admin_portal_provision_override',
                auth.uid(),
                p_parent_tnc_id,
                jsonb_build_object(
                    'function_name', 'provision_tnc_customer_portal',
                    'customer_org_id', p_customer_org_id,
                    'parent_tnc_id', p_parent_tnc_id,
                    'override_reason', 'invalid_hierarchy_bypassed',
                    'warning', 'SUPER_ADMIN bypassed hierarchy validation'
                ),
                NOW()
            );
        END IF;
    END IF;
    
    -- Get customer organization name for defaults
    SELECT o.name INTO customer_org_name 
    FROM organizations o
    WHERE o.id = p_customer_org_id;
    
    -- Generate subdomain if not provided
    generated_subdomain := COALESCE(
        p_portal_subdomain, 
        LOWER(REGEXP_REPLACE(customer_org_name, '[^a-zA-Z0-9]', '-', 'g'))
    );
    
    -- Generate unique portal config ID
    portal_config_id := gen_random_uuid();
    
    -- Insert portal configuration (create table if it doesn't exist)
    BEGIN
        INSERT INTO tnc_customer_portal_configs (
            id,
            tnc_customer_org_id,
            parent_tnc_org_id,
            portal_subdomain,
            brand_name,
            status,
            created_at
        ) VALUES (
            portal_config_id,
            p_customer_org_id,
            p_parent_tnc_id,
            generated_subdomain,
            COALESCE(p_brand_name, customer_org_name),
            'active',
            NOW()
        );
    EXCEPTION
        WHEN undefined_table THEN
            -- Create the table if it doesn't exist
            CREATE TABLE IF NOT EXISTS tnc_customer_portal_configs (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                tnc_customer_org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
                parent_tnc_org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
                portal_subdomain VARCHAR(100) NOT NULL,
                brand_name VARCHAR(255),
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            -- Retry the insert
            INSERT INTO tnc_customer_portal_configs (
                id,
                tnc_customer_org_id,
                parent_tnc_org_id,
                portal_subdomain,
                brand_name,
                status,
                created_at
            ) VALUES (
                portal_config_id,
                p_customer_org_id,
                p_parent_tnc_id,
                generated_subdomain,
                COALESCE(p_brand_name, customer_org_name),
                'active',
                NOW()
            );
    END;
    
    -- Copy permissions from parent TNC
    PERFORM inherit_permissions_from_parent_tnc(p_customer_org_id, p_parent_tnc_id);
    
    -- Log SUPER_ADMIN override action if applicable
    IF is_super_admin THEN
        INSERT INTO audit_logs (action, user_id, organization_id, details, created_at)
        VALUES (
            'super_admin_portal_provision',
            auth.uid(),
            p_parent_tnc_id,
            jsonb_build_object(
                'function_name', 'provision_tnc_customer_portal',
                'customer_org_id', p_customer_org_id,
                'parent_tnc_id', p_parent_tnc_id,
                'portal_config_id', portal_config_id,
                'subdomain', generated_subdomain,
                'override_reason', 'platform_administration'
            ),
            NOW()
        );
    END IF;
    
    -- Log portal provisioning action for all users
    INSERT INTO audit_logs (action, user_id, organization_id, details, created_at)
    VALUES (
        'tnc_customer_portal_provisioned',
        auth.uid(),
        p_parent_tnc_id,
        jsonb_build_object(
            'customer_org_id', p_customer_org_id,
            'portal_config_id', portal_config_id,
            'subdomain', generated_subdomain,
            'brand_name', COALESCE(p_brand_name, customer_org_name),
            'provisioned_by_super_admin', is_super_admin
        ),
        NOW()
    );
    
    RETURN portal_config_id;
END;
$function$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION provision_tnc_customer_portal(UUID, UUID, VARCHAR, VARCHAR) TO authenticated;

-- Create audit_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    action VARCHAR(100) NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    organization_id UUID REFERENCES organizations(id),
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create organization_permissions table if it doesn't exist
CREATE TABLE IF NOT EXISTS organization_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    permission_key VARCHAR(100) NOT NULL,
    permission_value BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, permission_key)
);

COMMIT;