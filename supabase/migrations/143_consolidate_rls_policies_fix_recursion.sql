-- Migration: 143_consolidate_rls_policies_fix_recursion.sql
-- Purpose: Consolidate duplicate RLS policies and fix infinite recursion
-- Date: 2025-01-15

BEGIN;

-- Fix profiles table - consolidate 7 policies into 1 comprehensive policy
DROP POLICY IF EXISTS "profiles_authenticated_users_own_access" ON profiles;
DROP POLICY IF EXISTS "profiles_multi_tenant_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_organization_access" ON profiles;
DROP POLICY IF EXISTS "profiles_own_access" ON profiles;
DROP POLICY IF EXISTS "profiles_production_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_super_admin_access" ON profiles;
-- Keep service role policy
-- DROP POLICY IF EXISTS "profiles_service_role_full_access" ON profiles;

CREATE POLICY "profiles_consolidated_access" ON profiles
  FOR ALL USING (
    -- Users can access their own profile
    auth.uid() = id
    OR
    -- SUPER_ADMIN can access all profiles (using safe function to avoid recursion)
    is_super_admin_safe()
    OR
    -- Users can see profiles of people in their organizations
    EXISTS (
      SELECT 1 FROM user_organizations uo1
      JOIN user_organizations uo2 ON uo1.organization_id = uo2.organization_id
      WHERE uo1.user_id = auth.uid() 
      AND uo2.user_id = profiles.id
      AND uo1.status = 'active'
      AND uo2.status = 'active'
    )
  );

-- Fix user_organizations table - consolidate 6 policies into 1
DROP POLICY IF EXISTS "user_organizations_isolation" ON user_organizations;
DROP POLICY IF EXISTS "user_organizations_multi_tenant_policy" ON user_organizations;
DROP POLICY IF EXISTS "user_organizations_own_records" ON user_organizations;
DROP POLICY IF EXISTS "user_organizations_production_policy" ON user_organizations;
DROP POLICY IF EXISTS "user_organizations_super_admin_access" ON user_organizations;
DROP POLICY IF EXISTS "user_organizations_users_can_read_own" ON user_organizations;
-- Keep service role policy
-- DROP POLICY IF EXISTS "user_organizations_service_role_access" ON user_organizations;

CREATE POLICY "user_organizations_consolidated_access" ON user_organizations
  FOR ALL USING (
    -- Users can access their own organization relationships
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all relationships (using safe function)
    is_super_admin_safe()
  );

-- Fix organizations table - consolidate 7 policies into 1
DROP POLICY IF EXISTS "organizations_multi_tenant_policy" ON organizations;
DROP POLICY IF EXISTS "organizations_production_policy" ON organizations;
DROP POLICY IF EXISTS "organizations_super_admin_access" ON organizations;
DROP POLICY IF EXISTS "organizations_super_admin_can_read_all" ON organizations;
DROP POLICY IF EXISTS "organizations_user_can_read_own" ON organizations;
-- Keep these specialized policies as they don't cause recursion:
-- DROP POLICY IF EXISTS "Four_tier_organization_access" ON organizations;
-- DROP POLICY IF EXISTS "organizations_service_role_access" ON organizations;

CREATE POLICY "organizations_consolidated_access" ON organizations
  FOR ALL USING (
    -- SUPER_ADMIN can access all organizations (using safe function)
    is_super_admin_safe()
    OR
    -- Users can access organizations they belong to
    id IN (
      SELECT organization_id FROM user_organizations
      WHERE user_id = auth.uid() AND status = 'active'
    )
    OR
    -- TNC admins can access their customer organizations
    EXISTS (
      SELECT 1 FROM user_organizations uo
      WHERE uo.user_id = auth.uid()
      AND (uo.organization_id = organizations.id OR organizations.parent_tnc_id = uo.organization_id)
      AND uo.status = 'active'
      AND EXISTS (
        SELECT 1 FROM auth.users au
        WHERE au.id = auth.uid()
        AND au.raw_user_meta_data->>'role' = 'TNC_ADMIN'
      )
    )
  );

-- Fix user_settings table - consolidate 4 policies into 1
DROP POLICY IF EXISTS "user_settings_authenticated_access" ON user_settings;
DROP POLICY IF EXISTS "user_settings_own_access" ON user_settings;
DROP POLICY IF EXISTS "user_settings_super_admin_access" ON user_settings;
-- Keep service role and existing access policy
-- DROP POLICY IF EXISTS "user_settings_service_role_full_access" ON user_settings;
-- DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;

-- The user_settings_access_policy already exists and is correct, so we don't need to recreate it

-- Log the consolidation (using correct column name 'metadata' instead of 'details')
INSERT INTO audit_logs (action, metadata, created_at) 
VALUES (
  '143_consolidate_rls_policies',
  jsonb_build_object(
    'migration', '143_consolidate_rls_policies_fix_recursion',
    'issue', 'duplicate_conflicting_rls_policies_causing_recursion',
    'solution', 'consolidated_policies_using_is_super_admin_safe',
    'policies_consolidated', jsonb_build_object(
      'profiles', 'consolidated 7 policies into 1',
      'user_organizations', 'consolidated 6 policies into 1', 
      'organizations', 'consolidated 5 policies into 1',
      'user_settings', 'kept existing correct policy'
    )
  ),
  NOW()
);

COMMIT;

-- Rollback instructions (commented):
-- This migration consolidates existing policies rather than removing functionality
-- To rollback, you would need to recreate the individual policies that were consolidated
-- However, this would bring back the infinite recursion issue