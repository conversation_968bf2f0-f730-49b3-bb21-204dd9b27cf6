-- Migration: 133_disable_rls_temporarily.sql
-- Purpose: Temporarily disable <PERSON><PERSON> to break infinite recursion and allow system to function
-- Date: 2025-01-14

BEGIN;

-- EMERGENCY FIX: Temporarily disable <PERSON><PERSON> on problematic tables
-- This is a temporary measure to get the system working while we fix the recursion

-- Disable RLS on profiles table
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Disable RLS on user_organizations table  
ALTER TABLE user_organizations DISABLE ROW LEVEL SECURITY;

-- Disable RLS on organizations table
ALTER TABLE organizations DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies that might be causing recursion
DROP POLICY IF EXISTS "profiles_access_policy" ON profiles;
DROP POLICY IF EXISTS "user_organizations_access_policy" ON user_organizations;
DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;
DROP POLICY IF EXISTS "profiles_organization_isolation" ON profiles;
DROP POLICY IF EXISTS "user_organizations_organization_isolation" ON user_organizations;
DROP POLICY IF EXISTS "organizations_organization_isolation" ON organizations;

-- Add a notice that this is temporary
DO $$
BEGIN
  RAISE NOTICE 'EMERGENCY RLS DISABLE COMPLETE';
  RAISE NOTICE '================================';
  RAISE NOTICE 'RLS has been temporarily disabled on:';
  RAISE NOTICE '- profiles table';
  RAISE NOTICE '- user_organizations table';
  RAISE NOTICE '- organizations table';
  RAISE NOTICE '';
  RAISE NOTICE 'This is a TEMPORARY measure to fix infinite recursion';
  RAISE NOTICE 'The system should now work without authentication errors';
  RAISE NOTICE 'RLS will be re-enabled with proper policies in a future migration';
END $$;

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;