-- Migration: Create TNC Customer Organizations
-- Description: Creates realistic TNC customer organizations that inherit from existing TNC accounts

BEGIN;

INSERT INTO organizations (
  name, 
  account_type, 
  parent_tnc_id,
  business_type,
  subscription_plan,
  status, 
  slug,
  organization_type,
  network_level,
  permission_template,
  feature_flags,
  created_at,
  updated_at
) VALUES 
  (
    'Metro Boston Downtown Office',
    'tnc_customer',
    (SELECT id FROM organizations WHERE name = 'Metro Ride Network' AND account_type = 'tnc_account' LIMIT 1),
    'transportation_hub',
    'professional',
    'active',
    'metro-boston-downtown',
    'segregated',
    1,
    'premium_client',
    '{"white_label_enabled": true, "inherit_tnc_network": true}'::jsonb,
    NOW(),
    NOW()
  ),
  (
    'Elite Boston Corporate Branch',
    'tnc_customer',
    (SELECT id FROM organizations WHERE name = 'Elite Corporate Travel' AND account_type = 'tnc_account' LIMIT 1),
    'corporate_branch',
    'enterprise',
    'active',
    'elite-boston-corporate',
    'isolated',
    1,
    'tnc_enterprise',
    '{"white_label_enabled": true, "inherit_tnc_network": true}'::jsonb,
    NOW(),
    NOW()
  ),
  (
    'Luxury Boston Hotel Services',
    'tnc_customer',
    (SELECT id FROM organizations WHERE name = 'Luxury Concierge Transportation' AND account_type = 'tnc_account' LIMIT 1),
    'hospitality',
    'enterprise',
    'active',
    'luxury-boston-hotels',
    'isolated',
    1,
    'tnc_enterprise',
    '{"white_label_enabled": true, "inherit_tnc_network": true}'::jsonb,
    NOW(),
    NOW()
  )
ON CONFLICT (slug) DO NOTHING;

DO $$
DECLARE
  tnc_customer_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO tnc_customer_count FROM organizations WHERE account_type = 'tnc_customer';
  RAISE NOTICE 'TNC CUSTOMERS CREATED: %', tnc_customer_count;
END $$;

COMMIT;