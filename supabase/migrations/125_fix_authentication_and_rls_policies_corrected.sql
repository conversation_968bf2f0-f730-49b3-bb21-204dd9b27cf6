-- Migration: 125_fix_authentication_and_rls_policies_corrected.sql
-- Purpose: Fix authentication and RLS policies (corrected version)
-- Date: 2025-01-13

BEGIN;

-- 1. Enable RLS on profiles table (not the view)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 2. Create RLS policy for profiles table
DROP POLICY IF EXISTS "profiles_organization_isolation" ON profiles;
CREATE POLICY "profiles_organization_isolation" ON profiles
  FOR ALL USING (
    -- Users can access their own profile
    id = auth.uid()
    OR
    -- Users can access profiles in their organization
    organization_id IN (
      SELECT organization_id FROM user_organizations 
      WHERE user_id = auth.uid()
    )
    OR 
    -- SUPER_ADMIN can access all profiles
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(roles)
    )
  );

-- 3. Ensure user_organizations table has proper RLS
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "user_organizations_isolation" ON user_organizations;
CREATE POLICY "user_organizations_isolation" ON user_organizations
  FOR ALL USING (
    -- Users can access their own organization relationships
    user_id = auth.uid()
    OR
    -- SUPER_ADMIN can access all organization relationships
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(roles)
    )
  );

-- 4. Create or update authentication helper function
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_roles TEXT[];
BEGIN
    -- Get user roles from profiles table
    SELECT roles INTO user_roles
    FROM profiles
    WHERE id = auth.uid();
    
    -- Return first role if exists, otherwise return null
    IF user_roles IS NOT NULL AND array_length(user_roles, 1) > 0 THEN
        RETURN user_roles[1];
    END IF;
    
    RETURN NULL;
END;
$$;

-- 5. Create function to check if user is SUPER_ADMIN
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND 'SUPER_ADMIN' = ANY(roles)
    );
END;
$$;

-- 6. Create function to get user organizations
CREATE OR REPLACE FUNCTION get_user_organizations()
RETURNS TABLE(organization_id UUID, role TEXT, permissions JSONB)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        uo.organization_id,
        uo.role,
        uo.permissions
    FROM user_organizations uo
    WHERE uo.user_id = auth.uid();
END;
$$;

-- 7. Update organizations table RLS if needed
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;
CREATE POLICY "organizations_access_policy" ON organizations
  FOR ALL USING (
    -- Users can access organizations they belong to
    id IN (
      SELECT organization_id FROM user_organizations 
      WHERE user_id = auth.uid()
    )
    OR 
    -- SUPER_ADMIN can access all organizations
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(roles)
    )
  );

-- 8. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON profiles TO authenticated;
GRANT SELECT ON user_organizations TO authenticated;
GRANT SELECT ON organizations TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION is_super_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_organizations() TO authenticated;

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- DROP POLICY IF EXISTS "profiles_organization_isolation" ON profiles;
-- DROP POLICY IF EXISTS "user_organizations_isolation" ON user_organizations;
-- DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;
-- DROP FUNCTION IF EXISTS get_user_role();
-- DROP FUNCTION IF EXISTS is_super_admin();
-- DROP FUNCTION IF EXISTS get_user_organizations();