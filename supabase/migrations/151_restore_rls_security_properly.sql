-- Migration: 151_restore_rls_security_properly.sql
-- Purpose: Restore RLS security with proper policies that prevent infinite recursion
-- Date: 2025-01-16

BEGIN;

-- Create helper function for SUPER_ADMIN bypass (prevents recursion)
CREATE OR REPLACE FUNCTION is_super_admin_safe()
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid()
    AND auth.users.raw_user_meta_data->>'role' = 'SUPER_ADMIN'
  );
$$;

-- Create helper function for user organization access
CREATE OR REPLACE FUNCTION user_has_organization_access(org_id UUID)
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM user_organizations uo
    WHERE uo.user_id = auth.uid()
    AND uo.organization_id = org_id
    AND uo.status = 'active'
  ) OR is_super_admin_safe();
$$;

-- Re-enable RLS on core tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to prevent conflicts
DROP POLICY IF EXISTS "profiles_organization_isolation" ON profiles;
DROP POLICY IF EXISTS "organizations_user_access" ON organizations;
DROP POLICY IF EXISTS "user_organizations_self_access" ON user_organizations;
DROP POLICY IF EXISTS "user_settings_self_access" ON user_settings;

-- PROFILES: Users can access their own profile + SUPER_ADMIN can access all
CREATE POLICY "profiles_self_and_super_admin_access" ON profiles
  FOR ALL USING (
    auth.uid() = id OR is_super_admin_safe()
  );

-- ORGANIZATIONS: Users can access organizations they belong to + SUPER_ADMIN can access all
CREATE POLICY "organizations_member_and_super_admin_access" ON organizations
  FOR ALL USING (
    user_has_organization_access(id) OR is_super_admin_safe()
  );

-- USER_ORGANIZATIONS: Users can see their own relationships + SUPER_ADMIN can see all
CREATE POLICY "user_organizations_self_and_super_admin_access" ON user_organizations
  FOR ALL USING (
    auth.uid() = user_id OR is_super_admin_safe()
  );

-- USER_SETTINGS: Users can access their own settings + SUPER_ADMIN can access all
CREATE POLICY "user_settings_self_and_super_admin_access" ON user_settings
  FOR ALL USING (
    auth.uid() = user_id OR is_super_admin_safe()
  );

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION is_super_admin_safe() TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_organization_access(UUID) TO authenticated;

-- Log security restoration
INSERT INTO audit_logs (action, metadata, created_at)
VALUES (
  'rls_security_restored',
  jsonb_build_object(
    'migration', '151_restore_rls_security_properly',
    'purpose', 'restore_multi_tenant_security',
    'policies_created', 4,
    'helper_functions_created', 2
  ),
  NOW()
);

COMMIT;