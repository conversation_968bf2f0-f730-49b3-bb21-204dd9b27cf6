-- Migration: 155_fix_rls_policies_clean.sql
-- Purpose: Fix infinite recursion in RLS policies by using is_super_admin_safe() function
-- Date: 2025-01-15

BEGIN;

-- Drop all existing RLS policies that might be causing recursion
DROP POLICY IF EXISTS "profiles_simple_access" ON profiles;
DROP POLICY IF EXISTS "user_organizations_simple_access" ON user_organizations;
DROP POLICY IF EXISTS "organizations_simple_access" ON organizations;
DROP POLICY IF EXISTS "user_settings_simple_access" ON user_settings;

-- Create simple, non-recursive RLS policies using is_super_admin_safe()

-- 1. Profiles policy - simple and direct
DROP POLICY IF EXISTS "profiles_access_policy" ON profiles;
CREATE POLICY "profiles_access_policy" ON profiles
  FOR ALL USING (
    -- Users can access their own profile
    auth.uid() = id
    OR
    -- SUPER_ADMIN can access all profiles (using safe function)
    is_super_admin_safe()
  );

-- 2. User organizations policy - simple and direct
DROP POLICY IF EXISTS "user_organizations_access_policy" ON user_organizations;
CREATE POLICY "user_organizations_access_policy" ON user_organizations
  FOR ALL USING (
    -- Users can access their own organization relationships
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all organization relationships
    is_super_admin_safe()
  );

-- 3. Organizations policy - simple and direct
DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;
CREATE POLICY "organizations_access_policy" ON organizations
  FOR ALL USING (
    -- Users can access organizations they belong to
    id IN (
      SELECT organization_id FROM user_organizations
      WHERE user_id = auth.uid()
    )
    OR
    -- SUPER_ADMIN can access all organizations
    is_super_admin_safe()
  );

-- 4. User settings policy - simple and direct
DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;
CREATE POLICY "user_settings_access_policy" ON user_settings
  FOR ALL USING (
    -- Users can access their own settings
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all settings
    is_super_admin_safe()
  );

-- Log the fix (using correct column name 'metadata' instead of 'details')
INSERT INTO audit_logs (action, metadata, created_at) 
VALUES (
  '143_fix_rls_policies_clean',
  jsonb_build_object(
    'migration', '143_fix_rls_policies_clean',
    'issue', 'infinite_recursion_in_rls_policies',
    'solution', 'replaced_with_simple_policies_using_is_super_admin_safe'
  ),
  NOW()
);

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- DROP POLICY IF EXISTS "profiles_access_policy" ON profiles;
-- DROP POLICY IF EXISTS "user_organizations_access_policy" ON user_organizations;
-- DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;
-- DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;