-- Migration: 141_fix_infinite_recursion_rls_emergency.sql
-- Purpose: Emergency fix for infinite recursion in RLS policies
-- Date: 2025-08-15

BEGIN;

-- Drop all problematic RLS policies that are causing infinite recursion
DROP POLICY IF EXISTS "profiles_organization_isolation" ON profiles;
DROP POLICY IF EXISTS "user_organizations_organization_isolation" ON user_organizations;
DROP POLICY IF EXISTS "organizations_member_access" ON organizations;
DROP POLICY IF EXISTS "user_settings_own_settings" ON user_settings;
-- Also drop policies that might conflict with what we're about to create
DROP POLICY IF EXISTS "profiles_access_policy" ON profiles;
DROP POLICY IF EXISTS "user_organizations_access_policy" ON user_organizations;
DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;
DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;

-- Create simple, non-recursive RLS policies

-- 1. Profiles policy - simple and direct
CREATE POLICY "profiles_access_policy" ON profiles
  FOR ALL USING (
    -- Users can access their own profile
    auth.uid() = id
    OR
    -- SUPER_ADMIN can access all profiles (direct check, no function calls)
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(p.roles)
    )
  );

-- 2. User organizations policy - simple and direct  
CREATE POLICY "user_organizations_access_policy" ON user_organizations
  FOR ALL USING (
    -- Users can access their own organization relationships
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all relationships (direct check, no function calls)
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(p.roles)
    )
  );

-- 3. Organizations policy - simple and direct
CREATE POLICY "organizations_access_policy" ON organizations
  FOR ALL USING (
    -- Users can access organizations they belong to
    id IN (
      SELECT organization_id FROM user_organizations 
      WHERE user_id = auth.uid()
    )
    OR
    -- SUPER_ADMIN can access all organizations (direct check, no function calls)
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(p.roles)
    )
  );

-- 4. User settings policy - simple and direct
-- Drop first to avoid conflicts with previous migrations
DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;
CREATE POLICY "user_settings_access_policy" ON user_settings
  FOR ALL USING (
    -- Users can access their own settings
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all settings (direct check, no function calls)
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(p.roles)
    )
  );

-- Log the fix (only if audit_logs table exists with correct schema)
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'audit_logs' AND table_schema = 'public'
  ) AND EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'audit_logs' AND column_name = 'details' AND table_schema = 'public'
  ) THEN
    INSERT INTO audit_logs (action, details, created_at) 
    VALUES (
      'rls_infinite_recursion_emergency_fix',
      jsonb_build_object(
        'migration', '141_fix_infinite_recursion_rls_emergency',
        'issue', 'infinite_recursion_in_rls_policies',
        'solution', 'replaced_with_simple_direct_policies'
      ),
      NOW()
    );
  END IF;
END $$;

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- DROP POLICY IF EXISTS "profiles_access_policy" ON profiles;
-- DROP POLICY IF EXISTS "user_organizations_access_policy" ON user_organizations;
-- DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;
-- DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;