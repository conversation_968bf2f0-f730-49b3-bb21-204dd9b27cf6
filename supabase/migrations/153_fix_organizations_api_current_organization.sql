-- Migration: 153_fix_organizations_api_current_organization.sql
-- Purpose: Fix the organizations API to include current organization information
-- Date: 2025-01-15

BEGIN;

-- This migration doesn't change the database schema, but documents the API fix needed
-- The issue is that the frontend component expects a 'currentOrganization' field in the API response
-- but the current API only returns the organizations list without current selection context

-- The fix needs to be applied to the API route to include current organization information
-- This will resolve the "grayed out organizations" issue in the Network Switcher

COMMIT;
