-- Migration: 131_fix_network_architecture_compliance.sql
-- Purpose: Fix network architecture without foreign key constraint issues
-- Date: 2025-01-14

BEGIN;

-- 1. Create TransFlow Shared Network (the platform-wide network)
INSERT INTO affiliate_networks (
    id,
    name,
    slug,
    description,
    network_type,
    coverage_area,
    auto_approval,
    min_rating_required,
    max_affiliates,
    requires_insurance,
    requires_background_check,
    commission_rate,
    membership_fee,
    fee_frequency,
    brand_color,
    logo_url,
    is_active,
    is_public,
    created_at,
    updated_at
) VALUES (
    '********-1111-1111-1111-********1111',
    'TransFlow Shared Network',
    'transflow-shared',
    'The platform-wide shared affiliate network available to all direct clients and as fallback for TNC customers.',
    'platform',
    jsonb_build_object(
        'coverage_type', 'global',
        'regions', ARRAY['North America', 'Europe', 'Asia Pacific'],
        'cities', ARRAY['New York', 'Los Angeles', 'Chicago', 'Houston', 'Miami', 'Boston', 'Seattle', 'Denver']
    ),
    true,
    '3.5',
    1000,
    true,
    true,
    '5.00',
    '99.00',
    'monthly',
    '#2563EB',
    'https://via.placeholder.com/200x60/2563EB/FFFFFF?text=TransFlow+Shared',
    true,
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- 2. Create TNC-specific networks for existing TNC organizations
-- Metro Ride Transportation Network
INSERT INTO affiliate_networks (
    id,
    name,
    slug,
    description,
    network_type,
    coverage_area,
    auto_approval,
    min_rating_required,
    max_affiliates,
    requires_insurance,
    requires_background_check,
    commission_rate,
    membership_fee,
    fee_frequency,
    brand_color,
    logo_url,
    is_active,
    is_public,
    created_at,
    updated_at
) VALUES (
    '********-3333-3333-3333-********3333',
    'Metro Ride Transportation Network',
    'metro-ride-network',
    'Premium metropolitan transportation network managed by Metro Ride Network organization.',
    'tnc_managed',
    jsonb_build_object(
        'coverage_type', 'metropolitan',
        'cities', ARRAY['Boston', 'New York', 'Philadelphia', 'Washington DC'],
        'specialties', ARRAY['corporate', 'airport', 'events']
    ),
    false,
    '4.2',
    150,
    true,
    true,
    '7.50',
    '199.00',
    'monthly',
    '#059669',
    'https://via.placeholder.com/200x60/059669/FFFFFF?text=Metro+Ride',
    true,
    false,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Elite Corporate Transportation Network
INSERT INTO affiliate_networks (
    id,
    name,
    slug,
    description,
    network_type,
    coverage_area,
    auto_approval,
    min_rating_required,
    max_affiliates,
    requires_insurance,
    requires_background_check,
    commission_rate,
    membership_fee,
    fee_frequency,
    brand_color,
    logo_url,
    is_active,
    is_public,
    created_at,
    updated_at
) VALUES (
    '********-2222-2222-2222-********2222',
    'Elite Corporate Transportation Network',
    'elite-corporate-network',
    'High-end corporate transportation network managed by Elite Corporate Travel organization.',
    'tnc_managed',
    jsonb_build_object(
        'coverage_type', 'corporate',
        'cities', ARRAY['Boston', 'New York', 'Chicago', 'San Francisco'],
        'specialties', ARRAY['executive', 'luxury', 'corporate_events']
    ),
    false,
    '4.5',
    75,
    true,
    true,
    '10.00',
    '399.00',
    'monthly',
    '#7C2D12',
    'https://via.placeholder.com/200x60/7C2D12/FFFFFF?text=Elite+Corporate',
    true,
    false,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Luxury Concierge Transportation Network
INSERT INTO affiliate_networks (
    id,
    name,
    slug,
    description,
    network_type,
    coverage_area,
    auto_approval,
    min_rating_required,
    max_affiliates,
    requires_insurance,
    requires_background_check,
    commission_rate,
    membership_fee,
    fee_frequency,
    brand_color,
    logo_url,
    is_active,
    is_public,
    created_at,
    updated_at
) VALUES (
    '8975c2e2-600c-49b6-8ee0-f0f71ce812a1',
    'Luxury Concierge Transportation Network',
    'luxury-concierge-network',
    'Ultra-premium luxury transportation network managed by Luxury Concierge Transportation organization.',
    'tnc_managed',
    jsonb_build_object(
        'coverage_type', 'luxury',
        'cities', ARRAY['Boston', 'New York', 'Miami', 'Los Angeles'],
        'specialties', ARRAY['luxury', 'concierge', 'vip', 'hotel_services']
    ),
    false,
    '4.8',
    50,
    true,
    true,
    '15.00',
    '799.00',
    'monthly',
    '#7C3AED',
    'https://via.placeholder.com/200x60/7C3AED/FFFFFF?text=Luxury+Concierge',
    true,
    false,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- 3. Add a new column to track network associations without foreign key constraints
-- (Skip the parent_network_id updates that were causing the foreign key constraint error)

-- 4. Create network ownership relationships table if it doesn't exist
CREATE TABLE IF NOT EXISTS network_ownership (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    network_id UUID NOT NULL REFERENCES affiliate_networks(id) ON DELETE CASCADE,
    owner_organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    ownership_type TEXT NOT NULL DEFAULT 'managed',
    can_modify_settings BOOLEAN DEFAULT true,
    can_add_affiliates BOOLEAN DEFAULT true,
    can_set_pricing BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT network_ownership_type_check CHECK (
        ownership_type IN ('owned', 'managed', 'inherited', 'shared')
    ),
    
    UNIQUE(network_id, owner_organization_id)
);

-- 5. Insert network ownership relationships
INSERT INTO network_ownership (network_id, owner_organization_id, ownership_type, can_modify_settings, can_add_affiliates, can_set_pricing)
SELECT 
    '********-3333-3333-3333-********3333',
    id,
    'managed',
    true,
    true,
    true
FROM organizations 
WHERE name = 'Metro Ride Network' AND account_type = 'tnc_account'
ON CONFLICT (network_id, owner_organization_id) DO NOTHING;

INSERT INTO network_ownership (network_id, owner_organization_id, ownership_type, can_modify_settings, can_add_affiliates, can_set_pricing)
SELECT 
    '********-2222-2222-2222-********2222',
    id,
    'managed',
    true,
    true,
    true
FROM organizations 
WHERE name = 'Elite Corporate Travel' AND account_type = 'tnc_account'
ON CONFLICT (network_id, owner_organization_id) DO NOTHING;

INSERT INTO network_ownership (network_id, owner_organization_id, ownership_type, can_modify_settings, can_add_affiliates, can_set_pricing)
SELECT 
    '8975c2e2-600c-49b6-8ee0-f0f71ce812a1',
    id,
    'managed',
    true,
    true,
    true
FROM organizations 
WHERE name = 'Luxury Concierge Transportation' AND account_type = 'tnc_account'
ON CONFLICT (network_id, owner_organization_id) DO NOTHING;

-- 6. Add RLS policies for network_ownership
ALTER TABLE network_ownership ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "network_ownership_organization_isolation" ON network_ownership;
CREATE POLICY "network_ownership_organization_isolation" ON network_ownership
    FOR ALL USING (
        owner_organization_id IN (
            SELECT organization_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
        OR 
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND 'SUPER_ADMIN' = ANY(roles)
        )
    );

-- Grant permissions
GRANT ALL ON network_ownership TO authenticated;

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- DROP TABLE IF EXISTS network_ownership CASCADE;
-- DELETE FROM affiliate_networks WHERE id IN (
--   '********-1111-1111-1111-********1111',
--   '********-3333-3333-3333-********3333', 
--   '********-2222-2222-2222-********2222',
--   '8975c2e2-600c-49b6-8ee0-f0f71ce812a1'
-- );