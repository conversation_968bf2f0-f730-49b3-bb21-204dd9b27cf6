-- Migration: 146_fix_migration_141_policy_conflict.sql
-- Purpose: Fix the policy conflict in migration 141
-- Date: 2025-01-15

BEGIN;

-- Drop the conflicting policy if it exists
DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;

-- Create the correct policy
CREATE POLICY "user_settings_access_policy" ON user_settings
  FOR ALL USING (
    -- Users can access their own settings
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all settings (direct check, no function calls)
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.id = auth.uid()
      AND 'SUPER_ADMIN' = ANY(p.roles)
    )
  );

-- Ensure RLS is enabled
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

COMMIT;

-- Success message
SELECT 'Migration 146 completed - Fixed user_settings policy conflict' as message;