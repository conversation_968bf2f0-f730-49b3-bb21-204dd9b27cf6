-- Migration: Create Super Admin Profile for Authentication Testing
-- Purpose: <NAME_EMAIL> profile with SUPER_ADMIN role
-- Date: 2025-01-08

BEGIN;

-- Check if profile already exists, if not create it
DO $$
BEGIN
    -- Only insert if profile doesn't exist
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE email = '<EMAIL>') THEN
        INSERT INTO profiles (
            id,
            email,
            roles,
            created_at,
            updated_at
        ) VALUES (
            '1f325048-4f4a-444b-984e-ceb62fffe664',
            '<EMAIL>',
            ARRAY['SUPER_ADMIN'::user_role],
            NOW(),
            NOW()
        );
    ELSE
        -- Update existing profile to ensure it has SUPER_ADMIN role
        UPDATE profiles 
        SET roles = ARRAY['SUPER_ADMIN'::user_role], updated_at = NOW()
        WHERE email = '<EMAIL>';
    END IF;
END $$;

-- Also <NAME_EMAIL> user for testing
INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    role,
    aud,
    confirmation_token,
    email_change_token_new,
    recovery_token,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    last_sign_in_at
) VALUES (
    '7165a1a3-bbf2-40dd-a84f-8c0902abc82f',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    '$2b$10$roNyNqOwSR9crpWslF93v.rphTnd1C1J295V0CyYXDofqVJlJ2Vd6', -- Hashed password for 'password123'
    NOW(),
    NOW(),
    NOW(),
    'authenticated',
    'authenticated',
    '',
    '',
    '',
    '{"provider": "email", "providers": ["email"]}',
    '{"name": "Super Admin", "role": "SUPER_ADMIN"}',
    false,
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    updated_at = NOW(),
    raw_user_meta_data = EXCLUDED.raw_user_meta_data;

-- Insert corresponding <NAME_EMAIL>
INSERT INTO profiles (
    id,
    email,
    roles,
    created_at,
    updated_at
) VALUES (
    '7165a1a3-bbf2-40dd-a84f-8c0902abc82f',
    '<EMAIL>',
    ARRAY['SUPER_ADMIN']::user_role[],
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    roles = EXCLUDED.roles,
    updated_at = NOW();

-- Get the default organization ID
DO $$
DECLARE
    default_org_id UUID;
BEGIN
    SELECT id INTO default_org_id FROM organizations WHERE slug = 'transflow-shared' LIMIT 1;
    
    IF default_org_id IS NOT NULL THEN
        -- Ensure both users have access to the default organization
        INSERT INTO user_organizations (
            user_id,
            organization_id,
            role,
            created_at
        ) VALUES 
            ('1f325048-4f4a-444b-984e-ceb62fffe664', default_org_id, 'SUPER_ADMIN', NOW()),
            ('7165a1a3-bbf2-40dd-a84f-8c0902abc82f', default_org_id, 'SUPER_ADMIN', NOW())
        ON CONFLICT (user_id, organization_id) DO UPDATE SET
            role = EXCLUDED.role,
            updated_at = NOW();
    END IF;
END $$;

COMMIT;

-- Verification queries (commented out for migration):
-- SELECT id, email, roles FROM profiles WHERE email IN ('<EMAIL>', '<EMAIL>');
-- SELECT id, email FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>');