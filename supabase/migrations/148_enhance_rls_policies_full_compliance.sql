-- Migration: 148_enhance_rls_policies_full_compliance.sql
-- Purpose: Enhance RLS policies with full SUPER_ADMIN control architecture compliance
-- Date: 2025-01-15
-- Addresses: Permission templates, four-tier architecture, feature flags, subscription validation

BEGIN;

-- Create enhanced RLS policies with full compliance

-- 1. Enhanced Organizations Policy with Full Compliance
DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;
CREATE POLICY "organizations_enhanced_access_policy" ON organizations
  FOR ALL USING (
    -- SUPER_ADMIN can access all organizations (highest priority)
    is_super_admin_safe()
    OR
    -- Regular user access to their organizations
    (id IN (
      SELECT organization_id FROM user_organizations 
      WHERE user_id = auth.uid() AND status = 'active'
    ))
    OR
    -- TNC_ADMIN with proper permission template can access network organizations
    (EXISTS (
      SELECT 1 FROM profiles p
      JOIN organizations o ON o.id = p.organization_id
      WHERE p.id = auth.uid()
      AND 'TNC_ADMIN' = ANY(p.roles)
      AND o.permission_template = 'tnc_enterprise'
      AND (
                -- Can access their own organization     
        o.id = organizations.id
        OR
        -- Can access TNC customers under their hierarchy
        EXISTS (
          SELECT 1 FROM organizations child
          WHERE child.id = organizations.id
          AND child.parent_tnc_id = o.id
          AND child.account_type = 'tnc_customer'
        )
      )
    ))
  );

-- 2. Enhanced Profiles Policy with Permission Template Validation
DROP POLICY IF EXISTS "profiles_access_policy" ON profiles;
CREATE POLICY "profiles_enhanced_access_policy" ON profiles
  FOR ALL USING (
    -- SUPER_ADMIN can access all profiles
    is_super_admin_safe()
    OR
    -- Users can access their own profile
    auth.uid() = id
    OR
    -- TNC_ADMIN can access profiles in their network
    (EXISTS (
      SELECT 1 FROM profiles admin_profile
      JOIN organizations admin_org ON admin_org.id = admin_profile.organization_id
      JOIN organizations target_org ON target_org.id = profiles.organization_id
      WHERE admin_profile.id = auth.uid()
      AND 'TNC_ADMIN' = ANY(admin_profile.roles)
      AND admin_org.permission_template = 'tnc_enterprise'
      AND (
        target_org.id = admin_org.id
        OR
        target_org.parent_tnc_id = admin_org.id
      )
    ))
  );

-- 3. Enhanced User Organizations Policy with Four-Tier Architecture
DROP POLICY IF EXISTS "user_organizations_access_policy" ON user_organizations;
CREATE POLICY "user_organizations_enhanced_access_policy" ON user_organizations
  FOR ALL USING (
    -- SUPER_ADMIN can access all relationships
    is_super_admin_safe()
    OR
    -- Users can access their own organization relationships
    auth.uid() = user_id
    OR
    -- TNC_ADMIN can access relationships in their network
    (EXISTS (
      SELECT 1 FROM profiles admin_profile
      JOIN organizations admin_org ON admin_org.id = admin_profile.organization_id
      WHERE admin_profile.id = auth.uid()
      AND 'TNC_ADMIN' = ANY(admin_profile.roles)
      AND admin_org.permission_template = 'tnc_enterprise'
      AND (
        user_organizations.organization_id = admin_org.id
        OR
        EXISTS (
          SELECT 1 FROM organizations child_org
          WHERE child_org.id = user_organizations.organization_id
          AND child_org.parent_tnc_id = admin_org.id
        )
      )
    ))
  );

-- 4. Enhanced User Settings Policy with Subscription Validation
DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;
CREATE POLICY "user_settings_enhanced_access_policy" ON user_settings
  FOR ALL USING (
    -- SUPER_ADMIN can access all settings
    is_super_admin_safe()
    OR
    -- Users can access their own settings
    auth.uid() = user_id
    OR
    -- TNC_ADMIN can access settings for users in their network (enterprise subscription required)
    (EXISTS (
      SELECT 1 FROM profiles admin_profile
      JOIN organizations admin_org ON admin_org.id = admin_profile.organization_id
      JOIN profiles target_profile ON target_profile.id = user_settings.user_id
      JOIN organizations target_org ON target_org.id = target_profile.organization_id
      WHERE admin_profile.id = auth.uid()
      AND 'TNC_ADMIN' = ANY(admin_profile.roles)
      AND admin_org.permission_template = 'tnc_enterprise'
      AND admin_org.subscription_plan = 'enterprise'
      AND (
        target_org.id = admin_org.id
        OR
        target_org.parent_tnc_id = admin_org.id
      )
    ))
  );

-- Create function to validate TNC hierarchy
CREATE OR REPLACE FUNCTION validate_tnc_hierarchy(
  p_customer_org_id UUID,
  p_parent_tnc_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_super_admin BOOLEAN;
BEGIN
  -- Check if user is SUPER_ADMIN
  is_super_admin := is_super_admin_safe();
  
  -- SUPER_ADMIN can bypass validation
  IF is_super_admin THEN
    RETURN TRUE;
  END IF;
  
  -- Validate TNC customer belongs to correct parent
  RETURN EXISTS (
    SELECT 1 FROM organizations customer
    JOIN organizations parent ON parent.id = customer.parent_tnc_id
    WHERE customer.id = p_customer_org_id
    AND customer.account_type = 'tnc_customer'
    AND parent.id = p_parent_tnc_id
    AND parent.account_type = 'tnc_admin'
  );
END;
$$;

-- Create function to validate feature flag access
CREATE OR REPLACE FUNCTION validate_feature_flag_access(
  p_organization_id UUID,
  p_feature_flag TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_super_admin BOOLEAN;
BEGIN
  -- Check if user is SUPER_ADMIN
  is_super_admin := is_super_admin_safe();
  
  -- SUPER_ADMIN can bypass validation
  IF is_super_admin THEN
    RETURN TRUE;
  END IF;
  
  -- Check if feature is enabled for organization
  RETURN EXISTS (
    SELECT 1 FROM organizations
    WHERE id = p_organization_id
    AND feature_flags->>p_feature_flag = 'true'
  );
END;
$$;

-- Create function to validate subscription access
CREATE OR REPLACE FUNCTION validate_subscription_access(
  p_organization_id UUID,
  p_required_plan TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_super_admin BOOLEAN;
  org_plan TEXT;
BEGIN
  -- Check if user is SUPER_ADMIN
  is_super_admin := is_super_admin_safe();
  
  -- SUPER_ADMIN can bypass validation
  IF is_super_admin THEN
    RETURN TRUE;
  END IF;
  
  -- Get organization subscription plan
  SELECT subscription_plan INTO org_plan
  FROM organizations
  WHERE id = p_organization_id;
  
  -- Validate subscription hierarchy
  CASE p_required_plan
    WHEN 'enterprise' THEN
      RETURN org_plan IN ('enterprise', 'tnc_enterprise');
    WHEN 'professional' THEN
      RETURN org_plan IN ('professional', 'enterprise', 'tnc_enterprise');
    WHEN 'starter' THEN
      RETURN org_plan IN ('starter', 'professional', 'enterprise', 'tnc_enterprise');
    ELSE
      RETURN TRUE; -- free_trial or unknown
  END CASE;
END;
$$;

-- Enhanced audit logging for SUPER_ADMIN overrides
CREATE OR REPLACE FUNCTION log_super_admin_override(
  p_action TEXT,
  p_table_name TEXT,
  p_record_id UUID DEFAULT NULL,
  p_details JSONB DEFAULT '{}'::jsonb
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only log if user is SUPER_ADMIN
  IF is_super_admin_safe() THEN
    INSERT INTO audit_logs (action, user_id, details, created_at)
    VALUES (
      'super_admin_rls_override',
      auth.uid(),
      jsonb_build_object(
        'action', p_action,
        'table_name', p_table_name,
        'record_id', p_record_id,
        'override_reason', 'super_admin_access',
        'additional_details', p_details
      ),
      NOW()
    );
  END IF;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION validate_tnc_hierarchy(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_feature_flag_access(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_subscription_access(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION log_super_admin_override(TEXT, TEXT, UUID, JSONB) TO authenticated;

-- Log the enhancement (skipped due to missing details column)
-- INSERT INTO audit_logs (action, details, created_at) VALUES (...);

COMMIT;

-- Success message
SELECT 'Migration 148 completed - RLS policies enhanced with full SUPER_ADMIN control architecture compliance' as message;