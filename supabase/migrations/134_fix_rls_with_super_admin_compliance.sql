-- Migration: 134_fix_rls_with_super_admin_compliance.sql
-- Purpose: Properly fix RLS policies with production-ready multi-tenant isolation
-- Date: 2025-01-14

BEGIN;

-- REVERT EMERGENCY FIX: Re-enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Drop any existing problematic policies
DROP POLICY IF EXISTS "profiles_access_policy" ON profiles;
DROP POLICY IF EXISTS "user_organizations_access_policy" ON user_organizations;
DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;

-- Create a safe function to check SUPER_ADMIN status without recursion
-- Note: Keep existing is_super_admin_safe() function as it's used by many policies
DROP FUNCTION IF EXISTS is_super_admin_user();
CREATE OR REPLACE FUNCTION check_super_admin_role()
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM auth.users 
    WHERE id = auth.uid() 
    AND raw_user_meta_data->>'role' = 'SUPER_ADMIN'
  );
$$;

-- PROFILES TABLE: Simple, non-recursive policy
CREATE POLICY "profiles_multi_tenant_policy" ON profiles
  FOR ALL USING (
    -- Users can access their own profile
    auth.uid() = id
    OR
    -- SUPER_ADMIN can access all profiles
    check_super_admin_role()
  );

-- USER_ORGANIZATIONS TABLE: Non-recursive policy
CREATE POLICY "user_organizations_multi_tenant_policy" ON user_organizations
  FOR ALL USING (
    -- Users can access their own organization relationships
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all user-organization relationships
    check_super_admin_role()
  );

-- ORGANIZATIONS TABLE: Break the circular dependency
CREATE POLICY "organizations_multi_tenant_policy" ON organizations
  FOR ALL USING (
    -- SUPER_ADMIN can access all organizations
    check_super_admin_role()
    OR
    -- Users can access organizations they belong to (using direct auth.uid check)
    id IN (
      SELECT organization_id 
      FROM user_organizations 
      WHERE user_id = auth.uid()
    )
  );

-- Grant execute permission on the helper function
GRANT EXECUTE ON FUNCTION check_super_admin_role() TO authenticated;
GRANT EXECUTE ON FUNCTION check_super_admin_role() TO anon;

-- Add comprehensive logging
DO $$
BEGIN
  RAISE NOTICE 'PRODUCTION RLS POLICIES IMPLEMENTED';
  RAISE NOTICE '====================================';
  RAISE NOTICE 'Created production-ready RLS policies with:';
  RAISE NOTICE '- Multi-tenant isolation maintained';
  RAISE NOTICE '- SUPER_ADMIN override capabilities';
  RAISE NOTICE '- No circular dependencies';
  RAISE NOTICE '- Proper security definer functions';
  RAISE NOTICE '';
  RAISE NOTICE 'Tables with RLS enabled:';
  RAISE NOTICE '- profiles (own profile + SUPER_ADMIN access)';
  RAISE NOTICE '- user_organizations (own relationships + SUPER_ADMIN access)';
  RAISE NOTICE '- organizations (member access + SUPER_ADMIN access)';
  RAISE NOTICE '';
  RAISE NOTICE 'Helper function created: check_super_admin_role()';
END $$;

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- DROP POLICY "profiles_multi_tenant_policy" ON profiles;
-- DROP POLICY "user_organizations_multi_tenant_policy" ON user_organizations;
-- DROP POLICY "organizations_multi_tenant_policy" ON organizations;
-- DROP FUNCTION check_super_admin_role();