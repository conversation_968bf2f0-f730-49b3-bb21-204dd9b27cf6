-- Migration: 132_fix_infinite_recursion_rls_policies.sql
-- Purpose: Fix infinite recursion in RLS policies that's preventing data access
-- Date: 2025-01-14

BEGIN;

-- Drop all existing RLS policies that might be causing infinite recursion
DROP POLICY IF EXISTS "profiles_organization_isolation" ON profiles;
DROP POLICY IF EXISTS "user_organizations_organization_isolation" ON user_organizations;
DROP POLICY IF EXISTS "organizations_organization_isolation" ON organizations;
DROP POLICY IF EXISTS "profiles_select_policy" ON profiles;
DROP POLICY IF EXISTS "user_organizations_select_policy" ON user_organizations;
DROP POLICY IF EXISTS "organizations_select_policy" ON organizations;

-- Create simple, non-recursive RLS policies for profiles
CREATE POLICY "profiles_access_policy" ON profiles
  FOR ALL USING (
    -- Users can access their own profile
    auth.uid() = id
    OR
    -- SUPER_ADMI<PERSON> can access all profiles (direct role check)
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'SUPER_ADMIN'
    )
  );

-- Create simple, non-recursive RLS policies for user_organizations
CREATE POLICY "user_organizations_access_policy" ON user_organizations
  FOR ALL USING (
    -- Users can access their own organization relationships
    auth.uid() = user_id
    OR
    -- SUPER_ADMIN can access all user-organization relationships
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'SUPER_ADMIN'
    )
  );

-- Create simple, non-recursive RLS policies for organizations
DROP POLICY IF EXISTS "organizations_access_policy" ON organizations;
CREATE POLICY "organizations_access_policy" ON organizations
  FOR ALL USING (
    -- SUPER_ADMIN can access all organizations
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'SUPER_ADMIN'
    )
    OR
    -- Users can access organizations they belong to
    EXISTS (
      SELECT 1 FROM user_organizations 
      WHERE user_organizations.organization_id = organizations.id 
      AND user_organizations.user_id = auth.uid()
    )
  );

-- Ensure RLS is enabled on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Add logging to track policy usage (skip if audit_logs doesn't have details column)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'audit_logs' AND column_name = 'details') THEN
    INSERT INTO audit_logs (action, details, created_at) 
    VALUES (
      'rls_policies_fixed',
      jsonb_build_object(
        'migration', '132_fix_infinite_recursion_rls_policies',
        'tables_fixed', ARRAY['profiles', 'user_organizations', 'organizations'],
        'issue', 'infinite_recursion_resolved'
      ),
      NOW()
    );
  END IF;
END $$;

COMMIT;

-- Rollback instructions (commented):
-- To rollback this migration:
-- DROP POLICY "profiles_access_policy" ON profiles;
-- DROP POLICY "user_organizations_access_policy" ON user_organizations;
-- DROP POLICY "organizations_access_policy" ON organizations;