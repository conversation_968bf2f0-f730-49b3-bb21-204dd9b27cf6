-- Migration: 140_fix_network_switcher_final_clean.sql
-- Purpose: Final clean fix for network switcher and user_settings 409 conflicts
-- Date: 2025-01-14

BEGIN;

-- Fix user_settings duplicates and add constraint
DELETE FROM user_settings 
WHERE ctid NOT IN (
    SELECT MIN(ctid) 
    FROM user_settings 
    GROUP BY user_id, setting_name
);

-- Add unique constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'user_settings_user_setting_unique'
    ) THEN
        ALTER TABLE user_settings 
        ADD CONSTRAINT user_settings_user_setting_unique 
        UNIQUE (user_id, setting_name);
    END IF;
END $$;

-- Create simple RLS policy for user_settings
DROP POLICY IF EXISTS "user_settings_access_policy" ON user_settings;
CREATE POLICY "user_settings_access_policy" ON user_settings
  FOR ALL USING (
    auth.uid() = user_id
    OR
    is_super_admin_safe()
  );

ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Ensure SUPER_ADMIN has access to all organizations
DO $$
DECLARE
    super_admin_user_id UUID;
    default_org_id UUID;
    org_count INTEGER;
BEGIN
    -- Get first SUPER_ADMIN user
    SELECT id INTO super_admin_user_id 
    FROM profiles 
    WHERE 'SUPER_ADMIN' = ANY(roles)
    ORDER BY created_at ASC 
    LIMIT 1;
    
    IF super_admin_user_id IS NOT NULL THEN
        -- Add missing organization access
        INSERT INTO user_organizations (user_id, organization_id, role, created_at)
        SELECT 
            super_admin_user_id,
            o.id,
            'SUPER_ADMIN',
            NOW()
        FROM organizations o
        WHERE o.id NOT IN (
            SELECT organization_id 
            FROM user_organizations 
            WHERE user_id = super_admin_user_id
        )
        ON CONFLICT (user_id, organization_id) DO NOTHING;
        
        -- Get count of organizations
        SELECT COUNT(*) INTO org_count
        FROM user_organizations 
        WHERE user_id = super_admin_user_id;
        
        -- Set default organization (use shared network if available)
        SELECT organization_id INTO default_org_id
        FROM user_organizations uo
        JOIN organizations o ON o.id = uo.organization_id
        WHERE uo.user_id = super_admin_user_id 
        AND (o.name ILIKE '%shared%' OR o.name ILIKE '%transflow%')
        ORDER BY o.created_at ASC 
        LIMIT 1;
        
        -- If no shared network, use first organization
        IF default_org_id IS NULL THEN
            SELECT organization_id INTO default_org_id
            FROM user_organizations 
            WHERE user_id = super_admin_user_id 
            ORDER BY created_at ASC 
            LIMIT 1;
        END IF;
        
        -- Update user settings
        IF default_org_id IS NOT NULL THEN
            INSERT INTO user_settings (user_id, setting_name, setting_value, created_at, updated_at)
            VALUES (
                super_admin_user_id, 
                'app.current_organization_id', 
                to_json(default_org_id::text),
                NOW(),
                NOW()
            )
            ON CONFLICT (user_id, setting_name) DO UPDATE 
            SET 
                setting_value = EXCLUDED.setting_value,
                updated_at = NOW();
        END IF;
        
        RAISE NOTICE 'SUPER_ADMIN user % now has access to % organizations with default org %', 
            super_admin_user_id, org_count, default_org_id;
        RAISE NOTICE 'Migration 140 completed - Network switcher and user_settings fixed with proper SUPER_ADMIN access';
    END IF;
END $$;

-- Drop existing function to avoid signature conflicts
DROP FUNCTION IF EXISTS get_user_accessible_organizations(UUID);

-- Create helper function for network switcher (matching existing signature)
CREATE OR REPLACE FUNCTION get_user_accessible_organizations(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    slug TEXT,
    organization_type TEXT,
    role TEXT,
    is_current BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_super_admin BOOLEAN := FALSE;
  default_org_id UUID;
BEGIN
  -- Check if user is SUPER_ADMIN
  SELECT 'SUPER_ADMIN' = ANY(p.roles) INTO is_super_admin
  FROM profiles p
  WHERE p.id = user_uuid;
  
  -- Get user's default organization from settings
  SELECT 
    COALESCE(
      (us.setting_value->>'current_organization_id')::UUID,
      '11111111-1111-1111-1111-111111111111'::UUID
    )
  INTO default_org_id
  FROM user_settings us
  WHERE us.user_id = user_uuid 
    AND us.setting_name = 'app.current_organization_id'
  UNION ALL
  SELECT '11111111-1111-1111-1111-111111111111'::UUID
  LIMIT 1;
  
  -- Return organizations based on user role
  IF is_super_admin THEN
    -- SUPER_ADMIN can access all organizations
    RETURN QUERY 
    SELECT 
      o.id,
      o.name::TEXT,
      o.slug::TEXT,
      o.organization_type::TEXT,
      'SUPER_ADMIN'::TEXT,
      (o.id = default_org_id)
    FROM organizations o
    WHERE o.status = 'active'
    ORDER BY o.name;
  ELSE
    -- Regular users can only access organizations they're assigned to
    RETURN QUERY 
    SELECT 
      o.id,
      o.name::TEXT,
      o.slug::TEXT,
      o.organization_type::TEXT,
      uo.role::TEXT,
      (o.id = default_org_id)
    FROM organizations o
    INNER JOIN user_organizations uo ON o.id = uo.organization_id
    WHERE o.status = 'active'
      AND uo.user_id = user_uuid
      AND uo.status = 'active'
    ORDER BY o.name;
  END IF;
END $$;

GRANT EXECUTE ON FUNCTION get_user_accessible_organizations(UUID) TO authenticated;

COMMIT;